business:
  companyManager: jinjiang
  financeManager: yuanmian
  incomeCalculateAutoCalculateProjectTypes: 629760732287729664,629766352067887104,629766914532442112
  incomeCalculateProjectTypes: 618017268428701696,618019338787487744,618020029195091968,618020489972940800,618804166369738752,618805461604368384
  marketingCenterManager: lidong2
  meiCloudUnitId: 616744224850706432
  pmoManager: mengfangao
  pmoNotRdmManager: chenlin35
  pmoRdmManager: qujz
  robotUnitId: 581144287652085760
  yueyunUnitId: 666278517574467584
esb:
  gfp:
    securitycode: 123456
esb_client:
  replyDestination: LOCALQ.PAM.RSP
  JMSConfiguration:
    CCSID: '1208'
    transportType: '1'
    connectionNameList: gw1esbjydev.midea.com(11001),gw2esbjydev.midea.com(12001)
    channel: PAM.DEF.SVRCONN
  receiveTimeout: '120000'
  longReceiveTimeout: '600000'
  targetDestination: LOCALQ.C.PAM.REQ
esb_server:
  JMSConfiguration:
    transportType: '1'
    CCSID: '1208'
    channel: PAM.DEF.SVRCONN
    connectionNameList1: gw1esbjydev.midea.com(11101)
    connectionNameList2: gw2esbjydev.midea.com(12101)
  sessionCacheSize: '100'
  targetDestination: LOCALQ.P.PAM.REQ
  concurrentConsumers: '5'
eureka:
  client:
    serviceUrl:
      defaultZone: http://localhost:8761/eureka
  instance:
    prefer-ip-address: true
expression:
  aop:
    pointcut: '@within(org.springframework.web.bind.annotation.RestController)'
logger:
  parameter:
    enable: false
    level: DEBUG
    mdc-storage:
      enable: false
    name: PARAMETER_LOG
  trace-code:
    enable: true
    mdc-trace-code-key: traceCode
meixing:
  accessToken: 2f5b38ff-f412-40aa-8a7d-0e004660eed9
  appKey: e21c13e0-b57a-49f1-985b-01af30232e1a
  jumpUrl: https://pamsit.midea.com
  mcWidgetIdentifier: sit
  tempMsgUrl: http://mapnew5sit.midea.com/mpm-api/api/tempmsg/create_temp_msg_and_push
mframework-provider:
  ribbon:
    MaxAutoRetriesNextServer: 2
    NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
    OkToRetryOnAllOperations: true
    listOfServers: ************:61001
mgp:
  carrier:
    discovery:
      env: sit
      system: pam
      version: 1.0
mip:
  api-info:
    contact:
      name: chengzy7
      pathsRegex: ^.*(?<!error)$
    title: pam
    version: 1.0.0
  conf:
    debugPageResult: false
    defaultLocale: zh_CN
    recordOperateLog: true
    supportLocales:
      - zh_CN
      - en_US
      - ja_JP
    unwarpUrls:
      - /**
  workflow:
    appId: pam-dev
    interfaceUrl: http://iflowsit.midea.com/mbpmEngineECO/data/
    secretKey: 95b9b1a3-7328-4c0f-a362-b131db25b4ff
perm:
  keyset: '{"primaryKeyId":38010253,"key":[{"keyData":{"typeUrl":"type.googleapis.com/google.crypto.tink.AesCtrHmacAeadKey","keyMaterialType":"SYMMETRIC","value":"EiYSAggQGiCaYy3+3iKHZRb4PPubtuQKxsijapOmKymb74BxYMAuHBooEgQIAxAgGiDKChGjivTOr9B3kfZqvo7GlxYLi8oqGk5zjH8J1b3nRQ=="},"outputPrefixType":"TINK","keyId":38010253,"status":"ENABLED"}]}'
  secret: 4.111519718186378E31
ribbon:
  ConnectTimeout: 60000
  MaxAutoRetries: 1
  MaxAutoRetriesNextServer: 1
  OkToRetryOnAllOperations: false
  ReadTimeout: 60000
route:
  contractUrl: https://pamsit.midea.com/#/contract/detail/
  leadUrl: https://pamsit.midea.com:8080/#/lead/detail/
  milepostNoticeUrl: https://pamsit.midea.com/#/project/milestone-warn-list?batchId=
  milepostUrl: https://pamsit.midea.com/#/project/detail/%?active=milestone
  myProjectPendingCloseUrl: https://pamsit.midea.com/#/project/my-wait-conclusion-list
  productTaskUrl: https://pamsit.midea.com/#/finance/revenue-forecast/task/product/
  projectTaskUrl: https://pamsit.midea.com/#/finance/revenue-forecast/task/project/
  projectUrl: https://pamsit.midea.com/#/project/detail/
  subContractUrl: https://pamsit.midea.com/#/contract/subcontract-detail/
  workingHourUrl: https://pamsit.midea.com/#/workhour/my?active=audit&userId=
sdk:
  mform:
    appID: SRMFILE
    appKey: f1d82e0b-26ba-40c3-8be8-14ded9fff336
    bgHost: https://iflowsit.midea.com/formEngine
    downLoadServiceName: /mip-document/document/docTranmission2/public/download
    forwardTo: mip4cdev5.midea.com:60050
    getInfoServiceName: /mip-document/document/sys/docMain/getById
    moduleName: SRMGSC
    serviceName: /document/sys/docTransmission/download
    uploadRestUrlHeader: https://imipsit.midea.com
    uploadServiceName: /mip-document/document/sys/docTranmission/upload
spring:
  cluster:
    redis:
      blockWhenExhausted: false
      database: 0
      expire: 1800
      host: *************
      masterName: cluster6379
      maxIdle: 50
      maxTotal: 200
      maxWaitMillis: 1500
      numTestsPerEvictionRun: 1024
      password: ygwHPik57
      port: 6379
      type: singleton
  datasource:
    driverClassName: com.mysql.jdbc.Driver
    filters: stat,wall,log4j
    initialSize: 5
    logAbandoned: true
    maxActive: 50
    maxPoolPreparedStatementPerConnectionSize: 20
    maxWait: 60000
    minEvictableIdleTimeMillis: 25200000
    minIdle: 5
    password: tqVhr1zM5
    platform: mysql
    poolPreparedStatements: true
    removeAbandoned: true
    removeAbandonedTimeout: 1800
    testOnBorrow: false
    testOnReturn: false
    testWhileIdle: true
    timeBetweenEvictionRunsMillis: 60000
    type: com.alibaba.druid.pool.DruidDataSource
    url: *************************************************************************************************************************************************
    useGlobalDataSourceStat: true
    username: pam_sit
    validationQuery: SELECT 1
swagger:
  enable: false
xxl:
  job:
    accessToken: 15b452943c464f05861d0f49a6c59f27
    admin:
      addresses: https://apiuat.midea.com/djs
    enabled: false
    executor:
      accessKeyId: vtYA2DSLDLpJnunayh3SZivf
      accessKeySecret: 18kM1A09xXX9Hei06ThZhc3KgDCTDca2
      appName: PAMJOB-SIT-STATISTICS
      idmTokenUrl: https://apiuat.midea.com/iam/v1/security/getAccessToken
      logPath: /apps/pam/logs/xxl-job/jobhandler
      logretentiondays: 7
      port: 9994
      version: 1.0