# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

PAM (Project and Asset Management) is a multi-module Spring Boot microservices application built for Midea Group. It's a comprehensive enterprise resource planning system handling customer relationship management, contract management, statistics, and gateway services.

## Architecture

The system follows a modular microservices architecture with the following main modules:

- **basedata** - Basic data services and master data management
- **crm** - Customer Relationship Management module
- **ctc** - Contract management module (main business logic)
- **gateway** - API gateway and routing service
- **statistics** - Reporting and analytics module
- **mdw** - Digital dashboard module
- **common-module** - Shared utilities and common components
- **support-component** - Supporting components and utilities
- **framework** - Core framework components

Each module follows a standard layered architecture:
- **-boot**: Spring Boot application entry point
- **-api**: API interfaces and DTOs
- **-web**: REST controllers and web layer
- **-impl**: Business logic implementation
- **-dao**: Data access layer with MyBatis
- **-common**: Common utilities and configurations
- **-esb**: Enterprise service bus integration

## Build and Development Commands

### Maven Commands
```bash
# Build entire project
mvn clean compile

# Build specific module
cd basedata && mvn clean compile

# Run tests
mvn test

# Package application
mvn clean package

# Run specific module locally
cd basedata/basedata-boot && mvn spring-boot:run
```

### Running Services
Each service can be started independently:
- **Basedata Service**: `com.midea.pam.basedata.boot.BasedataApp`
- **CRM Service**: `com.midea.pam.crm.CrmApp`  
- **CTC Service**: `com.midea.pam.ctc.boot.CtcApp`
- **Gateway Service**: Run from gateway-boot module
- **Statistics Service**: Run from statistics-boot module

### Environment Configuration
Services support multiple environments through Spring profiles:
- **local** - Local development
- **dev** - Development environment
- **sit** - System integration testing
- **uat** - User acceptance testing
- **ver** - Verification environment

Configuration files are located in `src/main/resources/{environment}/application.yml`

## Technology Stack

- **Java 8** with Maven
- **Spring Boot 2.0.3** with Spring Cloud Finchley
- **MyBatis** for database access with MySQL
- **Eureka** for service discovery
- **OpenFeign** for service-to-service communication
- **Redis** for caching
- **RocketMQ** for messaging
- **SkyWalking** for monitoring
- **Docker** for containerization

## Testing

- Uses **JUnit 4.12** with PowerMock for unit testing
- CRM module includes **JaCoCo** for code coverage
- Test files follow pattern `*Test.java`
- Run tests with: `mvn test` or `mvn surefire:test`

## Git Commit Conventions

Follow the commit message format from gateway/README.md:
- **feat**: New feature
- **fix**: Bug fix  
- **docs**: Documentation
- **style**: Formatting changes
- **refactor**: Code refactoring
- **test**: Adding tests
- **chore**: Build process or auxiliary tools

For RDM defect fixes, include the defect number: `fix: description (BUG2020040328034)`

## Database

Uses MySQL with extensive SQL migration scripts located in `common-module/sql/` organized by year and date. The system supports multiple database environments with different configurations per environment.

## Key Business Domains

- **Customer Management** (CRM module)
- **Contract Management** (CTC module) - Core business logic
- **Material and Procurement** (Basedata module)
- **Project Management** (CTC module)
- **Financial Integration** (Gateway module)
- **Reporting and Analytics** (Statistics module)

## Development Notes

- Each module maintains its own database schema and business logic
- Services communicate via Feign clients and REST APIs
- Configuration is externalized through Spring Cloud Config
- Logging uses Logback with structured JSON format
- Monitoring integrated with SkyWalking APM
- Supports multi-tenant architecture with tenant isolation