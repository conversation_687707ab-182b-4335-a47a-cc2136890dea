package com.midea.pam.basedata.web;

import com.github.pagehelper.PageInfo;
import com.midea.pam.basedata.service.MaterialPriceConfigDetailExecuteService;
import com.midea.pam.basedata.service.MaterialPriceService;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.MaterialPriceConfigDetailExecuteDto;
import com.midea.pam.common.basedata.dto.MaterialPriceDetailDto;
import com.midea.pam.common.basedata.dto.MaterialPriceDto;
import com.midea.pam.common.basedata.dto.MaterialPricePlanDto;
import com.midea.pam.common.basedata.entity.MaterialPriceConfigDetailExecute;
import com.midea.pam.common.crm.dto.PlanCurrencyCostDto;
import com.midea.pam.common.enums.MaterialPriceConfigExecutionStatusEnum;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Api("物料价格")
@RestController
@RequestMapping({"materialPrice"})
public class MaterialPriceController {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private MaterialPriceService materialPriceService;

    @Resource
    private MaterialPriceConfigDetailExecuteService materialPriceConfigDetailExecuteService;

    @ApiOperation(value = "物料价格查询-列表")
    @GetMapping("page")
    public Response page(MaterialPriceDto dto,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        PageInfo<MaterialPriceDto> page = materialPriceService.page(dto, pageSize, pageNum);
        return Response.dataResponse().setData(page);
    }

    @ApiOperation(value = "物料价格查询-列表导出")
    @GetMapping("exportList")
    public Response exportList(MaterialPriceDto dto) {
        List<MaterialPriceDto> exportList = materialPriceService.exportList(dto);
        return Response.dataResponse().setData(exportList);
    }

    @ApiOperation(value = "物料价格-详情")
    @GetMapping("view")
    public Response view(Long id) {
        MaterialPriceDetailDto dto = materialPriceService.getMaterialPriceDetailDtoById(id);
        return Response.dataResponse().setData(dto);
    }

    @ApiOperation(value = "物料价格-详情-获取执行快照")
    @GetMapping("configDetail")
    public Response configDetail(Long executeId) {
        MaterialPriceConfigDetailExecuteDto dto = materialPriceService.configDetail(executeId);
        return Response.dataResponse().setData(dto);
    }

    @ApiOperation(value = "物料价格-锁定")
    @PostMapping("lock")
    public Response lock(@RequestBody List<Long> ids) {
        DataResponse<Boolean> response = Response.dataResponse();
        materialPriceService.lock(ids);
        return response.setData(Boolean.TRUE);
    }

    @ApiOperation(value = "物料价格-解锁")
    @PostMapping("unLock")
    public Response unLock(@RequestBody List<Long> ids) {
        DataResponse<Boolean> response = Response.dataResponse();
        materialPriceService.unLock(ids);
        return response.setData(Boolean.TRUE);
    }

    @ApiOperation(value = "主动调用生成物料价格")
    @GetMapping("active")
    public Response active(Long configHeaderId) {
        DataResponse<Boolean> response = Response.dataResponse();
        //读取配置
        List<MaterialPriceConfigDetailExecute> executeList = materialPriceConfigDetailExecuteService.getAllMaterialPriceConfigDetail(configHeaderId);
        if (CollectionUtils.isEmpty(executeList)) {
            throw new ApplicationBizException("没有可执行的配置,请检查");
        }
        List<Long> configHeaderIds = Arrays.asList(configHeaderId);
        //更新material_price_config_header为运行中
        materialPriceService.updateExecutionStatus(MaterialPriceConfigExecutionStatusEnum.RUNNING.getCode(), configHeaderIds);
        //生成物料价格数据
        materialPriceService.generateMaterialPrice(executeList);
        return response.setData(Boolean.TRUE);
    }

    @ApiOperation(value = "定时测试")
    @GetMapping("/testJob")
    public Response testJob() {
        DataResponse<Boolean> dataResponse = Response.dataResponse();
        logger.info("根据类型配置生成物料价格定时任务开始");
        final long start = System.currentTimeMillis();
        //读取配置
        List<MaterialPriceConfigDetailExecute> executeList = materialPriceConfigDetailExecuteService.getAllMaterialPriceConfigDetail(null);
        if (CollectionUtils.isEmpty(executeList)) {
            throw new ApplicationBizException("没有可执行的配置,请检查");
        }
        List<Long> configHeaderIds = executeList.stream().map(MaterialPriceConfigDetailExecute::getConfigHeaderId).collect(Collectors.toList());
        //更新material_price_config_header为运行中
        materialPriceService.updateExecutionStatus(MaterialPriceConfigExecutionStatusEnum.RUNNING.getCode(), configHeaderIds);
        //生成物料价格数据
        materialPriceService.generateMaterialPrice(executeList);
        logger.info("根据类型配置生成物料价格定时任务结束，总共耗时:{}", System.currentTimeMillis() - start);
        return dataResponse.setData(Boolean.TRUE);
    }

    @ApiOperation(value = "根据使用单位获取物料价格列表-商机专用")
    @GetMapping("getMaterialPriceByFormConfig")
    public Response getMaterialPriceByFormConfig(@RequestParam Long unitId,
                                                 @ApiParam(value = "业务实体Id") Long ouId,
                                                 @ApiParam(value = "币种") String currency,
                                                 @RequestParam(required = false) @ApiParam(value = "pam编码") String pamCode,
                                                 @RequestParam(required = false) @ApiParam(value = "erp编码") String erpCode) {
        List<MaterialPricePlanDto> list = materialPriceService.getMaterialPriceByFormConfig(unitId, ouId, currency, pamCode, erpCode);
        return Response.dataResponse().setData(list);
    }

    @ApiOperation(value = "产品/物料成本批量导入")
    @PostMapping("importBatchFromExcel")
    public Response importBatchFromExcel(@RequestBody PlanCurrencyCostDto dto) {
        DataResponse<PlanCurrencyCostDto> response = Response.dataResponse();
        response.setData(materialPriceService.importBatchFromExcel(dto));
        return response;
    }

    @ApiOperation(value = "根据物料价格类型查询物料价格")
    @PostMapping("getMaterialPriceByConfigHeader")
    public Response getMaterialPriceByConfigHeader(@RequestBody MaterialPriceDto dto) {
        return Response.dataResponse().setData(materialPriceService.getMaterialPriceByConfigHeader(dto));
    }

    @ApiOperation(value = "根据物料价格类型查询物料价格实体信息")
    @PostMapping("getMaterialPriceEntityByConfigHeader")
    public Response getMaterialPriceEntityByConfigHeader(@RequestBody MaterialPriceDto dto) {
        return Response.dataResponse().setData(materialPriceService.getMaterialPriceByConfigHeader(dto));
    }
}
