business:
  carryoverBillCheckFilePath: /apps/pam/basedata/file/
  companyFilterOrgMap:
    '581144287652085760': 美的集团/库卡中国/工业自动化/技术部（顺德）,美的集团/库卡中国/工业自动化/工程部（顺德）,美的集团/库卡中国/工业自动化/项目管理部（顺德）,美的集团/库卡中国/工业服务/工程服务/方案设计,美的集团/库卡中国/工业服务/工程服务/项目管理,美的集团/库卡中国/工业服务/客户交互中心,美的集团/库卡中国/工业服务/库卡学院,美的集团/库卡中国/工业服务/备件及物流,美的集团/库卡中国/工业服务/产品服务,美的集团/库卡中国/工业服务/数字技术,美的集团/库卡中国/工业服务/服务销售,美的集团/库卡中国/工业服务/>诚新服务,美的集团/库卡中国/工业服务/顺德服务中心
    '814428597413478400': 美的集团/库卡中国/工业自动化/一般工业销售部,美的集团/库卡中国/工业自动化/零部件业务销售部,美的集团/库卡中国/工业自动化/技术部（昆山）,美的集团/库卡中国/工业自动化/工程部（昆山）,美的集团/库卡中国/工业自动化/项目管理部（昆山）,美的集团/库卡中国/工业服务/工程服务/工程执行,美的集团/库卡中国/工业服务/工程服务/昆山服务,美的集团/库卡中国/质量部/库卡工业自动化质量管理,美的集团/库卡中国/制造工厂/昆山工厂
    '844572813460242432': 美的集团/楼宇科技事业部/上海美控公司,美的集团/楼宇科技事业部/上海美控公司/工程与服务部,美的集团/楼宇科技事业部/上海美控公司/工程与服务部/工程组,美的集团/楼宇科技事业部/上海美控公司/工程与服务部/服务组
    '846396138532634624': 美的集团/库卡中国/移动机器人业务部,美的集团/库卡中国/移动机器人业务部/项目交付
    '869874391805067264': 美的集团/库卡中国/瑞仕格医疗/项目管理部,美的集团/库卡中国/瑞仕格医疗/客户服务部,美的集团/库卡中国/瑞仕格医疗/客户服务部/北区售后,美的集团/库卡中国/瑞仕格医疗/客户服务部/南区售后,美的集团/库卡中国/瑞仕格医疗/项目管理部/项目调试,美的集团/库卡中国/瑞仕格医疗/产品技术部,美的集团/库卡中国/瑞仕格医疗/产品技术部/机械结构,美的集团/库卡中国/瑞仕格医疗/产品技术部/软件开发,美的集团/库卡中国/瑞仕格医疗/客户服务部/中区售后,美的集团/库卡中国/瑞仕格医疗/客户服务部/中区售后,美的集团/库卡中国/瑞仕格医疗/项目管理部/现场管理,美的集团/库卡中国/瑞仕格医疗/项目管理部/仓管,美的集团/库卡中国/瑞仕格医疗/产品技术部/产品方案,美的集团/库卡中国/瑞仕格医疗/产品技术部/测试验证,美的集团/库卡中国/瑞仕格医疗/产品技术部/文档管理,美的集团/库卡中国/瑞仕格医疗/项目管理部/项目管理
  companyIdRuleMap:
    '1103432849530290176': LYKJ
    '581144287652085760': ROBOT
    '616744224850706432': MEIYUN
    '814428597413478400': KS
    '884455544528568320': YL
  companyRoleRuleMap:
    '616744224850706432': unit
    '666278517574467584': position
  hrUrlPath: http://icgps.midea.com.cn:9080/mjattend/services/mxAttendService?wsdl
  initRoleMap:
    '607791044196564992': 美的集团/库卡中国
    '617440830977736704': 美的集团/美云智数,美的集团/广东粤云
  meiCloudUnitId: 616744224850706432
  robotUnitId: 581144287652085760
  userUnitMap:
    '581144287652085760': 美的集团/库卡中国
    '616744224850706432': 美的集团/美云智数
    '666278517574467584': 美的集团/广东粤云
  yueyunUnitId: 666278517574467584
esb:
  gfp:
    securitycode: 123456
esb_client:
  replyDestination: LOCALQ.PAM.RSP
  JMSConfiguration:
    CCSID: '1208'
    transportType: '1'
    connectionNameList: Testgw1esbjydev.midea.com(11001),Testgw2esbjydev.midea.com(12001)
    channel: PAM.DEF.SVRCONN
  receiveTimeout: '120000'
  longReceiveTimeout: '600000'
  targetDestination: LOCALQ.C.PAM.REQ
esb_server:
  JMSConfiguration:
    transportType: '1'
    CCSID: '1208'
    channel: PAM.DEF.SVRCONN
    connectionNameList1: gw1esbjydev.midea.com(11101)
    connectionNameList2: gw2esbjydev.midea.com(12101)
  sessionCacheSize: '100'
  targetDestination: LOCALQ.P.PAM.REQ
  concurrentConsumers: '5'
eureka:
  client:
    serviceUrl:
      defaultZone: http://localhost:8761/eureka
  instance:
    prefer-ip-address: true
expression:
  aop:
    pointcut: '@within(org.springframework.web.bind.annotation.RestController)'
idm:
  apiurl: https://permuatapi.midea.com
  appKey: 392860fac70f4dc58e80e9f0b0130658
  incrementalDay: 7
  incrementalHour: 6
  orgFilter: ************,00023888,3072878917,7871248373
  secretId: 8e80e9f0b0130658
ldap:
  employeeBase: ou=People,o=midea.com.cn,o=isp
  employeeFilter: (&(objectclass=top)(objectclass=person)(objectclass=OrganizationalPerson)(objectclass=inetorgperson)(objectclass=midea-person))
  externalEmployeeBase: ou=External,o=midea.com.cn,o=isp
  externalEmployeeFilter: (&(objectclass=top)(objectclass=person)(objectclass=OrganizationalPerson)(objectclass=inetorgperson)(objectclass=midea-person))
  externalOrgFilter: (o=123)
  internalOrgFilter: (|(o=************)(o=00023888)(o=3072878917))
  orgBase: ou=departmentNumber,o=mideainfo
  orgFilter: (&(objectclass=top)(objectclass=midea-attr)(objectclass=midea-organization)(objectclass=organization))
  password: Mq4Km4Yi2L
  positionBase: ou=midea-position,o=mideainfo
  positionFilter: (&(objectclass=top)(objectclass=midea-attr))
  url: ldap://ldapuat.midea.com:389
  username: uid=pam_bind,ou=applications,o=midea.com.cn,o=isp
logger:
  parameter:
    enable: false
    level: DEBUG
    mdc-storage:
      enable: false
    name: PARAMETER_LOG
  trace-code:
    enable: true
    mdc-trace-code-key: traceCode
mbf:
  mq:
    consumer:
      consume-thread-max: 64
      core-pool-size: 20
      feign:
        context-path: /pam-basedata
        service-name: pam
      groups:
        GROUP-BASEMQ-TEST-01:
          consume-thread-max: 64
          core-pool-size: 20
        GROUP-BASEMQ-TEST-02:
          consume-thread-max: 64
          core-pool-size: 20
        GROUP-BASEMQ-TEST-03:
          consume-thread-max: 64
          core-pool-size: 20
      id-type: auto-increment
      max-fail-retry-times: 1
      persist-type: feign
  txmq:
    close-persist: false
    enabled: true
    id-type: pam-id4j
mframework-provider:
  ribbon:
    MaxAutoRetriesNextServer: 2
    NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
    OkToRetryOnAllOperations: true
    listOfServers: 10.23.121.30:61001
mgp:
  apiurl: https://apisit.midea.com/
  carrier:
    discovery:
      env: sit
      system: pam
      version: 1.0
perm:
  keyset: '{"primaryKeyId":38010253,"key":[{"keyData":{"typeUrl":"type.googleapis.com/google.crypto.tink.AesCtrHmacAeadKey","keyMaterialType":"SYMMETRIC","value":"EiYSAggQGiCaYy3+3iKHZRb4PPubtuQKxsijapOmKymb74BxYMAuHBooEgQIAxAgGiDKChGjivTOr9B3kfZqvo7GlxYLi8oqGk5zjH8J1b3nRQ=="},"outputPrefixType":"TINK","keyId":38010253,"status":"ENABLED"}]}'
  secret: 4.111519718186378E31
ribbon:
  ConnectTimeout: 60000
  MaxAutoRetries: 1
  MaxAutoRetriesNextServer: 1
  OkToRetryOnAllOperations: false
  ReadTimeout: 60000
rocketmq:
  basedata:
    inv-item-import:
      group: CG-PAM-BASEDATA_INV_ITEM_IMPORT
      topic: TOPIC-PAM-BASEDATA_INV_ITEM_IMPORT
  consumer:
    access-key: fap-mq-sit
    group: pam-basedata
    secret-key: Vz3#nK7@q9
  name-server: 10.23.121.88:9876
  producer:
    access-key: fap-mq-sit
    group: pam-basedata
    secret-key: Vz3#nK7@q9
    send-message-timeout: 3000
route:
  contractUrl: https://pamsit.midea.com/#/contract/detail/
  milepostNoticeUrl: https://pamsit.midea.com/#/project/milestone-warn-list?batchId=
  milepostUrl: https://pamsit.midea.com/#/project/detail/%?active=milestone
  projectUrl: https://pamsit.midea.com/#/project/detail/
  subContractUrl: https://pamsit.midea.com/#/contract/subcontract-detail/
  workingHourUrl: https://pamsit.midea.com/#/workhour/my?active=audit&userId=
sdk:
  mform:
    appID: SRMFILE
    appKey: f1d82e0b-26ba-40c3-8be8-14ded9fff336
    bgHost: https://iflowsit.midea.com/formEngine
    downLoadServiceName: /mip-document/document/docTranmission2/public/download
    forwardTo: mip4cdev5.midea.com:60050
    getInfoServiceName: /mip-document/document/sys/docMain/getById
    moduleName: SRMGSC
    serviceName: /document/sys/docTransmission/download
    uploadRestUrlHeader: https://imipsit.midea.com
    uploadServiceName: /mip-document/document/sys/docTranmission/upload
sdp:
  appKey: PAM
  appSecret: l442bis73hjttfvc2lc1xjw0fgp7e58y
spring:
  cluster:
    redis:
      blockWhenExhausted: false
      database: 1
      expire: 1800
      host: *************
      masterName: cluster6379
      maxIdle: 50
      maxTotal: 200
      maxWaitMillis: 1500
      numTestsPerEvictionRun: 1024
      password: ygwHPik57
      port: 6379
      type: singleton
  datasource:
    driverClassName: com.mysql.jdbc.Driver
    filters: stat,wall,log4j
    initialSize: 5
    logAbandoned: true
    maxActive: 50
    maxPoolPreparedStatementPerConnectionSize: 20
    maxWait: 60000
    minEvictableIdleTimeMillis: 25200000
    minIdle: 5
    password: tqVhr1zM5
    platform: mysql
    poolPreparedStatements: true
    removeAbandoned: true
    removeAbandonedTimeout: 1800
    testOnBorrow: false
    testOnReturn: false
    testWhileIdle: true
    timeBetweenEvictionRunsMillis: 60000
    type: com.alibaba.druid.pool.DruidDataSource
    url: jdbc:mysql://*************:3306/pam_basedata?useUnicode=true&characterEncoding=UTF-8&serverTimezone=GMT%2B8&useSSL=false&allowMultiQueries=true
    useGlobalDataSourceStat: true
    username: pam_sit
    validationQuery: SELECT 1
swagger:
  enable: false
xxl:
  job:
    accessToken: 62d3dda4d6174d81af539f5d5d98368c
    admin:
      addresses: https://apiuat.midea.com/djs
    enabled: false
    executor:
      accessKeyId: vtYA2DSLDLpJnunayh3SZivf
      accessKeySecret: 18kM1A09xXX9Hei06ThZhc3KgDCTDca2
      appName: PAMJOB-SIT-BASEDATA
      idmTokenUrl: https://apiuat.midea.com/iam/v1/security/getAccessToken
      logPath: /apps/pam/logs/xxl-job/jobhandler
      logretentiondays: 7
      port: 9991
      version: 1.3
