business:
  carryoverBillCheckFilePath: /apps/pam/ctc/file/
  companyIdRuleMap:
    '1103432849530290176': LYKJ
    '581144287652085760': ROBOT
    '616744224850706432': MEIYUN
    '814428597413478400': KS
    '884455544528568320': YL
  hrUrlPath: http://10.16.13.84:8080/mjattend/services/mxAttendService?wsdl
  ihrAttendUrl: ihr/attend-esb/backend/attend/esb/synchronize/pam/detail
  kukaRouxingUnitId: 989894511423389696
  meiCloudUnitId: 616744224850706432
  mifAppId: PAM
  mifAppKey: fec292ca2b2443908ff9e44996b0a8fc
  mifBlueInvoicePushPath: https://invoicetest.midea.com/invoice_sit/invoice/trans/blue
  mifInvoiceSearchPath: https://invoicetest.midea.com/invoice_sit/invoice/trans/invoiceQueryHt
  mifRedInvoicePushPath: https://invoicetest.midea.com/invoice_sit/invoice/trans/specialRed
  robotUnitId: 581144287652085760
  yueyunUnitId: 666278517574467584
  ruiShiGeUnitId: 1362481220383592448
esb:
  gfp:
    securitycode: Mid@2022Edp$
esb_client:
  replyDestination: LOCALQ.PAM.RSP
  JMSConfiguration:
    CCSID: '1208'
    transportType: '1'
    connectionNameList: localesbjydev.midea.com(11001),localesbjydev.midea.com(12001)
    channel: PAM.DEF.SVRCONN
  receiveTimeout: '120000'
  longReceiveTimeout: '600000'
  targetDestination: LOCALQ.C.PAM.REQ
esb_server:
  JMSConfiguration:
    transportType: '1'
    CCSID: '1208'
    channel: PAM.DEF.SVRCONN
    connectionNameList1: localesbjydev.midea.com(11101)
    connectionNameList2: localbjydev.midea.com(12101)
  sessionCacheSize: '100'
  targetDestination: LOCALQ.P.PAM.REQ
  concurrentConsumers: '5'
eureka:
  client:
    registry-fetch-interval-seconds: 3    # 拉取服务列表间隔，默认30秒
    initial-instance-info-replication-interval-seconds: 5  # 初始化实例信息复制间隔
    # 实例信息复制间隔时间（秒）
    instance-info-replication-interval-seconds: 2
    serviceUrl:
      defaultZone: http://localhost:8761/eureka
  instance:
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 2  # 心跳间隔，默认30秒
    lease-expiration-duration-in-seconds: 6 # 服务过期时间，默认90秒
expression:
  aop:
    pointcut: '@within(org.springframework.web.bind.annotation.RestController)'
fdp:
  url: https://efssit.midea.com/edp-api
geam:
  url: http://eamtest.midea.com:9080/sit
  v2-url: https://ceamsit.midea.com/api-ceam-svc/public/pam/
glegal:
  pushinfo:
    accessKeyId: KQ6HqECZhsR2Domogbazg6zt
    accessKeySecret: ********************************
    appId: C-PAM
    glegalContractUrl: https://apisit.midea.com/g-lms/contract-application/toC-PAM/
    glegalPropertyUrl: https://apisit.midea.com/g-lem/property-application/toC-PAM/api/
    grantType: client_credential
    interfaceUrl: https://glegalsit.midea.com/contract-application/api/
    interfaceUrlSec: https://glegalsit.midea.com/property-application/api/
    secretKey: 763d0e71-6656-4c36-bf6c-31959f53d5b4
    tokenUrl: https://apisit.midea.com/iam/idaas-apis/apis/
logger:
  level:
    org.apache.cxf.transport.jms.JMSDestination: ERROR
  parameter:
    enable: false
    level: DEBUG
    mdc-storage:
      enable: false
    name: PARAMETER_LOG
  trace-code:
    enable: true
    mdc-trace-code-key: traceCode
mbf:
  mq:
    consumer:
      consume-thread-max: 64
      core-pool-size: 20
      feign:
        context-path: /pam
        service-name: pam
      groups:
        GROUP-BASEMQ-TEST-01:
          consume-thread-max: 64
          core-pool-size: 20
        GROUP-BASEMQ-TEST-02:
          consume-thread-max: 64
          core-pool-size: 20
        GROUP-BASEMQ-TEST-03:
          consume-thread-max: 64
          core-pool-size: 20
      id-type: auto-increment
      max-fail-retry-times: 1
      persist-type: feign
  txmq:
    close-persist: false
    enabled: true
    id-type: pam-id4j
meixing:
  accessToken: 2f5b38ff-f412-40aa-8a7d-0e004660eed9
  appKey: e21c13e0-b57a-49f1-985b-01af30232e1a
  jumpUrl: https://pamsit.midea.com
  mcWidgetIdentifier: sit
  tempMsgUrl: http://mapnew5sit.midea.com/mpm-api/api/tempmsg/create_temp_msg_and_push
mframework-provider:
  ribbon:
    MaxAutoRetriesNextServer: 2
    NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
    OkToRetryOnAllOperations: true
    listOfServers: 10.23.121.30:61001
mgp:
  accessKeyId: KQ6HqECZhsR2Domogbazg6zt
  accessKeySecret: ********************************
  apiurl: https://apisit.midea.com/
  carrier:
    discovery:
      env: sit
      system: pam
      version: 1.0
print:
  tenantCode: T633021084
  urlPath: https://apisit.midea.com/T-LPAAS/logistics-paas-printer/pdf/
  userCode: MC589936124
rdm:
  url: http://itrdmsit.midea.com:8888
rocketmq:
  consumer:
    group: pam-ctc
  ctc:
    flexible-mdp-change-submit:
      group: CG-PAM-CTC-FLEXIBLE-MDP-SUBMIT-DEV
      topic: TOPIC-PAM-CTC-FLEXIBLE-MDP-SUBMIT-DEV
    flexible-mdp-submit:
      group: CG-PAM-CTC-FLEXIBLE-MDP-CHANGE-SUBMIT-DEV
      topic: TOPIC-PAM-CTC-FLEXIBLE-MDP-CHANGE-SUBMIT-DEV
  name-server: 10.23.112.247:9876
  producer:
    group: pam-ctc
    send-message-timeout: 3000
route:
  contractUrl: https://pamsit.midea.com/#/contract/detail/
  materialAdjustDetailUrl: https://pamsit.midea.com/#/material/create-material-detail/
  materialDelistUrl: https://pamsit.midea.com/#/design-scheme/material-delisting-detail/
  milepostDesignPlanChangeUrl: https://pamuat.midea.com/#/detailed-design-change-wbs-model/
  milepostDesignPlanNotPublishRequirementUrl: https://pamsit.midea.com/#/design-scheme/design-scheme-unrequirement-list/
  milepostNoticeUrl: https://pamsit.midea.com/#/project/milestone-warn-list?batchId=
  milepostUrl: https://pamsit.midea.com/#/project/detail/%?active=milestone
  paymentApplyEmailUrl: http://pamsit.midea.com/#/payment/payment-apply-detail/
  productTaskUrl: https://pamsit.midea.com/#/finance/revenue-forecast/task/product/
  projectAwardEmailUrl: http://pamsit.midea.com/#/project-prize/todo-list
  projectProblemEmailUrl: https://pamsit.midea.com/#/project/questions/detail/
  projectTaskUrl: https://pamsit.midea.com/#/finance/revenue-forecast/task/project/
  projectUrl: https://pamsit.midea.com/#/project/detail/
  projectWbsReceiptsRejectUrl: https://pamuat.midea.com/#/detailed-design-change-wbs-model/
  projectWbsReceiptsUrl: https://pamuat.midea.com/#/design-scheme-wbs/need-release/detail/
  subContractUrl: https://pamsit.midea.com/#/contract/subcontract-detail/
  workingHourSubmitUrl: https://pamsit.midea.com/#/workhour/my
  workingHourUrl: https://pamsit.midea.com/#/workhour/my?active=audit&userId=
  wbsChangeReceiptsUrl: https://pamsit.midea.com/#/design-scheme-wbs-change-budget/detail
  purchaseContractDetailUrl: https://pamsit.midea.com/?ticket=ST-28129-xn3j7iXbpdcg4zk9sYcn-signinuat&ticket=ST-38844-QKiXIH1lf7FXgqMYz1sd-signinuat#/purchase/contract-detail/
sdk:
  mform:
    appID: SRMFILE
    appKey: f1d82e0b-26ba-40c3-8be8-14ded9fff336
    bgHost: https://iflowsit.midea.com/formEngine
    downLoadServiceName: /mip-document/document/docTranmission2/public/download
    forwardTo: mip4cdev5.midea.com:60050
    getInfoServiceName: /mip-document/document/sys/docMain/getById
    moduleName: SRMGSC
    serviceName: /document/sys/docTransmission/download
    uploadRestUrlHeader: https://imipsit.midea.com
    uploadServiceName: /mip-document/document/sys/docTranmission/upload
sdp:
  appKey: PAM
  appSecret: l442bis73hjttfvc2lc1xjw0fgp7e58y
  callbackUrl: https://pamsit.midea.com/pam/sdp/callback
  rabbitmq:
    address: *************:5672,*************:5672,*************:5672
    enabled: true
    password: apps#0820
    username: apps
    virtual-host: /
server:
  tomcat:
    max-http-post-size: -1
spring:
  cluster:
    redis:
      blockWhenExhausted: false
      database: 0
      expire: 1800
      host: *************
      masterName: cluster6379
      maxIdle: 50
      maxTotal: 200
      maxWaitMillis: 1500
      numTestsPerEvictionRun: 1024
      password: ygwHPik57
      port: 6379
      type: singleton
  datasource:
    driverClassName: com.mysql.jdbc.Driver
    filters: stat,wall,log4j
    initialSize: 5
    logAbandoned: true
    maxActive: 50
    maxPoolPreparedStatementPerConnectionSize: 20
    maxWait: 60000
    minEvictableIdleTimeMillis: 25200000
    minIdle: 5
    password: tqVhr1zM5
    platform: mysql
    poolPreparedStatements: true
    removeAbandoned: true
    removeAbandonedTimeout: 1800
    testOnBorrow: false
    testOnReturn: false
    testWhileIdle: true
    timeBetweenEvictionRunsMillis: 60000
    type: com.alibaba.druid.pool.DruidDataSource
    url: ******************************************************************************************************************************************
    useGlobalDataSourceStat: true
    username: pam_sit
    validationQuery: SELECT 1
swagger:
  enable: false
xxl:
  job:
    accessToken: fb9428861af84c0897bdc04e503a13a9
    admin:
      addresses:
    enabled: false
    executor:
      accessKeyId: vtYA2DSLDLpJnunayh3SZivf
      accessKeySecret: 18kM1A09xXX9Hei06ThZhc3KgDCTDca2!
      appName: PAMJOB-SIT-CTC
      idmTokenUrl:
      logPath: /apps/pam/logs/xxl-job/jobhandler
      logretentiondays: 7
      port: 9993
      version: 1.3

logging:
  level:
    org.apache.cxf.transport.jms.JMSDestination: ERROR