package com.midea.pam.ctc.service.impl;

import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import com.midea.pam.common.basedata.dto.VendorSiteBankDto;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.ctc.dto.PaymentPlanDTO;
import com.midea.pam.common.ctc.entity.OrganizationCustomDict;
import com.midea.pam.common.ctc.entity.PaymentInvoiceDetail;
import com.midea.pam.common.ctc.entity.PaymentPlan;
import com.midea.pam.common.ctc.entity.PaymentPlanExample;
import com.midea.pam.common.ctc.entity.PurchaseContract;
import com.midea.pam.common.ctc.entity.PurchaseContractDetail;
import com.midea.pam.common.ctc.entity.PurchaseContractDetailExample;
import com.midea.pam.common.ctc.entity.PurchaseContractPunishment;
import com.midea.pam.common.ctc.entity.SdpLog;
import com.midea.pam.common.ctc.entity.SdpPurchaseContractInfo;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.enums.PurchaseContractStatus;
import com.midea.pam.common.enums.SdpLogOperaTypeEnum;
import com.midea.pam.common.enums.SdpLogStatusEnum;
import com.midea.pam.common.enums.TopicCodeEnum;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.mapper.PaymentApplyMapper;
import com.midea.pam.ctc.mapper.PaymentInvoiceDetailExtMapper;
import com.midea.pam.ctc.mapper.PaymentPlanExtMapper;
import com.midea.pam.ctc.mapper.PaymentPlanMapper;
import com.midea.pam.ctc.mapper.PurchaseContractDetailMapper;
import com.midea.pam.ctc.mapper.PurchaseContractMapper;
import com.midea.pam.ctc.mapper.PurchaseContractPunishmentExtMapper;
import com.midea.pam.ctc.sdp.service.SdpLogService;
import com.midea.pam.ctc.sdp.vo.ResponseObj;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.SdpCarrierServicel;
import com.midea.pam.ctc.service.SdpPurchaseContractService;
import com.midea.pam.system.SystemContext;
import com.midea.sdp.dto.common.SdpResult;
  import org.assertj.core.util.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.ValidationException;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

public class SdpPurchaseContractImpl implements SdpPurchaseContractService {

    private static final Logger logger = LoggerFactory.getLogger(SdpPurchaseContractImpl.class);

    @Resource
    private PurchaseContractMapper purchaseContractMapper;

    @Resource
    private PaymentApplyMapper paymentApplyMapper;

    @Resource
    private PaymentPlanMapper paymentPlanMapper;

    @Resource
    private OrganizationCustomDictService organizationCustomDictService;

    @Resource
    private PaymentPlanExtMapper paymentPlanExtMapper;

    @Resource
    private SdpCarrierServicel sdpCarrierService;

    @Resource
    private SdpLogService sdpLogService;

    @Resource
    private PurchaseContractDetailMapper purchaseContractDetailMapper;

    @Resource
    private PaymentInvoiceDetailExtMapper paymentInvoiceDetailExtMapper;

    @Resource
    private PurchaseContractPunishmentExtMapper punishmentExtMapper;

    @Resource
    private BasedataExtService basedataExtService;


    @Override
    public void pushPurchaseContractInfoForReplay(Long purchaseContractId,String sdpBusinessId) {
        pushPurchaseContractInfo("重放",purchaseContractId,true,sdpBusinessId);
    }

    @Override
    public void pushPurchaseContractInfo(String triggerTypeName,Long purchaseContractId,boolean isReplay,String sdpBusinessId) {
        SdpLog sdpLog = new SdpLog();
        SdpLog replaySdpLog = null;
        boolean notSend = false;
        Long unitId = SystemContext.getUnitId();
        TopicCodeEnum topicCodeEnum = TopicCodeEnum.PURCHASE_CONTRACT;
        sdpLog.setOperationType(SdpLogOperaTypeEnum.PUSH.getCode());
        sdpLog.setApplyId(purchaseContractId);
        sdpLog.setDataCount(1);
        sdpLog.setSdpBusinessType(topicCodeEnum.getTopicCode());
        sdpLog.setDeletedFlag(Boolean.FALSE);
        if(!isReplay){
            List<Long> ous = SystemContext.getOus();
            if(ListUtils.isNotEmpty(ous)){
                sdpLog.setOuId(ous.get(0));
            }
        }

        try {
            logger.info("SDP采购合同信息推送,触发类型:{},采购合同ID:{}",triggerTypeName,purchaseContractId);

            if(Objects.isNull(purchaseContractId)){
                logger.info("SDP采购合同信息推送,采购合同ID为空,不推送");
                notSend = true;
                return;
            }

            if(isReplay){
                replaySdpLog = sdpLogService.getSdpLog(purchaseContractId, sdpBusinessId);
                if(Objects.nonNull(replaySdpLog)){
                    unitId = CacheDataUtils.getTopUnitIdByOuId(replaySdpLog.getOuId());
                }
            }

            //查询当前单位是否配置预付款标识
            List<OrganizationCustomDict> customDictList = organizationCustomDictService.queryByOrdId(unitId, "预付款标识", OrgCustomDictOrgFrom.COMPANY);
            if(ListUtils.isEmpty(customDictList)){
                logger.info("SDP采购合同信息推送,当前单位没有配置[预付款标识],unitId:{},不推送",unitId);
                notSend = true;
                return;
            }
            OrganizationCustomDict prePaymentDict = customDictList.get(0);
            if(!Objects.equals(prePaymentDict.getValue(),"是")){
                logger.info("SDP采购合同信息推送,当前单位[预付款标识]配置不开启,unitId:{},不推送",unitId);
                notSend = true;
                return;
            }
            //查询采购合同
            PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(purchaseContractId);


            if(Objects.isNull(purchaseContract)){
                logger.info("SDP采购合同信息推送,找不到采购合同，采购合同ID：{},不推送",purchaseContractId);
                notSend = true;
                return;
            }

            if(!Objects.equals(purchaseContract.getCurrency(),"CNY")){
                logger.info("SDP采购合同信息推送,采购合同币种不是CNY，采购合同ID：{},币种:{},不推送",purchaseContractId, Optional.ofNullable(purchaseContract.getCurrency()).orElse(""));
                notSend = true;
                return;
            }
            //合同状态
            Integer purchaseContractStatus = purchaseContract.getStatus();

            logger.info("SDP采购合同信息推送,采购合同ID：{},状态：{}",purchaseContractId,PurchaseContractStatus.getValue(purchaseContractStatus));

            //查询付款计划 包含已删除的付款计划
            PaymentPlanExample paymentPlanExample = new PaymentPlanExample();
            paymentPlanExample.createCriteria().andContractIdEqualTo(purchaseContractId);
            List<PaymentPlan> paymentPlans = paymentPlanMapper.selectByExample(paymentPlanExample);

            if(ListUtils.isEmpty(paymentPlans)){
                logger.info("SDP采购合同信息推送,找不到付款计划，采购合同ID：{},不推送",purchaseContractId);
                notSend = true;
                return;
            }

            boolean hasPrepaymentFlag = paymentPlans.stream().anyMatch(paymentPlan -> Objects.equals(paymentPlan.getPrepaymentFlag(), (byte) 1));
            boolean pushed = false;
            //根据采购合同ID查询SDP日志表，如果存在记录代表已经推送过则一直推送
            SdpLog querySdpLog = sdpLogService.getSdpLog(purchaseContractId);
            pushed = Objects.nonNull(querySdpLog);

            if(!hasPrepaymentFlag && !pushed){
                logger.info("SDP采购合同信息推送,付款计划集合没有包含预付款，采购合同ID：{},不推送",purchaseContractId);
                notSend = true;
                return;
            }

            VendorSiteBankDto vendorSiteBankDto = null;
            if(Objects.nonNull(purchaseContract.getVendorId())){
                 vendorSiteBankDto = basedataExtService.getVendorSiteBankDto(purchaseContract.getVendorId());
            }

            //设置SDP采购合同推送信息
            SdpPurchaseContractInfo sdpPurchaseContractInfo = new SdpPurchaseContractInfo();
            sdpPurchaseContractInfo.setOuId(purchaseContract.getOuId());
            sdpPurchaseContractInfo.setPurchaseCode(purchaseContract.getCode());
            sdpPurchaseContractInfo.setVendorCode(purchaseContract.getVendorCode());
            sdpPurchaseContractInfo.setErpVendorId(Optional.ofNullable(vendorSiteBankDto).map(VendorSiteBankDto::getErpVendorId).orElse(-1L));
            sdpPurchaseContractInfo.setErpVendorSiteId(Long.valueOf(Optional.ofNullable(purchaseContract.getErpVendorSiteId()).orElse("0")));
            if(Objects.nonNull(purchaseContract.getPurchasingFollower())){
                UserInfo userInfo = CacheDataUtils.findUserById(purchaseContract.getPurchasingFollower());
                sdpPurchaseContractInfo.setPurchasingFollower(userInfo.getUsername());
            }

            sdpPurchaseContractInfo.setCurrency(purchaseContract.getCurrency());
            sdpPurchaseContractInfo.setConversionRate(purchaseContract.getConversionRate());
            sdpPurchaseContractInfo.setAmount(purchaseContract.getAmount());
            sdpPurchaseContractInfo.setExcludingTaxAmount(purchaseContract.getExcludingTaxAmount());

            ArrayList<SdpPurchaseContractInfo.PaymentPlan> sdpPrePaymentPlanList = new ArrayList<>();

            for (PaymentPlan paymentPlan : paymentPlans) {
                SdpPurchaseContractInfo.PaymentPlan sdpPrePaymentPlan = new SdpPurchaseContractInfo.PaymentPlan();
                sdpPrePaymentPlan.setPaymentPlanCode(paymentPlan.getCode());
                sdpPrePaymentPlan.setPrepaymentFlag(paymentPlan.getPrepaymentFlag() != null ? (int) paymentPlan.getPrepaymentFlag() : 0);
                sdpPrePaymentPlan.setPlanPaymentDate(paymentPlan.getDate());
                sdpPrePaymentPlan.setAmount(paymentPlan.getAmount());
                List<PaymentPlanDTO> paymentPlanDTOS = paymentPlanExtMapper.calculateSurplusAmount(paymentPlan.getId());
                if(ListUtils.isNotEmpty(paymentPlanDTOS)){
                    PaymentPlanDTO surplusPaymentPlanInfo = paymentPlanDTOS.get(0);
                    BigDecimal surplusAmount = Optional.ofNullable(surplusPaymentPlanInfo.getSurplusAmount()).orElse(BigDecimal.ZERO);
                    sdpPrePaymentPlan.setSurplusAmount(surplusAmount);
                }
                sdpPrePaymentPlan.setRequirement(paymentPlan.getRequirement());
                sdpPrePaymentPlan.setPlanPaymentMethod(paymentPlan.getPaymentMethodName());
                sdpPrePaymentPlan.setDeletedFlag(Objects.equals(Boolean.FALSE,paymentPlan.getDeletedFlag()) ? 0 : 1);

                if(ArrayUtil.contains(new Integer[]{PurchaseContractStatus.EFFECTIVE.getCode()}, purchaseContractStatus)){
                    sdpPrePaymentPlan.setAuditStatus(1);
                }else if(ArrayUtil.contains(new Integer[]{PurchaseContractStatus.CHANGEING.getCode()}, purchaseContractStatus)){
                    sdpPrePaymentPlan.setAuditStatus(2);
                }
                sdpPrePaymentPlanList.add(sdpPrePaymentPlan);
            }

            ArrayList<SdpPurchaseContractInfo.purchaseContractTax> purchaseContractTaxes = new ArrayList<>();
            //处理采购合同税率行信息
            List<PurchaseContractDetail> purchaseContractDetailList = getPurchaseContractDetailList(purchaseContractId);
            int serialNumber = 1;
            //对purchaseContractDetailList 按照taxRate分组
            Map<String, List<PurchaseContractDetail>> purchaseContractDetailMap = purchaseContractDetailList.stream()
                    .collect(Collectors.groupingBy(d -> d.getTaxRate().replace("%", "")));
            //对同一种税率处理
            for (String taxRate : purchaseContractDetailMap.keySet()) {
                List<PurchaseContractDetail> purchaseContractDetails = purchaseContractDetailMap.get(taxRate);
                //过滤已删除的数据获取总价(含税)
                BigDecimal totalPrice = purchaseContractDetails.stream()
                        .filter(p->Objects.equals(p.getDeletedFlag(),Boolean.FALSE))
                        .map(PurchaseContractDetail::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);

                //过滤已删除的数据获取总价(不含税)
                BigDecimal noTaxTotalPrice = purchaseContractDetails.stream()
                        .filter(p->Objects.equals(p.getDeletedFlag(),Boolean.FALSE))
                        .map(PurchaseContractDetail::getNoTaxTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);

                //统计合同下同一税率开票金额
                List<PaymentInvoiceDetail> invoiceDetailList = paymentInvoiceDetailExtMapper.getDetailListByContractIdAndTaxRate(purchaseContractId, taxRate);
                //含税税票金额
                BigDecimal invoiceDetailIncludePriceTotal = invoiceDetailList.stream().map(PaymentInvoiceDetail::getTaxIncludedPrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                //不含税税票金额
                BigDecimal invoiceDetailExcludePriceTotal = invoiceDetailList.stream().map(PaymentInvoiceDetail::getTaxExcludedPrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

                //查询采购合同罚扣
                List<PurchaseContractPunishment> punishmentList = punishmentExtMapper.getPunishmentListByPurchaseContractIdAndTaxRate(purchaseContractId, taxRate);
                //含税罚扣金额
                BigDecimal punishmentTotalIncludeAmount = punishmentList.stream().map(p -> p.getAmount().add(p.getAmount().multiply(new BigDecimal(p.getTaxRate()))))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal punishmentTotalExcludeAmount = punishmentList.stream().map(PurchaseContractPunishment::getAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

                //TODO 扣减ISP发票

                totalPrice =  totalPrice.subtract(invoiceDetailIncludePriceTotal).subtract(punishmentTotalIncludeAmount);

                noTaxTotalPrice = noTaxTotalPrice.subtract(invoiceDetailExcludePriceTotal).subtract(punishmentTotalExcludeAmount);

                SdpPurchaseContractInfo.purchaseContractTax purchaseContractTax = new SdpPurchaseContractInfo.purchaseContractTax();
                purchaseContractTax.setSerialNumber(serialNumber);
                purchaseContractTax.setTaxRate(taxRate);
                purchaseContractTax.setRemainingInvAmtExcTax( noTaxTotalPrice);
                purchaseContractTax.setRemainingInvAmtIncTax(totalPrice);
                purchaseContractTax.setDeletedFlag(purchaseContractDetails.stream().noneMatch(e -> Objects.equals(e.getDeletedFlag(), Boolean.FALSE)) ? 1 : 0);
                serialNumber++;
                purchaseContractTaxes.add(purchaseContractTax);
            }

            sdpPurchaseContractInfo.setPaymentPlanList(sdpPrePaymentPlanList);
            sdpPurchaseContractInfo.setPamPurchaseContractTaxList(purchaseContractTaxes);
            SdpServiceImpl sdpService = new SdpServiceImpl();
            String businessId = null;
            if(isReplay){
                sdpLog.setId(replaySdpLog.getId());
                businessId = replaySdpLog.getSdpBusinessId();
                sdpPurchaseContractInfo.setSourceId(businessId);
                logger.info("SDP采购合同信息重新推送，businessId:{}",businessId);
            }else{
                businessId = sdpService.generateSequence(topicCodeEnum);
                sdpPurchaseContractInfo.setSourceId(businessId);
                logger.info("SDP采购合同信息推送，businessId:{}",businessId);
                sdpLog.setSdpBusinessId(businessId);
            }
            //校验必填字段
            validateContractInfo(sdpPurchaseContractInfo);
            //发起请求
            SdpResult<ResponseObj> sdpDataImportResult = sdpCarrierService.doSdpDataImport(topicCodeEnum.getTargetSystemCode(), topicCodeEnum.getTopicCode(), businessId, Lists.newArrayList(sdpPurchaseContractInfo));
            if (Objects.equals(sdpDataImportResult.getSuccess(), Boolean.TRUE) && Objects.equals(sdpDataImportResult.getCode(),"0")) {
                sdpLog.setStatus(SdpLogStatusEnum.SENDING.getCode());
                logger.info("SDP采购合同信息推送成功,result:{}",JSON.toJSONString(sdpDataImportResult));
            } else {
                String result = JSON.toJSONString(sdpDataImportResult);
                sdpLog.setStatus(SdpLogStatusEnum.SEND_FAIL.getCode());
                sdpLog.setResponseCode(sdpDataImportResult.getCode());
                sdpLog.setResponseMessage(sdpDataImportResult.getMessage());
                logger.error("SDP采购合同信息推送失败,result:{}",result);
            }
        }catch(ValidationException e){
            sdpLog.setStatus(SdpLogStatusEnum.SEND_FAIL.getCode());
            sdpLog.setResponseMessage(e.getMessage());
            logger.error("SDP采购合同信息校验异常",e);
        } catch (Exception e) {
            sdpLog.setStatus(SdpLogStatusEnum.SEND_FAIL.getCode());
            sdpLog.setResponseMessage(e.getMessage());
            logger.error("SDP采购合同信息推送异常",e);
        }finally {
           if(!notSend){
               if(Objects.isNull(replaySdpLog)){
                   sdpLogService.save(sdpLog);
               }else{
                   sdpLogService.update(sdpLog);
               }
           }
        }
    }

    /**
     * 获取采购合同详情（包含逻辑删除记录）
     * @param purchaseContractId
     * @return
     */
    private List<PurchaseContractDetail> getPurchaseContractDetailList(Long purchaseContractId){
        PurchaseContractDetailExample purchaseContractDetailExample = new PurchaseContractDetailExample();
        purchaseContractDetailExample.createCriteria().andContractIdEqualTo(purchaseContractId);
        return purchaseContractDetailMapper.selectByExample(purchaseContractDetailExample);
    }

    /**
     * 验证合同信息及其付款计划
     * 此方法会对所有必填字段进行校验，如果存在为空的必填字段，将抛出详细的验证异常
     *
     * @param contract 需要验证的合同信息对象
     * @throws ValidationException 当存在必填字段为空时抛出此异常
     */
    private void validateContractInfo(SdpPurchaseContractInfo contract) {
        // 创建验证器工厂和验证器实例
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();

        // 存储所有验证错误信息
        List<String> errorMessages = new ArrayList<>();

        // 验证主合同信息
        Set<ConstraintViolation<SdpPurchaseContractInfo>> violations = validator.validate(contract);
        for (ConstraintViolation<SdpPurchaseContractInfo> violation : violations) {
            errorMessages.add(violation.getMessage());
        }

        // 验证付款计划列表
        if (contract.getPaymentPlanList() != null) {
            for (int i = 0; i < contract.getPaymentPlanList().size(); i++) {
                SdpPurchaseContractInfo.PaymentPlan plan = contract.getPaymentPlanList().get(i);
                Set<ConstraintViolation<SdpPurchaseContractInfo.PaymentPlan>> planViolations =
                        validator.validate(plan);

                // 如果付款计划存在验证错误，添加到错误信息列表中，并标注是第几个付款计划
                for (ConstraintViolation<SdpPurchaseContractInfo.PaymentPlan> violation : planViolations) {
                    errorMessages.add(String.format("付款计划%d: %s", i + 1, violation.getMessage()));
                }
            }
        }

        // 如果存在验证错误，抛出异常并包含所有错误信息
        if (!errorMessages.isEmpty()) {
            throw new ValidationException("合同信息验证失败：\n" +
                    String.join("\n", errorMessages));
        }
    }
}
