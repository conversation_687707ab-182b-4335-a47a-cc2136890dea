<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.SwapApplyMemberMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.SwapApplyMember">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="swap_apply_id" jdbcType="BIGINT" property="swapApplyId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_level" jdbcType="VARCHAR" property="userLevel" />
    <result column="level_id" jdbcType="VARCHAR" property="levelId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="user_role" jdbcType="VARCHAR" property="userRole" />
    <result column="role_id" jdbcType="VARCHAR" property="roleId" />
    <result column="num" jdbcType="INTEGER" property="num" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="total_days" jdbcType="VARCHAR" property="totalDays" />
    <result column="swap_unit_price" jdbcType="DECIMAL" property="swapUnitPrice" />
    <result column="income_amount" jdbcType="DECIMAL" property="incomeAmount" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
    <result column="attribute1" jdbcType="VARCHAR" property="attribute1" />
    <result column="attribute2" jdbcType="VARCHAR" property="attribute2" />
    <result column="attribute3" jdbcType="VARCHAR" property="attribute3" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, swap_apply_id, user_id, user_level, level_id, user_name, user_role, role_id, 
    num, start_time, end_time, total_days, swap_unit_price, income_amount, create_at, 
    create_by, update_at, update_by, deleted_flag, attribute1, attribute2, attribute3
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.ctc.entity.SwapApplyMemberExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from swap_apply_member
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from swap_apply_member
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from swap_apply_member
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.ctc.entity.SwapApplyMember">
    insert into swap_apply_member (id, swap_apply_id, user_id, 
      user_level, level_id, user_name, 
      user_role, role_id, num, 
      start_time, end_time, total_days, 
      swap_unit_price, income_amount, create_at, 
      create_by, update_at, update_by, 
      deleted_flag, attribute1, attribute2, 
      attribute3)
    values (#{id,jdbcType=BIGINT}, #{swapApplyId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, 
      #{userLevel,jdbcType=VARCHAR}, #{levelId,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, 
      #{userRole,jdbcType=VARCHAR}, #{roleId,jdbcType=VARCHAR}, #{num,jdbcType=INTEGER}, 
      #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{totalDays,jdbcType=VARCHAR}, 
      #{swapUnitPrice,jdbcType=DECIMAL}, #{incomeAmount,jdbcType=DECIMAL}, #{createAt,jdbcType=TIMESTAMP}, 
      #{createBy,jdbcType=BIGINT}, #{updateAt,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=BIGINT}, 
      #{deletedFlag,jdbcType=TINYINT}, #{attribute1,jdbcType=VARCHAR}, #{attribute2,jdbcType=VARCHAR}, 
      #{attribute3,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.ctc.entity.SwapApplyMember">
    insert into swap_apply_member
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="swapApplyId != null">
        swap_apply_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userLevel != null">
        user_level,
      </if>
      <if test="levelId != null">
        level_id,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="userRole != null">
        user_role,
      </if>
      <if test="roleId != null">
        role_id,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="totalDays != null">
        total_days,
      </if>
      <if test="swapUnitPrice != null">
        swap_unit_price,
      </if>
      <if test="incomeAmount != null">
        income_amount,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
      <if test="attribute1 != null">
        attribute1,
      </if>
      <if test="attribute2 != null">
        attribute2,
      </if>
      <if test="attribute3 != null">
        attribute3,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="swapApplyId != null">
        #{swapApplyId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="userLevel != null">
        #{userLevel,jdbcType=VARCHAR},
      </if>
      <if test="levelId != null">
        #{levelId,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userRole != null">
        #{userRole,jdbcType=VARCHAR},
      </if>
      <if test="roleId != null">
        #{roleId,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="totalDays != null">
        #{totalDays,jdbcType=VARCHAR},
      </if>
      <if test="swapUnitPrice != null">
        #{swapUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="incomeAmount != null">
        #{incomeAmount,jdbcType=DECIMAL},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="attribute1 != null">
        #{attribute1,jdbcType=VARCHAR},
      </if>
      <if test="attribute2 != null">
        #{attribute2,jdbcType=VARCHAR},
      </if>
      <if test="attribute3 != null">
        #{attribute3,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.ctc.entity.SwapApplyMemberExample" resultType="java.lang.Long">
    select count(*) from swap_apply_member
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.ctc.entity.SwapApplyMember">
    update swap_apply_member
    <set>
      <if test="swapApplyId != null">
        swap_apply_id = #{swapApplyId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="userLevel != null">
        user_level = #{userLevel,jdbcType=VARCHAR},
      </if>
      <if test="levelId != null">
        level_id = #{levelId,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userRole != null">
        user_role = #{userRole,jdbcType=VARCHAR},
      </if>
      <if test="roleId != null">
        role_id = #{roleId,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        num = #{num,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="totalDays != null">
        total_days = #{totalDays,jdbcType=VARCHAR},
      </if>
      <if test="swapUnitPrice != null">
        swap_unit_price = #{swapUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="incomeAmount != null">
        income_amount = #{incomeAmount,jdbcType=DECIMAL},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="attribute1 != null">
        attribute1 = #{attribute1,jdbcType=VARCHAR},
      </if>
      <if test="attribute2 != null">
        attribute2 = #{attribute2,jdbcType=VARCHAR},
      </if>
      <if test="attribute3 != null">
        attribute3 = #{attribute3,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.ctc.entity.SwapApplyMember">
    update swap_apply_member
    set swap_apply_id = #{swapApplyId,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=BIGINT},
      user_level = #{userLevel,jdbcType=VARCHAR},
      level_id = #{levelId,jdbcType=VARCHAR},
      user_name = #{userName,jdbcType=VARCHAR},
      user_role = #{userRole,jdbcType=VARCHAR},
      role_id = #{roleId,jdbcType=VARCHAR},
      num = #{num,jdbcType=INTEGER},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      total_days = #{totalDays,jdbcType=VARCHAR},
      swap_unit_price = #{swapUnitPrice,jdbcType=DECIMAL},
      income_amount = #{incomeAmount,jdbcType=DECIMAL},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      attribute1 = #{attribute1,jdbcType=VARCHAR},
      attribute2 = #{attribute2,jdbcType=VARCHAR},
      attribute3 = #{attribute3,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>