<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.PaymentApplyExtMapper">

    <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.dto.PaymentApplyDto">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="payment_apply_code" jdbcType="VARCHAR" property="paymentApplyCode" />
        <result column="is_charge" jdbcType="BIT" property="isCharge" />
        <result column="payment_plan_id" jdbcType="BIGINT" property="paymentPlanId" />
        <result column="payment_plan_code" jdbcType="VARCHAR" property="paymentPlanCode" />
        <result column="payment_plan_num" jdbcType="INTEGER" property="paymentPlanNum" />
        <result column="payment_plan_date" jdbcType="TIMESTAMP" property="paymentPlanDate" />
        <result column="requirement" jdbcType="VARCHAR" property="requirement" />
        <result column="currency" jdbcType="VARCHAR" property="currency" />
        <result column="money_back" jdbcType="DECIMAL" property="moneyBack" />
        <result column="vendor_id" jdbcType="BIGINT" property="vendorId" />
        <result column="vendor_erp_id" jdbcType="BIGINT" property="vendorErpId" />
        <result column="vendor_code" jdbcType="VARCHAR" property="vendorCode" />
        <result column="vendor_name" jdbcType="VARCHAR" property="vendorName" />
        <result column="erp_vendor_site_id" jdbcType="BIGINT" property="erpVendorSiteId" />
        <result column="vendor_site_code" jdbcType="VARCHAR" property="vendorSiteCode" />
        <result column="project_id" jdbcType="BIGINT" property="projectId" />
        <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
        <result column="project_name" jdbcType="VARCHAR" property="projectName" />
        <result column="purchase_contract_id" jdbcType="BIGINT" property="purchaseContractId" />
        <result column="purchase_contract_code" jdbcType="VARCHAR" property="purchaseContractCode" />
        <result column="contract_code_pam" jdbcType="VARCHAR" property="contractCodePam" />
        <result column="contract_code_legal" jdbcType="VARCHAR" property="contractCodeLegal" />
        <result column="purchase_contract_name" jdbcType="VARCHAR" property="purchaseContractName" />
        <result column="item_number" jdbcType="VARCHAR" property="itemNumber" />
        <result column="item_number_name" jdbcType="VARCHAR" property="itemNumberName" />
        <result column="ou_id" jdbcType="BIGINT" property="ouId" />
        <result column="ou_name" jdbcType="VARCHAR" property="ouName" />
        <result column="is_advance" jdbcType="BIT" property="isAdvance" />
        <result column="date_entry" jdbcType="TIMESTAMP" property="dateEntry" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="payment_method_id" jdbcType="BIGINT" property="paymentMethodId" />
        <result column="payment_method_name" jdbcType="VARCHAR" property="paymentMethodName" />
        <result column="vendor_bank_num" jdbcType="VARCHAR" property="vendorBankNum" />
        <result column="payment_method" jdbcType="VARCHAR" property="paymentMethod" />
        <result column="account_num" jdbcType="VARCHAR" property="accountNum" />
        <result column="account_name" jdbcType="VARCHAR" property="accountName" />
        <result column="tax_plan_included_price" jdbcType="DECIMAL" property="taxPlanIncludedPrice" />
        <result column="tax_pay_included_price" jdbcType="DECIMAL" property="taxPayIncludedPrice" />
        <result column="really_pay_included_price" jdbcType="DECIMAL" property="reallyPayIncludedPrice" />
        <result column="total_cancel_amount" jdbcType="DECIMAL" property="totalCancelAmount" />
        <result column="invoice_method" jdbcType="BIT" property="invoiceMethod" />
        <result column="penalty_amount" jdbcType="DECIMAL" property="penaltyAmount" />
        <result column="write_off_status" jdbcType="TINYINT" property="writeOffStatus" />
        <result column="audit_status" jdbcType="TINYINT" property="auditStatus" />
        <result column="esb_status" jdbcType="VARCHAR" property="esbStatus" />
        <result column="gceb_status" jdbcType="VARCHAR" property="gcebStatus" />
        <result column="erp_status" jdbcType="VARCHAR" property="erpStatus" />
        <result column="gceb_msg" jdbcType="VARCHAR" property="gcebMsg" />
        <result column="erp_msg" jdbcType="VARCHAR" property="erpMsg" />
        <result column="audit_date" jdbcType="TIMESTAMP" property="auditDate" />
        <result column="submit_date" jdbcType="TIMESTAMP" property="submitDate" />
        <result column="submit_by" jdbcType="BIGINT" property="submitBy" />
        <result column="submit_name" jdbcType="VARCHAR" property="submitName" />
        <result column="create_by" jdbcType="BIGINT" property="createBy" />
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
        <result column="update_by" jdbcType="BIGINT" property="updateBy" />
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
        <result column="deleted_flag" jdbcType="BIT" property="deletedFlag" />
        <result column="version" jdbcType="BIGINT" property="version" />
        <result column="bill_bank" jdbcType="VARCHAR" property="billBank" />
        <result column="conversion_type" jdbcType="BIGINT" property="conversionType" />
        <result column="conversion_date" jdbcType="BIGINT" property="conversionDate" />
        <result column="conversion_rate" jdbcType="BIGINT" property="conversionRate" />
    </resultMap>
    <sql id="Base_Column_List">
    id, payment_apply_code, is_charge, payment_plan_id, payment_plan_code, payment_plan_num,
    payment_plan_date, requirement, currency, money_back, vendor_id, vendor_erp_id, vendor_code,
    vendor_name, erp_vendor_site_id, vendor_site_code, project_id, project_code, project_name,
    purchase_contract_id, purchase_contract_code, contract_code_pam, contract_code_legal,
    purchase_contract_name, item_number, item_number_name, ou_id, ou_name, is_advance,
    date_entry, remark, payment_method_id, payment_method_name, vendor_bank_num, payment_method,
    account_num, account_name, tax_plan_included_price, tax_pay_included_price, really_pay_included_price,
    total_cancel_amount, invoice_method, penalty_amount, write_off_status, audit_status, esb_status, gceb_status,
    erp_status, gceb_msg, erp_msg, audit_date, submit_date, submit_by, submit_name, create_by, create_at,
    update_by, update_at, deleted_flag, version,conversion_type,conversion_date,conversion_rate,bill_bank
    </sql>
    <select id="selectPage" parameterType="com.midea.pam.common.ctc.query.PaymentApplyQuery"
            resultMap="BaseResultMap">
          select
            <include refid="Base_Column_List" />
          from
          payment_apply pa
          where 1 = 1
        <include refid="queryCondition" />

         ORDER BY pa.submit_date desc

  </select>

    <sql id="queryCondition">
        <!-- id-->
        <if test="id != null">
            AND pa.id = #{id}
        </if>

        <!-- 付款申请单号-->
        <if test="paymentApplyCode != null and paymentApplyCode != ''">
            AND pa.payment_apply_code like concat('%', #{paymentApplyCode}, '%')
        </if>


        <!-- 付款计划ID-->
        <if test="paymentPlanId != null">
            AND pa.payment_plan_id = #{paymentPlanId}
        </if>

        <!-- 付款计划编号-->
        <if test="paymentPlanCode != null and paymentPlanCode != ''">
            AND pa.payment_plan_code like concat('%', #{paymentPlanCode}, '%')
        </if>

        <!-- purchaseContractName-->
        <if test="purchaseContractName != null and purchaseContractName != ''">
            AND pa.purchase_contract_name like concat('%', #{purchaseContractName}, '%')
        </if>

        <!-- vendorName-->
        <if test="vendorName != null and vendorName != ''">
            AND pa.vendor_name like concat('%', #{vendorName}, '%')
        </if>

        <!-- projectName-->
        <if test="projectName != null and projectName != ''">
            AND pa.project_name like concat('%', #{projectName}, '%')
        </if>

        <!-- projectCode-->
        <if test="projectCode != null and projectCode != ''">
            AND pa.project_code like concat('%', #{projectCode}, '%')
        </if>

        <!-- submitName-->
        <if test="submitName != null and submitName != ''">
            AND pa.submit_name like concat('%', #{submitName}, '%')
        </if>


        <!-- 提交日期开始日期-->
        <if test="startDate != null">
            AND pa.submit_date <![CDATA[ >= ]]> #{startDate}
        </if>

        <!-- 提交日期结束日期-->
        <if test="endDate != null">
            AND pa.submit_date <![CDATA[ <= ]]> #{endDate}
        </if>

        <!-- gceb同步状态-->
        <if test="gcebStatus != null">
            AND pa.gceb_status = #{gcebStatus}
        </if>

        <!-- erp同步状态-->
        <if test="erpStatus != null">
            AND pa.erp_status =  #{erpStatus}
        </if>


        <!-- 流程审批状态-->
        <if test="auditStatus != null">
            AND pa.audit_status = #{auditStatus}
        </if>

        <!-- 接口同步状态-->
        <if test="esbStatus != null">
            AND pa.esb_status = #{esbStatus}
        </if>

        <!-- 接口同步状态-->
        <if test="gcebAndErpStatus != null">
            AND pa.gceb_status = #{gcebAndErpStatus} or erp_status=#{gcebAndErpStatus}
        </if>

    </sql>



    <!--
        累计已经申请金额（含税）
        审批状态：DRAFT(1, "草稿"),PENDING(2, "审核中"),REFUSE(3, "驳回"),EFFECTIVE(4, "生效"),CANCEL(5, "作废")
    -->
    <select id="totalApplyAmount" parameterType="java.lang.Long" resultType="java.math.BigDecimal">
        select sum(tax_pay_included_price)
          from payment_apply
            where
            audit_status != 5
            AND (deleted_flag = 0 or deleted_flag is null)
            and payment_plan_id = #{paymentPlanId,jdbcType=BIGINT}
    </select>


    <!--
        累计在途金额（含税）
        `audit_status` tinyint(100) DEFAULT NULL COMMENT '审批状态（1草稿,2审核中,3驳回,4生效,5作废）',
        `esb_status` 接口发送状态(0-待同步,1-同步异常,2-同步成功)',
        `gceb_status` 'gceb同步状态(1-支付完成,2-取消支付,3-部分支付,4-付款中,5-异常完结)',
        `erp_status` 'erp同步状态(1-支付完成,2-取消支付,3-部分支付,4-付款中,5-异常完结)',
    -->
    <select id="totalOnTheWayAmount" parameterType="java.lang.Long" resultType="java.math.BigDecimal">
        select IFNULL(sum(tax_pay_included_price-ifnull(total_cancel_amount,0)-ifnull(really_pay_included_price,0)),0)
        from payment_apply
        where
        audit_status != 5
        AND (deleted_flag = 0 or deleted_flag is null)
        and payment_plan_id = #{paymentPlanId,jdbcType=BIGINT}
    </select>

    <!-- 累计付款金额（含税） -->
    <select id="actualAmount" parameterType="java.lang.Long" resultType="java.math.BigDecimal">
        SELECT
            IFNULL(sum(amount),0)
        FROM
	      payment_record
        WHERE
            payment_apply_id IN (
                SELECT
                    id
                FROM
                    payment_apply
                WHERE
                    payment_plan_id = #{paymentPlanId,jdbcType=BIGINT}
                    AND (deleted_flag = 0 OR deleted_flag is null)
            )
          AND (deleted_flag = 0 or deleted_flag is null)
    </select>

    <!-- 累计申请付款金额（含税） 排除作废和审批中 -->
    <select id="actualApplyAmount" parameterType="java.lang.Long" resultType="java.math.BigDecimal">
        SELECT
            IFNULL(sum(tax_pay_included_price-ifnull(total_cancel_amount,0)),0)
        FROM
            payment_apply
        WHERE
          audit_status =4
          AND (deleted_flag = 0 or deleted_flag is null)
          AND payment_plan_id = #{paymentPlanId,jdbcType=BIGINT}
    </select>

    <!-- 在途金额（审批中的金额） -->
    <select id="transitAmount" parameterType="java.lang.Long" resultType="java.math.BigDecimal">
        SELECT
            IFNULL(sum(tax_pay_included_price-ifnull(total_cancel_amount,0)-ifnull(really_pay_included_price,0)),0)
        FROM
            payment_apply
        WHERE
          audit_status != 5
          AND (deleted_flag = 0 or deleted_flag is null)
          AND payment_plan_id = #{paymentPlanId,jdbcType=BIGINT}
    </select>

    <!-- 累计罚扣款（含税） -->
    <select id="totalpenaltyAmount" parameterType="java.lang.Long" resultType="java.math.BigDecimal">
        select IFNULL(sum(penalty_amount),0)
        from payment_apply
        where
        audit_status in (1,2,3,4)
        AND gceb_status <![CDATA[ <> ]]> 2
        AND erp_status <![CDATA[ <> ]]> 2
        AND (deleted_flag = 0 or deleted_flag is null)
        AND payment_plan_id = #{paymentPlanId,jdbcType=BIGINT}
    </select>


    <select id="countApplyByContractId" parameterType="java.lang.Long" resultType="java.lang.Long">
        select count(id)
        from payment_apply
        where
        audit_status != 5
        AND (deleted_flag = 0 or deleted_flag is null)
        AND gceb_status != 2
        AND purchase_contract_id = #{contractId,jdbcType=BIGINT}
    </select>

    <select id="totalTaxPayIncludedPrice" parameterType="java.lang.Long" resultType="java.math.BigDecimal">
        select IFNULL(sum(tax_pay_included_price),0)
        from payment_apply
        where
        audit_status != 5
        AND (deleted_flag = 0 or deleted_flag is null)
        AND gceb_status != 2
        AND purchase_contract_id = #{contractId,jdbcType=BIGINT}
    </select>

    <select id="findByPaymentCode" resultType="com.midea.pam.common.ctc.dto.PaymentApplyDto">
            SELECT
              pa.id,
              pa.payment_apply_code as paymentApplyCode,
              pa.is_charge as isCharge,
              pa.payment_plan_id as paymentPlanId,
              pa.payment_plan_code as paymentPlanCode,
              pa.payment_plan_num as paymentPlanNum,
              pa.payment_plan_date as paymentPlanDate,
              pa.requirement,
              pa.currency,
              pa.money_back as moneyBack,
              pa.vendor_id as vendorId,
              pa.vendor_erp_id as vendorErpId,
              pa.vendor_code as vendorCode,
              pa.vendor_name as vendorName,
              pa.erp_vendor_site_id as erpVendorSiteId,
              pa.vendor_site_code as vendorSiteCode,
              pa.project_id as projectId,
              pa.project_code as projectCode,
              pa.project_name as projectName,
              pa.purchase_contract_id as purchaseContractId,
              pa.purchase_contract_code as purchaseContractCode,
              pa.contract_code_pam as contractCodePam,
              pa.contract_code_legal as contractCodeLegal,
              pa.purchase_contract_name as purchaseContractName,
              pa.item_number as itemNumber,
              pa.item_number_name as itemNumberName,
              pa.ou_id as ouId,
              pa.ou_name as ouName,
              pa.is_advance as isAdvance,
              pa.date_entry as dateEntry,
              pa.remark,
              pa.payment_method_id as paymentMethodId,
              pa.payment_method_name as paymentMethodName,
              pa.vendor_bank_num as vendorBanknum,
              pa.payment_method as paymentMethod,
              pa.account_num as accountNum,
              pa.account_name as accountName,
              pa.tax_plan_included_price as taxplanIncludedPrice,
              pa.tax_pay_included_price as taxPayIncludedPrice,
              pa.really_pay_included_price as reallyPayIncludedPrice,
              pa.total_cancel_amount as totalCancelAmount,
              pa.invoice_method as invoiceMethod,
              pa.penalty_amount as penaltyAmount,
              pa.write_off_status as writeOffStatus,
              pa.audit_status as auditStatus,
              pa.esb_status as esbStatus,
              pa.gceb_status as gcebStatus,
              pa.erp_status as erpStatus,
              pa.gceb_msg as gcebMsg,
              pa.erp_msg as erpMsg,
              pa.audit_date as auditDate,
              pa.submit_date as submitDate,
              pa.submit_by as submitBy,
              pa.submit_name as submitName,
              pa.create_by as createBy,
              pa.create_at as createAt,
              pa.update_by as updateBy,
              pa.update_at as updateAt,
              pa.deleted_flag as deletedFlag,
              pa.VERSION,
              pa.conversion_type as conversionType,
              pa.conversion_date as conversionDate,
              pa.conversion_rate as conversionRate,
              pa.original_payment_id as originalPaymentId,
              ABS(SUM(IFNULL(a.really_pay_included_price,0))) as refundAmount
            FROM
              payment_apply pa
              LEFT JOIN payment_apply a
                ON pa.id = a.original_payment_id
                AND a.deleted_flag = 0 and a.audit_status != 5
            WHERE pa.deleted_flag = 0
            and pa.payment_apply_code = #{originalPaymentCode}
            and pa.audit_status != 5
            GROUP BY pa.id
    </select>
    <select id="statisticalNum" resultType="java.lang.Integer">
        SELECT
          COUNT(0)
        FROM
          payment_apply pa
        WHERE pa.payment_apply_code LIKE CONCAT('%',#{code}, '%')
    </select>

    <!-- 根据应付发票ID获取付款申请 -->
    <select id="listByInvoiceId" resultMap="BaseResultMap">
        SELECT
            t1.id,
            t1.audit_status,
            t1.is_charge,
            t1.gceb_status,
            t1.erp_status
        FROM
            payment_apply t1
            INNER JOIN payment_apply_invoice_rel pair
                   on t1.id  = pair.payment_apply_id
        WHERE pair.payment_invoice_id  = #{paymentInvoiceId}
            and t1.deleted_flag = 0
            and pair.deleted_flag = 0
    </select>

    <select id="summaryApplyAmount" resultType="com.midea.pam.common.ctc.dto.PaymentApplyDto">
        select
            payment_plan_id paymentPlanId,
            sum( if( audit_status=4,
                     tax_pay_included_price - ifnull(total_cancel_amount,0) ,
                     0)) actualAmount,
            sum( if( audit_status!=4,
                     tax_pay_included_price - ifnull(total_cancel_amount,0) ,
                     0)) totalOnTheWayAmount
        from payment_apply
        where payment_plan_id in
        <foreach collection="planIds" item="planId" open="(" separator="," close=")">
            #{planId}
        </foreach>
        and deleted_flag = 0
        and audit_status != 5
        group by payment_plan_id
    </select>
</mapper>