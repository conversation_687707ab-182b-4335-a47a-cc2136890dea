<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.LaborCostDetailMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.LaborCostDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="cost_collection_id" jdbcType="BIGINT" property="costCollectionId" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="user_type" jdbcType="VARCHAR" property="userType" />
    <result column="project_role" jdbcType="VARCHAR" property="projectRole" />
    <result column="apply_date" jdbcType="TIMESTAMP" property="applyDate" />
    <result column="system_apply_date" jdbcType="TIMESTAMP" property="systemApplyDate" />
    <result column="actual_working_hours" jdbcType="DECIMAL" property="actualWorkingHours" />
    <result column="cost_money" jdbcType="DECIMAL" property="costMoney" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="plan_cost_money" jdbcType="DECIMAL" property="planCostMoney" />
    <result column="cost_total" jdbcType="DECIMAL" property="costTotal" />
    <result column="fill_in_date" jdbcType="DATE" property="fillInDate" />
    <result column="working_hour_accounting_id" jdbcType="BIGINT" property="workingHourAccountingId" />
    <result column="accounting_batch_num" jdbcType="VARCHAR" property="accountingBatchNum" />
    <result column="working_hour_id" jdbcType="BIGINT" property="workingHourId" />
    <result column="accounting_flag" jdbcType="TINYINT" property="accountingFlag" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
    <result column="hard_working" jdbcType="VARCHAR" property="hardWorking" />
    <result column="wbs_budget_code" jdbcType="VARCHAR" property="wbsBudgetCode" />
    <result column="labor_wbs_cost_id" jdbcType="BIGINT" property="laborWbsCostId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, cost_collection_id, project_id, user_id, user_name, user_type, project_role, 
    apply_date, system_apply_date, actual_working_hours, cost_money, status, plan_cost_money, 
    cost_total, fill_in_date, working_hour_accounting_id, accounting_batch_num, working_hour_id, 
    accounting_flag, create_by, create_at, update_at, update_by, deleted_flag, hard_working, 
    wbs_budget_code, labor_wbs_cost_id
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.ctc.entity.LaborCostDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from labor_cost_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from labor_cost_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from labor_cost_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.ctc.entity.LaborCostDetail">
    insert into labor_cost_detail (id, cost_collection_id, project_id, 
      user_id, user_name, user_type, 
      project_role, apply_date, system_apply_date, 
      actual_working_hours, cost_money, status, 
      plan_cost_money, cost_total, fill_in_date, 
      working_hour_accounting_id, accounting_batch_num, 
      working_hour_id, accounting_flag, create_by, 
      create_at, update_at, update_by, 
      deleted_flag, hard_working, wbs_budget_code, 
      labor_wbs_cost_id)
    values (#{id,jdbcType=BIGINT}, #{costCollectionId,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, 
      #{userId,jdbcType=BIGINT}, #{userName,jdbcType=VARCHAR}, #{userType,jdbcType=VARCHAR}, 
      #{projectRole,jdbcType=VARCHAR}, #{applyDate,jdbcType=TIMESTAMP}, #{systemApplyDate,jdbcType=TIMESTAMP}, 
      #{actualWorkingHours,jdbcType=DECIMAL}, #{costMoney,jdbcType=DECIMAL}, #{status,jdbcType=TINYINT}, 
      #{planCostMoney,jdbcType=DECIMAL}, #{costTotal,jdbcType=DECIMAL}, #{fillInDate,jdbcType=DATE}, 
      #{workingHourAccountingId,jdbcType=BIGINT}, #{accountingBatchNum,jdbcType=VARCHAR}, 
      #{workingHourId,jdbcType=BIGINT}, #{accountingFlag,jdbcType=TINYINT}, #{createBy,jdbcType=BIGINT}, 
      #{createAt,jdbcType=TIMESTAMP}, #{updateAt,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=BIGINT}, 
      #{deletedFlag,jdbcType=TINYINT}, #{hardWorking,jdbcType=VARCHAR}, #{wbsBudgetCode,jdbcType=VARCHAR}, 
      #{laborWbsCostId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.ctc.entity.LaborCostDetail">
    insert into labor_cost_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="costCollectionId != null">
        cost_collection_id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="userType != null">
        user_type,
      </if>
      <if test="projectRole != null">
        project_role,
      </if>
      <if test="applyDate != null">
        apply_date,
      </if>
      <if test="systemApplyDate != null">
        system_apply_date,
      </if>
      <if test="actualWorkingHours != null">
        actual_working_hours,
      </if>
      <if test="costMoney != null">
        cost_money,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="planCostMoney != null">
        plan_cost_money,
      </if>
      <if test="costTotal != null">
        cost_total,
      </if>
      <if test="fillInDate != null">
        fill_in_date,
      </if>
      <if test="workingHourAccountingId != null">
        working_hour_accounting_id,
      </if>
      <if test="accountingBatchNum != null">
        accounting_batch_num,
      </if>
      <if test="workingHourId != null">
        working_hour_id,
      </if>
      <if test="accountingFlag != null">
        accounting_flag,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
      <if test="hardWorking != null">
        hard_working,
      </if>
      <if test="wbsBudgetCode != null">
        wbs_budget_code,
      </if>
      <if test="laborWbsCostId != null">
        labor_wbs_cost_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="costCollectionId != null">
        #{costCollectionId,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userType != null">
        #{userType,jdbcType=VARCHAR},
      </if>
      <if test="projectRole != null">
        #{projectRole,jdbcType=VARCHAR},
      </if>
      <if test="applyDate != null">
        #{applyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="systemApplyDate != null">
        #{systemApplyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="actualWorkingHours != null">
        #{actualWorkingHours,jdbcType=DECIMAL},
      </if>
      <if test="costMoney != null">
        #{costMoney,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="planCostMoney != null">
        #{planCostMoney,jdbcType=DECIMAL},
      </if>
      <if test="costTotal != null">
        #{costTotal,jdbcType=DECIMAL},
      </if>
      <if test="fillInDate != null">
        #{fillInDate,jdbcType=DATE},
      </if>
      <if test="workingHourAccountingId != null">
        #{workingHourAccountingId,jdbcType=BIGINT},
      </if>
      <if test="accountingBatchNum != null">
        #{accountingBatchNum,jdbcType=VARCHAR},
      </if>
      <if test="workingHourId != null">
        #{workingHourId,jdbcType=BIGINT},
      </if>
      <if test="accountingFlag != null">
        #{accountingFlag,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="hardWorking != null">
        #{hardWorking,jdbcType=VARCHAR},
      </if>
      <if test="wbsBudgetCode != null">
        #{wbsBudgetCode,jdbcType=VARCHAR},
      </if>
      <if test="laborWbsCostId != null">
        #{laborWbsCostId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.ctc.entity.LaborCostDetailExample" resultType="java.lang.Long">
    select count(*) from labor_cost_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.ctc.entity.LaborCostDetail">
    update labor_cost_detail
    <set>
      <if test="costCollectionId != null">
        cost_collection_id = #{costCollectionId,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userType != null">
        user_type = #{userType,jdbcType=VARCHAR},
      </if>
      <if test="projectRole != null">
        project_role = #{projectRole,jdbcType=VARCHAR},
      </if>
      <if test="applyDate != null">
        apply_date = #{applyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="systemApplyDate != null">
        system_apply_date = #{systemApplyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="actualWorkingHours != null">
        actual_working_hours = #{actualWorkingHours,jdbcType=DECIMAL},
      </if>
      <if test="costMoney != null">
        cost_money = #{costMoney,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="planCostMoney != null">
        plan_cost_money = #{planCostMoney,jdbcType=DECIMAL},
      </if>
      <if test="costTotal != null">
        cost_total = #{costTotal,jdbcType=DECIMAL},
      </if>
      <if test="fillInDate != null">
        fill_in_date = #{fillInDate,jdbcType=DATE},
      </if>
      <if test="workingHourAccountingId != null">
        working_hour_accounting_id = #{workingHourAccountingId,jdbcType=BIGINT},
      </if>
      <if test="accountingBatchNum != null">
        accounting_batch_num = #{accountingBatchNum,jdbcType=VARCHAR},
      </if>
      <if test="workingHourId != null">
        working_hour_id = #{workingHourId,jdbcType=BIGINT},
      </if>
      <if test="accountingFlag != null">
        accounting_flag = #{accountingFlag,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="hardWorking != null">
        hard_working = #{hardWorking,jdbcType=VARCHAR},
      </if>
      <if test="wbsBudgetCode != null">
        wbs_budget_code = #{wbsBudgetCode,jdbcType=VARCHAR},
      </if>
      <if test="laborWbsCostId != null">
        labor_wbs_cost_id = #{laborWbsCostId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.ctc.entity.LaborCostDetail">
    update labor_cost_detail
    set cost_collection_id = #{costCollectionId,jdbcType=BIGINT},
      project_id = #{projectId,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=BIGINT},
      user_name = #{userName,jdbcType=VARCHAR},
      user_type = #{userType,jdbcType=VARCHAR},
      project_role = #{projectRole,jdbcType=VARCHAR},
      apply_date = #{applyDate,jdbcType=TIMESTAMP},
      system_apply_date = #{systemApplyDate,jdbcType=TIMESTAMP},
      actual_working_hours = #{actualWorkingHours,jdbcType=DECIMAL},
      cost_money = #{costMoney,jdbcType=DECIMAL},
      status = #{status,jdbcType=TINYINT},
      plan_cost_money = #{planCostMoney,jdbcType=DECIMAL},
      cost_total = #{costTotal,jdbcType=DECIMAL},
      fill_in_date = #{fillInDate,jdbcType=DATE},
      working_hour_accounting_id = #{workingHourAccountingId,jdbcType=BIGINT},
      accounting_batch_num = #{accountingBatchNum,jdbcType=VARCHAR},
      working_hour_id = #{workingHourId,jdbcType=BIGINT},
      accounting_flag = #{accountingFlag,jdbcType=TINYINT},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      hard_working = #{hardWorking,jdbcType=VARCHAR},
      wbs_budget_code = #{wbsBudgetCode,jdbcType=VARCHAR},
      labor_wbs_cost_id = #{laborWbsCostId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>