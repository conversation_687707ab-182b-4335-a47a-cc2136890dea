<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.ReceiptClaimExtMapper">
    <resultMap id="DtoMap" type="com.midea.pam.common.ctc.dto.ReceiptClaimDto">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="source_system_code" jdbcType="VARCHAR" property="sourceSystemCode"/>
        <result column="cash_receipt_code" jdbcType="VARCHAR" property="cashReceiptCode"/>
        <result column="cash_status" jdbcType="TINYINT" property="cashStatus"/>
        <result column="divide_status" jdbcType="TINYINT" property="divideStatus"/>
        <result column="pay_name" jdbcType="VARCHAR" property="payName"/>
        <result column="pay_bank_code" jdbcType="VARCHAR" property="payBankCode"/>
        <result column="pay_bank_name" jdbcType="VARCHAR" property="payBankName"/>
        <result column="bill_code" jdbcType="VARCHAR" property="billCode"/>
        <result column="pay_amount" jdbcType="DECIMAL" property="payAmount"/>
        <result column="claim_amount" jdbcType="DECIMAL" property="claimAmount"/>
        <result column="currency_code" jdbcType="VARCHAR" property="currencyCode"/>
        <result column="bill_type" jdbcType="VARCHAR" property="billType"/>
        <result column="settle_way" jdbcType="VARCHAR" property="settleWay"/>
        <result column="rec_method" jdbcType="VARCHAR" property="recMethod"/>
        <result column="rec_bank_id" jdbcType="BIGINT" property="recBankId"/>
        <result column="rec_bank_code" jdbcType="VARCHAR" property="recBankCode"/>
        <result column="rec_account_no" jdbcType="VARCHAR" property="recAccountNo"/>
        <result column="rec_org_name" jdbcType="VARCHAR" property="recOrgName"/>
        <result column="rec_org_code" jdbcType="VARCHAR" property="recOrgCode"/>
        <result column="budget_item_code" jdbcType="VARCHAR" property="budgetItemCode"/>
        <result column="ou_id" jdbcType="BIGINT" property="ouId"/>
        <result column="pay_date" jdbcType="TIMESTAMP" property="payDate"/>
        <result column="accounting_date" jdbcType="TIMESTAMP" property="accountingDate"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="conntrans_code" jdbcType="VARCHAR" property="conntransCode"/>
        <result column="transfer_code" jdbcType="VARCHAR" property="transferCode"/>
        <result column="serial_number" jdbcType="VARCHAR" property="serialNumber"/>
        <result column="attribute1" jdbcType="VARCHAR" property="attribute1"/>
        <result column="attribute2" jdbcType="VARCHAR" property="attribute2"/>
        <result column="attribute3" jdbcType="VARCHAR" property="attribute3"/>
        <result column="attribute4" jdbcType="VARCHAR" property="attribute4"/>
        <result column="attribute5" jdbcType="VARCHAR" property="attribute5"/>
        <result column="attribute6" jdbcType="VARCHAR" property="attribute6"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt"/>
        <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag"/>
        <result column="source" jdbcType="TINYINT" property="source" />
        <!--Detail-->
        <result column="receipt_claim_id" jdbcType="BIGINT" property="receiptClaimId"/>
        <result column="receipt_code" jdbcType="VARCHAR" property="receiptCode"/>
        <result column="business_type" jdbcType="TINYINT" property="businessType"/>
        <result column="accounting_date" jdbcType="TIMESTAMP" property="accountingDate"/>
        <result column="amount_received" jdbcType="DECIMAL" property="amountReceived"/>
        <result column="commission" jdbcType="DECIMAL" property="commission"/>
        <result column="claim_amount" jdbcType="DECIMAL" property="claimAmount"/>
        <result column="claim_status" jdbcType="TINYINT" property="claimStatus"/>
        <result column="claim_date" jdbcType="TIMESTAMP" property="claimDate"/>
        <result column="claim_by" jdbcType="BIGINT" property="claimBy"/>
        <result column="customer_id" jdbcType="BIGINT" property="customerId"/>
        <result column="customer_code" jdbcType="VARCHAR" property="customerCode"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="contract_status" jdbcType="TINYINT" property="contractStatus"/>
        <result column="busi_scene_id" jdbcType="BIGINT" property="busiSceneId"/>
        <result column="busi_scene" jdbcType="VARCHAR" property="busiScene"/>
        <result column="vendor_code" jdbcType="VARCHAR" property="vendorCode"/>
        <result column="vendor_name" jdbcType="VARCHAR" property="vendorName"/>
        <result column="vendor_site_code" jdbcType="VARCHAR" property="vendorSiteCode"/>
        <result column="ap_invoice_subject" jdbcType="VARCHAR" property="apInvoiceSubject"/>
        <result column="gl_subject" jdbcType="VARCHAR" property="glSubject"/>
        <result column="receivables_trx_id" jdbcType="BIGINT" property="receivablesTrxId"/>
        <result column="ar_receipt_activity" jdbcType="VARCHAR" property="arReceiptActivity"/>
        <result column="cust_trx_type_id" jdbcType="BIGINT" property="custTrxTypeId"/>
        <result column="ar_invoice_type" jdbcType="VARCHAR" property="arInvoiceType"/>
        <result column="erp_status" jdbcType="TINYINT" property="erpStatus"/>
        <result column="erp_message" jdbcType="VARCHAR" property="erpMessage"/>
        <result column="erp_reversal_date" jdbcType="TIMESTAMP" property="erpReversalDate"/>
        <result column="erp_reversal_reason" jdbcType="TIMESTAMP" property="erpReversalReason"/>
        <result column="write_off_status" jdbcType="TINYINT" property="writeOffStatus"/>
        <result column="original_payment_code" jdbcType="VARCHAR" property="originalPaymentCode"/>
        <result column="customer_id" jdbcType="BIGINT" property="customerId"/>
        <result column="payment_invoice_id" jdbcType="BIGINT" property="paymentInvoiceId"/>
        <result column="payment_apply_id" jdbcType="BIGINT" property="paymentApplyId"/>

        <!--invoicePlanDetail-->
        <result column="code" jdbcType="VARCHAR" property="invoicePlanCode"/>
        <result column="excluding_tax_amount" jdbcType="DECIMAL" property="invoicePlanExcludingTaxAmount"/>
        <result column="amount" jdbcType="DECIMAL" property="invoicePlanAmount"/>
        <result column="allocated_amount" jdbcType="DECIMAL" property="allocatedAmount"/>

        <result column="refund_amount" jdbcType="DECIMAL" property="refundAmount"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="refund_apply_code" jdbcType="VARCHAR" property="refundApplyCode"/>

    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>

    <sql id="Base_Column_List">
        rcd.id,rcd.receipt_claim_id,
        rc.source_system_code,rc.cash_receipt_code,rc.cash_status,rc.divide_status,
        rc.pay_name,rc.pay_bank_code,rc.pay_bank_name,rc.bill_code,rc.pay_amount,
        rc.currency_code,rc.bill_type,rc.settle_way,rc.rec_method,
        rc.rec_bank_id,rc.rec_bank_code,rc.rec_account_no,rc.rec_org_name,rc.rec_org_code,
        rc.budget_item_code,rc.ou_id,rc.pay_date,
        rc.remark,rc.conntrans_code,rc.transfer_code,rc.serial_number,
        rc.source,
        rcd.receipt_code,rcd.business_type,rcd.claim_status,rcd.claim_date,rcd.claim_amount,rcd.amount_received,
        rcd.commission,rcd.claim_by,rcd.accounting_date,rcd.customer_id,rcd.customer_code,
        rcd.customer_name,rcd.contract_status,rcd.busi_scene_id,rcd.busi_scene,rcd.vendor_code,
        rcd.vendor_name,rcd.vendor_site_code,rcd.ap_invoice_subject,rcd.gl_subject,
        rcd.receivables_trx_id,rcd.cust_trx_type_id,rcd.ar_receipt_activity,rcd.ar_invoice_type,
        rcd.erp_status,rcd.erp_message,rcd.erp_reversal_date,rcd.erp_reversal_reason,rcd.deleted_flag,
        rcd.write_off_status,
        rcd.original_payment_code,
        rcd.payment_invoice_id,
        rcd.payment_apply_id
    </sql>

    <sql id="Base_Column_List1">
        distinct
        rcd.id,rcd.receipt_claim_id,
        rc.source_system_code,rc.cash_receipt_code,rc.cash_status,rc.divide_status,
        rc.pay_name,rc.pay_bank_code,rc.pay_bank_name,rc.bill_code,rc.pay_amount,
        rc.currency_code,rc.bill_type,rc.settle_way,rc.rec_method,
        rc.rec_bank_id,rc.rec_bank_code,rc.rec_account_no,rc.rec_org_name,rc.rec_org_code,
        rc.budget_item_code,rc.ou_id,rc.pay_date,
        rccr.remark,rc.conntrans_code,rc.transfer_code,rc.serial_number,
        rcd.receipt_code,rcd.business_type,rcd.claim_status,rcd.claim_date,rcd.claim_amount,rcd.amount_received,
        rcd.commission, rcd.claim_by,rcd.accounting_date,rcd.customer_id,rcd.customer_code,
        rcd.customer_name,rcd.contract_status,rcd.busi_scene_id,rcd.busi_scene,rcd.vendor_code,
        rcd.vendor_name,rcd.vendor_site_code,rcd.ap_invoice_subject,rcd.gl_subject,
        rcd.receivables_trx_id,rcd.cust_trx_type_id,rcd.ar_receipt_activity,rcd.ar_invoice_type,
        rcd.erp_status,rcd.erp_message,rcd.erp_reversal_date,rcd.erp_reversal_reason,rcd.deleted_flag,
        rcd.write_off_status,rpd.code,rpd.excluding_tax_amount,rpd.amount,rccr.allocated_amount
    </sql>

    <update id="updateClaimDetailErpStatus">
        update receipt_claim_detail
        set
        erp_status = #{erpStatus,jdbcType=TINYINT},
        erp_message = #{erpMessage,jdbcType=VARCHAR},
        contract_sync_status = #{contractSyncStatus,jdbcType=TINYINT},
        contract_sync_message = #{contractSyncMessage,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT} and erp_status <![CDATA[ <> ]]> 3
    </update>

    <update id="updateClaimDetailContractSyncStatus">
        update receipt_claim_detail
        set
        contract_sync_status = #{contractSyncStatus,jdbcType=TINYINT},
        contract_sync_message = #{contractSyncMessage,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
        and contract_sync_status <![CDATA[ <> ]]> 1
    </update>

    <update id="updateContractSyncStatusByErpStatus">
        update receipt_claim_detail
        set
        contract_sync_status = #{contractSyncStatus,jdbcType=TINYINT},
        contract_sync_message = #{contractSyncMessage,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
        and erp_status = 3
    </update>

    <update id="updateDetailDeletedFlagById">
        update pam_ctc.receipt_claim_detail
        set deleted_flag = #{deletedFlag}
        where receipt_claim_id = #{receiptClaimId}
    </update>

    <select id="queryDetailList" parameterType="com.midea.pam.common.ctc.dto.ReceiptClaimDto" resultMap="DtoMap">
        select
        <include refid="Base_Column_List"/>
        from
        receipt_claim_detail rcd left join receipt_claim rc on rcd.receipt_claim_id = rc.id
        where 1=1
        and rcd.deleted_flag = '0'
        <if test="id != null">
            and rcd.id = #{id}
        </if>
        <if test="idList != null">
            and rcd.id in
            <foreach close=")" collection="idList" index="index" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="receiptClaimId != null">
            and rcd.receipt_claim_id = #{receiptClaimId}
        </if>
        <if test="businessType != null">
            and rcd.business_type = #{businessType}
        </if>
        <if test="claimStatus != null">
            and rcd.claim_status = #{claimStatus}
        </if>
        <if test="erpStatus != null">
            and rcd.erp_status = #{erpStatus}
        </if>
        <if test="contractStatus != null">
            and rcd.contract_status = #{contractStatus}
        </if>
        <if test="contractStatusList != null and contractStatusList.size() > 0">
            and rcd.contract_status in
            <foreach close=")" collection="contractStatusList" index="index" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="writeOffStatus != null">
            and rcd.write_off_status = #{writeOffStatus}
        </if>
        <if test="erpStatusList != null">
            and rcd.erp_status in
            <foreach close=")" collection="erpStatusList" index="index" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="writeOffStatusList != null and writeOffStatusList.size() > 0">
            and rcd.write_off_status in
            <foreach close=")" collection="writeOffStatusList" index="index" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="receiptCode != null">
            and rcd.receipt_code like CONCAT(CONCAT('%',#{receiptCode}),'%')
        </if>
        <if test="accountingDate != null">
            <![CDATA[ and to_days(rcd.accounting_date) = to_days(#{accountingDate}) ]]>
        </if>
        <if test="customerCode != null">
            and rcd.customer_code like CONCAT(CONCAT('%',#{customerCode}),'%')
        </if>
        <if test="customerName != null">
            and rcd.customer_name like CONCAT(CONCAT('%',#{customerName}),'%')
        </if>
        <if test="claimAmount != null">
            <![CDATA[ and rcd.claim_amount = #{claimAmount} ]]>
        </if>
        <if test="cashStatus != null">
            and rc.cash_status = #{cashStatus}
        </if>
        <if test="currencyCode != null">
            and rc.currency_code = #{currencyCode}
        </if>
        <if test="ouId != null">
            and rc.ou_id = #{ouId}
        </if>
        <if test="cashReceiptCode != null">
            and rc.cash_receipt_code like CONCAT(CONCAT('%',#{cashReceiptCode}),'%')
        </if>
        <if test="payDate != null">
            <![CDATA[ and rc.pay_date = #{payDate} ]]>
        </if>
        order by rc.pay_date desc, rcd.receipt_code desc, rcd.id desc
    </select>

    <select id="getDivideList" parameterType="com.midea.pam.common.ctc.dto.ReceiptClaimDto" resultMap="DtoMap">
        <!--select-->
        <!--<include refid="Claim_Sale_Column_List" />-->
        <!--from receipt_claim_sale rcs-->
        <!--where 1=1-->
        <!--<if test="receiptClaimId != null">-->
        <!--and rcs.receipt_claim_id = #{receiptClaimId}-->
        <!--</if>-->
        <!--UNION ALL-->
        <!--select-->
        <!--<include refid="Claim_Non_Sale_Column_List" />-->
        <!--from receipt_claim_non_sale rcns-->
        <!--where 1=1-->
        <!--<if test="receiptClaimId != null">-->
        <!--and rcns.receipt_claim_id = #{receiptClaimId}-->
        <!--</if>-->
    </select>

    <select id="queryDetailList1" parameterType="com.midea.pam.common.ctc.dto.ReceiptClaimDto" resultMap="DtoMap">
        select
        <include refid="Base_Column_List1"/>
        from
        receipt_claim_detail rcd
        left join receipt_claim rc on rcd.receipt_claim_id = rc.id
        left join receipt_claim_contract_rel rccr on rcd.id = rccr.receipt_claim_detail_id and rccr.deleted_flag = 0
        left join invoice_plan_detail rpd on rccr.invoice_plan_detail_id = rpd.id
        where 1=1
        and rcd.deleted_flag = '0'
        <if test="id != null">
            and rcd.id = #{id}
        </if>
        <if test="idList != null">
            and rcd.id in
            <foreach close=")" collection="idList" index="index" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="contractId != null">
            and rpd.contract_id = #{contractId}
        </if>
        order by accounting_date desc
    </select>

    <select id="getCashReceiptCodeForRefund" parameterType="java.util.Map" resultMap="DtoMap">
        select t.* from (select
        ifnull((select sum(rad.refund_amount)
        from refund_apply_detail rad
        inner join refund_apply ra on rad.refund_apply_id = ra.id and ra.deleted_flag = 0
        where rad.receipt_claim_detail_id = rcd.id
        and rad.deleted_flag = 0 and ra.refund_apply_status != -1),0) as refund_amount,
        <include refid="Base_Column_List"/>
        from receipt_claim_detail rcd
        left join receipt_claim rc on rcd.receipt_claim_id = rc.id and rc.deleted_flag = 0
        inner join receipt_claim_contract_rel rccr on rccr.receipt_claim_detail_id = rcd.id and rccr.deleted_flag = 0) t
        where t.claim_status = 2 and t.deleted_flag = 0
        <if test="ouIdList != null">
            and t.ou_id in
            <foreach collection="ouIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="cashReceiptCode != null">
            and t.cash_receipt_code like CONCAT(CONCAT('%',#{cashReceiptCode}),'%')
        </if>
        <if test="payDate != null">
            and t.pay_date like CONCAT(CONCAT('%',#{payDate}),'%')
        </if>
        <if test="customerName != null">
            and t.customer_name like CONCAT(CONCAT('%',#{customerName}),'%')
        </if>
        <if test="customerCode != null">
            and t.customer_code like CONCAT(CONCAT('%',#{customerCode}),'%')
        </if>
        <if test="contractCode != null">
            and t.contract_code like CONCAT(CONCAT('%',#{contractCode}),'%')
        </if>
        <if test="contractName != null">
            and t.contract_name like CONCAT(CONCAT('%',#{contractName}),'%')
        </if>
        <if test="projectCode != null">
            and t.project_code like CONCAT(CONCAT('%',#{projectCode}),'%')
        </if>
        <if test="projectName != null">
            and t.project_name like CONCAT(CONCAT('%',#{projectName}),'%')
        </if>
        <if test="receiptCode != null">
            and t.receipt_code like CONCAT(CONCAT('%',#{receiptCode}),'%')
        </if>
        group by t.id
    </select>

    <select id="getContractRelInfo" parameterType="java.util.Map"
            resultType="com.midea.pam.common.ctc.dto.ReceiptClaimContractRelDto">
        select rccr.id,
        rc.id receiptClaimId,
        c.id contractId,
        c.code contractCode,
        c.name contractName,
        c.currency currencyCode,
        ipd.id invoicePlanDetailId,
        ipd.code invoicePlanDetailCode,
        p.id projectId,
        p.name projectName,
        p.code projectCode,
        rcd.receipt_code receiptCode,
        rcd.claim_amount claimAmount,
        rcd.id receiptClaimDetailId,
        rccr.allocated_amount allocatedAmount,
        ifnull((select sum(rad.refund_amount) from refund_apply_detail rad inner join refund_apply ra on
        rad.refund_apply_id = ra.id and ra.deleted_flag = 0
        where rad.contract_id = rccr.contract_id and rad.invoice_plan_detail_id = rccr.invoice_plan_detail_id and
        rad.deleted_flag = 0
        and rad.receipt_claim_detail_id = rcd.id and ra.refund_apply_status != -1),0) contractRefundedAmount
        from receipt_claim_contract_rel rccr
        left join receipt_claim_detail rcd on rccr.receipt_claim_detail_id = rcd.id and rcd.deleted_flag = 0
        left join receipt_claim rc on rc.id = rcd.receipt_claim_id and rc.deleted_flag = 0
        left join contract c on rccr.contract_id = c.id and c.deleted_flag = 0
        left join project_contract_rs pcr on c.id = pcr.contract_id and pcr.deleted_flag = 0
        left join project p on pcr.project_id = p.id and p.deleted_flag = 0
        left join invoice_plan_detail ipd on rccr.invoice_plan_detail_id = ipd.id and ipd.deleted_flag = 0
        where 1=1 and rccr.deleted_flag = 0
        <if test="customerId != null">
            and c.customer_id = #{customerId}
        </if>
        <if test="id != null">
            and rcd.id = #{id}
        </if>
        <if test="contractCode != null">
            and c.code like CONCAT(CONCAT('%',#{contractCode}),'%')
        </if>
        <if test="contractName != null">
            and c.name like CONCAT(CONCAT('%',#{contractName}),'%')
        </if>
        <if test="projectCode != null">
            and p.code like CONCAT(CONCAT('%',#{projectCode}),'%')
        </if>
        <if test="projectName != null">
            and p.name like CONCAT(CONCAT('%',#{projectName}),'%')
        </if>
        <if test="receiptCode != null">
            and rcd.receipt_code like CONCAT(CONCAT('%',#{receiptCode}),'%')
        </if>
        <if test="invoicePlanDetailCode != null">
            and ipd.code like CONCAT(CONCAT('%',#{invoicePlanDetailCode}),'%')
        </if>
        <if test="idList != null and idList.size() > 0">
            and rcd.id in
            <foreach collection="idList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="ouIdList != null">
            and c.ou_id in
            <foreach collection="ouIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryReceiptClaimContractRel" parameterType="java.util.Map"
            resultType="com.midea.pam.common.ctc.dto.ReceiptClaimContractRelDto">
        select
        t.receipt_claim_detail_id as receiptClaimDetailId,
        group_concat(t.code) as contractCode
        from
        (
        select
        distinct rccr.receipt_claim_detail_id,
        c.code
        from
        pam_ctc.receipt_claim_contract_rel rccr
        inner join pam_ctc.contract c on
        c.id = rccr.contract_id
        where
        rccr.deleted_flag = 0
        <if test="idList != null">
            and rccr.receipt_claim_detail_id in
            <foreach close=")" collection="idList" index="index" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        ) t
        group by
        t.receipt_claim_detail_id
    </select>

    <select id="findEffectiveContractRel" resultType="java.lang.Integer">
        SELECT
        count(0)
        FROM
        receipt_claim_contract_rel cr
        WHERE cr.deleted_flag = 0
        AND cr.receipt_claim_detail_id = #{id}
    </select>

    <select id="getByReceiptCodeAndCustomerId" resultType="com.midea.pam.common.ctc.dto.ReceiptClaimDetailDTO">
        select rcd.*, rc.currency_code as currency
        from receipt_claim_detail rcd
        left join receipt_claim rc on rcd.receipt_claim_id = rc.id
        where rcd.deleted_flag = 0
        and rc.deleted_flag = 0
        and rc.ou_id = #{ouId}
        and rcd.receipt_code = #{receiptCode}
        and rcd.customer_id = #{customerId}
    </select>

    <select id="queryCustomerTransferByReceiptClaimDetailId"
            resultType="com.midea.pam.common.ctc.entity.CustomerTransfer">
        select ct.*
        from pam_ctc.receipt_claim_detail rcd
        left join pam_ctc.receipt_claim rc on rcd.receipt_claim_id = rc.id
        left join pam_ctc.customer_transfer ct on rc.customer_transfer_id = ct.id
        where rcd.id = #{receiptClaimDetailId}
    </select>
</mapper>