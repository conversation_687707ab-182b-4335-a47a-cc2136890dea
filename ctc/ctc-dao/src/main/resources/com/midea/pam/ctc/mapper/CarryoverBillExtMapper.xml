<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.CarryoverBillExtMapper">
    <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.CarryoverBill">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="carryover_batch_num" jdbcType="VARCHAR" property="carryoverBatchNum" />
        <result column="bill_num" jdbcType="VARCHAR" property="billNum" />
        <result column="gl_period_id" jdbcType="BIGINT" property="glPeriodId" />
        <result column="period_name" jdbcType="VARCHAR" property="periodName" />
        <result column="ou_id" jdbcType="BIGINT" property="ouId" />
        <result column="ou_name" jdbcType="VARCHAR" property="ouName" />
        <result column="project_id" jdbcType="BIGINT" property="projectId" />
        <result column="project_num" jdbcType="VARCHAR" property="projectNum" />
        <result column="project_name" jdbcType="VARCHAR" property="projectName" />
        <result column="project_type" jdbcType="BIGINT" property="projectType" />
        <result column="customer_id" jdbcType="BIGINT" property="customerId" />
        <result column="customer_num" jdbcType="VARCHAR" property="customerNum" />
        <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
        <result column="project_milepost_id" jdbcType="BIGINT" property="projectMilepostId" />
        <result column="project_milepost_name" jdbcType="VARCHAR" property="projectMilepostName" />
        <result column="income_milepost_actual_end_time" jdbcType="TIMESTAMP" property="incomeMilepostActualEndTime" />
        <result column="cumulative_income_percent" jdbcType="DECIMAL" property="cumulativeIncomePercent" />
        <result column="cumulative_cost_percent" jdbcType="DECIMAL" property="cumulativeCostPercent" />
        <result column="cumulative_income_total" jdbcType="DECIMAL" property="cumulativeIncomeTotal" />
        <result column="standard_cumulative_income_total" jdbcType="DECIMAL" property="standardCumulativeIncomeTotal" />
        <result column="cumulative_cost_total" jdbcType="DECIMAL" property="cumulativeCostTotal" />
        <result column="current_income_percent" jdbcType="DECIMAL" property="currentIncomePercent" />
        <result column="should_carryover_cost" jdbcType="DECIMAL" property="shouldCarryoverCost" />
        <result column="current_income_amount" jdbcType="DECIMAL" property="currentIncomeAmount" />
        <result column="current_income_adjustment" jdbcType="DECIMAL" property="currentIncomeAdjustment" />
        <result column="current_income_total_amount" jdbcType="DECIMAL" property="currentIncomeTotalAmount" />
        <result column="standard_current_income_total_amount" jdbcType="DECIMAL" property="standardCurrentIncomeTotalAmount" />
        <result column="current_cost_actual" jdbcType="DECIMAL" property="currentCostActual" />
        <result column="spread_adjustment_amount" jdbcType="DECIMAL" property="spreadAdjustmentAmount" />
        <result column="cost_ratio_config_detail_id" jdbcType="BIGINT" property="costRatioConfigDetailId" />
        <result column="cost_ratio_config_detail_cost_ratio" jdbcType="DECIMAL" property="costRatioConfigDetailCostRatio" />
        <result column="current_cost_outsourcing_cost" jdbcType="DECIMAL" property="currentCostOutsourcingCost" />
        <result column="gross_profit_rate" jdbcType="DECIMAL" property="grossProfitRate" />
        <result column="local_currency" jdbcType="VARCHAR" property="localCurrency" />
        <result column="total_income" jdbcType="DECIMAL" property="totalIncome" />
        <result column="total_budget" jdbcType="DECIMAL" property="totalBudget" />
        <result column="erp_sync_status" jdbcType="INTEGER" property="erpSyncStatus" />
        <result column="cost_method" jdbcType="VARCHAR" property="costMethod" />
        <result column="cost_method_name" jdbcType="VARCHAR" property="costMethodName" />
        <result column="income_point" jdbcType="VARCHAR" property="incomePoint" />
        <result column="income_point_name" jdbcType="VARCHAR" property="incomePointName" />
        <result column="period_total_confirmation" jdbcType="INTEGER" property="periodTotalConfirmation" />
        <result column="period_confirmed" jdbcType="INTEGER" property="periodConfirmed" />
        <result column="source" jdbcType="INTEGER" property="source" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="reverse_status" jdbcType="INTEGER" property="reverseStatus" />
        <result column="reverse_reason" jdbcType="VARCHAR" property="reverseReason" />
        <result column="reverse_period" jdbcType="VARCHAR" property="reversePeriod" />
        <result column="resource_type" jdbcType="INTEGER" property="resourceType" />
        <result column="deleted_flag" jdbcType="BIT" property="deletedFlag" />
        <result column="create_by" jdbcType="BIGINT" property="createBy" />
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
        <result column="update_by" jdbcType="BIGINT" property="updateBy" />
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
        <result column="handmade_income" jdbcType="DECIMAL" property="handmadeIncome" />
        <result column="handmade_cost" jdbcType="DECIMAL" property="handmadeCost" />
        <result column="original_id" jdbcType="BIGINT" property="originalId" />
        <result column="original_num" jdbcType="VARCHAR" property="originalNum" />
        <result column="sync_erp_time" jdbcType="TIMESTAMP" property="syncErpTime" />
        <result column="conversion_type" jdbcType="VARCHAR" property="conversionType" />
        <result column="conversion_date" jdbcType="TIMESTAMP" property="conversionDate" />
        <result column="conversion_rate" jdbcType="DECIMAL" property="conversionRate" />
    </resultMap>
    <sql id="Base_Column_List">
    bill.id, bill.carryover_batch_num, bill.bill_num, bill.gl_period_id, bill.period_name, bill.ou_id, bill.ou_name, bill.project_id,
    bill.project_num, bill.project_name, bill.project_type, bill.customer_id, bill.customer_num, bill.customer_name,
    bill.project_milepost_id, bill.project_milepost_name, bill.income_milepost_actual_end_time, bill.cumulative_income_percent, bill.cumulative_cost_percent,
    bill.cumulative_income_total, bill.cumulative_cost_total, bill.current_income_percent, bill.current_income_amount, bill.current_income_adjustment,
    bill.current_income_total_amount, bill.current_cost_actual, bill.spread_adjustment_amount, bill.cost_ratio_config_detail_id,
    bill.cost_ratio_config_detail_cost_ratio, bill.current_cost_outsourcing_cost, bill.gross_profit_rate, bill.should_carryover_cost,
    bill.local_currency, bill.total_income, bill.total_budget, bill.erp_sync_status, bill.cost_method, bill.cost_method_name,
    bill.income_point, bill.income_point_name, bill.period_total_confirmation, bill.period_confirmed, bill.source,
    bill.status, bill.reverse_status, bill.reverse_reason, bill.reverse_period, bill.resource_type, bill.deleted_flag,
    bill.create_by, bill.create_at, bill.update_by, bill.update_at, bill.handmade_income, bill.handmade_cost, bill.original_id,
    bill.original_num, bill.accumulated_carryover_cost, bill.seq, bill.standard_cumulative_income_total, bill.standard_current_income_total_amount,
    bill.sync_erp_time, bill.conversion_type, bill.conversion_date, bill.conversion_rate
    </sql>

    <insert id="insertCarryoverBillDetail" parameterType="com.midea.pam.common.ctc.entity.ClassificationCarryoverBill">
        insert into classification_carryover_bill (id, head_id, carryover_bill_id,
        project_id, project_code, project_name,
        current_income_total_amount, sync_erp_time,
        deleted_flag, create_by, create_at,
        update_by, update_at) values
        <foreach collection="list" item="record" index="index" separator=",">
            (#{record.id,jdbcType=BIGINT}, #{record.headId,jdbcType=BIGINT}, #{record.carryoverBillId,jdbcType=BIGINT},
            #{record.projectId,jdbcType=BIGINT}, #{record.projectCode,jdbcType=VARCHAR}, #{record.projectName,jdbcType=VARCHAR},
            #{record.currentIncomeTotalAmount,jdbcType=DECIMAL}, #{record.syncErpTime,jdbcType=TIMESTAMP},
            #{record.deletedFlag,jdbcType=TINYINT}, #{record.createBy,jdbcType=BIGINT}, #{record.createAt,jdbcType=TIMESTAMP},
            #{record.updateBy,jdbcType=BIGINT}, #{record.updateAt,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <insert id="insertInvoiceReceivableDetail" parameterType="com.midea.pam.common.ctc.entity.ClassificationInvoiceReceivable">
        insert into classification_invoice_receivable (id, head_id, invoice_receivable_id,
        contract_id, contract_code, project_id,
        project_code, project_name, invoice_type,
        exclusive_of_tax, conversion_rate, sync_erp_time,
        deleted_flag, create_by, create_at,
        update_by, update_at) values
        <foreach collection="list" item="record" index="index" separator=",">
            (#{record.id,jdbcType=BIGINT}, #{record.headId,jdbcType=BIGINT}, #{record.invoiceReceivableId,jdbcType=BIGINT},
            #{record.contractId,jdbcType=BIGINT}, #{record.contractCode,jdbcType=VARCHAR}, #{record.projectId,jdbcType=BIGINT},
            #{record.projectCode,jdbcType=VARCHAR}, #{record.projectName,jdbcType=VARCHAR}, #{record.invoiceType,jdbcType=VARCHAR},
            #{record.exclusiveOfTax,jdbcType=DECIMAL}, #{record.conversionRate,jdbcType=DECIMAL}, #{record.syncErpTime,jdbcType=TIMESTAMP},
            #{record.deletedFlag,jdbcType=TINYINT}, #{record.createBy,jdbcType=BIGINT}, #{record.createAt,jdbcType=TIMESTAMP},
            #{record.updateBy,jdbcType=BIGINT}, #{record.updateAt,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <insert id="insertRevenueCostOrderDetail" parameterType="com.midea.pam.common.ctc.entity.ClassificationRevenueCostOrder">
        insert into classification_revenue_cost_order (id, head_id, revenue_cost_order_id,
        order_code, project_id, project_code,
        project_name, cost, deleted_flag,
        create_by, create_at, update_by,
        update_at) values
        <foreach collection="list" item="record" index="index" separator=",">
            (#{record.id,jdbcType=BIGINT}, #{record.headId,jdbcType=BIGINT}, #{record.revenueCostOrderId,jdbcType=BIGINT},
             #{record.orderCode,jdbcType=VARCHAR}, #{record.projectId,jdbcType=BIGINT}, #{record.projectCode,jdbcType=VARCHAR},
             #{record.projectName,jdbcType=VARCHAR}, #{record.cost,jdbcType=DECIMAL}, #{record.deletedFlag,jdbcType=TINYINT},
             #{record.createBy,jdbcType=BIGINT}, #{record.createAt,jdbcType=TIMESTAMP}, #{record.updateBy,jdbcType=BIGINT},
             #{record.updateAt,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <select id="list" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            carryover_bill bill
        WHERE bill.deleted_flag = 0

        <if test="fuzzyProjectNum != null and fuzzyProjectNum != ''">
            AND bill.project_num like concat('%', #{fuzzyProjectNum, jdbcType=CHAR}, '%')
        </if>

        <if test="fuzzyProjectName != null and fuzzyProjectName != ''">
            AND bill.project_name like concat('%', #{fuzzyProjectName, jdbcType=CHAR}, '%')
        </if>

        <if test="projectNameOrCode != null and projectNameOrCode != ''">
            AND (bill.project_name like concat('%', #{projectNameOrCode, jdbcType=CHAR}, '%')
            OR bill.project_num like concat('%', #{projectNameOrCode, jdbcType=CHAR}, '%'))
        </if>

        <if test="billNum != null and billNum != ''">
            AND bill.bill_num like concat('%', #{billNum, jdbcType=CHAR}, '%')
        </if>

        <if test="periodName != null and periodName != ''">
            AND bill.period_name like concat('%', #{periodName, jdbcType=CHAR}, '%')
        </if>

        <if test="ouId != null">
            AND bill.ou_id = #{ouId, jdbcType=BIGINT}
        </if>

        <if test="projectType != null">
            AND bill.project_type = #{projectType}
        </if>

        <if test="projectId != null">
            AND bill.project_id = #{projectId}
        </if>

        <if test="status != null">
            AND bill.status = #{status}
        </if>

        <if test="erpSyncStatus != null">
            AND bill.erp_sync_status = #{erpSyncStatus}
        </if>

        <if test="carryoverBatchNum != null and carryoverBatchNum != ''">
            AND bill.carryover_batch_num like concat('%', #{carryoverBatchNum, jdbcType=CHAR}, '%')
        </if>

        <if test="reverseStatus != null">
            AND bill.reverse_status = #{reverseStatus}
        </if>

        <if test="costMethod != null">
            AND bill.cost_method = #{costMethod}
        </if>

        <if test="unitId != null">
            AND bill.project_id in (select p.id from project p where p.unit_id = #{unitId} and p.deleted_flag = 0)
        </if>

    </select>

    <select id="findWaitFroCarryoverByBatchNum" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
            carryover_bill bill,
            project_milepost milepost
        WHERE
            bill.project_milepost_id = milepost.id
        AND bill.carryover_batch_num = milepost.carryover_batch_num
        AND milepost.carry_status = true
        AND milepost.carryover_bill_id IS NULL
        AND bill.carryover_batch_num = #{carryoverBatchNum,jdbcType=VARCHAR}
        AND bill.deleted_flag = 0
    </select>

    <select id="totalIncomeCalculationOfMilepost"
            resultType="com.midea.pam.common.ctc.dto.CarryoverBillProcessScheduleDto">
        SELECT
            IFNULL(
            sum(bill.current_income_amount),
            0
            ) AS 'currentPeriodIncome',
            IFNULL(
            sum(bill.current_income_amount * ifnull(bill.conversion_rate, 1)),
            0
            ) AS 'standardCurrentPeriodIncome',
            IFNULL(
                    sum(bill.handmade_income),
                    0
            ) AS 'handmadeIncome',
            IFNULL(
            sum(bill.current_income_adjustment),
            0
            ) AS 'currentPeriodAdjustmentForIncome',
            IFNULL(
            sum(bill.current_cost_actual),
            0
            ) AS 'currentPeriodActualCost'
        FROM
        carryover_bill bill,
        project_milepost milepost
        WHERE
        bill.project_milepost_id = milepost.id
        AND milepost.carry_status = TRUE
        AND bill.carryover_batch_num = #{carryoverBatchNum,jdbcType=VARCHAR}
    </select>

    <select id="totalIncomeCalculationOfIncomeCostPlan"
            resultType="com.midea.pam.common.ctc.dto.CarryoverBillProcessScheduleDto">
        SELECT
            IFNULL(
            sum(bill.current_income_amount),
            0
            ) AS 'currentPeriodIncome',
            IFNULL(
            sum(bill.current_income_amount * ifnull(bill.conversion_rate, 1)),
            0
            ) AS 'standardCurrentPeriodIncome',
            IFNULL(
                    sum(bill.handmade_income),
                    0
            ) AS 'handmadeIncome',
            IFNULL(
            sum(bill.current_income_adjustment),
            0
            ) AS 'currentPeriodAdjustmentForIncome',
            IFNULL(
            sum(bill.current_cost_actual),
            0
            ) AS 'currentPeriodActualCost'
        FROM
        carryover_bill bill,
        project_income_cost_plan picp
        WHERE
        bill.project_milepost_id = picp.id
        AND picp.carry_status = TRUE
        AND bill.carryover_batch_num = #{carryoverBatchNum,jdbcType=VARCHAR}
    </select>

    <select id="countCarryBillByContractId" parameterType="java.lang.Long" resultType="java.lang.Long">
        select count(cb.id)
        from carryover_bill_outsourcing_cost_collection occ left join carryover_bill cb on occ.carryover_bill_id = cb.id AND (cb.deleted_flag is null or cb.deleted_flag = 0)
        where
          (occ.deleted_flag is null or occ.deleted_flag = 0)
          AND occ.purchase_contract_id = #{contractId,jdbcType=BIGINT}
    </select>

    <update id="removeCarryoverBill">
        update carryover_bill
        set deleted_flag = 1
        where carryover_batch_num = #{carryoverBatchNum, jdbcType=VARCHAR}
        and resource_type = #{type}
        and project_milepost_id = #{projectMilepostId}
        and deleted_flag = 0
        and status in (0,3)
    </update>

    <update id="batchUpdateCarryoverBillCostCollectionRel">
        update carryover_bill_cost_collection_rel
        set carryover_bill_id = #{id}
        where carryover_batch_num = #{carryoverBatchNum, jdbcType=VARCHAR}
          and cost_collection_project_id = #{projectId}
          and carryover_bill_id is null
          and deleted_flag = 0
          and (type is null or type = 0)
    </update>
    <update id="updateCarryBillStatus">
        update carryover_bill
        set status = 2
        where id = #{carryoverBillId}
          and (status is null
           or status in (0, 3));
    </update>

    <select id="getNoReverseCarryoverBillOfMilepost" resultMap="BaseResultMap">
        select cb.*
        from carryover_bill cb, project_milepost pm
        where cb.id = pm.carryover_bill_id
        and cb.status = 1 and pm.carry_status = 1 and pm.carryover_bill_id is not null
        and cb.deleted_flag = 0 and pm.deleted_flag = 0
        and cb.reverse_status = 1
        and pm.project_id = #{projectId}
        and pm.order_num > (select m.order_num from project_milepost m where m.id = #{projectMilepostId})
        and pm.help_flag = (select m.help_flag from project_milepost m where m.id = #{projectMilepostId})
    </select>

    <select id="getNoReverseCarryoverBillOfCostPlan" resultMap="BaseResultMap">
        select cb.* from carryover_bill cb, project_income_cost_plan picp
        where cb.id = picp.carryover_bill_id
        and cb.status = 1 and picp.carry_status = 1 and picp.carryover_bill_id is not null
        and cb.deleted_flag = 0 and picp.deleted_flag = 0
        and cb.reverse_status = 1
        and picp.project_id = #{projectId}
        and picp.order_num > (select m.order_num from pam_ctc.project_income_cost_plan m where m.id = #{projectMilepostId})
    </select>

    <select id="getIncomeDetailOfWorkingHour" resultType="com.midea.pam.common.ctc.entity.CarryoverBillWorkingHourRel">
      select
        distinct wh.id as workingHourId, ccwh.id as rdmWorkingHourId, wh.project_id as projectId
      from pam_ctc.cost_collection_working_hour ccwh, pam_ctc.working_hour wh
      where ccwh.mipname = wh.user_mip and ccwh.att_date = wh.apply_date
      and wh.rdm_flag = 1 and wh.status = 4 and wh.project_id = #{projectId} and ccwh.working_hour_id = wh.id
        and ccwh.id not in (
          select r.rdm_working_hour_id from pam_ctc.carryover_bill_working_hour_rel r
          where r.project_id = #{projectId} and r.carry_status = 1 and r.deleted_flag = 0
      )
      and (wh.delete_flag = 0 or (wh.delete_flag = 1 and wh.write_off_status = 1))
      and ccwh.deleted_flag = 0
      and report_date <![CDATA[<=]]>  #{endDate}
    </select>

    <select id="getTotalIncomeOfWorkingHour" resultType="decimal">
      select
        round(ifnull(sum(ccwh.confirm_hour * day_price), 0) / 8, 2)
      from pam_ctc.cost_collection_working_hour ccwh, pam_ctc.working_hour wh
      where ccwh.mipname = wh.user_mip and ccwh.att_date = wh.apply_date
      and wh.rdm_flag = 1 and wh.status = 4 and wh.project_id = #{projectId} and ccwh.working_hour_id = wh.id
      and ccwh.id not in (
          select r.rdm_working_hour_id from pam_ctc.carryover_bill_working_hour_rel r
          where r.project_id = #{projectId} and r.carry_status = 1 and r.deleted_flag = 0
      )
      and (wh.delete_flag = 0 or (wh.delete_flag = 1 and wh.write_off_status = 1))
      and ccwh.deleted_flag = 0
      and report_date <![CDATA[<=]]> #{endDate}
    </select>

    <select id="calculateSumOfCostRatioFromCarryoverd" resultType="decimal">
        SELECT
            ifnull(sum(ifnull(costRatioConfigDetail.cost_ratio, 0)), 0) as costRatio
        FROM
            cost_ratio_config_detail costRatioConfigDetail
        INNER JOIN cost_ratio_config costRatioConfig ON costRatioConfigDetail.cost_ratio_config_id = costRatioConfig.id
        INNER JOIN material_outsourcing_contract_config contractConfig ON costRatioConfig.material_outsourcing_contract_config_id = contractConfig.id
        INNER join project p on costRatioConfigDetail.project_type_ids like concat('%', p.`type` , '%')
        INNER join project_milepost pm on pm.project_id = p.id and pm.carryover_bill_id is not null and pm.deleted_flag = 0
        WHERE
            costRatioConfigDetail.deleted_flag = 0
        AND costRatioConfig.deleted_flag = 0
        AND contractConfig.deleted_flag = 0
        AND costRatioConfigDetail.milepost_template_stage_name = pm.name
        AND p.id = #{projectId}
        AND contractConfig.outsourcing_contract_type_name = #{outsourcingContractTypeName}
        AND costRatioConfigDetail.unit_id = #{unitId,jdbcType=BIGINT}

    </select>
    <select id="getMaxSeq" resultType="java.lang.Integer">
        select ifnull(max(seq),0) as seq
        from carryover_bill where project_id = #{projectId} and status = 1 and deleted_flag = 0 and reverse_status = 1
    </select>

    <select id="getCarryoverBillByProjectIdAndLimitSeq" resultType="com.midea.pam.common.ctc.entity.CarryoverBill">
        select ifnull(b.accumulated_carryover_cost, 0) as accumulatedCarryoverCost,
               ifnull(b.should_carryover_cost, 0) as shouldCarryoverCost
        from carryover_bill b
        where b.project_id = #{projectId}
          and ((b.cost_method = '0412' and b.income_point = '0002') or b.cost_method = '0413')
          and status = 1
          and deleted_flag = 0
          and reverse_status = 1
        order by b.seq desc limit 1
    </select>

    <select id="getCarryoverBillTotalIncome" resultType="com.midea.pam.common.ctc.entity.CarryoverBill">
        select ifnull(sum(b.current_income_amount), 0) as currentIncomeTotalAmount,
               ifnull(sum(b.current_income_amount * ifnull(b.conversion_rate, 1)), 0) as standardCurrentIncomeTotalAmount,
               ifnull(sum(b.current_income_percent), 0) as cumulativeIncomePercent
        from carryover_bill b
        where b.project_id = #{projectId}
          and ((b.cost_method = '0412' and b.income_point = '0002') or b.cost_method = '0413')
          and b.status = 1
          and b.deleted_flag = 0
          and b.reverse_status = 1
    </select>

    <select id="selectCarryoverBill" resultType="com.midea.pam.common.ctc.entity.ProjectInvoiceAmount">
        select
            p.id as projectId,
            p.name as projectName,
            p.code as projectNum,
            ifnull(ccb.projectAmount, 0) as projectAmount,
            ifnull(invoice.amount1, 0) as invoiceAmountOriginal,
            ifnull(invoice.amount2, 0) as projectAmountStandard,
            sum(ifnull(ccb.projectAmount, 0)- ifnull(invoice.amount2, 0)) as differenceAmount
        from
            pam_ctc.project p
        inner join pam_ctc.project_contract_rs pcr on
            p.id = pcr.project_id
            and pcr.deleted_flag = 0
        left join (
            select
                cb.project_id as projectId,
                ifnull(sum(cb.current_income_total_amount), 0) as projectAmount
            from
                pam_ctc.carryover_bill cb
            where
                cb.deleted_flag = 0
                and cb.erp_sync_status = 1
                and cb.status = 1
                and (cb.create_at is null
                    or (cb.create_at is not null
                    and cb.create_at <![CDATA[<=]]> #{createAt}))
                and (cb.sync_erp_time is null
                    or (cb.sync_erp_time is not null
                    and cb.sync_erp_time <![CDATA[<=]]> #{createAt}))
            group by
                project_id ) ccb on
            p.id = ccb.projectId
        left join (
            select
                sum(ir.exclusive_of_tax) as amount1,
                SUM(ir.exclusive_of_tax * ifnull(ir.conversion_rate, 1))as amount2,
                ir.contract_id as contractId
            from
                pam_ctc.invoice_receivable ir
            where
                ir.erp_status = 1
                and ir.deleted_flag = 0
                and (ir.create_at is null
                    or (ir.create_at is not null
                    and ir.create_at <![CDATA[<=]]> #{createAt}))
                and (ir.sync_erp_time is null
                    or (ir.sync_erp_time is not null
                    and ir.sync_erp_time <![CDATA[<=]]> #{createAt}))
                <if test="invoiceTypeList != null">
                    and ir.invoice_type in
                    <foreach collection="invoiceTypeList" item="invoiceType" index="index" open="(" separator="," close=")">
                        #{invoiceType}
                    </foreach>
                </if>
            group by
                ir.contract_id )invoice on
            pcr.contract_id = invoice.contractId
        where
            p.deleted_flag = 0
            and P.status != 0
            and P.preview_flag = 0
            and p.ou_id = #{ouId}
        group by
            p.id
    </select>

    <select id="sumCarryoverBill" resultType="java.math.BigDecimal">
        select
            ifnull(sum(t.differenceAmount), 0)
        from
            (
            select
                p.id as projectId,
                ifnull(ccb.projectAmount, 0)- ifnull(invoice.amount2, 0) as differenceAmount
            from
                pam_ctc.project p
            inner join pam_ctc.project_contract_rs pcr on
                p.id = pcr.project_id
                and pcr.deleted_flag = 0
            left join (
                select
                    cb.project_id as projectId,
                    ifnull(sum(cb.current_income_total_amount), 0) as projectAmount
                from
                    pam_ctc.carryover_bill cb
                where
                    cb.deleted_flag = 0
                    and cb.erp_sync_status = 1
                    and cb.status = 1
                group by
                    project_id) ccb on
                p.id = ccb.projectId
            left join (
                select
                    ifnull(sum(ir.exclusive_of_tax), 0) as amount1,
                    ifnull(sum(ir.exclusive_of_tax * ifnull(ir.conversion_rate, 1)), 0) as amount2,
                    ir.contract_id as contractId
                from
                    pam_ctc.invoice_receivable ir
                where
                    ir.erp_status = 1
                    and ir.deleted_flag = 0
                    <if test="invoiceTypeList != null">
                        and ir.invoice_type in
                        <foreach collection="invoiceTypeList" item="invoiceType" index="index" open="(" separator="," close=")">
                            #{invoiceType}
                        </foreach>
                    </if>
                group by
                    ir.contract_id ) invoice on
                pcr.contract_id = invoice.contractId
            where
                p.deleted_flag = 0
                and p.status != 0
                and p.preview_flag = 0
                and p.ou_id = #{ouId}
            group by
                p.id ) t
        where
            t.differenceAmount > 0
    </select>

    <select id="countCarryBillByProjectId" resultType="java.lang.Integer">
        select count(id)
            from carryover_bill
        where status in (1,2)
          and project_id = #{projectId}
          and project_milepost_id = #{projectMilepostId}
          and (deleted_flag = 0 or deleted_flag is null)
          and (reverse_status = 1 or reverse_status is null)
    </select>

    <select id="getCumulativeCostCarryoverBill" resultType="java.math.BigDecimal">
        select ifnull(sum(current_cost_actual), 0)
        from carryover_bill
        where status in (1,2)
          and project_id = #{projectId}
          and (deleted_flag = 0 or deleted_flag is null)
        <if test="carryoverBillId != null">
            and id <![CDATA[ <> ]]> #{carryoverBillId}
        </if>
    </select>

    <select id="getCumulativeIncomeCarryoverBill" resultType="java.math.BigDecimal">
        select ifnull(sum(current_income_total_amount), 0)
        from carryover_bill
        where status in (1,2)
          and project_id = #{projectId}
          and (deleted_flag = 0 or deleted_flag is null)
        <if test="carryoverBillId != null">
            and id <![CDATA[ <> ]]> #{carryoverBillId}
        </if>
    </select>

    <select id="getStandardCumulativeIncomeCarryoverBill" resultType="java.math.BigDecimal">
        select ifnull(sum(current_income_total_amount * ifnull(conversion_rate, 1)), 0)
        from carryover_bill
        where status in (1,2)
        and project_id = #{projectId}
        and (deleted_flag = 0 or deleted_flag is null)
        <if test="carryoverBillId != null">
            and id <![CDATA[ <> ]]> #{carryoverBillId}
        </if>
    </select>

    <select id="calculateForCcft" resultType="java.math.BigDecimal">
        select
            ifnull(sum(t.differenceAmount), 0)
        from
            (
            select
                p.id as projectId,
                ifnull(ccb.projectAmount, 0)- ifnull(invoice.amount2, 0) as differenceAmount
            from
                pam_ctc.project p
            left join (
                select
                    cb.project_id as projectId,
                    ifnull(sum(cb.current_income_total_amount), 0) as projectAmount
                from
                    pam_ctc.classification_carryover_bill cb
                where
                    cb.deleted_flag = 0
                    and cb.head_id = #{contractClassificationHeadId}
                group by
                    cb.project_id) ccb on
                p.id = ccb.projectId
            left join (
                select
                    ifnull(sum(ir.exclusive_of_tax), 0) as amount1,
                    ifnull(sum(ir.exclusive_of_tax * ifnull(ir.conversion_rate, 1)), 0) as amount2,
                    ir.project_id as project_id
                from
                    pam_ctc.classification_invoice_receivable ir
                where
                    ir.deleted_flag = 0
                    and ir.head_id = #{contractClassificationHeadId}
                group by
                    ir.project_id ) invoice on
                p.id = invoice.project_id
            where
                p.deleted_flag = 0
                and p.status != 0
                and p.preview_flag = 0
                and p.ou_id = #{ouId}
            group by
                p.id ) t
        where
            t.differenceAmount > 0
    </select>

    <select id="calculateForLyft" resultType="java.math.BigDecimal">
        select
            ifnull(sum(t.cost), 0)
        from (
            select
                project_id,
                ifnull(sum(cost), 0) as cost
            from
                classification_revenue_cost_order
            where deleted_flag = 0
                and head_id = #{contractClassificationHeadId}
            group by project_id
        having ifnull(sum(cost), 0) > 0 ) t
    </select>

    <select id="queryProjectInvoiceDetail" resultType="com.midea.pam.common.ctc.dto.ProjectInvoiceAmountDTO">
        select
            p.id as projectId,
            p.name as projectName,
            p.code as projectNum,
            ifnull(ccb.projectAmount, 0) as projectAmount,
            ifnull(invoice.amount1, 0) as invoiceAmountOriginal,
            ifnull(invoice.amount2, 0) as projectAmountStandard,
            sum(ifnull(ccb.projectAmount, 0)- ifnull(invoice.amount2, 0)) as differenceAmount
        from
            pam_ctc.project p
        inner join (
            select
                cb.project_id as projectId,
                ifnull(sum(cb.current_income_total_amount), 0) as projectAmount
            from
                pam_ctc.classification_carryover_bill cb
            where
                cb.deleted_flag = 0
                and cb.head_id = #{contractClassificationHeadId}
            group by
                project_id ) ccb on
            p.id = ccb.projectId
        left join (
            select
                sum(ir.exclusive_of_tax) as amount1,
                SUM(ir.exclusive_of_tax * ifnull(ir.conversion_rate, 1)) as amount2,
                ir.project_id as project_id
            from
                pam_ctc.classification_invoice_receivable ir
            where
                ir.deleted_flag = 0
                and ir.head_id = #{contractClassificationHeadId}
            group by
                ir.project_id ) invoice on
            p.id = invoice.project_id
        where
            p.deleted_flag = 0
            and P.status != 0
            and P.preview_flag = 0
            and p.ou_id = #{ouId}
        group by
            p.id
    </select>

    <select id="queryRevenueCostOrderDetail"
            resultType="com.midea.pam.common.ctc.entity.ClassificationRevenueCostOrder">
        select
                #{contractClassificationHeadId} as headId,
                rco.id revenueCostOrderId,
                rco.order_code orderCode,
                rco.project_id projectId,
                p.code projectCode,
                p.name projectName,
                ifnull(rco.cost, 0) cost,
                0 deletedFlag
            from
                revenue_cost_order rco
            inner join pam_ctc.project p on
                p.id = rco.project_id
            where rco.deleted_flag = 0
                and rco.type = '自动成本补差'
                <if test="projectIds != null">
                    AND rco.project_id IN
                    <foreach collection="projectIds" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
    </select>

    <select id="queryCarryoverBillDetail"
            resultType="com.midea.pam.common.ctc.entity.ClassificationCarryoverBill">
        select
            #{contractClassificationHeadId} as headId,
            cb.id as carryoverBillId,
            p.id as projectId,
            p.code as projectCode,
            p.name as projectName,
            ifnull(cb.current_income_total_amount, 0) as currentIncomeTotalAmount,
            cb.sync_erp_time as syncErpTime,
            0 as deletedFlag
        from
            pam_ctc.project p
                left join pam_ctc.carryover_bill cb on
                        p.id = cb.project_id
                    and cb.deleted_flag = 0
                    and cb.erp_sync_status = 1
                    and cb.status = 1
        where
            p.deleted_flag = 0
          and p.status != 0
          and p.preview_flag = 0
          and p.ou_id = #{ouId}
    </select>
    <select id="queryInvoiceReceivableDetail"
            resultType="com.midea.pam.common.ctc.entity.ClassificationInvoiceReceivable">
        select
            #{contractClassificationHeadId} headId,
            ir.id invoiceReceivableId,
            c.id contractId,
            c.code contractCode,
            p.id projectId,
            p.code projectCode,
            p.name projectName,
            ir.invoice_type invoiceType,
            ifnull(ir.exclusive_of_tax, 0)  exclusiveOfTax,
            ifnull(ir.conversion_rate, 1) conversionRate,
            ir.sync_erp_time syncErpTime,
            0 deletedFlag
        from
            pam_ctc.project p
        left join pam_ctc.project_contract_rs pcr on
            p.id = pcr.project_id
            and pcr.deleted_flag = 0
        left join pam_ctc.contract c on
            c.id = pcr.contract_id
        left join pam_ctc.invoice_receivable ir on
            pcr.contract_id = ir.contract_id
            and ir.erp_status = 1
            and ir.deleted_flag = 0
            <if test="invoiceTypeList != null">
                and ir.invoice_type in
                <foreach collection="invoiceTypeList" item="invoiceType" index="index" open="(" separator="," close=")">
                    #{invoiceType}
                </foreach>
            </if>
        where
            p.deleted_flag = 0
            and p.status != 0
            and p.preview_flag = 0
            and p.ou_id = #{ouId}
    </select>

    <select id="queryDetailInfoForLyft"
            resultType="com.midea.pam.common.ctc.excelVo.RevenueCostOrderSumExcelVo">
        select
            p.code as projectNum,
            p.name as projectName,
            '计划收入百分比' as costMethod,
            'CNY' as currency,
            rco.cost as totalMainIncome
        from
            (
            select
                project_id,
                ifnull(sum(cost), 0) as cost
            from
                classification_revenue_cost_order
            where
                deleted_flag = 0
                and head_id = #{contractClassificationHeadId}
            group by
                project_id) rco
        inner join project p on
            rco.project_id = p.id
        inner join project_type pt on
            pt.id = p.`type`
        where
            p.ou_id = #{ouId}
            and pt.cost_method = '0413'
    </select>

    <select id="queryCustomerIncomeDetail"
            resultType="com.midea.pam.common.ctc.dto.CarryoverBillIncomeCollectionDetailDTO">
        select cb.carryover_batch_num as carryoverBatchNum,
               cbic.id                as incomeCollectionId,
               cb.id                  as carryoverBillId,
               cb.bill_num            as carryoverBillNum,
               cb.project_id          as projectId,
               cb.project_num         as projectNum,
               c.customer_id          as customerId,
               ifnull(sum(c.excluding_tax_amount), 0) as totalIncome,
               (select
                    ifnull(sum(cbicd.current_income_amount), 0)
                from
                    pam_ctc.carryover_bill_income_collection_detail cbicd
                inner join pam_ctc.carryover_bill bill
                    on cbicd.carryover_bill_id = bill.id
                where
                    cbicd.customer_id = c.customer_id
                    and cbicd.project_id = cb.project_id
                    and bill.status in (1,2)
                    and bill.deleted_flag = 0
                    and cbicd.deleted_flag = 0) as cumulativeIncomeTotal
        from pam_ctc.carryover_bill cb
                 inner join pam_ctc.project_contract_rs pcr on
            pcr.project_id = cb.project_id
                 inner join pam_ctc.contract c on
            c.id = pcr.contract_id
                 inner join pam_ctc.carryover_bill_income_collection cbic on
                    cbic.carryover_bill_id = cb.id
                and cbic.deleted_flag = 0
            where cb.id = #{carryoverBillId}
            group by c.customer_id
    </select>

    <select id="queryCustomerIncomeDetailByProjectId"
            resultType="com.midea.pam.common.ctc.dto.CarryoverBillIncomeCollectionDetailDTO">
        select cb.carryover_batch_num as carryoverBatchNum,
               cbic.id                as incomeCollectionId,
               cb.id                  as carryoverBillId,
               cb.bill_num            as carryoverBillNum,
               cb.project_id          as projectId,
               cb.project_num         as projectNum,
               cb.customer_id          as customerId,
               ifnull(p.amount, 0) as totalIncome,
               (select
                    ifnull(sum(cbicd.current_income_amount), 0)
                from
                    pam_ctc.carryover_bill_income_collection_detail cbicd
                        inner join pam_ctc.carryover_bill bill
                                   on cbicd.carryover_bill_id = bill.id
                where
                    cbicd.customer_id = p.customer_id
                  and cbicd.project_id = cb.project_id
                  and bill.status in (1,2)
                  and bill.deleted_flag = 0
                  and cbicd.deleted_flag = 0) as cumulativeIncomeTotal
        from pam_ctc.carryover_bill cb
                 inner join pam_ctc.project p on p.id = cb.project_id
                 inner join pam_ctc.carryover_bill_income_collection cbic on
                    cbic.carryover_bill_id = cb.id
                and cbic.deleted_flag = 0
        where cb.id = #{carryoverBillId}
    </select>

</mapper>