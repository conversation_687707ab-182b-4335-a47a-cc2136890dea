<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.ProjectMilepostMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.ProjectMilepost">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="template_id" jdbcType="BIGINT" property="templateId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="order_num" jdbcType="TINYINT" property="orderNum" />
    <result column="ratio" jdbcType="DECIMAL" property="ratio" />
    <result column="base_start_time" jdbcType="TIMESTAMP" property="baseStartTime" />
    <result column="base_end_time" jdbcType="TIMESTAMP" property="baseEndTime" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="milepost_template_stage_id" jdbcType="BIGINT" property="milepostTemplateStageId" />
    <result column="annex_type" jdbcType="VARCHAR" property="annexType" />
    <result column="annex" jdbcType="VARCHAR" property="annex" />
    <result column="project_type" jdbcType="BIGINT" property="projectType" />
    <result column="fixed" jdbcType="TINYINT" property="fixed" />
    <result column="notice" jdbcType="VARCHAR" property="notice" />
    <result column="responsible" jdbcType="BIGINT" property="responsible" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
    <result column="help_flag" jdbcType="TINYINT" property="helpFlag" />
    <result column="deliver_time" jdbcType="TIMESTAMP" property="deliverTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="income_flag" jdbcType="TINYINT" property="incomeFlag" />
    <result column="income_ratio" jdbcType="DECIMAL" property="incomeRatio" />
    <result column="income" jdbcType="DECIMAL" property="income" />
    <result column="cost_ratio" jdbcType="DECIMAL" property="costRatio" />
    <result column="cost" jdbcType="DECIMAL" property="cost" />
    <result column="actual_start_time" jdbcType="TIMESTAMP" property="actualStartTime" />
    <result column="actual_end_time" jdbcType="TIMESTAMP" property="actualEndTime" />
    <result column="server_end_time" jdbcType="TIMESTAMP" property="serverEndTime" />
    <result column="temp_actual_start_time" jdbcType="TIMESTAMP" property="tempActualStartTime" />
    <result column="temp_actual_end_time" jdbcType="TIMESTAMP" property="tempActualEndTime" />
    <result column="temp_server_end_time" jdbcType="TIMESTAMP" property="tempServerEndTime" />
    <result column="carryover_batch_num" jdbcType="VARCHAR" property="carryoverBatchNum" />
    <result column="carryover_bill_id" jdbcType="BIGINT" property="carryoverBillId" />
    <result column="carry_status" jdbcType="TINYINT" property="carryStatus" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="pre_deleted_flag" jdbcType="TINYINT" property="preDeletedFlag" />
    <result column="commited_time" jdbcType="TIMESTAMP" property="commitedTime" />
    <result column="approved_time" jdbcType="TIMESTAMP" property="approvedTime" />
    <result column="transfer_project" jdbcType="TINYINT" property="transferProject" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="parallel_delivery_line_flag" jdbcType="TINYINT" property="parallelDeliveryLineFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, template_id, name, order_num, ratio, base_start_time, base_end_time, 
    start_time, end_time, milepost_template_stage_id, annex_type, annex, project_type, 
    fixed, notice, responsible, deleted_flag, help_flag, deliver_time, status, income_flag, 
    income_ratio, income, cost_ratio, cost, actual_start_time, actual_end_time, server_end_time, 
    temp_actual_start_time, temp_actual_end_time, temp_server_end_time, carryover_batch_num, 
    carryover_bill_id, carry_status, note, create_by, create_at, update_by, update_at, 
    pre_deleted_flag, commited_time, approved_time, transfer_project, parent_id, group_id, 
    parallel_delivery_line_flag
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.ctc.entity.ProjectMilepostExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_milepost
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_milepost
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_milepost
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.ctc.entity.ProjectMilepost">
    insert into project_milepost (id, project_id, template_id, 
      name, order_num, ratio, 
      base_start_time, base_end_time, start_time, 
      end_time, milepost_template_stage_id, annex_type, 
      annex, project_type, fixed, 
      notice, responsible, deleted_flag, 
      help_flag, deliver_time, status, 
      income_flag, income_ratio, income, 
      cost_ratio, cost, actual_start_time, 
      actual_end_time, server_end_time, temp_actual_start_time, 
      temp_actual_end_time, temp_server_end_time, 
      carryover_batch_num, carryover_bill_id, carry_status, 
      note, create_by, create_at, 
      update_by, update_at, pre_deleted_flag, 
      commited_time, approved_time, transfer_project, 
      parent_id, group_id, parallel_delivery_line_flag
      )
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{templateId,jdbcType=BIGINT}, 
      #{name,jdbcType=VARCHAR}, #{orderNum,jdbcType=TINYINT}, #{ratio,jdbcType=DECIMAL}, 
      #{baseStartTime,jdbcType=TIMESTAMP}, #{baseEndTime,jdbcType=TIMESTAMP}, #{startTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP}, #{milepostTemplateStageId,jdbcType=BIGINT}, #{annexType,jdbcType=VARCHAR}, 
      #{annex,jdbcType=VARCHAR}, #{projectType,jdbcType=BIGINT}, #{fixed,jdbcType=TINYINT}, 
      #{notice,jdbcType=VARCHAR}, #{responsible,jdbcType=BIGINT}, #{deletedFlag,jdbcType=TINYINT}, 
      #{helpFlag,jdbcType=TINYINT}, #{deliverTime,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}, 
      #{incomeFlag,jdbcType=TINYINT}, #{incomeRatio,jdbcType=DECIMAL}, #{income,jdbcType=DECIMAL}, 
      #{costRatio,jdbcType=DECIMAL}, #{cost,jdbcType=DECIMAL}, #{actualStartTime,jdbcType=TIMESTAMP}, 
      #{actualEndTime,jdbcType=TIMESTAMP}, #{serverEndTime,jdbcType=TIMESTAMP}, #{tempActualStartTime,jdbcType=TIMESTAMP}, 
      #{tempActualEndTime,jdbcType=TIMESTAMP}, #{tempServerEndTime,jdbcType=TIMESTAMP}, 
      #{carryoverBatchNum,jdbcType=VARCHAR}, #{carryoverBillId,jdbcType=BIGINT}, #{carryStatus,jdbcType=TINYINT}, 
      #{note,jdbcType=VARCHAR}, #{createBy,jdbcType=BIGINT}, #{createAt,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=BIGINT}, #{updateAt,jdbcType=TIMESTAMP}, #{preDeletedFlag,jdbcType=TINYINT}, 
      #{commitedTime,jdbcType=TIMESTAMP}, #{approvedTime,jdbcType=TIMESTAMP}, #{transferProject,jdbcType=TINYINT}, 
      #{parentId,jdbcType=BIGINT}, #{groupId,jdbcType=BIGINT}, #{parallelDeliveryLineFlag,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.ctc.entity.ProjectMilepost">
    insert into project_milepost
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="templateId != null">
        template_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="orderNum != null">
        order_num,
      </if>
      <if test="ratio != null">
        ratio,
      </if>
      <if test="baseStartTime != null">
        base_start_time,
      </if>
      <if test="baseEndTime != null">
        base_end_time,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="milepostTemplateStageId != null">
        milepost_template_stage_id,
      </if>
      <if test="annexType != null">
        annex_type,
      </if>
      <if test="annex != null">
        annex,
      </if>
      <if test="projectType != null">
        project_type,
      </if>
      <if test="fixed != null">
        fixed,
      </if>
      <if test="notice != null">
        notice,
      </if>
      <if test="responsible != null">
        responsible,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
      <if test="helpFlag != null">
        help_flag,
      </if>
      <if test="deliverTime != null">
        deliver_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="incomeFlag != null">
        income_flag,
      </if>
      <if test="incomeRatio != null">
        income_ratio,
      </if>
      <if test="income != null">
        income,
      </if>
      <if test="costRatio != null">
        cost_ratio,
      </if>
      <if test="cost != null">
        cost,
      </if>
      <if test="actualStartTime != null">
        actual_start_time,
      </if>
      <if test="actualEndTime != null">
        actual_end_time,
      </if>
      <if test="serverEndTime != null">
        server_end_time,
      </if>
      <if test="tempActualStartTime != null">
        temp_actual_start_time,
      </if>
      <if test="tempActualEndTime != null">
        temp_actual_end_time,
      </if>
      <if test="tempServerEndTime != null">
        temp_server_end_time,
      </if>
      <if test="carryoverBatchNum != null">
        carryover_batch_num,
      </if>
      <if test="carryoverBillId != null">
        carryover_bill_id,
      </if>
      <if test="carryStatus != null">
        carry_status,
      </if>
      <if test="note != null">
        note,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="preDeletedFlag != null">
        pre_deleted_flag,
      </if>
      <if test="commitedTime != null">
        commited_time,
      </if>
      <if test="approvedTime != null">
        approved_time,
      </if>
      <if test="transferProject != null">
        transfer_project,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="parallelDeliveryLineFlag != null">
        parallel_delivery_line_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="templateId != null">
        #{templateId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="orderNum != null">
        #{orderNum,jdbcType=TINYINT},
      </if>
      <if test="ratio != null">
        #{ratio,jdbcType=DECIMAL},
      </if>
      <if test="baseStartTime != null">
        #{baseStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="baseEndTime != null">
        #{baseEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="milepostTemplateStageId != null">
        #{milepostTemplateStageId,jdbcType=BIGINT},
      </if>
      <if test="annexType != null">
        #{annexType,jdbcType=VARCHAR},
      </if>
      <if test="annex != null">
        #{annex,jdbcType=VARCHAR},
      </if>
      <if test="projectType != null">
        #{projectType,jdbcType=BIGINT},
      </if>
      <if test="fixed != null">
        #{fixed,jdbcType=TINYINT},
      </if>
      <if test="notice != null">
        #{notice,jdbcType=VARCHAR},
      </if>
      <if test="responsible != null">
        #{responsible,jdbcType=BIGINT},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="helpFlag != null">
        #{helpFlag,jdbcType=TINYINT},
      </if>
      <if test="deliverTime != null">
        #{deliverTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="incomeFlag != null">
        #{incomeFlag,jdbcType=TINYINT},
      </if>
      <if test="incomeRatio != null">
        #{incomeRatio,jdbcType=DECIMAL},
      </if>
      <if test="income != null">
        #{income,jdbcType=DECIMAL},
      </if>
      <if test="costRatio != null">
        #{costRatio,jdbcType=DECIMAL},
      </if>
      <if test="cost != null">
        #{cost,jdbcType=DECIMAL},
      </if>
      <if test="actualStartTime != null">
        #{actualStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualEndTime != null">
        #{actualEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="serverEndTime != null">
        #{serverEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tempActualStartTime != null">
        #{tempActualStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tempActualEndTime != null">
        #{tempActualEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tempServerEndTime != null">
        #{tempServerEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="carryoverBatchNum != null">
        #{carryoverBatchNum,jdbcType=VARCHAR},
      </if>
      <if test="carryoverBillId != null">
        #{carryoverBillId,jdbcType=BIGINT},
      </if>
      <if test="carryStatus != null">
        #{carryStatus,jdbcType=TINYINT},
      </if>
      <if test="note != null">
        #{note,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="preDeletedFlag != null">
        #{preDeletedFlag,jdbcType=TINYINT},
      </if>
      <if test="commitedTime != null">
        #{commitedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="approvedTime != null">
        #{approvedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="transferProject != null">
        #{transferProject,jdbcType=TINYINT},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=BIGINT},
      </if>
      <if test="parallelDeliveryLineFlag != null">
        #{parallelDeliveryLineFlag,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.ctc.entity.ProjectMilepostExample" resultType="java.lang.Long">
    select count(*) from project_milepost
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.ctc.entity.ProjectMilepost">
    update project_milepost
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="orderNum != null">
        order_num = #{orderNum,jdbcType=TINYINT},
      </if>
      <if test="ratio != null">
        ratio = #{ratio,jdbcType=DECIMAL},
      </if>
      <if test="baseStartTime != null">
        base_start_time = #{baseStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="baseEndTime != null">
        base_end_time = #{baseEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="milepostTemplateStageId != null">
        milepost_template_stage_id = #{milepostTemplateStageId,jdbcType=BIGINT},
      </if>
      <if test="annexType != null">
        annex_type = #{annexType,jdbcType=VARCHAR},
      </if>
      <if test="annex != null">
        annex = #{annex,jdbcType=VARCHAR},
      </if>
      <if test="projectType != null">
        project_type = #{projectType,jdbcType=BIGINT},
      </if>
      <if test="fixed != null">
        fixed = #{fixed,jdbcType=TINYINT},
      </if>
      <if test="notice != null">
        notice = #{notice,jdbcType=VARCHAR},
      </if>
      <if test="responsible != null">
        responsible = #{responsible,jdbcType=BIGINT},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="helpFlag != null">
        help_flag = #{helpFlag,jdbcType=TINYINT},
      </if>
      <if test="deliverTime != null">
        deliver_time = #{deliverTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="incomeFlag != null">
        income_flag = #{incomeFlag,jdbcType=TINYINT},
      </if>
      <if test="incomeRatio != null">
        income_ratio = #{incomeRatio,jdbcType=DECIMAL},
      </if>
      <if test="income != null">
        income = #{income,jdbcType=DECIMAL},
      </if>
      <if test="costRatio != null">
        cost_ratio = #{costRatio,jdbcType=DECIMAL},
      </if>
      <if test="cost != null">
        cost = #{cost,jdbcType=DECIMAL},
      </if>
      <if test="actualStartTime != null">
        actual_start_time = #{actualStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualEndTime != null">
        actual_end_time = #{actualEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="serverEndTime != null">
        server_end_time = #{serverEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tempActualStartTime != null">
        temp_actual_start_time = #{tempActualStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tempActualEndTime != null">
        temp_actual_end_time = #{tempActualEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tempServerEndTime != null">
        temp_server_end_time = #{tempServerEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="carryoverBatchNum != null">
        carryover_batch_num = #{carryoverBatchNum,jdbcType=VARCHAR},
      </if>
      <if test="carryoverBillId != null">
        carryover_bill_id = #{carryoverBillId,jdbcType=BIGINT},
      </if>
      <if test="carryStatus != null">
        carry_status = #{carryStatus,jdbcType=TINYINT},
      </if>
      <if test="note != null">
        note = #{note,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="preDeletedFlag != null">
        pre_deleted_flag = #{preDeletedFlag,jdbcType=TINYINT},
      </if>
      <if test="commitedTime != null">
        commited_time = #{commitedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="approvedTime != null">
        approved_time = #{approvedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="transferProject != null">
        transfer_project = #{transferProject,jdbcType=TINYINT},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=BIGINT},
      </if>
      <if test="parallelDeliveryLineFlag != null">
        parallel_delivery_line_flag = #{parallelDeliveryLineFlag,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.ctc.entity.ProjectMilepost">
    update project_milepost
    set project_id = #{projectId,jdbcType=BIGINT},
      template_id = #{templateId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      order_num = #{orderNum,jdbcType=TINYINT},
      ratio = #{ratio,jdbcType=DECIMAL},
      base_start_time = #{baseStartTime,jdbcType=TIMESTAMP},
      base_end_time = #{baseEndTime,jdbcType=TIMESTAMP},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      milepost_template_stage_id = #{milepostTemplateStageId,jdbcType=BIGINT},
      annex_type = #{annexType,jdbcType=VARCHAR},
      annex = #{annex,jdbcType=VARCHAR},
      project_type = #{projectType,jdbcType=BIGINT},
      fixed = #{fixed,jdbcType=TINYINT},
      notice = #{notice,jdbcType=VARCHAR},
      responsible = #{responsible,jdbcType=BIGINT},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      help_flag = #{helpFlag,jdbcType=TINYINT},
      deliver_time = #{deliverTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=TINYINT},
      income_flag = #{incomeFlag,jdbcType=TINYINT},
      income_ratio = #{incomeRatio,jdbcType=DECIMAL},
      income = #{income,jdbcType=DECIMAL},
      cost_ratio = #{costRatio,jdbcType=DECIMAL},
      cost = #{cost,jdbcType=DECIMAL},
      actual_start_time = #{actualStartTime,jdbcType=TIMESTAMP},
      actual_end_time = #{actualEndTime,jdbcType=TIMESTAMP},
      server_end_time = #{serverEndTime,jdbcType=TIMESTAMP},
      temp_actual_start_time = #{tempActualStartTime,jdbcType=TIMESTAMP},
      temp_actual_end_time = #{tempActualEndTime,jdbcType=TIMESTAMP},
      temp_server_end_time = #{tempServerEndTime,jdbcType=TIMESTAMP},
      carryover_batch_num = #{carryoverBatchNum,jdbcType=VARCHAR},
      carryover_bill_id = #{carryoverBillId,jdbcType=BIGINT},
      carry_status = #{carryStatus,jdbcType=TINYINT},
      note = #{note,jdbcType=VARCHAR},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      pre_deleted_flag = #{preDeletedFlag,jdbcType=TINYINT},
      commited_time = #{commitedTime,jdbcType=TIMESTAMP},
      approved_time = #{approvedTime,jdbcType=TIMESTAMP},
      transfer_project = #{transferProject,jdbcType=TINYINT},
      parent_id = #{parentId,jdbcType=BIGINT},
      group_id = #{groupId,jdbcType=BIGINT},
      parallel_delivery_line_flag = #{parallelDeliveryLineFlag,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>