<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.ProjectAwardExtMapper">
    <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.dto.ProjectAwardTargetDetailDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="batch_num" jdbcType="VARCHAR" property="batchNum"/>
        <result column="execute_id" jdbcType="BIGINT" property="executeId"/>
        <result column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="project_name" jdbcType="VARCHAR" property="projectName"/>
        <result column="project_code" jdbcType="VARCHAR" property="projectCode"/>
        <result column="department_name" jdbcType="VARCHAR" property="departmentName"/>
        <result column="department_code" jdbcType="VARCHAR" property="departmentCode"/>
        <result column="unit_id" jdbcType="BIGINT" property="unitId"/>
        <result column="parent_unit_id" jdbcType="BIGINT" property="parentUnitId"/>
        <result column="unit_name" jdbcType="VARCHAR" property="unitName"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="project_type" jdbcType="VARCHAR" property="projectType"/>
        <result column="project_manager_name" jdbcType="VARCHAR" property="projectManagerName"/>
        <result column="project_status" jdbcType="TINYINT" property="projectStatus"/>
        <result column="price_type" jdbcType="VARCHAR" property="priceType"/>
        <result column="project_award_id" jdbcType="BIGINT" property="projectAwardId"/>
        <result column="project_award_name" jdbcType="VARCHAR" property="projectAwardName"/>
        <result column="total_amount" jdbcType="DECIMAL" property="totalAmount"/>
        <result column="total_actual_amount" jdbcType="DECIMAL" property="totalActualAmount"/>
        <result column="milepost_amount" jdbcType="DECIMAL" property="milepostAmount"/>
        <result column="pause_amount" jdbcType="DECIMAL" property="pauseAmount"/>
        <result column="other_amount" jdbcType="DECIMAL" property="otherAmount"/>
        <result column="other_remark" jdbcType="VARCHAR" property="otherRemark"/>
        <result column="award_status" jdbcType="BIT" property="awardStatus"/>
        <result column="pause_flag" jdbcType="BIT" property="pauseFlag"/>
        <result column="pause_reason" jdbcType="VARCHAR" property="pauseReason"/>
        <result column="beyond_budget_amount" jdbcType="DECIMAL" property="beyondBudgetAmount"/>
        <result column="current_award_ratio" jdbcType="DECIMAL" property="currentAwardRatio"/>
        <result column="beyond_budget_ratio" jdbcType="DECIMAL" property="beyondBudgetRatio"/>
        <result column="budget_risk_rank" jdbcType="BIT" property="budgetRiskRank"/>
        <result column="cost_award_ratio" jdbcType="DECIMAL" property="costAwardRatio"/>
        <result column="milepost_delay_day" jdbcType="INTEGER" property="milepostDelayDay"/>
        <result column="milepost_delay_rank" jdbcType="BIT" property="milepostDelayRank"/>
        <result column="progress_award_ratio" jdbcType="DECIMAL" property="progressAwardRatio"/>
        <result column="total_income_amount" jdbcType="DECIMAL" property="totalIncomeAmount"/>
        <result column="total_budget_amount" jdbcType="DECIMAL" property="totalBudgetAmount"/>
        <result column="total_return_amount" jdbcType="DECIMAL" property="totalReturnAmount"/>
        <result column="total_cost_amount" jdbcType="DECIMAL" property="totalCostAmount"/>
        <result column="total_purchase_amount" jdbcType="DECIMAL" property="totalPurchaseAmount"/>
        <result column="award_base_amount" jdbcType="DECIMAL" property="awardBaseAmount"/>
        <result column="award_base_ratio" jdbcType="DECIMAL" property="awardBaseRatio"/>
        <result column="objective_flag" jdbcType="TINYINT" property="objectiveFlag"/>
        <result column="is_import" jdbcType="TINYINT" property="isImport"/>
        <result column="contract_code" jdbcType="VARCHAR" property="contractCode"/>
        <result column="contract_name" jdbcType="VARCHAR" property="contractName"/>
        <result column="main_contract_name" jdbcType="VARCHAR" property="mainContractName"/>
        <result column="contract_included_amount" jdbcType="DECIMAL" property="contractIncludedAmount"/>
        <result column="contract_amount" jdbcType="DECIMAL" property="contractAmount"/>
        <result column="current_actual_amount" jdbcType="DECIMAL" property="currentActualAmount"/>
        <result column="current_amount" jdbcType="DECIMAL" property="currentAmount"/>
        <result column="current_delayed_amount" jdbcType="DECIMAL" property="currentDelayedAmount"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt"/>
        <result column="deleted_flag" jdbcType="BIT" property="deletedFlag"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <update id="updateProjectAwardId">
        update project_award_target_detail patd
            left join project_award_deduction pad
            on patd.project_id = pad.project_id
                   and pad.deleted_flag = 0
                and pad.project_award_id = #{id}
        set patd.project_award_id = #{id},
            patd.project_award_name = #{name}
        where patd.deleted_flag = 0
          and patd.execute_id = (select id
                                 from project_award_target_execute_record
                                 where deleted_flag = 0
                                   and status = 2
                                 order by create_at desc
                                 limit 1)
    </update>

    <select id="getTargetDetailByAward" resultMap="BaseResultMap">
        select patd.*, pad.current_actual_amount, pad.current_amount, pad.current_delayed_amount
        from project_award_target_detail patd
                 left join project_award_deduction pad
                            on patd.project_id = pad.project_id
                                   and patd.project_award_id = pad.project_award_id
                                    and pad.deleted_flag = 0
        where patd.deleted_flag = 0
          and patd.project_award_id = #{projectAwardId}

    </select>

    <select id="getLalatestTargetDetail" resultType="com.midea.pam.common.ctc.dto.ProjectAwardTargetDetailDTO">
        select patd.*, pad.current_actual_amount, pad.current_amount, pad.current_delayed_amount
        from project_award_target_detail patd
                 left join project_award_deduction pad
                           on patd.project_id = pad.project_id
                               and pad.project_award_id = #{projectAwardId}
                               and pad.deleted_flag = 0
        where patd.deleted_flag = 0
          and patd.execute_id = (select id
                                 from project_award_target_execute_record
                                 where deleted_flag = 0 and status = 2
                                 order by create_at desc
                                 limit 1)
        <!-- 项目奖发放状态-->
        <if test="awardStatusList != null and awardStatusList.size > 0">
            and patd.award_status in
            <foreach collection="awardStatusList" item="awardStatus" index="index" open="(" separator="," close=")">
                #{awardStatus}
            </foreach>
        </if>
    </select>

    <!-- 1、累计已确认收入金额 -->
    <select id="getProjectSumConfirmIncome" resultType="java.math.BigDecimal">
        SELECT
        ifnull(sum(cb.current_income_total_amount),0)
        FROM pam_ctc.carryover_bill cb
        LEFT JOIN pam_ctc.project_milepost pm ON pm.id = cb.project_milepost_id
        LEFT JOIN pam_ctc.project_type pt ON pt.id = cb.project_type
        WHERE
        cb.deleted_flag = 0 AND cb.bill_num IS NOT NULL
        AND
        cb.project_id = #{projectId}
        AND
        cb.period_name <![CDATA[<=]]> #{projectAwardEndDate}
    </select>

    <!-- 2 累计已回款金额 -->
    <select id="getProjectSumReceiptClaim" resultType="java.math.BigDecimal">
        <!-- 下面没有初始化回款 -->
        SELECT ifnull(sum(amount),0) AS amount
        FROM (
        SELECT t1.project_id, t1.allocated_amount * t2.rate AS amount
        FROM (
        SELECT tem1.project_id, tem2.contract_id, tem2.allocated_amount
        FROM (
        SELECT pcr.project_id, pcr.contract_id
        FROM pam_ctc.project_contract_rs pcr
        WHERE pcr.deleted_flag = 0
        ) tem1
        LEFT JOIN (
        SELECT rccr.contract_id, rccr.allocated_amount
        FROM pam_ctc.receipt_claim_contract_rel rccr
        LEFT JOIN pam_ctc.receipt_claim_detail rcd
        ON rccr.receipt_claim_detail_id = rcd.id
        AND rccr.deleted_flag = 0
        LEFT JOIN pam_ctc.receipt_claim rc
        ON rcd.receipt_claim_id = rc.id
        AND rc.deleted_flag = 0
        WHERE ifnull(date_format(rc.pay_date, '%Y-%m'), '2000-1') <![CDATA[<=]]> #{projectAwardEndDate}
        ) tem2
        ON tem1.contract_id = tem2.contract_id
        ) t1
        LEFT JOIN (
        SELECT r.id, ifnull(r.excluding_tax_amount / r.amount, 1) AS rate
        FROM (
        SELECT c.id
        , ifnull(ifnull(c.excluding_tax_amount, c.amount), 0) AS excluding_tax_amount
        , c.amount
        FROM pam_ctc.contract c
        WHERE c.deleted_flag = 0
        ) r
        ) t2
        ON t1.contract_id = t2.id
        ) s
        GROUP BY s.project_id
        HAVING s.project_id = #{projectId}
    </select>


    <!-- 3、累计外部采购金额 -->
    <select id="getProjectSumOutPurchase" resultType="java.math.BigDecimal">
        #<!-- 下面的SQL只是“所有系统外部总成本” -->
        SELECT ifnull(sum(tem1.amount),0)
        FROM (
        SELECT cc.project_id AS project_id, fcd.`type` AS `type`, fcd.type_code AS type_code, fcd.local_currency_amount
        AS amount
        FROM pam_ctc.cost_collection cc, pam_ctc.fee_cost_detail fcd
        WHERE cc.id = fcd.cost_collection_id
        AND fcd.deleted_flag = 0
        AND cc.deleted_flag = 0
        AND cc.project_id = #{projectId}
        AND fcd.gl_date <![CDATA[<=]]> #{projectAwardEndDate}
        ) tem1
        WHERE tem1.type_code IN ('JJSX0545', 'JJSX0133', 'JJSX0169')
    </select>


    <!-- 计提比例 -->
    <select id="getProjectOutOrInnerType" resultType="java.lang.String">
        SELECT p.price_type
        FROM pam_ctc.project p
        WHERE p.price_type IN ('1', '2')
	    AND p.id = #{projectId}
    </select>


</mapper>