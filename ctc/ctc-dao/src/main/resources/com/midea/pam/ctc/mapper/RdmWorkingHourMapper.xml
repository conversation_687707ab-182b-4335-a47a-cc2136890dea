<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.RdmWorkingHourMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.RdmWorkingHour">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="mipname" jdbcType="VARCHAR" property="mipname" />
    <result column="att_hour" jdbcType="DECIMAL" property="attHour" />
    <result column="att_date" jdbcType="TIMESTAMP" property="attDate" />
    <result column="report_hour" jdbcType="DECIMAL" property="reportHour" />
    <result column="report_date" jdbcType="TIMESTAMP" property="reportDate" />
    <result column="project_manager" jdbcType="VARCHAR" property="projectManager" />
    <result column="check_hour" jdbcType="DECIMAL" property="checkHour" />
    <result column="check_date" jdbcType="TIMESTAMP" property="checkDate" />
    <result column="confirm_hour" jdbcType="DECIMAL" property="confirmHour" />
    <result column="confirm_date" jdbcType="TIMESTAMP" property="confirmDate" />
    <result column="docker" jdbcType="VARCHAR" property="docker" />
    <result column="day_price" jdbcType="DECIMAL" property="dayPrice" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="supplier" jdbcType="VARCHAR" property="supplier" />
    <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, username, mipname, att_hour, att_date, report_hour, report_date, project_manager, 
    check_hour, check_date, confirm_hour, confirm_date, docker, day_price, unit, supplier, 
    supplier_code, deleted_flag, create_at, create_by, update_at, update_by
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.ctc.entity.RdmWorkingHourExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from rdm_working_hour
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from rdm_working_hour
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from rdm_working_hour
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.ctc.entity.RdmWorkingHour">
    insert into rdm_working_hour (id, username, mipname, 
      att_hour, att_date, report_hour, 
      report_date, project_manager, check_hour, 
      check_date, confirm_hour, confirm_date, 
      docker, day_price, unit, 
      supplier, supplier_code, deleted_flag, 
      create_at, create_by, update_at, 
      update_by)
    values (#{id,jdbcType=BIGINT}, #{username,jdbcType=VARCHAR}, #{mipname,jdbcType=VARCHAR}, 
      #{attHour,jdbcType=DECIMAL}, #{attDate,jdbcType=TIMESTAMP}, #{reportHour,jdbcType=DECIMAL}, 
      #{reportDate,jdbcType=TIMESTAMP}, #{projectManager,jdbcType=VARCHAR}, #{checkHour,jdbcType=DECIMAL}, 
      #{checkDate,jdbcType=TIMESTAMP}, #{confirmHour,jdbcType=DECIMAL}, #{confirmDate,jdbcType=TIMESTAMP}, 
      #{docker,jdbcType=VARCHAR}, #{dayPrice,jdbcType=DECIMAL}, #{unit,jdbcType=VARCHAR}, 
      #{supplier,jdbcType=VARCHAR}, #{supplierCode,jdbcType=VARCHAR}, #{deletedFlag,jdbcType=TINYINT}, 
      #{createAt,jdbcType=TIMESTAMP}, #{createBy,jdbcType=BIGINT}, #{updateAt,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.ctc.entity.RdmWorkingHour">
    insert into rdm_working_hour
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="username != null">
        username,
      </if>
      <if test="mipname != null">
        mipname,
      </if>
      <if test="attHour != null">
        att_hour,
      </if>
      <if test="attDate != null">
        att_date,
      </if>
      <if test="reportHour != null">
        report_hour,
      </if>
      <if test="reportDate != null">
        report_date,
      </if>
      <if test="projectManager != null">
        project_manager,
      </if>
      <if test="checkHour != null">
        check_hour,
      </if>
      <if test="checkDate != null">
        check_date,
      </if>
      <if test="confirmHour != null">
        confirm_hour,
      </if>
      <if test="confirmDate != null">
        confirm_date,
      </if>
      <if test="docker != null">
        docker,
      </if>
      <if test="dayPrice != null">
        day_price,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="supplier != null">
        supplier,
      </if>
      <if test="supplierCode != null">
        supplier_code,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="mipname != null">
        #{mipname,jdbcType=VARCHAR},
      </if>
      <if test="attHour != null">
        #{attHour,jdbcType=DECIMAL},
      </if>
      <if test="attDate != null">
        #{attDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reportHour != null">
        #{reportHour,jdbcType=DECIMAL},
      </if>
      <if test="reportDate != null">
        #{reportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="projectManager != null">
        #{projectManager,jdbcType=VARCHAR},
      </if>
      <if test="checkHour != null">
        #{checkHour,jdbcType=DECIMAL},
      </if>
      <if test="checkDate != null">
        #{checkDate,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmHour != null">
        #{confirmHour,jdbcType=DECIMAL},
      </if>
      <if test="confirmDate != null">
        #{confirmDate,jdbcType=TIMESTAMP},
      </if>
      <if test="docker != null">
        #{docker,jdbcType=VARCHAR},
      </if>
      <if test="dayPrice != null">
        #{dayPrice,jdbcType=DECIMAL},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="supplier != null">
        #{supplier,jdbcType=VARCHAR},
      </if>
      <if test="supplierCode != null">
        #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.ctc.entity.RdmWorkingHourExample" resultType="java.lang.Long">
    select count(*) from rdm_working_hour
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.ctc.entity.RdmWorkingHour">
    update rdm_working_hour
    <set>
      <if test="username != null">
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="mipname != null">
        mipname = #{mipname,jdbcType=VARCHAR},
      </if>
      <if test="attHour != null">
        att_hour = #{attHour,jdbcType=DECIMAL},
      </if>
      <if test="attDate != null">
        att_date = #{attDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reportHour != null">
        report_hour = #{reportHour,jdbcType=DECIMAL},
      </if>
      <if test="reportDate != null">
        report_date = #{reportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="projectManager != null">
        project_manager = #{projectManager,jdbcType=VARCHAR},
      </if>
      <if test="checkHour != null">
        check_hour = #{checkHour,jdbcType=DECIMAL},
      </if>
      <if test="checkDate != null">
        check_date = #{checkDate,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmHour != null">
        confirm_hour = #{confirmHour,jdbcType=DECIMAL},
      </if>
      <if test="confirmDate != null">
        confirm_date = #{confirmDate,jdbcType=TIMESTAMP},
      </if>
      <if test="docker != null">
        docker = #{docker,jdbcType=VARCHAR},
      </if>
      <if test="dayPrice != null">
        day_price = #{dayPrice,jdbcType=DECIMAL},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="supplier != null">
        supplier = #{supplier,jdbcType=VARCHAR},
      </if>
      <if test="supplierCode != null">
        supplier_code = #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.ctc.entity.RdmWorkingHour">
    update rdm_working_hour
    set username = #{username,jdbcType=VARCHAR},
      mipname = #{mipname,jdbcType=VARCHAR},
      att_hour = #{attHour,jdbcType=DECIMAL},
      att_date = #{attDate,jdbcType=TIMESTAMP},
      report_hour = #{reportHour,jdbcType=DECIMAL},
      report_date = #{reportDate,jdbcType=TIMESTAMP},
      project_manager = #{projectManager,jdbcType=VARCHAR},
      check_hour = #{checkHour,jdbcType=DECIMAL},
      check_date = #{checkDate,jdbcType=TIMESTAMP},
      confirm_hour = #{confirmHour,jdbcType=DECIMAL},
      confirm_date = #{confirmDate,jdbcType=TIMESTAMP},
      docker = #{docker,jdbcType=VARCHAR},
      day_price = #{dayPrice,jdbcType=DECIMAL},
      unit = #{unit,jdbcType=VARCHAR},
      supplier = #{supplier,jdbcType=VARCHAR},
      supplier_code = #{supplierCode,jdbcType=VARCHAR},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>