<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.RdmResourcePlanMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.RdmResourcePlan">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="mipname" jdbcType="VARCHAR" property="mipname" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="is_person" jdbcType="TINYINT" property="isPerson" />
    <result column="it_product" jdbcType="VARCHAR" property="itProduct" />
    <result column="field_name" jdbcType="VARCHAR" property="fieldName" />
    <result column="level_name" jdbcType="VARCHAR" property="levelName" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="people_days" jdbcType="INTEGER" property="peopleDays" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
    <result column="project_manager" jdbcType="VARCHAR" property="projectManager" />
    <result column="project_manager_mip" jdbcType="VARCHAR" property="projectManagerMip" />
    <result column="resource_code" jdbcType="VARCHAR" property="resourceCode" />
    <result column="resource_name" jdbcType="VARCHAR" property="resourceName" />
    <result column="project_status" jdbcType="VARCHAR" property="projectStatus" />
    <result column="pam_enabled" jdbcType="BIT" property="pamEnabled" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, username, mipname, status, supplier_code, supplier_name, is_person, it_product, 
    field_name, level_name, start_time, end_time, people_days, project_name, project_code, 
    project_manager, project_manager_mip, resource_code, resource_name, project_status, 
    pam_enabled, deleted_flag, create_at, create_by, update_at, update_by
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.ctc.entity.RdmResourcePlanExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from rdm_resource_plan
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from rdm_resource_plan
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from rdm_resource_plan
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.ctc.entity.RdmResourcePlan">
    insert into rdm_resource_plan (id, username, mipname, 
      status, supplier_code, supplier_name, 
      is_person, it_product, field_name, 
      level_name, start_time, end_time, 
      people_days, project_name, project_code, 
      project_manager, project_manager_mip, resource_code, 
      resource_name, project_status, pam_enabled, 
      deleted_flag, create_at, create_by, 
      update_at, update_by)
    values (#{id,jdbcType=BIGINT}, #{username,jdbcType=VARCHAR}, #{mipname,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{supplierCode,jdbcType=VARCHAR}, #{supplierName,jdbcType=VARCHAR}, 
      #{isPerson,jdbcType=TINYINT}, #{itProduct,jdbcType=VARCHAR}, #{fieldName,jdbcType=VARCHAR}, 
      #{levelName,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, 
      #{peopleDays,jdbcType=INTEGER}, #{projectName,jdbcType=VARCHAR}, #{projectCode,jdbcType=VARCHAR}, 
      #{projectManager,jdbcType=VARCHAR}, #{projectManagerMip,jdbcType=VARCHAR}, #{resourceCode,jdbcType=VARCHAR}, 
      #{resourceName,jdbcType=VARCHAR}, #{projectStatus,jdbcType=VARCHAR}, #{pamEnabled,jdbcType=BIT}, 
      #{deletedFlag,jdbcType=TINYINT}, #{createAt,jdbcType=TIMESTAMP}, #{createBy,jdbcType=BIGINT}, 
      #{updateAt,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.ctc.entity.RdmResourcePlan">
    insert into rdm_resource_plan
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="username != null">
        username,
      </if>
      <if test="mipname != null">
        mipname,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="supplierCode != null">
        supplier_code,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="isPerson != null">
        is_person,
      </if>
      <if test="itProduct != null">
        it_product,
      </if>
      <if test="fieldName != null">
        field_name,
      </if>
      <if test="levelName != null">
        level_name,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="peopleDays != null">
        people_days,
      </if>
      <if test="projectName != null">
        project_name,
      </if>
      <if test="projectCode != null">
        project_code,
      </if>
      <if test="projectManager != null">
        project_manager,
      </if>
      <if test="projectManagerMip != null">
        project_manager_mip,
      </if>
      <if test="resourceCode != null">
        resource_code,
      </if>
      <if test="resourceName != null">
        resource_name,
      </if>
      <if test="projectStatus != null">
        project_status,
      </if>
      <if test="pamEnabled != null">
        pam_enabled,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="mipname != null">
        #{mipname,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="supplierCode != null">
        #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="isPerson != null">
        #{isPerson,jdbcType=TINYINT},
      </if>
      <if test="itProduct != null">
        #{itProduct,jdbcType=VARCHAR},
      </if>
      <if test="fieldName != null">
        #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="levelName != null">
        #{levelName,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="peopleDays != null">
        #{peopleDays,jdbcType=INTEGER},
      </if>
      <if test="projectName != null">
        #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="projectCode != null">
        #{projectCode,jdbcType=VARCHAR},
      </if>
      <if test="projectManager != null">
        #{projectManager,jdbcType=VARCHAR},
      </if>
      <if test="projectManagerMip != null">
        #{projectManagerMip,jdbcType=VARCHAR},
      </if>
      <if test="resourceCode != null">
        #{resourceCode,jdbcType=VARCHAR},
      </if>
      <if test="resourceName != null">
        #{resourceName,jdbcType=VARCHAR},
      </if>
      <if test="projectStatus != null">
        #{projectStatus,jdbcType=VARCHAR},
      </if>
      <if test="pamEnabled != null">
        #{pamEnabled,jdbcType=BIT},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.ctc.entity.RdmResourcePlanExample" resultType="java.lang.Long">
    select count(*) from rdm_resource_plan
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.ctc.entity.RdmResourcePlan">
    update rdm_resource_plan
    <set>
      <if test="username != null">
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="mipname != null">
        mipname = #{mipname,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="supplierCode != null">
        supplier_code = #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="isPerson != null">
        is_person = #{isPerson,jdbcType=TINYINT},
      </if>
      <if test="itProduct != null">
        it_product = #{itProduct,jdbcType=VARCHAR},
      </if>
      <if test="fieldName != null">
        field_name = #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="levelName != null">
        level_name = #{levelName,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="peopleDays != null">
        people_days = #{peopleDays,jdbcType=INTEGER},
      </if>
      <if test="projectName != null">
        project_name = #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="projectCode != null">
        project_code = #{projectCode,jdbcType=VARCHAR},
      </if>
      <if test="projectManager != null">
        project_manager = #{projectManager,jdbcType=VARCHAR},
      </if>
      <if test="projectManagerMip != null">
        project_manager_mip = #{projectManagerMip,jdbcType=VARCHAR},
      </if>
      <if test="resourceCode != null">
        resource_code = #{resourceCode,jdbcType=VARCHAR},
      </if>
      <if test="resourceName != null">
        resource_name = #{resourceName,jdbcType=VARCHAR},
      </if>
      <if test="projectStatus != null">
        project_status = #{projectStatus,jdbcType=VARCHAR},
      </if>
      <if test="pamEnabled != null">
        pam_enabled = #{pamEnabled,jdbcType=BIT},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.ctc.entity.RdmResourcePlan">
    update rdm_resource_plan
    set username = #{username,jdbcType=VARCHAR},
      mipname = #{mipname,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      supplier_code = #{supplierCode,jdbcType=VARCHAR},
      supplier_name = #{supplierName,jdbcType=VARCHAR},
      is_person = #{isPerson,jdbcType=TINYINT},
      it_product = #{itProduct,jdbcType=VARCHAR},
      field_name = #{fieldName,jdbcType=VARCHAR},
      level_name = #{levelName,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      people_days = #{peopleDays,jdbcType=INTEGER},
      project_name = #{projectName,jdbcType=VARCHAR},
      project_code = #{projectCode,jdbcType=VARCHAR},
      project_manager = #{projectManager,jdbcType=VARCHAR},
      project_manager_mip = #{projectManagerMip,jdbcType=VARCHAR},
      resource_code = #{resourceCode,jdbcType=VARCHAR},
      resource_name = #{resourceName,jdbcType=VARCHAR},
      project_status = #{projectStatus,jdbcType=VARCHAR},
      pam_enabled = #{pamEnabled,jdbcType=BIT},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>