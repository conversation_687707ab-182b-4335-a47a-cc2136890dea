<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.ReceiptPlanExtMapper">
    <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.ReceiptPlan">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="contract_id" jdbcType="BIGINT" property="contractId"/>
        <result column="product_type_id" jdbcType="BIGINT" property="productTypeId"/>
        <result column="product_id" jdbcType="BIGINT" property="productId"/>
        <result column="module_id" jdbcType="BIGINT" property="moduleId"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="deleted_flag" jdbcType="BIT" property="deletedFlag"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt"/>
    </resultMap>

    <update id="updateAmountByIds" parameterType="java.lang.Long">
        update receipt_plan p
            inner join (
                select plan_id,
                       ifnull(sum(amount),0) as amount_sum,
                       ifnull(sum(actual_amount),0) as actual_amount_sum
                from receipt_plan_detail
                where plan_id in 
                    <foreach collection="receiptPlanIds" open="(" item="planId" close=")" separator=",">
                        #{planId}
                    </foreach>
                and (deleted_flag = 0 or deleted_flag is null)
                group by plan_id
            )t on t.plan_id = p.id
        set p.amount = t.amount_sum,
            p.actual_amount = t.actual_amount_sum
    </update>

    <delete id="deleteByContractId" parameterType="java.lang.Long">
    delete from receipt_plan
    where contract_id = #{contractId,jdbcType=BIGINT}
    </delete>

    <select id="getContractReceiptAmount" parameterType="java.lang.Long" resultType="java.math.BigDecimal">
        select sum(ifnull(actual_amount,0))
        from receipt_plan_detail
        where deleted_flag = 0
        and contract_id = #{contractId};
    </select>

    <select id="getParentContractReceiptAmount" parameterType="java.lang.Long" resultType="java.math.BigDecimal">
        select sum(ifnull(actual_amount,0))
        from receipt_plan_detail
        where deleted_flag = 0
        and contract_id in (
         select c.id from contract c where c.parent_id = #{contractId} and c.deleted_flag = 0
        );
    </select>

    <select id="getFrameContractReceiptAmount" parameterType="java.lang.Long" resultType="java.math.BigDecimal">
        select sum(ifnull(actual_amount,0))
        from receipt_plan_detail
        where deleted_flag = 0
        and contract_id in (
        select c.id from contract c where c.deleted_flag = 0 and c.frame_contract_id = #{contractId}
        );
    </select>

</mapper>