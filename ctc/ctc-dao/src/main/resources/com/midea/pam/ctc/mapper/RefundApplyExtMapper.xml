<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.RefundApplyExtMapper">

    <select id="getRefundApplyDTOList" parameterType="java.util.Map"
            resultType="com.midea.pam.common.ctc.dto.RefundApplyDTO">
        select distinct
               ra.id,
               ra.refund_apply_code refundApplyCode,
               ra.refund_apply_status refundApplyStatus,
               ra.amount,
               ra.refund_entry_date refundEntryDate,
               ra.customer_code customerCode,
               ra.customer_name customerName,
               ra.payee_bank_name payeeBankName,
               ra.payee_bank_account payeeBankAccount,
               ra.payment_type paymentType,
               ra.apply_name applyName,
               ra.apply_date applyDate,
               ra.outer_status outerStatus,
               ra.ou_name ouName
        from refund_apply ra
        left join refund_apply_detail rad on ra.id = rad.refund_apply_id and rad.deleted_flag = 0
        where 1=1 and ra.deleted_flag = 0
        <if test="refundApplyCode != null">
            and ra.refund_apply_code like CONCAT(CONCAT('%',#{refundApplyCode}),'%')
        </if>
        <if test="refundApplyStatusList != null">
            and ra.refund_apply_status in
            <foreach collection="refundApplyStatusList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="refundEntryStartDate != null">
            and ra.refund_entry_date <![CDATA[>= ]]> #{refundEntryStartDate}
        </if>
        <if test="refundEntryEndDate != null">
            and ra.refund_entry_date <![CDATA[<= ]]> #{refundEntryEndDate}
        </if>
        <if test="customerCode != null">
            and ra.customer_code like CONCAT(CONCAT('%',#{customerCode}),'%')
        </if>
        <if test="customerName != null">
            and ra.customer_name like CONCAT(CONCAT('%',#{customerName}),'%')
        </if>
        <if test="payeeBankName != null">
            and ra.payee_bank_name like CONCAT(CONCAT('%',#{payeeBankName}),'%')
        </if>
        <if test="payeeBankAccount != null">
            and ra.payee_bank_account like CONCAT(CONCAT('%',#{payeeBankAccount}),'%')
        </if>
        <if test="paymentTypeStatusList != null">
            and ra.payment_type in
            <foreach collection="paymentTypeStatusList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="contractCode != null">
            and rad.contract_code like CONCAT(CONCAT('%',#{contractCode}),'%')
        </if>
        <if test="contractName != null">
            and rad.contract_name like CONCAT(CONCAT('%',#{contractName}),'%')
        </if>
        <if test="applyName != null">
            and ra.apply_name like CONCAT(CONCAT('%',#{applyName}),'%')
        </if>
        <if test="applyStartDate != null">
            and ra.apply_date <![CDATA[>= ]]> #{refundEntryStartDate}
        </if>
        <if test="applyEndDate != null">
            and ra.apply_date <![CDATA[<= ]]> #{applyEndDate}
        </if>

        <if test="outerStatusStatusList != null">
            and ra.outer_status in
            <foreach collection="outerStatusStatusList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="ouIdList != null">
            and ra.ou_id in
            <foreach collection="ouIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="currentOuIdList != null">
            and ra.ou_id in
            <foreach collection="currentOuIdList" index="index" item="ouItem" open="(" separator="," close=")">
                #{ouItem}
            </foreach>
        </if>
        order by ra.apply_date desc
    </select>
</mapper>