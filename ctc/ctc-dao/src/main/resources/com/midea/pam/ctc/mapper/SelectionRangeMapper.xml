<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.SelectionRangeMapper">
    <sql id="queryCondition">

        <!-- 模糊项目编号-->
        <if test="fuzzyProjectNum != null and fuzzyProjectNum != ''">
            AND project.code like concat('%', #{fuzzyProjectNum, jdbcType=CHAR}, '%')
        </if>

        <!-- 模糊项目名称-->
        <if test="fuzzyProjectName != null and fuzzyProjectName != ''">
            AND project.name like concat('%', #{fuzzyProjectName, jdbcType=CHAR}, '%')
        </if>

        <!--实际结束时间检索-->
        <if test="actualEndTimeBegin != null and actualEndTimeEndEnd != null">
            AND (
            milepost.actual_end_time &gt;= #{actualEndTimeBegin, jdbcType=TIMESTAMP}
            AND
            milepost.actual_end_time &lt;= #{actualEndTimeEndEnd, jdbcType=TIMESTAMP}
            )
        </if>

<!--        &lt;!&ndash; 成本方法编码&ndash;&gt;
        <if test="costMethod != null">
            AND projectProfit.cost_method_main = #{costMethod, jdbcType=VARCHAR}
        </if>-->

        <if test="costMethods != null ">
            AND projectProfit.cost_method_main in
            <foreach collection="costMethods" index="index" item="costMethod" open="(" separator="," close=")">
                #{costMethod}
            </foreach>
        </if>

        <!-- 来源类型(待定)-->
        <if test="sourceType != null">

        </if>

        <!-- 模糊客户名称-->
        <if test="fuzzyCustomerName != null and fuzzyCustomerName != ''">
            AND project.customer_name like concat('%', #{fuzzyCustomerName, jdbcType=CHAR}, '%')
        </if>

        <!-- 项目业务实体id-->
        <if test="projectOuId != null">
            AND project.ou_id = #{projectOuId, jdbcType=BIGINT}
        </if>

        <!-- 项目名称或者编号 -->
        <if test="projectNameOrCode != null and projectNameOrCode != ''">
            AND (project.name like concat('%', #{projectNameOrCode, jdbcType=CHAR}, '%')
            OR project.code like concat('%', #{projectNameOrCode, jdbcType=CHAR}, '%'))
        </if>

        <!-- 项目业务分类id-->
        <if test="unitId != null">
            AND project.unit_id = #{unitId, jdbcType=BIGINT}
        </if>

        <!-- 项目状态-->
        <if test="projectStatuses != null and projectStatuses.size > 0">
            AND project.`status` IN
            <foreach close=")" collection="projectStatuses" index="index" item="projectStatusesItem" open="("
                     separator=",">
                #{projectStatusesItem}
            </foreach>
        </if>
    </sql>

    <sql id="queryConditionOfIncomeCostPlan">

        <!-- 模糊项目编号-->
        <if test="fuzzyProjectNum != null and fuzzyProjectNum != ''">
            AND project.code like concat('%', #{fuzzyProjectNum, jdbcType=CHAR}, '%')
        </if>

        <!-- 模糊项目名称-->
        <if test="fuzzyProjectName != null and fuzzyProjectName != ''">
            AND project.name like concat('%', #{fuzzyProjectName, jdbcType=CHAR}, '%')
        </if>

        <!--实际结束时间检索-->
        <if test="actualEndTimeBegin != null and actualEndTimeEndEnd != null">
            AND (
            picp.actual_end_time &gt;= #{actualEndTimeBegin, jdbcType=TIMESTAMP}
            AND
            picp.actual_end_time &lt;= #{actualEndTimeEndEnd, jdbcType=TIMESTAMP}
            )
        </if>

        <!-- 来源类型(待定)-->
        <if test="sourceType != null">

        </if>

        <!-- 模糊客户名称-->
        <if test="fuzzyCustomerName != null and fuzzyCustomerName != ''">
            AND project.customer_name like concat('%', #{fuzzyCustomerName, jdbcType=CHAR}, '%')
        </if>

        <!-- 项目业务实体id-->
        <if test="projectOuId != null">
            AND project.ou_id = #{projectOuId, jdbcType=BIGINT}
        </if>

        <!-- 项目名称或者编号 -->
        <if test="projectNameOrCode != null and projectNameOrCode != ''">
            AND (project.name like concat('%', #{projectNameOrCode, jdbcType=CHAR}, '%')
            OR project.code like concat('%', #{projectNameOrCode, jdbcType=CHAR}, '%'))
        </if>

        <!-- 项目业务分类id-->
        <if test="unitId != null">
            AND project.unit_id = #{unitId, jdbcType=BIGINT}
        </if>

        <!-- 项目状态-->
        <if test="projectStatuses != null and projectStatuses.size &gt; 0">
            AND project.`status` IN
            <foreach collection="projectStatuses" item="projectStatusItem" index="index" open="(" separator="," close=")">
                #{projectStatusItem}
            </foreach>
        </if>

        <!-- 结转期间 -->
        <if test="carryoverPeriod != null">
            AND picp.name &lt;= #{carryoverPeriod}
        </if>

        <if test="costMethods != null ">
            AND profit.cost_method_main in
            <foreach collection="costMethods" index="index" item="costMethod" open="(" separator="," close=")">
                #{costMethod}
            </foreach>
        </if>
    </sql>
    <select id="selectionRange"
            parameterType="com.midea.pam.common.ctc.dto.SelectionRangeDto"
            resultType="com.midea.pam.common.ctc.dto.SelectionRangeDto">
        SELECT
        project.id AS 'projectId',
        project.`code` AS 'projectCode',
        project.`name` AS 'projectName',
        projectType. NAME AS 'projectType',
        milepost.id AS 'milepostId',
        milepost. NAME AS 'milepostName',
        milepost.actual_start_time AS 'milepostActualStartTime',
        milepost.actual_end_time AS 'milepostActualEndTime',
        milepost.carry_status AS 'milepostCarryStatus',
        project.customer_id AS 'projectCustomerId',
        project.customer_name AS 'projectCustomerName',
        projectProfit.cost_method_main AS 'costMethod',
        project.manager_id as 'managerId',
        project.manager_name as 'managerName',
        1 as 'sourceType',
        2 as 'type'
        FROM
        project_milepost milepost
        LEFT JOIN project project ON milepost.project_id = project.id
        LEFT JOIN project_type projectType ON projectType.id = project.type
        LEFT JOIN project_profit projectProfit ON projectProfit.project_id = project.id
        WHERE
        ( milepost.deleted_flag = 0 OR milepost.deleted_flag IS NULL )
        AND ( project.deleted_flag = 0 OR project.deleted_flag IS NULL )
        AND ( projectProfit.deleted_flag = 0 OR projectProfit.deleted_flag IS NULL )
        AND milepost.carryover_bill_id IS NULL
        AND milepost.income_flag = 1
        AND milepost.actual_end_time IS NOT NULL
        <include refid="queryCondition" />
        ORDER BY
        project.`code` asc,
        milepost.actual_end_time desc
    </select>

    <select id="selectionRangeOfMilepost"
            parameterType="com.midea.pam.common.ctc.dto.SelectionRangeDto"
            resultType="com.midea.pam.common.ctc.dto.SelectionRangeDto">
        SELECT project.id AS 'projectId', project.`code` AS 'projectCode', project.`name` AS 'projectName',
            projectType.NAME AS 'projectType', milepost.id AS 'milepostId'
            , milepost.NAME AS 'milepostName', milepost.actual_start_time AS 'milepostActualStartTime',
            milepost.actual_end_time AS 'milepostActualEndTime'
            , milepost.carry_status AS 'milepostCarryStatus', project.customer_id AS 'projectCustomerId'
            , project.customer_name AS 'projectCustomerName', projectProfit.cost_method_main AS 'costMethod',
            project.manager_id AS 'managerId'
            , project.manager_name AS 'managerName', 1 AS 'sourceType'
            , 1 AS 'type', milepost.order_num AS orderNum, milepost.carryover_batch_num as carryoverBatchNum, project.amount as amount, project.budget_cost as budgetTotal
        FROM project_milepost milepost
        LEFT JOIN project project ON milepost.project_id = project.id
        LEFT JOIN project_type projectType ON projectType.id = project.type
        LEFT JOIN project_profit projectProfit ON projectProfit.project_id = project.id
        WHERE (milepost.deleted_flag = 0
        OR milepost.deleted_flag IS NULL)
        AND (project.deleted_flag = 0
        OR project.deleted_flag IS NULL)
        AND (projectProfit.deleted_flag = 0
        OR projectProfit.deleted_flag IS NULL)
        AND milepost.carryover_bill_id IS NULL
        AND milepost.income_flag = 1
        AND projectProfit.income_point_main = '0002'
        AND milepost.actual_end_time IS NOT NULL
        AND milepost.status in (2, 26)
        AND project.preview_flag = 0
        <include refid="queryCondition"/>
        AND milepost.id =
        (
            SELECT pm.id
            FROM project_milepost pm
            LEFT JOIN project project ON pm.project_id = project.id
            LEFT JOIN project_type projectType ON projectType.id = project.type
            LEFT JOIN project_profit projectProfit ON projectProfit.project_id = project.id
            WHERE (pm.deleted_flag = 0
            OR pm.deleted_flag IS NULL)
            AND (project.deleted_flag = 0
            OR project.deleted_flag IS NULL)
            AND (projectProfit.deleted_flag = 0
            OR projectProfit.deleted_flag IS NULL)
            AND pm.carryover_bill_id IS NULL
            AND pm.income_flag = 1
            AND projectProfit.income_point_main = '0002'
            AND pm.actual_end_time IS NOT NULL
            and pm.project_id = milepost.project_id
            AND project.preview_flag = 0
            AND pm.status in (2, 26)
        <include refid="queryCondition2"/>
            ORDER BY pm.actual_end_time ASC
            limit 1
        )
        ORDER BY project.code ASC
    </select>

    <sql id="queryCondition2">

        <!-- 模糊项目编号-->
        <if test="fuzzyProjectNum != null and fuzzyProjectNum != ''">
            AND project.code like concat('%', #{fuzzyProjectNum, jdbcType=CHAR}, '%')
        </if>

        <!-- 模糊项目名称-->
        <if test="fuzzyProjectName != null and fuzzyProjectName != ''">
            AND project.name like concat('%', #{fuzzyProjectName, jdbcType=CHAR}, '%')
        </if>

        <!--实际结束时间检索-->
        <if test="actualEndTimeBegin != null and actualEndTimeEndEnd != null">
            AND (
            pm.actual_end_time &gt;= #{actualEndTimeBegin, jdbcType=TIMESTAMP}
            AND
            pm.actual_end_time &lt;= #{actualEndTimeEndEnd, jdbcType=TIMESTAMP}
            )
        </if>

  <!--      &lt;!&ndash; 成本方法编码&ndash;&gt;
        <if test="costMethod != null">
            AND projectProfit.cost_method_main IN ( #{costMethod, jdbcType=VARCHAR})
        </if>-->

        <if test="costMethods != null ">
            AND projectProfit.cost_method_main in
            <foreach collection="costMethods" index="index" item="costMethod" open="(" separator="," close=")">
                #{costMethod}
            </foreach>
        </if>

        <!-- 来源类型(待定)-->
        <if test="sourceType != null">

        </if>

        <!-- 模糊客户名称-->
        <if test="fuzzyCustomerName != null and fuzzyCustomerName != ''">
            AND project.customer_name like concat('%', #{fuzzyCustomerName, jdbcType=CHAR}, '%')
        </if>

        <!-- 项目业务实体id-->
        <if test="projectOuId != null">
            AND project.ou_id = #{projectOuId, jdbcType=BIGINT}
        </if>

        <!-- 项目名称或者编号 -->
        <if test="projectNameOrCode != null and projectNameOrCode != ''">
            AND (project.name like concat('%', #{projectNameOrCode, jdbcType=CHAR}, '%')
            OR project.code like concat('%', #{projectNameOrCode, jdbcType=CHAR}, '%'))
        </if>

        <!-- 项目业务分类id-->
        <if test="unitIds != null and unitIds.size>0">
            AND project.unit_id in
            <foreach collection="unitIds" item="unitId" open="(" separator="," close=")">
                #{unitId}
            </foreach>
        </if>

        <!-- 项目状态-->
        <if test="projectStatuses != null and projectStatuses.size &gt; 0">
            AND project.`status` in
            <foreach collection="projectStatuses" item="projectStatusItem" index="index" open="(" separator="," close=")">
                #{projectStatusItem}
            </foreach>
        </if>
    </sql>


    <select id="selectionRangeOfIncomeCostPlan"
            parameterType="com.midea.pam.common.ctc.dto.SelectionRangeDto"
            resultType="com.midea.pam.common.ctc.dto.SelectionRangeDto">
        SELECT project.id AS 'projectId', project.`code` AS 'projectCode', project.`name` AS 'projectName', projectType.NAME AS 'projectType', picp.id AS 'milepostId'
            , picp.NAME AS 'milepostName', picp.actual_start_time AS 'milepostActualStartTime', picp.actual_end_time AS 'milepostActualEndTime'
            , picp.carry_status AS 'milepostCarryStatus', project.customer_id AS 'projectCustomerId'
            , project.customer_name AS 'projectCustomerName', profit.cost_method_main AS 'costMethod', project.manager_id AS 'managerId', project.manager_name AS 'managerName', 1 AS 'sourceType', 2 AS 'type'
            , picp.order_num AS orderNum, picp.actual_end_time as actualEndTime, picp.carryover_batch_num as carryoverBatchNum
        FROM project_income_cost_plan picp
        LEFT JOIN project project ON picp.project_id = project.id
        LEFT JOIN project_profit profit ON profit.project_id = project.id and profit.deleted_flag = 0
        LEFT JOIN project_type projectType ON projectType.id = project.type
        WHERE (picp.deleted_flag = 0
        OR picp.deleted_flag IS NULL)
        AND (project.deleted_flag = 0
        OR project.deleted_flag IS NULL)
        AND picp.carryover_bill_id IS NULL
        AND picp.income_flag = 1
        AND profit.income_point_main = '0003'
        AND project.preview_flag = 0
        <include refid="queryConditionOfIncomeCostPlan" />
        and picp.id = (
            SELECT cp.id
            FROM project_income_cost_plan cp
            LEFT JOIN project project ON cp.project_id = project.id
            LEFT JOIN project_profit profit ON profit.project_id = project.id
            WHERE (cp.deleted_flag = 0
            OR cp.deleted_flag IS NULL)
            AND (project.deleted_flag = 0
            OR project.deleted_flag IS NULL)
            AND (profit.deleted_flag = 0
            OR profit.deleted_flag IS NULL)
            AND cp.carryover_bill_id IS NULL
            AND cp.income_flag = 1
            AND profit.income_point_main = '0003'
            AND picp.project_id = cp.project_id
            AND project.preview_flag = 0
        <include refid="queryConditionOfIncomeCostPlan2" />
            ORDER BY cp.actual_end_time ASC limit 1
        )
        ORDER BY project.`code` ASC
    </select>

    <sql id="queryConditionOfIncomeCostPlan2">

        <!-- 模糊项目编号-->
        <if test="fuzzyProjectNum != null and fuzzyProjectNum != ''">
            AND project.code like concat('%', #{fuzzyProjectNum, jdbcType=CHAR}, '%')
        </if>

        <!-- 模糊项目名称-->
        <if test="fuzzyProjectName != null and fuzzyProjectName != ''">
            AND project.name like concat('%', #{fuzzyProjectName, jdbcType=CHAR}, '%')
        </if>

        <!--实际结束时间检索-->
        <if test="actualEndTimeBegin != null and actualEndTimeEndEnd != null">
            AND (
            cp.actual_end_time &gt;= #{actualEndTimeBegin, jdbcType=TIMESTAMP}
            AND
            cp.actual_end_time &lt;= #{actualEndTimeEndEnd, jdbcType=TIMESTAMP}
            )
        </if>

        <!-- 来源类型(待定)-->
        <if test="sourceType != null">

        </if>

        <!-- 模糊客户名称-->
        <if test="fuzzyCustomerName != null and fuzzyCustomerName != ''">
            AND project.customer_name like concat('%', #{fuzzyCustomerName, jdbcType=CHAR}, '%')
        </if>

        <!-- 项目业务实体id-->
        <if test="projectOuId != null">
            AND project.ou_id = #{projectOuId, jdbcType=BIGINT}
        </if>

        <!-- 项目名称或者编号 -->
        <if test="projectNameOrCode != null and projectNameOrCode != ''">
            AND (project.name like concat('%', #{projectNameOrCode, jdbcType=CHAR}, '%')
            OR project.code like concat('%', #{projectNameOrCode, jdbcType=CHAR}, '%'))
        </if>

        <!-- 项目业务分类id-->
        <if test="unitIds != null and unitIds.size>0">
            AND project.unit_id in
            <foreach collection="unitIds" item="unitId" open="(" separator="," close=")">
                #{unitId}
            </foreach>
        </if>

        <!-- 项目状态-->
        <if test="projectStatuses != null and projectStatuses.size &gt; 0">
            AND project.`status` in
            <foreach collection="projectStatuses" item="projectStatusItem" index="index" open="(" separator="," close=")">
                #{projectStatusItem}
            </foreach>
        </if>

        <!-- 结转期间 -->
        <if test="carryoverPeriod != null">
            AND cp.name &lt;= #{carryoverPeriod}
        </if>

        <if test="costMethods != null ">
            AND profit.cost_method_main in
            <foreach collection="costMethods" index="index" item="costMethod" open="(" separator="," close=")">
                #{costMethod}
            </foreach>
        </if>
    </sql>

</mapper>