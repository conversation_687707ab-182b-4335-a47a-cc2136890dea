<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.SwapExecuteFeeMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.SwapExecuteFee">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="mip_name" jdbcType="VARCHAR" property="mipName" />
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="expense_fee" jdbcType="DECIMAL" property="expenseFee" />
    <result column="swaped_cost" jdbcType="DECIMAL" property="swapedCost" />
    <result column="current_swap_cost" jdbcType="DECIMAL" property="currentSwapCost" />
    <result column="swap_execute_id" jdbcType="BIGINT" property="swapExecuteId" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="deleted_flag" jdbcType="BIGINT" property="deletedFlag" />
    <result column="attribute1" jdbcType="VARCHAR" property="attribute1" />
    <result column="attribute2" jdbcType="VARCHAR" property="attribute2" />
    <result column="attribute3" jdbcType="VARCHAR" property="attribute3" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_id, user_name, mip_name, product_id, product_name, expense_fee, swaped_cost, 
    current_swap_cost, swap_execute_id, create_at, create_by, update_at, update_by, deleted_flag, 
    attribute1, attribute2, attribute3
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.ctc.entity.SwapExecuteFeeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from swap_execute_fee
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from swap_execute_fee
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from swap_execute_fee
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.ctc.entity.SwapExecuteFee">
    insert into swap_execute_fee (id, user_id, user_name, 
      mip_name, product_id, product_name, 
      expense_fee, swaped_cost, current_swap_cost, 
      swap_execute_id, create_at, create_by, 
      update_at, update_by, deleted_flag, 
      attribute1, attribute2, attribute3
      )
    values (#{id,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{userName,jdbcType=VARCHAR}, 
      #{mipName,jdbcType=VARCHAR}, #{productId,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, 
      #{expenseFee,jdbcType=DECIMAL}, #{swapedCost,jdbcType=DECIMAL}, #{currentSwapCost,jdbcType=DECIMAL}, 
      #{swapExecuteId,jdbcType=BIGINT}, #{createAt,jdbcType=TIMESTAMP}, #{createBy,jdbcType=BIGINT}, 
      #{updateAt,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=BIGINT}, #{deletedFlag,jdbcType=BIGINT}, 
      #{attribute1,jdbcType=VARCHAR}, #{attribute2,jdbcType=VARCHAR}, #{attribute3,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.ctc.entity.SwapExecuteFee">
    insert into swap_execute_fee
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="mipName != null">
        mip_name,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="expenseFee != null">
        expense_fee,
      </if>
      <if test="swapedCost != null">
        swaped_cost,
      </if>
      <if test="currentSwapCost != null">
        current_swap_cost,
      </if>
      <if test="swapExecuteId != null">
        swap_execute_id,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
      <if test="attribute1 != null">
        attribute1,
      </if>
      <if test="attribute2 != null">
        attribute2,
      </if>
      <if test="attribute3 != null">
        attribute3,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="mipName != null">
        #{mipName,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="expenseFee != null">
        #{expenseFee,jdbcType=DECIMAL},
      </if>
      <if test="swapedCost != null">
        #{swapedCost,jdbcType=DECIMAL},
      </if>
      <if test="currentSwapCost != null">
        #{currentSwapCost,jdbcType=DECIMAL},
      </if>
      <if test="swapExecuteId != null">
        #{swapExecuteId,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=BIGINT},
      </if>
      <if test="attribute1 != null">
        #{attribute1,jdbcType=VARCHAR},
      </if>
      <if test="attribute2 != null">
        #{attribute2,jdbcType=VARCHAR},
      </if>
      <if test="attribute3 != null">
        #{attribute3,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.ctc.entity.SwapExecuteFeeExample" resultType="java.lang.Long">
    select count(*) from swap_execute_fee
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.ctc.entity.SwapExecuteFee">
    update swap_execute_fee
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="mipName != null">
        mip_name = #{mipName,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="expenseFee != null">
        expense_fee = #{expenseFee,jdbcType=DECIMAL},
      </if>
      <if test="swapedCost != null">
        swaped_cost = #{swapedCost,jdbcType=DECIMAL},
      </if>
      <if test="currentSwapCost != null">
        current_swap_cost = #{currentSwapCost,jdbcType=DECIMAL},
      </if>
      <if test="swapExecuteId != null">
        swap_execute_id = #{swapExecuteId,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=BIGINT},
      </if>
      <if test="attribute1 != null">
        attribute1 = #{attribute1,jdbcType=VARCHAR},
      </if>
      <if test="attribute2 != null">
        attribute2 = #{attribute2,jdbcType=VARCHAR},
      </if>
      <if test="attribute3 != null">
        attribute3 = #{attribute3,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.ctc.entity.SwapExecuteFee">
    update swap_execute_fee
    set user_id = #{userId,jdbcType=BIGINT},
      user_name = #{userName,jdbcType=VARCHAR},
      mip_name = #{mipName,jdbcType=VARCHAR},
      product_id = #{productId,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      expense_fee = #{expenseFee,jdbcType=DECIMAL},
      swaped_cost = #{swapedCost,jdbcType=DECIMAL},
      current_swap_cost = #{currentSwapCost,jdbcType=DECIMAL},
      swap_execute_id = #{swapExecuteId,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      deleted_flag = #{deletedFlag,jdbcType=BIGINT},
      attribute1 = #{attribute1,jdbcType=VARCHAR},
      attribute2 = #{attribute2,jdbcType=VARCHAR},
      attribute3 = #{attribute3,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>