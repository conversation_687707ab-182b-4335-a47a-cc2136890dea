<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.ReceiptPlanHisMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.ReceiptPlanHis">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="receipt_plan_id" jdbcType="BIGINT" property="receiptPlanId" />
    <result column="contract_id" jdbcType="BIGINT" property="contractId" />
    <result column="product_type_id" jdbcType="BIGINT" property="productTypeId" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="module_id" jdbcType="BIGINT" property="moduleId" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="actual_amount" jdbcType="DECIMAL" property="actualAmount" />
    <result column="deleted_flag" jdbcType="BIT" property="deletedFlag" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="changeway_id" jdbcType="BIGINT" property="changewayId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, receipt_plan_id, contract_id, product_type_id, product_id, module_id, amount, 
    actual_amount, deleted_flag, create_by, create_at, update_by, update_at, changeway_id
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.ctc.entity.ReceiptPlanHisExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from receipt_plan_his
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from receipt_plan_his
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from receipt_plan_his
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByChangewayId" parameterType="java.lang.Long">
    delete from receipt_plan_his
    where changeway_id = #{changewayId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.ctc.entity.ReceiptPlanHis">
    insert into receipt_plan_his (id, receipt_plan_id, contract_id, 
      product_type_id, product_id, module_id, 
      amount, actual_amount, deleted_flag, 
      create_by, create_at, update_by, 
      update_at, changeway_id)
    values (#{id,jdbcType=BIGINT}, #{receiptPlanId,jdbcType=BIGINT}, #{contractId,jdbcType=BIGINT}, 
      #{productTypeId,jdbcType=BIGINT}, #{productId,jdbcType=BIGINT}, #{moduleId,jdbcType=BIGINT}, 
      #{amount,jdbcType=DECIMAL}, #{actualAmount,jdbcType=DECIMAL}, #{deletedFlag,jdbcType=BIT}, 
      #{createBy,jdbcType=BIGINT}, #{createAt,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=BIGINT}, 
      #{updateAt,jdbcType=TIMESTAMP}, #{changewayId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.ctc.entity.ReceiptPlanHis">
    insert into receipt_plan_his
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="receiptPlanId != null">
        receipt_plan_id,
      </if>
      <if test="contractId != null">
        contract_id,
      </if>
      <if test="productTypeId != null">
        product_type_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="moduleId != null">
        module_id,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="actualAmount != null">
        actual_amount,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="changewayId != null">
        changeway_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="receiptPlanId != null">
        #{receiptPlanId,jdbcType=BIGINT},
      </if>
      <if test="contractId != null">
        #{contractId,jdbcType=BIGINT},
      </if>
      <if test="productTypeId != null">
        #{productTypeId,jdbcType=BIGINT},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="moduleId != null">
        #{moduleId,jdbcType=BIGINT},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="actualAmount != null">
        #{actualAmount,jdbcType=DECIMAL},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=BIT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="changewayId != null">
        #{changewayId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.ctc.entity.ReceiptPlanHisExample" resultType="java.lang.Long">
    select count(*) from receipt_plan_his
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.ctc.entity.ReceiptPlanHis">
    update receipt_plan_his
    <set>
      <if test="receiptPlanId != null">
        receipt_plan_id = #{receiptPlanId,jdbcType=BIGINT},
      </if>
      <if test="contractId != null">
        contract_id = #{contractId,jdbcType=BIGINT},
      </if>
      <if test="productTypeId != null">
        product_type_id = #{productTypeId,jdbcType=BIGINT},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="moduleId != null">
        module_id = #{moduleId,jdbcType=BIGINT},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="actualAmount != null">
        actual_amount = #{actualAmount,jdbcType=DECIMAL},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=BIT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="changewayId != null">
        changeway_id = #{changewayId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.ctc.entity.ReceiptPlanHis">
    update receipt_plan_his
    set receipt_plan_id = #{receiptPlanId,jdbcType=BIGINT},
      contract_id = #{contractId,jdbcType=BIGINT},
      product_type_id = #{productTypeId,jdbcType=BIGINT},
      product_id = #{productId,jdbcType=BIGINT},
      module_id = #{moduleId,jdbcType=BIGINT},
      amount = #{amount,jdbcType=DECIMAL},
      actual_amount = #{actualAmount,jdbcType=DECIMAL},
      deleted_flag = #{deletedFlag,jdbcType=BIT},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      changeway_id = #{changewayId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>