<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.FileDownloadLogMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.gateway.entity.FileDownloadLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="file_id" jdbcType="BIGINT" property="fileId" />
    <result column="delete_flag" jdbcType="TINYINT" property="deleteFlag" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, file_id, delete_flag, create_at, create_by, update_at, update_by
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.gateway.entity.FileDownloadLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from file_download_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from file_download_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from file_download_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.gateway.entity.FileDownloadLog">
    insert into file_download_log (id, file_id, delete_flag, 
      create_at, create_by, update_at, 
      update_by)
    values (#{id,jdbcType=BIGINT}, #{fileId,jdbcType=BIGINT}, #{deleteFlag,jdbcType=TINYINT}, 
      #{createAt,jdbcType=TIMESTAMP}, #{createBy,jdbcType=BIGINT}, #{updateAt,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.gateway.entity.FileDownloadLog">
    insert into file_download_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fileId != null">
        file_id,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fileId != null">
        #{fileId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=TINYINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.gateway.entity.FileDownloadLogExample" resultType="java.lang.Long">
    select count(*) from file_download_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.gateway.entity.FileDownloadLog">
    update file_download_log
    <set>
      <if test="fileId != null">
        file_id = #{fileId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=TINYINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.gateway.entity.FileDownloadLog">
    update file_download_log
    set file_id = #{fileId,jdbcType=BIGINT},
      delete_flag = #{deleteFlag,jdbcType=TINYINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>