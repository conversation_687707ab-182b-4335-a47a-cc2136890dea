<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>gateway</artifactId>
        <groupId>com.midea.pam</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>gateway-web</artifactId>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <dependencies>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-commons</artifactId>
        </dependency>

        <dependency>
            <groupId>com.midea.pam</groupId>
            <artifactId>gateway-common</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.midea.pam</groupId>
            <artifactId>gateway-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.midea.pam</groupId>
            <artifactId>gateway-impl</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.midea.pam.framework</groupId>
            <artifactId>core</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>midea-djs-core</artifactId>
            <version>2.3.5</version>
        </dependency>
        <dependency>
            <groupId>com.midea.sdp</groupId>
            <artifactId>sdp-domains</artifactId>
            <version>1.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <!-- 死信治理 -->
        <dependency>
            <groupId>com.midea.apaas</groupId>
            <artifactId>consumer-service-spring-boot-starter</artifactId>
            <version>1.0.2-SNAPSHOT</version>
        </dependency>
        <!--rocketmq-->
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
            <version>2.2.1</version>
        </dependency>

        <!--<dependency>-->
            <!--<groupId>com.meicloud.light</groupId>-->
            <!--<artifactId>light-core-api-web</artifactId>-->
            <!--<version>${light.version}</version>-->
        <!--</dependency>-->
    </dependencies>


</project>