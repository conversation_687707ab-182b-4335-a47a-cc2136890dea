package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.SwapExecuteDTO;
import com.midea.pam.common.ctc.vo.SwapExecuteExcelVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExcelUtil;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * @program: common-module
 * @description: 调剂执行报表
 * @author:zhongpeng
 * @create:2020-12-18 19:47
 **/
@Api("调剂执行报表")
@RestController
@RequestMapping("statistics/swapExecute")
public class SwapExecuteStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "调剂执行列表分页查询")
    @GetMapping("page")
    public Response page(@RequestParam(required = false) @ApiParam("调剂执行单号") final String swapExecuteCode,
                         @RequestParam(required = false) @ApiParam("调剂月份") final String swapExecuteMonth,
                         @RequestParam(required = false) @ApiParam("状态") final String statusStr,
                         @RequestParam(required = false) @ApiParam("项目编号") final String projectCode,
                         @RequestParam(required = false) @ApiParam("项目名称") final String projectName,
                         @RequestParam(required = false) @ApiParam("需方产品部门") final String requireProductOrgIdStr,
                         @RequestParam(required = false) @ApiParam("供方产品部门") final String provideProductOrgIdStr,
                         @RequestParam(required = false) @ApiParam("需方对接人") final String requireUserName,
                         @RequestParam(required = false) @ApiParam("供方对接人") final String provideUserName,
                         @RequestParam(required = false) @ApiParam("是否为我的申请单") final Boolean likeMe,
                         @RequestParam(required = false) @ApiParam("排序") final String orderParam,
                         @RequestParam(required = false) @ApiParam("排序类型(1:升序；2：降序)") final Integer orderType,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        Map<String, Object> param = new HashMap<>();
        param.put("swapExecuteCode", swapExecuteCode);
        param.put("swapExecuteMonth", swapExecuteMonth);
        param.put("statusStr", statusStr);
        param.put("projectCode", projectCode);
        param.put("projectName",projectName);
        param.put("requireProductOrgIdStr",requireProductOrgIdStr);
        param.put("provideProductOrgIdStr", provideProductOrgIdStr);
        param.put("requireUserName", requireUserName);
        param.put("provideUserName", provideUserName);
        param.put("likeMe", likeMe);
        param.put("orderParam", orderParam);
        param.put("orderType", orderType);
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "swapExecute/page", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<SwapExecuteDTO>>>() {
        });
    }

    @ApiOperation(value = "调剂执行列表导出Excel")
    @GetMapping("export/excel")
    public void excel(HttpServletResponse response, @RequestParam(required = false) @ApiParam("调剂执行单号") final String swapExecuteCode,
                      @RequestParam(required = false) @ApiParam("调剂月份") final String swapExecuteMonth,
                      @RequestParam(required = false) @ApiParam("状态") final String statusStr,
                      @RequestParam(required = false) @ApiParam("项目编号") final String projectCode,
                      @RequestParam(required = false) @ApiParam("项目名称") final String projectName,
                      @RequestParam(required = false) @ApiParam("需方产品部门") final String requireProductOrgIdStr,
                      @RequestParam(required = false) @ApiParam("供方产品部门") final String provideProductOrgIdStr,
                      @RequestParam(required = false) @ApiParam("需方对接人") final String requireUserName,
                      @RequestParam(required = false) @ApiParam("供方对接人") final String provideUserName,
                      @RequestParam(required = false) @ApiParam("是否为我的申请单") final Boolean likeMe,
                      @RequestParam(required = false) @ApiParam("排序") final String orderParam,
                      @RequestParam(required = false) @ApiParam("排序类型(1:升序；2：降序)") final Integer orderType) {
        Map<String, Object> param = new HashMap<>();
        param.put("swapExecuteCode", swapExecuteCode);
        param.put("swapExecuteMonth", swapExecuteMonth);
        param.put("statusStr", statusStr);
        param.put("projectCode", projectCode);
        param.put("projectName",projectName);
        param.put("requireProductOrgIdStr",requireProductOrgIdStr);
        param.put("provideProductOrgIdStr", provideProductOrgIdStr);
        param.put("requireUserName", requireUserName);
        param.put("provideUserName", provideUserName);
        param.put("likeMe", likeMe);
        param.put("orderParam", orderParam);
        param.put("orderType", orderType);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "swapExecute/export/excel", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Map<String,Object>> dataResponse = JSON.parseObject(res,new TypeReference<DataResponse<Map<String,Object>>>(){});
        Map<String, Object> resultMap = dataResponse.getData();
        JSONArray list = (JSONArray) resultMap.get("list");
        if (list!=null){
            StringBuffer fileName = new StringBuffer();
            fileName.append("内部调剂执行_"+ DateUtils.format(new Date(),"yyyyMMddHHmmss"));
            fileName.append(".xls");
            List<SwapExecuteExcelVO> swapExecuteExcelVO = JSONObject.parseArray(list.toString(),SwapExecuteExcelVO.class);
            for (int i=0;i<swapExecuteExcelVO.size();i++){
                swapExecuteExcelVO.get(i).setNumber(i+1);
                String approvedDate = swapExecuteExcelVO.get(i).getApprovedDate();
                if(StringUtils.isNotEmpty(approvedDate)){
                    if(approvedDate.length()>10){
                        swapExecuteExcelVO.get(i).setApprovedDate(approvedDate.substring(0,10));
                    }else{
                        swapExecuteExcelVO.get(i).setApprovedDate(approvedDate);
                    }

                }
            }
            Workbook workbook = ExportExcelUtil.buildDefaultSheet(swapExecuteExcelVO,SwapExecuteExcelVO.class, null, "内部调剂执行", Boolean.TRUE);
            ExportExcelUtil.downLoadExcel(fileName.toString(),response,workbook);
        }

        /*return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<SwapExecuteDTO>>>() {
        });*/
    }



    @ApiOperation(value = "调剂执行统计查询")
    @GetMapping("countSwapData")
    public Response countSwapData(@RequestParam(required = false) @ApiParam("截止月份") final String cutOffMonth,
                                  @RequestParam(required = false) @ApiParam("状态") final String statusStr,
                         @RequestParam(required = false) @ApiParam("是否为我的申请单") final Boolean likeMe,
                         @RequestParam(required = false) @ApiParam("排序") final String orderParam,
                         @RequestParam(required = false) @ApiParam("排序类型(1:升序；2：降序)") final Integer orderType,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        Map<String, Object> param = new HashMap<>();
        param.put("cutOffMonth", cutOffMonth);
        param.put("statusStr", statusStr);
        param.put("likeMe", likeMe);
        param.put("orderParam", orderParam);
        param.put("orderType", orderType);
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "swapExecute/countSwapData", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<SwapExecuteDTO>>>() {
        });
    }

    @ApiOperation(value = "调剂执行汇总导出")
    @GetMapping("export/countSwapData")
    public void exportCountSwapData(HttpServletResponse response, @RequestParam(required = false) @ApiParam("截止月份") final String cutOffMonth,
                                  @RequestParam(required = false) @ApiParam("状态") final String statusStr,
                                  @RequestParam(required = false) @ApiParam("是否为我的申请单") final Boolean likeMe,
                                  @RequestParam(required = false) @ApiParam("排序") final String orderParam,
                                  @RequestParam(required = false) @ApiParam("排序类型(1:升序；2：降序)") final Integer orderType) {
        Map<String, Object> param = new HashMap<>();
        param.put("cutOffMonth", cutOffMonth);
        param.put("statusStr", statusStr);
        param.put("likeMe", likeMe);
        param.put("orderParam", orderParam);
        param.put("orderType", orderType);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "swapExecute/export/countSwapData", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Map<String,Object>> dataResponse = JSON.parseObject(res,new TypeReference<DataResponse<Map<String,Object>>>(){});

        Map<String, Object> resultMap = dataResponse.getData();
        JSONArray list = (JSONArray) resultMap.get("list");

        if (list!=null){
            StringBuffer fileName = new StringBuffer();
            fileName.append("内部资源调剂汇总_"+ DateUtils.format(new Date(),"yyyyMMddHHmmss"));
            fileName.append(".xls");

            LinkedHashMap<String,String> map = new LinkedHashMap<>();
            map.put("division","事业部");
            map.put("produectDepartment","产品部门");
            map.put("contractAmount","当月调整");
            map.put("accruedContractAmount","累计调整");
            map.put("leftSwapReceiptAmount","当月调整");
            map.put("accruedLeftSwapReceiptAmount","累计调整");
            map.put("swapExecuteTotalAmount","当月调整");
            map.put("accruedSwapExecuteTotalAmount","累计调整");

            map.put("memberSwapCost","当月调整");
            map.put("accruedMemberSwapCost","累计调整");
            map.put("feeTotalCost","当月调整");
            map.put("accruedFeeTotalCost","累计调整");

            String[] title2 = {"事业部","产品部门","合同","合同","回款","回款","收入","收入","人工成本","人工成本","费用","费用"};
            Workbook workbook = ExcelUtil.writeExcel(list,map,"内部资源调剂汇总","内部资源调剂汇总",null,title2,fileName.toString());
            ExcelUtil.mergedRegion(workbook,0,1,1,2,null);//产品部门标题
            ExcelUtil.mergedRegion(workbook,0,0,1,list.size()+2,null);
            ExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
        }
    }

}
