package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.basedata.dto.UserCollectDto;
import com.midea.pam.common.basedata.entity.UserCollect;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("人员选择器收藏夹")
@RestController
@RequestMapping("userCollect")
public class UserCollectController extends ControllerHelper {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "根据收藏者userId获取收藏夹人员列表", response = UserCollectDto.class)
    @GetMapping({"listByCollecterUserId"})
    public Response selectList(@RequestParam String collecterUserId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("collecterUserId", collecterUserId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "userCollect/listByCollecterUserId", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<UserCollectDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<UserCollectDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "添加或者删除收藏夹人员")
    @PostMapping({"addOrDelCollectUserer"})
    public Response addProduct(@RequestBody UserCollectDto dto) {
        String url = String.format("%suserCollect/addOrDelCollectUserer", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<Boolean> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }
}
