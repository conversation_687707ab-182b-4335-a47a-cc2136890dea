package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.ctc.dto.InvoicePlanDetailDto;
import com.midea.pam.common.ctc.dto.InvoiceReceivableDto;
import com.midea.pam.common.ctc.excelVo.InvoicePlanDetailExcelVO;
import com.midea.pam.common.ctc.vo.InvoiceReceivableExcelVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/6/18
 * @description
 */
@Api("开票计划")
@RestController
@RequestMapping("statistics/invoicePlan")
public class InvoicePlanStaController extends ControllerHelper {
    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "列表导出", response = InvoiceReceivableDto.class)
    @GetMapping("exportList")
    public void export(HttpServletResponse response,
                          @RequestParam(required = false) @ApiParam(value = "超期状态") String expiredQuery,
                          @RequestParam(required = false) @ApiParam(value = "开票计划编号") String code,
                          @RequestParam(required = false) @ApiParam(value = "计划开票日期开始") String startDate,
                          @RequestParam(required = false) @ApiParam(value = "计划开票日期结束") String endDate,
                          @RequestParam(required = false) @ApiParam(value = "子合同编号") String contractCode,
                          @RequestParam(required = false) @ApiParam(value = "子合同名称") String contractName,
                          @RequestParam(required = false) @ApiParam(value = "合同状态") String contractStatusQuery,
                          @RequestParam(required = false) @ApiParam(value = "客户CRM编码") String customerCode,
                          @RequestParam(required = false) @ApiParam(value = "客户名称") String customerName,
                          @RequestParam(required = false) @ApiParam(value = "关联项目号") String projectCode,
                          @RequestParam(required = false) @ApiParam(value = "关联项目") String projectName,
                          @RequestParam(required = false) @ApiParam(value = "开票状态") String invoiceStatusQuery,
                          @RequestParam(required = false) @ApiParam(value = "业务实体") String ouIdQuery,
                          @RequestParam(required = false) @ApiParam(value = "合同关联的采购申请号") String applyCode,
                          @RequestParam(required = false) @ApiParam(value = "业务分类") String profitDepartmentName,
                          @RequestParam(required = false) @ApiParam(value = "业务类型id（多个）") String businessTypeIdQuery,
                          @RequestParam(required = false) Boolean isMe
                       ){
        final Map<String,Object> param = new HashMap<>();

        param.put("expiredQuery",expiredQuery);
        param.put("code",code);
        param.put("startDate",startDate);
        param.put("endDate",endDate);
        param.put("contractCode",contractCode);
        param.put("contractName",contractName);
        param.put("contractStatusQuery",contractStatusQuery);
        param.put("customerCode",customerCode);
        param.put("customerName",customerName);
        param.put("projectCode",projectCode);
        param.put("projectName",projectName);
        param.put("invoiceStatusQuery",invoiceStatusQuery);
        param.put("ouIdQuery",ouIdQuery);
        param.put("me",isMe);
        param.put("applyCode",applyCode);
        param.put("profitDepartmentName", profitDepartmentName);
        param.put("businessTypeIdQuery", businessTypeIdQuery);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(),"statistics/invoicePlan/export",param);
        String res = restTemplate.getForEntity(url, String.class).getBody();

        DataResponse<Map<String,Object>> dataResponse = JSON.parseObject(res,new TypeReference<DataResponse<Map<String,Object>>>(){});
        Map<String, Object> resultMap = dataResponse.getData();
        JSONArray invoicePlanDetailDtoArr = (JSONArray) resultMap.get("list");

        if (invoicePlanDetailDtoArr!=null){
            StringBuffer fileName = new StringBuffer();
            fileName.append("开票计划_"+ DateUtils.format(new Date(),"yyyyMMddHHmmss"));
            fileName.append(".xls");

            List<InvoicePlanDetailExcelVO> invoicePlanDetailExcelVOS = JSONObject.parseArray(invoicePlanDetailDtoArr.toString(), InvoicePlanDetailExcelVO.class);
            for (int i=0;i<invoicePlanDetailExcelVOS.size();i++){
                invoicePlanDetailExcelVOS.get(i).setNumber(i+1);
            }
            Workbook workbook = ExportExcelUtil.buildDefaultSheet(invoicePlanDetailExcelVOS, InvoicePlanDetailExcelVO.class, null, "开票计划", Boolean.TRUE);
            ExportExcelUtil.downLoadExcel(fileName.toString(),response,workbook);
        }
    }

    @ApiOperation(value = "列表查询", response = InvoiceReceivableDto.class)
    @GetMapping("query")
    public Response query(@RequestParam(required = false) @ApiParam(value = "超期状态") String expiredQuery,
                          @RequestParam(required = false) @ApiParam(value = "开票计划编号") String code,
                          @RequestParam(required = false) @ApiParam(value = "计划开票日期开始") String startDate,
                          @RequestParam(required = false) @ApiParam(value = "计划开票日期结束") String endDate,
                          @RequestParam(required = false) @ApiParam(value = "子合同编号") String contractCode,
                          @RequestParam(required = false) @ApiParam(value = "子合同名称") String contractName,
                          @RequestParam(required = false) @ApiParam(value = "合同状态") String contractStatusQuery,
                          @RequestParam(required = false) @ApiParam(value = "客户CRM编码") String customerCode,
                          @RequestParam(required = false) @ApiParam(value = "客户名称") String customerName,
                          @RequestParam(required = false) @ApiParam(value = "关联项目号") String projectCode,
                          @RequestParam(required = false) @ApiParam(value = "关联项目") String projectName,
                          @RequestParam(required = false) @ApiParam(value = "开票状态") String invoiceStatusQuery,
                          @RequestParam(required = false) @ApiParam(value = "业务实体") String ouIdQuery,
                          @RequestParam(required = false) @ApiParam(value = "开票计划行id（多个）") String planDetailIdQuery,
                          @RequestParam(required = false) Boolean isMe,
                          @RequestParam(required = false) @ApiParam(value = "合同关联的采购申请号") String applyCode,
                          @RequestParam(required = false) @ApiParam(value = "业务分类") String profitDepartmentName,
                          @RequestParam(required = false) @ApiParam(value = "业务类型id（多个）") String businessTypeIdQuery,
                          @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                          @RequestParam(required = false, defaultValue = "10") Integer pageSize){

        final Map<String,Object> param = new HashMap<>();

        param.put("expiredQuery",expiredQuery);
        param.put("code",code);
        param.put("startDate",startDate);
        param.put("endDate",endDate);
        param.put("contractCode",contractCode);
        param.put("contractName",contractName);
        param.put("contractStatusQuery",contractStatusQuery);
        param.put("customerCode",customerCode);
        param.put("customerName",customerName);
        param.put("projectCode",projectCode);
        param.put("projectName",projectName);
        param.put("invoiceStatusQuery",invoiceStatusQuery);
        param.put("ouIdQuery",ouIdQuery);
        param.put("planDetailIdQuery",planDetailIdQuery);
        param.put("isMe",isMe);
        param.put("applyCode",applyCode);
        param.put("profitDepartmentName", profitDepartmentName);
        param.put("businessTypeIdQuery", businessTypeIdQuery);
        param.put("pageNum",pageNum);
        param.put("pageSize",pageSize);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(),"statistics/invoicePlan/query",param);
        String res = restTemplate.getForEntity(url,String.class).getBody();
        return JSON.parseObject(res,new TypeReference<DataResponse<PageInfo<InvoicePlanDetailDto>>>(){});
    }

    @ApiOperation(value = "详情查询", response = InvoiceReceivableDto.class)
    @GetMapping("queryById")
    public Response queryById(@RequestParam @ApiParam(value = "开票申请行id") Long id){

        final Map<String,Object> param = new HashMap<>();
        param.put("id",id);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(),"statistics/invoicePlan/queryById",param);
        String res = restTemplate.getForEntity(url,String.class).getBody();
        return JSON.parseObject(res,new TypeReference<DataResponse<InvoicePlanDetailDto>>(){});
    }
}
