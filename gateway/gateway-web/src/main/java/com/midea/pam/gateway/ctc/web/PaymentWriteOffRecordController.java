package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.ctc.entity.PaymentWriteOffRecord;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

@Api("核销记录")
@RestController
@RequestMapping("paymentWriteOffRecord")
public class PaymentWriteOffRecordController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "手工核销")
    @PutMapping("submit")
    public Response submit(@RequestBody @ApiParam(name = "writeOffRecords", value = "核销对象") PaymentWriteOffRecord[] writeOffRecords,
                           @RequestParam(required = false) Long userId) {
        String url = String.format("%spaymentWriteOffRecord/submit", ModelsEnum.CTC.getBaseUrl());
        if (userId != null) {
            url = url + "?userId=" + userId;
        }
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, new HttpEntity<PaymentWriteOffRecord[]>(writeOffRecords), String.class);
        String res = cleanStr(responseEntity.getBody());
        DataResponse<String> response = JSON.parseObject(res, new TypeReference<DataResponse<String>>(){});
        return response;
    }

    @ApiOperation(value = "撤销核销")
    @GetMapping("cancel")
    public Response cancel(@RequestParam @ApiParam(name = "writeOffRecordId", value = "核销对象Id") Long writeOffRecordId,
                           @RequestParam(required = false) @ApiParam(value = "入账日期") Date happenDate) {
        final Map<String, Object> param = new HashMap<>();
        param.put("writeOffRecordId", writeOffRecordId);
        if(happenDate != null){
            param.put("happenDate", happenDate.getTime());
        }
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "paymentWriteOffRecord/cancel", param);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<String> response = JSON.parseObject(res, new TypeReference<DataResponse<String>>(){});
        return response;
    }

    @ApiOperation(value = "撤销核销")
    @GetMapping("delete")
    public Response delete(@RequestParam @ApiParam(name = "writeOffRecordId", value = "核销对象Id") Long writeOffRecordId,
                           @RequestParam(required = false) @ApiParam(value = "入账日期") Date happenDate,
                           @RequestParam(required = false) @ApiParam(value = "发票类型") String invoiceType) {
        final Map<String, Object> param = new HashMap<>();
        param.put("writeOffRecordId", writeOffRecordId);
        if(happenDate != null){
            param.put("happenDate", happenDate.getTime());
        }
        if(StringUtils.isNotEmpty(invoiceType)){
            param.put("invoiceType", invoiceType);
        }
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "paymentWriteOffRecord/delete", param);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<String> response = JSON.parseObject(res, new TypeReference<DataResponse<String>>(){});
        return response;
    }

    @ApiOperation(value = "重新核销")
    @GetMapping("resend")
    public Response resend(@RequestParam @ApiParam(name = "writeOffRecordId", value = "核销对象Id") Long writeOffRecordId,
                           @RequestParam(required = false) @ApiParam(value = "入账日期") Date happenDate,
                           @RequestParam(required = false) @ApiParam(value = "发票类型") String invoiceType) {
        final Map<String, Object> param = new HashMap<>();
        param.put("writeOffRecordId", writeOffRecordId);
        if(happenDate != null){
            param.put("happenDate", happenDate.getTime());
        }
        if(StringUtils.isNotEmpty(invoiceType)){
            param.put("invoiceType", invoiceType);
        }
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "paymentWriteOffRecord/resend", param);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<String> response = JSON.parseObject(res, new TypeReference<DataResponse<String>>(){});
        return response;
    }

//    @ApiOperation(value = "重新同步")
//    @GetMapping("testPositiveAndNegativeEsb")
//    public void testPositiveAndNegativeEsb(@RequestParam @ApiParam(value = "核销对象id") Long id){
//        final Map<String, Object> param = new HashMap<>();
//        param.put("id", id);
//        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "paymentWriteOffRecord/testPositiveAndNegativeEsb", param);
//        String res = restTemplate.getForObject(url, String.class);
//    }
}






