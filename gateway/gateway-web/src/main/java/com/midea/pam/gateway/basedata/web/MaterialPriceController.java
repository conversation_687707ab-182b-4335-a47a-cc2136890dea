package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.MaterialPriceConfigDetailExecuteDto;
import com.midea.pam.common.basedata.dto.MaterialPriceDetailDto;
import com.midea.pam.common.basedata.dto.MaterialPriceDto;
import com.midea.pam.common.basedata.excelVo.MaterialPriceExportExcelVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.BeanConverter;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Api("物料价格")
@RequestMapping({"materialPrice"})
public class MaterialPriceController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "物料价格查询-列表")
    @GetMapping("page")
    public Response page(
            @RequestParam(required = false) @ApiParam("模糊:PAM物料编码") String pamCode,
            @RequestParam(required = false) @ApiParam("模糊:ERP物料编码") String erpCode,
            @RequestParam(required = false) @ApiParam("模糊:物料名称") String materialName,
            @RequestParam(required = false) @ApiParam("模糊:物料描述") String materielDescr,
            @RequestParam(required = false) @ApiParam("物料状态（下拉多选）") String materialStatusStr,
            @RequestParam(required = false) @ApiParam("模糊:价格类型名称") String configHeaderName,
            @RequestParam(required = false) @ApiParam("更新日期开始") String updateStartTime,
            @RequestParam(required = false) @ApiParam("更新日期开始") String updateEndTime,
            @RequestParam(required = false) @ApiParam("模糊:物料大类") String materialClassification,
            @RequestParam(required = false) @ApiParam("模糊:物料中类") String codingMiddleClass,
            @RequestParam(required = false) @ApiParam("模糊:物料小类") String materielType,
            @RequestParam(required = false) @ApiParam("库存组织ID（下拉多选）") String organizationIdStr,
            @RequestParam(required = false) @ApiParam("是否锁定") Integer priceIsLock,
            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        Map<String, Object> param = new HashMap<>();
        param.put("pamCode", pamCode);
        param.put("erpCode", erpCode);
        param.put("materialName", materialName);
        param.put("materielDescr", materielDescr);
        param.put("materialStatusStr", materialStatusStr);
        param.put("configHeaderName", configHeaderName);
        param.put("updateStartTime", updateStartTime);
        param.put("updateEndTime", updateEndTime);
        param.put("materialClassification", materialClassification);
        param.put("codingMiddleClass", codingMiddleClass);
        param.put("materielType", materielType);
        param.put("organizationIdStr", organizationIdStr);
        param.put("priceIsLock", priceIsLock);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "materialPrice/page", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<MaterialPriceDto>>>() {
        });
    }

    @ApiOperation(value = "物料价格查询-列表导出")
    @GetMapping("export")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false) @ApiParam("模糊:PAM物料编码") String pamCode,
                       @RequestParam(required = false) @ApiParam("模糊:ERP物料编码") String erpCode,
                       @RequestParam(required = false) @ApiParam("模糊:物料名称") String materialName,
                       @RequestParam(required = false) @ApiParam("模糊:物料描述") String materielDescr,
                       @RequestParam(required = false) @ApiParam("物料状态（下拉多选）") String materialStatusStr,
                       @RequestParam(required = false) @ApiParam("模糊:价格类型名称") String configHeaderName,
                       @RequestParam(required = false) @ApiParam("更新日期开始") String updateStartTime,
                       @RequestParam(required = false) @ApiParam("更新日期开始") String updateEndTime,
                       @RequestParam(required = false) @ApiParam("模糊:物料大类") String materialClassification,
                       @RequestParam(required = false) @ApiParam("模糊:物料中类") String codingMiddleClass,
                       @RequestParam(required = false) @ApiParam("模糊:物料小类") String materielType,
                       @RequestParam(required = false) @ApiParam("库存组织ID（下拉多选）") String organizationIdStr,
                       @RequestParam(required = false) @ApiParam("是否锁定") Integer priceIsLock) {
        Map<String, Object> param = new HashMap<>();
        param.put("pamCode", pamCode);
        param.put("erpCode", erpCode);
        param.put("materialName", materialName);
        param.put("materielDescr", materielDescr);
        param.put("materialStatusStr", materialStatusStr);
        param.put("configHeaderName", configHeaderName);
        param.put("updateStartTime", updateStartTime);
        param.put("updateEndTime", updateEndTime);
        param.put("materialClassification", materialClassification);
        param.put("codingMiddleClass", codingMiddleClass);
        param.put("materielType", materielType);
        param.put("organizationIdStr", organizationIdStr);
        param.put("priceIsLock", priceIsLock);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "materialPrice/exportList", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<MaterialPriceDto>> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<List<MaterialPriceDto>>>() {
        });
        List<MaterialPriceDto> detailDtos = dataResponse.getData();
        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("物料价格查询_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");

        List<MaterialPriceExportExcelVo> exportExcelVos = null;
        if (ListUtils.isNotEmpty(detailDtos)) {
            exportExcelVos = BeanConverter.copy(detailDtos, MaterialPriceExportExcelVo.class);
            int number = 1;
            for (MaterialPriceExportExcelVo exportExcelVo : exportExcelVos) {
                exportExcelVo.setNum(number++);
            }
        }
        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(exportExcelVos, MaterialPriceExportExcelVo.class, null,
                "物料价格查询", true);
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    @ApiOperation(value = "物料价格-详情")
    @GetMapping("view/{id}")
    public Response view(@PathVariable Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "materialPrice/view", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<MaterialPriceDetailDto>>() {
        });
    }

    @ApiOperation(value = "物料价格-详情-获取执行快照")
    @GetMapping("configDetail/{executeId}")
    public Response configDetail(@PathVariable Long executeId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("executeId", executeId);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "materialPrice/configDetail", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<MaterialPriceConfigDetailExecuteDto>>() {
        });
    }

    @ApiOperation(value = "物料价格-锁定")
    @PostMapping("lock")
    public Response lock(@RequestBody @ApiParam("物料价格id集合") List<Long> ids) {
        String url = String.format("%smaterialPrice/lock", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, ids, String.class);
        return JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "物料价格-解锁")
    @PostMapping("unLock")
    public Response unLock(@RequestBody @ApiParam("物料价格id集合") List<Long> ids) {
        String url = String.format("%smaterialPrice/unLock", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, ids, String.class);
        return JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "主动调用生成物料价格")
    @GetMapping("active/{configHeaderId}")
    public Response active(@PathVariable Long configHeaderId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("configHeaderId", configHeaderId);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "materialPrice/active", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "定时测试")
    @GetMapping("/testJob")
    public Response testJob() {
        final Map<String, Object> params = new HashMap<>();
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/materialPrice/testJob", params);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

}
