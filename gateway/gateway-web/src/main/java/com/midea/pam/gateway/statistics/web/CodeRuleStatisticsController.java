package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;

import com.midea.pam.common.ctc.dto.CodeRuleDto;
import com.midea.pam.common.ctc.dto.InvoicePlanDetailDto;
import com.midea.pam.common.ctc.entity.CodeRule;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Api("编码规则")
@RestController
@RequestMapping("statistics/codeRule")
public class CodeRuleStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = " 分页编码规则")
    @GetMapping("page")
    public Response page(@RequestParam(required = false) @ApiParam(value = "使用单位") String unitName,
                         @RequestParam(required = false) @ApiParam(value = "规则名称") String ruleName,
                         @RequestParam(required = false) @ApiParam(value = "编码规则类型(多选)") String ruleTypeStr,
                         @RequestParam(required = false) @ApiParam(value = "是否完成") Integer isComplete,
                         @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        Map<String, Object> param = new HashMap<>();
        param.put("unitName", unitName);
        param.put("ruleName", ruleName);
        param.put("ruleTypeStr",ruleTypeStr);
        param.put("isComplete", isComplete);
        param.put("pageNum",pageNum);
        param.put("pageSize",pageSize);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/codeRule/page", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<CodeRuleDto>>>() {
        });
    }
}
