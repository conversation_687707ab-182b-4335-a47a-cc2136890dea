package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.constants.WbsBudgetFieldConstant;
import com.midea.pam.common.ctc.cache.WbsTemplateRuleCache;
import com.midea.pam.common.ctc.cache.WbsTemplateRuleDetailCache;
import com.midea.pam.common.ctc.dto.AsyncRequestResultDto;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsDto;
import com.midea.pam.common.ctc.excelVo.MilepostDesignPlanDetailWbsExportVo;
import com.midea.pam.common.ctc.excelVo.ProjectWbsReceiptsBudgetChangeHistoryExportApproveVo;
import com.midea.pam.common.ctc.excelVo.ProjectWbsReceiptsBudgetChangeHistorySumApproveExportVo;
import com.midea.pam.common.ctc.excelVo.ProjectWbsReceiptsBudgetExportApproveVo;
import com.midea.pam.common.ctc.excelVo.ProjectWbsReceiptsBudgetExportVo;
import com.midea.pam.common.ctc.excelVo.ProjectWbsReceiptsBudgetSumApproveExportVo;
import com.midea.pam.common.ctc.excelVo.ProjectWbsReceiptsBudgetSumExportVo;
import com.midea.pam.common.ctc.excelVo.ProjectWbsReceiptsDetailExcelVo;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.statistics.excelVo.ProjectWbsCostExcelVo;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExcelUtil;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.ProjectWbsBudgetUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.common.util.cache.WbsTemplateRuleDetailCacheUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.utils.FileUtil;
import com.midea.pam.gateway.service.FormInstanceService;
import com.midea.pam.gateway.service.MipWorkflowInnerService;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Api("详细设计单据")
@RestController
@RequestMapping({"projectWbsRequirementPublish", "projectWbsReceipts"})
public class ProjectWbsReceiptsController extends ControllerHelper {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private MipWorkflowInnerService mipWorkflowInnerService;
    @Resource
    private FormInstanceService formInstanceService;


    /**
     * 查看详情/审批页面/新建界面第三步详情
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "查看详情", response = ProjectWbsReceiptsDto.class)
    @GetMapping("findRequirementDetail")
    public DataResponse findDetail(@ApiParam("详细设计单据id") @RequestParam Long id) {
        final String url = String.format("%sprojectWbsReceipts/findRequirementDetail?id=%s",
                ModelsEnum.CTC.getBaseUrl(), id);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectWbsReceiptsDto>>() {
        });
    }

    /**
     * 详设列表筛选
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "搜索", response = ProjectWbsReceiptsDto.class)
    @PostMapping("search")
    public DataResponse search(@RequestBody ProjectWbsReceiptsDto dto) {
        final String url = String.format("%sprojectWbsReceipts/search", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<ProjectWbsReceiptsDto>>() {
        });
    }

    /**
     * 下一步 新建界面第一步
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "下一步", response = ProjectWbsReceiptsDto.class)
    @PostMapping("check")
    public DataResponse check(@RequestBody ProjectWbsReceiptsDto dto) {
        final String url = String.format("%sprojectWbsReceipts/check", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<ProjectWbsReceiptsDto>>() {
        });
    }

    /**
     * 保存 新建界面第二步
     *
     * @param dto
     * @return 详细设计单据id
     */
    @ApiOperation(value = "保存", response = Long.class)
    @PostMapping("save")
    public DataResponse save(@RequestBody ProjectWbsReceiptsDto dto) {
        final String url = String.format("%sprojectWbsReceipts/save", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<ProjectWbsReceiptsDto>>() {
        });
    }

    /**
     * 提交 新建界面第三步
     *
     * @param dto
     * @return 流程headerId（详细设计单据id）
     */
    @ApiOperation(value = "提交", response = ProjectWbsReceiptsDto.class)
    @PostMapping("submit")
    public DataResponse submit(@RequestBody ProjectWbsReceiptsDto dto) {
        final String url = String.format("%sprojectWbsReceipts/submit", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<ProjectWbsReceiptsDto>>() {
        });
    }

    /**
     * 退回 新建界面第三步
     *
     * @param id
     * @return 详细设计单据id
     */
    @ApiOperation(value = "退回", response = Integer.class)
    @PutMapping("reject/{id}")
    public DataResponse reject(@ApiParam("详细设计单据id") @PathVariable Long id) {
        String url = String.format("%sprojectWbsReceipts/reject/" + id, ModelsEnum.CTC.getBaseUrl());
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
    }

    /**
     * 作废 新建界面第二步(草稿状态)
     *
     * @param id
     * @return 详细设计单据id
     */
    @ApiOperation(value = "作废", response = Integer.class)
    @PutMapping("cancel/{id}")
    public DataResponse cancel(@ApiParam("详细设计单据id") @PathVariable Long id) {
        String url = String.format("%sprojectWbsReceipts/cancel/" + id, ModelsEnum.CTC.getBaseUrl());
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        //同时作废工作流
        if (responseEntity.getStatusCodeValue() == 0) {
            mipWorkflowInnerService.draftAbandon("wbsDesignPlanNoPurchaseSubmitApp", id);
            mipWorkflowInnerService.draftAbandon("wbsReceiptsApp", id);
        }
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
    }

    /**
     * 查看详设发布单据详情/审批页面/
     *
     * @param id
     * @return 详细设计单据
     */
    @ApiOperation(value = "查看详设发布单据详情", response = ProjectWbsReceiptsDto.class)
    @GetMapping("findDetailForSubmit")
    public DataResponse findDetailForSubmit(@ApiParam("详细设计单据id") @RequestParam Long id) {
        final String url = String.format("%sprojectWbsReceipts/findDetailForSubmit?id=%s",
                ModelsEnum.CTC.getBaseUrl(), id);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectWbsReceiptsDto>>() {
        });
    }

    @ApiOperation(value = "查看非层级发布单据详情", response = ProjectWbsReceiptsDto.class)
    @GetMapping("findNonHierarchicalSubmitDetail")
    public DataResponse findNonHierarchicalSubmitDetail(@ApiParam("详细设计单据id") @RequestParam Long id) {
        final String url = String.format("%sprojectWbsReceipts/findNonHierarchicalSubmitDetail?id=%s",
                ModelsEnum.CTC.getBaseUrl(), id);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectWbsReceiptsDto>>() {
        });
    }

    /**
     * 需求发布单据详情导出
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "需求发布单据详情导出")
    @GetMapping("detail/export")
    public void detailExport(HttpServletResponse response, @ApiParam("需求发布单据id") @RequestParam Long id) {
        String url = String.format("%sprojectWbsReceipts/detail/export?id=%s", ModelsEnum.CTC.getBaseUrl(), id);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<Map<String, Object>>>() {
                });

        Map<String, Object> resultMap = dataResponse.getData();
        JSONArray receiptsDetailArr = (JSONArray) resultMap.get("receiptsDetailList");
        JSONArray designPlanDetailArr = (JSONArray) resultMap.get("designPlanDetailList");
        JSONArray budgetSummaryArr = (JSONArray) resultMap.get("budgetSummaryList");
        JSONArray budgetDetailArr = (JSONArray) resultMap.get("budgetDetailList");

        List<ProjectWbsReceiptsDetailExcelVo> receiptsDetailExcelList = new ArrayList<>();
        List<MilepostDesignPlanDetailWbsExportVo> designPlanDetailExcelList = new ArrayList<>();
        List<ProjectWbsReceiptsBudgetSumExportVo> budgetSummaryExcelList = new ArrayList<>();
        List<ProjectWbsReceiptsBudgetExportVo> budgetDetailExcelList = new ArrayList<>();

        if (receiptsDetailArr != null) {
            receiptsDetailExcelList = JSONObject.parseArray(receiptsDetailArr.toJSONString(),
                    ProjectWbsReceiptsDetailExcelVo.class);
        }
        if (designPlanDetailArr != null) {
            designPlanDetailExcelList = JSONObject.parseArray(designPlanDetailArr.toJSONString(),
                    MilepostDesignPlanDetailWbsExportVo.class);
        }
        if (budgetSummaryArr != null) {
            budgetSummaryExcelList = JSONObject.parseArray(budgetSummaryArr.toJSONString(),
                    ProjectWbsReceiptsBudgetSumExportVo.class);
            for (int i = 0; i < budgetSummaryExcelList.size(); i++) {
                budgetSummaryExcelList.get(i).setNum(i + 1);
            }
        }
        if (budgetDetailArr != null) {
            budgetDetailExcelList = JSONObject.parseArray(budgetDetailArr.toJSONString(),
                    ProjectWbsReceiptsBudgetExportVo.class);
            for (int i = 0; i < budgetDetailExcelList.size(); i++) {
                budgetDetailExcelList.get(i).setNum(i + 1);
            }
        }

        Workbook workbook = ExportExcelUtil.buildDefaultSheet(receiptsDetailExcelList,
                ProjectWbsReceiptsDetailExcelVo.class, null, "基本信息", true);
        ExportExcelUtil.addSheet(workbook, designPlanDetailExcelList,
                MilepostDesignPlanDetailWbsExportVo.class, null, "明细内容", true);
        ExportExcelUtil.addSheet(workbook, budgetSummaryExcelList,
                ProjectWbsReceiptsBudgetSumExportVo.class, null, "预算信息-汇总", true);
        ExportExcelUtil.addSheet(workbook, budgetDetailExcelList,
                ProjectWbsReceiptsBudgetExportVo.class, null, "预算信息-明细", true);

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("需求发布-详细设计单据-详情");
        fileName.append(".xls");
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    /**
     * 需求发布单据审批页导出
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "需求发布单据审批页导出")
    @GetMapping("approve/export")
    public void approveExport(HttpServletResponse response, @ApiParam("需求发布单据id") @RequestParam Long id) {
        String url = String.format("%sprojectWbsReceipts/detail/export?id=%s", ModelsEnum.CTC.getBaseUrl(), id);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<Map<String, Object>>>() {
                });

        Map<String, Object> resultMap = dataResponse.getData();
        JSONArray receiptsDetailArr = (JSONArray) resultMap.get("receiptsDetailList");
        JSONArray designPlanDetailArr = (JSONArray) resultMap.get("designPlanDetailList");
        JSONArray budgetSummaryArr = (JSONArray) resultMap.get("budgetSummaryList");
        JSONArray budgetDetailArr = (JSONArray) resultMap.get("budgetDetailList");

        List<ProjectWbsReceiptsDetailExcelVo> receiptsDetailExcelList = new ArrayList<>();
        List<MilepostDesignPlanDetailWbsExportVo> designPlanDetailExcelList = new ArrayList<>();
        List<ProjectWbsReceiptsBudgetSumApproveExportVo> budgetSummaryExcelList = new ArrayList<>();
        List<ProjectWbsReceiptsBudgetExportApproveVo> budgetDetailExcelList = new ArrayList<>();

        if (receiptsDetailArr != null) {
            receiptsDetailExcelList = JSONObject.parseArray(receiptsDetailArr.toJSONString(),
                    ProjectWbsReceiptsDetailExcelVo.class);
        }
        if (designPlanDetailArr != null) {
            designPlanDetailExcelList = JSONObject.parseArray(designPlanDetailArr.toJSONString(),
                    MilepostDesignPlanDetailWbsExportVo.class);
        }
        if (budgetSummaryArr != null) {
            budgetSummaryExcelList = JSONObject.parseArray(budgetSummaryArr.toJSONString(),
                    ProjectWbsReceiptsBudgetSumApproveExportVo.class);
            for (int i = 0; i < budgetSummaryExcelList.size(); i++) {
                budgetSummaryExcelList.get(i).setNum(i + 1);
            }
        }
        if (budgetDetailArr != null) {
            budgetDetailExcelList = JSONObject.parseArray(budgetDetailArr.toJSONString(),
                    ProjectWbsReceiptsBudgetExportApproveVo.class);
            for (int i = 0; i < budgetDetailExcelList.size(); i++) {
                budgetDetailExcelList.get(i).setNum(i + 1);
            }
        }

        Workbook workbook = ExportExcelUtil.buildDefaultSheet(receiptsDetailExcelList,
                ProjectWbsReceiptsDetailExcelVo.class, null, "基本信息", true);
        ExportExcelUtil.addSheet(workbook, designPlanDetailExcelList,
                MilepostDesignPlanDetailWbsExportVo.class, null, "明细内容", true);
        ExportExcelUtil.addSheet(workbook, budgetSummaryExcelList,
                ProjectWbsReceiptsBudgetSumApproveExportVo.class, null, "预算信息-汇总", true);
        ExportExcelUtil.addSheet(workbook, budgetDetailExcelList,
                ProjectWbsReceiptsBudgetExportApproveVo.class, null, "预算信息-明细", true);

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("需求发布-详细设计单据-审批");
        fileName.append(".xls");
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    /**
     * 详设变更单据审批页导出
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "详设变更单据审批页导出")
    @GetMapping("approve/changeExport")
    public void changeExport(HttpServletResponse response, @ApiParam("单据id") @RequestParam Long id) {
        String url = String.format("%sprojectWbsReceipts/detail/export?id=%s", ModelsEnum.CTC.getBaseUrl(), id);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<Map<String, Object>>>() {
                });

        Map<String, Object> resultMap = dataResponse.getData();
        JSONArray receiptsDetailArr = (JSONArray) resultMap.get("receiptsDetailList");
        JSONArray designPlanDetailArr = (JSONArray) resultMap.get("designPlanDetailList");
        JSONArray budgetSummaryArr = (JSONArray) resultMap.get("budgetSummaryList");
        JSONArray budgetDetailArr = (JSONArray) resultMap.get("budgetDetailList");

        List<ProjectWbsReceiptsDetailExcelVo> receiptsDetailExcelList = new ArrayList<>();
        List<MilepostDesignPlanDetailWbsExportVo> designPlanDetailExcelList = new ArrayList<>();
        List<ProjectWbsReceiptsBudgetChangeHistorySumApproveExportVo> budgetSummaryExcelList = new ArrayList<>();
        List<ProjectWbsReceiptsBudgetChangeHistoryExportApproveVo> budgetDetailExcelList = new ArrayList<>();

        if (receiptsDetailArr != null) {
            receiptsDetailExcelList = JSONObject.parseArray(receiptsDetailArr.toJSONString(),
                    ProjectWbsReceiptsDetailExcelVo.class);
        }
        if (designPlanDetailArr != null) {
            designPlanDetailExcelList = JSONObject.parseArray(designPlanDetailArr.toJSONString(),
                    MilepostDesignPlanDetailWbsExportVo.class);
        }
        if (budgetSummaryArr != null) {
            budgetSummaryExcelList = JSONObject.parseArray(budgetSummaryArr.toJSONString(),
                    ProjectWbsReceiptsBudgetChangeHistorySumApproveExportVo.class);
            for (int i = 0; i < budgetSummaryExcelList.size(); i++) {
                budgetSummaryExcelList.get(i).setNum(i + 1);
            }
        }
        if (budgetDetailArr != null) {
            budgetDetailExcelList = JSONObject.parseArray(budgetDetailArr.toJSONString(),
                    ProjectWbsReceiptsBudgetChangeHistoryExportApproveVo.class);
            for (int i = 0; i < budgetDetailExcelList.size(); i++) {
                budgetDetailExcelList.get(i).setNum(i + 1);
            }
        }

        Workbook workbook = ExportExcelUtil.buildDefaultSheet(receiptsDetailExcelList,
                ProjectWbsReceiptsDetailExcelVo.class, null, "基本信息", true);
        ExportExcelUtil.addSheet(workbook, designPlanDetailExcelList,
                MilepostDesignPlanDetailWbsExportVo.class, null, "明细内容", true);
        ExportExcelUtil.addSheet(workbook, budgetSummaryExcelList,
                ProjectWbsReceiptsBudgetChangeHistorySumApproveExportVo.class, null, "预算信息-汇总", true);
        ExportExcelUtil.addSheet(workbook, budgetDetailExcelList,
                ProjectWbsReceiptsBudgetChangeHistoryExportApproveVo.class, null, "预算信息-明细", true);

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("详细设计变更-详细设计单据-审批");
        fileName.append(".xls");
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    /**
     * 单据详情导出
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "详细设计单据详情导出")
    @GetMapping("detail/submitExport")
    public void submitExport(HttpServletResponse response, @ApiParam("详细设计单据id") @RequestParam Long id) {
        String url = String.format("%sprojectWbsReceipts/detail/export?id=%s", ModelsEnum.CTC.getBaseUrl(), id);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<Map<String, Object>>>() {
                });

        Map<String, Object> resultMap = dataResponse.getData();
        JSONArray receiptsDetailArr = (JSONArray) resultMap.get("receiptsDetailList");
        JSONArray designPlanDetailArr = (JSONArray) resultMap.get("designPlanDetailList");

        List<ProjectWbsReceiptsDetailExcelVo> receiptsDetailExcelList = new ArrayList<>();
        List<MilepostDesignPlanDetailWbsExportVo> designPlanDetailExcelList = new ArrayList<>();

        if (receiptsDetailArr != null) {
            receiptsDetailExcelList = JSONObject.parseArray(receiptsDetailArr.toJSONString(),
                    ProjectWbsReceiptsDetailExcelVo.class);
        }
        if (designPlanDetailArr != null) {
            designPlanDetailExcelList = JSONObject.parseArray(designPlanDetailArr.toJSONString(),
                    MilepostDesignPlanDetailWbsExportVo.class);
        }


        Workbook workbook = ExportExcelUtil.buildDefaultSheet(receiptsDetailExcelList,
                ProjectWbsReceiptsDetailExcelVo.class, null, "基本信息", true);
        ExportExcelUtil.addSheet(workbook, designPlanDetailExcelList,
                MilepostDesignPlanDetailWbsExportVo.class, null, "明细内容", true);

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("详细设计发布-详细设计单据-详情");
        fileName.append(".xls");
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    @ApiOperation(value = "创建变更单据(异步)", response = ProjectWbsReceiptsDto.class)
    @PostMapping({"saveChangeReceiptsWithDetailAsync"})
    public Response saveChangeReceiptsWithDetailAsync(@RequestBody ProjectWbsReceiptsDto dto) {
        String url = String.format("%sprojectWbsReceipts/saveChangeReceiptsWithDetailAsync", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, dto, String.class).getBody();
        DataResponse<AsyncRequestResultDto> response = JSON.parseObject(res, new TypeReference<DataResponse<AsyncRequestResultDto>>() {
        });
        AsyncRequestResultDto data = null;
        if (response != null) {
            data = response.getData();
        }
        if (Objects.nonNull(data) && StringUtils.isNotEmpty(data.getOldHandleUserMip())) {
            //处理修改处理人后将修改前的处理人的流程实例作废
            Long formInstanceId = data.getMilepostDesignPlanDetailChangeRecordId();
            String formUrl = "mdpChangeTheChangeApp";
            Long companyId = SystemContext.getUnitId();
            String oldHandleUserMip = data.getOldHandleUserMip();
            //删除流程实例
            formInstanceService.logicDeleteAndMipWorkflowDraftAbandon(formInstanceId, formUrl, oldHandleUserMip, companyId);
        }
        return response;
    }

    /**
     * 查看详设变更单据详情/审批页面/
     *
     * @param id
     * @return 详细设计单据
     */
    @ApiOperation(value = "查看详设变更单据详情", response = ProjectWbsReceiptsDto.class)
    @GetMapping("findDetailForChange")
    public DataResponse findDetailForChange(@ApiParam("详细设计单据id") @RequestParam Long id) {
        final String url = String.format("%sprojectWbsReceipts/findDetailForChange?id=%s", ModelsEnum.CTC.getBaseUrl(), id);
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(600 * 1000);
        httpRequestFactory.setConnectTimeout(600 * 1000);
        httpRequestFactory.setReadTimeout(600 * 1000);
        restTemplate.setRequestFactory(httpRequestFactory);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectWbsReceiptsDto>>() {
        });
    }

    /**
     * 提交 新建界面第三步
     *
     * @param dto
     * @return 流程headerId（详细设计单据id）
     */
    @ApiOperation(value = "提交", response = Long.class)
    @PostMapping("submitChangeReceipts")
    public DataResponse submitChangeReceipts(@RequestBody ProjectWbsReceiptsDto dto) {
        final String url = String.format("%sprojectWbsReceipts/submitChangeReceipts", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<ProjectWbsReceiptsDto> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<ProjectWbsReceiptsDto>>() {
        });
        ProjectWbsReceiptsDto data = null;
        if (dataResponse != null) {
            data = dataResponse.getData();
        }
        if (Objects.nonNull(data) && StringUtils.isNotEmpty(data.getOldHandleUserMip())) {
            //处理修改处理人后将修改前的处理人的流程实例作废
            Long formInstanceId = data.getMilepostDesignPlanDetailChangeRecordId();
            String formUrl = "mdpChangeTheChangeApp";
            Long companyId = SystemContext.getUnitId();
            String oldHandleUserMip = data.getOldHandleUserMip();
            //删除流程实例
            formInstanceService.logicDeleteAndMipWorkflowDraftAbandon(formInstanceId, formUrl, oldHandleUserMip, companyId);
        }
        return dataResponse;
    }

    @ApiOperation(value = "同步详设变更单据预算")
    @PutMapping("projectWbsReceiptsBudgetChangeHistory/{projectWbsChangeReceiptsId}")
    public DataResponse projectWbsReceiptsBudgetChangeHistory(@ApiParam("详细设计变更单据id") @PathVariable Long projectWbsChangeReceiptsId) {
        String url = String.format("%sprojectWbsReceipts/projectWbsReceiptsBudgetChangeHistory/" + projectWbsChangeReceiptsId,
                ModelsEnum.CTC.getBaseUrl());
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    /**
     * 退回 新建界面第三步
     *
     * @param id
     * @return 详细设计单据id
     */
    @ApiOperation(value = "退回", response = Integer.class)
    @PutMapping("changeReceiptsReject/{id}")
    public DataResponse changeReceiptsReject(@ApiParam("详细设计单据id") @PathVariable Long id) {
        String url = String.format("%sprojectWbsReceipts/changeReceiptsReject/" + id, ModelsEnum.CTC.getBaseUrl());
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    /**
     * 作废 新建界面第二步(草稿状态)
     *
     * @param id
     * @return 详细设计单据id
     */
    @ApiOperation(value = "作废", response = Integer.class)
    @PutMapping("changeReceiptsCancel/{id}")
    public DataResponse changeReceiptsCancel(@ApiParam("详细设计单据id") @PathVariable Long id) {
        String url = String.format("%sprojectWbsReceipts/changeReceiptsCancel/" + id, ModelsEnum.CTC.getBaseUrl());
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);

        //同时作废工作流
        if (responseEntity.getStatusCodeValue() == 0) {
            mipWorkflowInnerService.draftAbandon("mdpChangeTheChangeApp", id);
        }
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    /**
     * 需求发布移动端审批
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "查询移动审批需求发布", response = ResponseMap.class)
    @GetMapping("/getRequirementPublishApp")
    public Response getRequirementPublishApp(@RequestParam Long id) {
        final String url = String.format("%sprojectWbsReceipts/getRequirementPublishApp?id=%s",
                ModelsEnum.CTC.getBaseUrl(), id);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<ResponseMap>>() {
        });
    }

    @ApiOperation(value = "通过单据id获取单据详情", response = ProjectWbsReceiptsDto.class)
    @GetMapping("getById")
    public DataResponse getById(@ApiParam("详细设计单据id") @RequestParam Long id) {
        final String url = String.format("%sprojectWbsReceipts/getById?id=%s", ModelsEnum.CTC.getBaseUrl(), id);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectWbsReceiptsDto>>() {
        });
    }

    @ApiOperation(value = "通过单据号模糊查询详细设计单据", response = ProjectWbsReceiptsDto.class)
    @GetMapping("getByRequirementCodeFuzzy")
    public DataResponse getByRequirementCodeFuzzy(@RequestParam String fuzzyLike) {
        final String url = String.format("%sprojectWbsReceipts/getByRequirementCodeFuzzy?fuzzyLike=%s", ModelsEnum.CTC.getBaseUrl(), fuzzyLike);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<List<ProjectWbsReceiptsDto>>>() {
        });
    }

    /**
     * 查看详设变更单据详情页面中的预算和详设的是否外包对应不上的所有问题单据号
     *
     * @return 详细设计单据号
     */
    @ApiOperation(value = "查看详设变更单据详情页面中的预算和详设的是否外包对应不上的所有问题单据号")
    @GetMapping("getProjectWbsReceiptsBudgetErrorCode")
    public DataResponse getProjectWbsReceiptsBudgetErrorCode(@ApiParam("开始详细设计单据id") @RequestParam Long startId,
                                                             @ApiParam("结束详细设计单据id") @RequestParam Long endId) {
        String url = String.format("%sprojectWbsReceipts/getProjectWbsReceiptsBudgetErrorCode?startId=%s&endId=%s", ModelsEnum.CTC.getBaseUrl(),
                startId, endId);
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(600 * 1000);
        httpRequestFactory.setConnectTimeout(600 * 1000);
        httpRequestFactory.setReadTimeout(600 * 1000);
        restTemplate.setRequestFactory(httpRequestFactory);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<List<String>>>() {
        });
    }

    @ApiOperation(value = "将详设的状态变为详设单据通过")
    @GetMapping("changeMilepostDesignPlanDetailStatusReceiptsPassed")
    public DataResponse changeMilepostDesignPlanDetailStatusReceiptsPassed(@ApiParam("详细设计id列表，以','隔开") @RequestParam String milepostDesignPlanDetailIdStr) {
        String url = String.format("%sprojectWbsReceipts/changeMilepostDesignPlanDetailStatusReceiptsPassed?milepostDesignPlanDetailIdStr=%s",
                ModelsEnum.CTC.getBaseUrl(), milepostDesignPlanDetailIdStr);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectWbsReceiptsDto>>() {
        });
    }

    @ApiOperation(value = "删除")
    @PutMapping("deleteById/{id}")
    public DataResponse deleteById(@ApiParam("详细设计单据id") @PathVariable Long id) {
        String url = String.format("%sprojectWbsReceipts/deleteById/" + id, ModelsEnum.CTC.getBaseUrl());
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation("获取供应商罚扣提交审批记录id")
    @GetMapping("getMilepostDesignPlanDetailChangeRecordId")
    public Response getMilepostDesignPlanDetailChangeRecordId(@RequestParam Long originFormInstanceId, @RequestParam String formUrl) {
        String url = ModelsEnum.CTC.getBaseUrl() + "projectWbsReceipts/getMilepostDesignPlanDetailChangeRecordId?originFormInstanceId="
                + originFormInstanceId + "&formUrl=" + formUrl;
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<Long>>() {
        });
    }

    @ApiOperation("获取供应商罚扣主表id")
    @GetMapping("getOriginFormInstanceId")
    public Response getOriginFormInstanceId(@RequestParam Long milepostDesignPlanDetailChangeRecordId) {
        String url = ModelsEnum.CTC.getBaseUrl() + "projectWbsReceipts/getOriginFormInstanceId?milepostDesignPlanDetailChangeRecordId="
                + milepostDesignPlanDetailChangeRecordId;
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<Long>>() {
        });
    }

    @ApiOperation("获取单据编码")
    @GetMapping("generateRequirementCode")
    public Response generateRequirementCode() {
        String url = ModelsEnum.CTC.getBaseUrl() + "projectWbsReceipts/generateRequirementCode";
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "模板下载", notes = "场景：WBS变更")
    @GetMapping("/wbsChange/exportTemplate")
    public void exportTemplateWbsChange(@RequestParam Map<String, Object> param, HttpServletResponse servletResponse) {
        Long projectId = MapUtils.getLong(param, WbsBudgetFieldConstant.PROJECT_ID);
        Asserts.notEmpty(projectId, ErrorCode.CTC_PROJECT_ID_NULL);

        /* 查询项目 */
        Map<String, Object> projectParamMap = new HashMap();
        projectParamMap.put("id", projectId);
        String url1 = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/findOnlyProjectById", projectParamMap);
        String res1 = restTemplate.getForEntity(url1, String.class).getBody();
        DataResponse<ProjectDto> projectResponse = JSON.parseObject(res1, new TypeReference<DataResponse<ProjectDto>>() {
        });
        ProjectDto projectDto = projectResponse.getData();

        /* 查询wbs预算 */
        String url = String.format("%sstatistics/project/wbsCost/summary", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, param, String.class);
        DataResponse<ProjectWbsCostExcelVo> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<ProjectWbsCostExcelVo>>() {
        });

        ProjectWbsCostExcelVo projectWbsCostExcelVo = response.getData();
        if (projectWbsCostExcelVo == null || CollectionUtils.isEmpty(projectWbsCostExcelVo.getMapList())) {
            throw new BizException(ErrorCode.CTC_PROJECT_WBS_BUDGET_NOT_FOUND);
        }
        // wbs预算数据
        List<Map<String, Object>> dataMaps = projectWbsCostExcelVo.getMapList();
        dataMaps.removeIf(s -> Objects.equals(MapUtils.getString(s, WbsBudgetFieldConstant.ACTIVITY_CODE), "System"));
        dataMaps.sort(Comparator.comparing(s -> MapUtils.getString(s, WbsBudgetFieldConstant.WBS_FULL_CODE)));

        try {
            /* 导出Excel配置 */
            String fileName = String.format("%sWBS变更%s%s", projectDto.getCode(), DateUtils.format(new Date(), "yyyyMMddHHmmss"), ".xls");
            Workbook workbook = ExcelUtil.createWorkBook(fileName);
            Sheet sheet = ExcelUtil.createSheet(workbook, "变更WBS");
            CellStyle titleStyle = ExcelUtil.creatTitleStyle(workbook);
            CellStyle titleStyle1 = ExcelUtil.createTitleStyle(workbook, IndexedColors.RED.getIndex());
            CellStyle style = ExcelUtil.creatCellStyle(workbook);

            // wbs预算组装序号
//            List<Map<String, Object>> dataMaps = ProjectWbsBudgetDto.dto2MapBatch(projectWbsBudgetDtoList);
            for (int i = 0; i < dataMaps.size(); i++) {
                Map<String, Object> currentMap = dataMaps.get(i);
                if (StringUtils.isNotEmpty(MapUtils.getString(currentMap, WbsBudgetFieldConstant.FEE_TYPE_NAME))) {
                    // 能匹配到，boolean转字典
                    currentMap.put(WbsBudgetFieldConstant.FEE_SYNC_EMS, Boolean.TRUE.equals(MapUtils.getBoolean(currentMap, WbsBudgetFieldConstant.FEE_SYNC_EMS)) ? "是" : "否");
                } else {
                    // 匹配不到，不返回内容
                    currentMap.remove(WbsBudgetFieldConstant.FEE_SYNC_EMS);
                }
                currentMap.put(WbsBudgetFieldConstant.ORDER_NO, i + 1);
                currentMap.put(WbsBudgetFieldConstant.WBS_SUMMARY_CODE, MapUtils.getString(currentMap, WbsBudgetFieldConstant.PROJECT_CODE) + "-" + MapUtils.getString(currentMap, WbsBudgetFieldConstant.WBS_FULL_CODE));
            }
            // wbs动态列
            List<WbsTemplateRuleCache> wbsCaches = ProjectWbsBudgetUtils.getEligibilityWbsCache(projectDto.getWbsTemplateInfoId());

            /* 导出数据JsonArray格式 */
            JSONArray dataJsonArray = JSONArray.parseArray(JSON.toJSONString(dataMaps));

            /* 标题 */
            LinkedHashMap<String, String> titleMap = new LinkedHashMap<>();
            titleMap.put(WbsBudgetFieldConstant.ORDER_NO, "序号");
            titleMap.put(WbsBudgetFieldConstant.TAG, "操作类型");
            if (!org.springframework.util.CollectionUtils.isEmpty(wbsCaches)) {
                for (WbsTemplateRuleCache wbsCacge : wbsCaches) {
                    titleMap.put(wbsCacge.getKey(), wbsCacge.getRuleName());
                }
            }
            titleMap.put(WbsBudgetFieldConstant.WBS_SUMMARY_CODE, "WBS号");
            titleMap.put(WbsBudgetFieldConstant.DESCRIPTION, "描述");
            titleMap.put(WbsBudgetFieldConstant.ACTIVITY_CODE, "活动事项编码");
            titleMap.put(WbsBudgetFieldConstant.AFTER_CHANGE_PRICE, "设计预估金额");
            titleMap.put(WbsBudgetFieldConstant.REMARK, "备注");

            ExcelUtil.setExcelData(sheet, dataJsonArray, titleMap, null, null, null, titleStyle, style, 1);

            // 设置表头
            ArrayList<String> arrayList = new ArrayList<>();
            arrayList.add("填写说明：");
            arrayList.add("1、原有WBS如无须Set0，操作类型无需填写");
            arrayList.add("2、操作类型=Set0，代表不需要该层级，预算设置为0，无需填写设计预估金额");
            arrayList.add("3、操作类型=New，需要填写红色字段，活动事项编码、设计预估金额、备注非必填");
            String content = String.join("\n", arrayList);
            Row row0 = sheet.createRow(0);
            row0.createCell(0).setCellValue(content);
            row0.setHeightInPoints(60);
            //创建一个单元格样式，用于设置换行
            CellStyle headStyle = workbook.createCellStyle();
            headStyle.setWrapText(true); // 启用自动换行
            row0.getCell(0).setCellStyle(headStyle); // 应用样式
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, titleMap.size() - 1)); // 合并单元格

            // 标题设置红色字段
            Row row1 = sheet.getRow(1);
            for (int colNum = 2; colNum < 2 + wbsCaches.size(); colNum++) {
                row1.getCell(colNum).setCellStyle(titleStyle1);
            }

            //固定表头
            sheet.createFreezePane(0, 2, 0, 2);

            //设置【操作类型】下拉框的值
            String[] tagValues = new String[]{"New", "Set0"};
            ExportExcelUtil.createDropDownExpandList(workbook, tagValues, 2, 10000, 1, 1);

            //单元格设为文本格式，默认10000行（WBS相关行）
            CellStyle textStyle = workbook.createCellStyle();
            textStyle.setDataFormat(workbook.createDataFormat().getFormat("@")); //文本格式
            for (int i = 2 + dataMaps.size(); i < 10000; i++) {
                Row row = sheet.createRow(i);
                for (int j = 2; j < wbsCaches.size() + 3; j++) {
                    Cell cell = row.createCell(j);
                    cell.setCellStyle(textStyle);
                }
            }

            /** 设置sheet2：WBS模板 **/
            createWbsChangeSheet2(workbook, wbsCaches);

            ExcelUtil.downLoadExcel(fileName, servletResponse, workbook);
        } catch (Exception e) {
            throw new BizException(ErrorCode.ERROR, "模板导出异常");
        }
    }

    public void createWbsChangeSheet2(Workbook workbook, List<WbsTemplateRuleCache> wbsCaches) {
        CellStyle titleStyle = ExportExcelUtil.getCellStyle(workbook, HSSFCellStyle.ALIGN_CENTER, null, IndexedColors.LIGHT_CORNFLOWER_BLUE.getIndex(), Boolean.FALSE, Boolean.FALSE);
        CellStyle style = ExportExcelUtil.getCellStyle(workbook, HSSFCellStyle.ALIGN_CENTER, null, null, Boolean.FALSE, Boolean.FALSE);
        Sheet sheet2 = ExcelUtil.createSheet(workbook, "WBS模板");

        //筛掉非严控的
        wbsCaches.removeIf(s -> !s.getStrictlyControl());
        if (CollectionUtils.isEmpty(wbsCaches)) {
            return;
        }

        Row row0 = sheet2.createRow(0);
        for (int j = 0; j < wbsCaches.size(); j++) {
            //标题
            WbsTemplateRuleCache wbsCache = wbsCaches.get(j);
            Cell cell1 = row0.createCell(j * 3);
            cell1.setCellValue(wbsCache.getRuleName());
            cell1.setCellStyle(titleStyle);
            Cell cell2 = row0.createCell(j * 3 + 1);
            cell2.setCellValue("描述");
            cell2.setCellStyle(titleStyle);

            //行数据
            List<WbsTemplateRuleDetailCache> wbsDetailCaches = WbsTemplateRuleDetailCacheUtils.listCache(wbsCache.getId());
            wbsDetailCaches.removeIf(WbsTemplateRuleDetailCache::getParentState); //筛掉父级
            wbsDetailCaches.sort(Comparator.comparing(WbsTemplateRuleDetailCache::getCode));
            for (int i = 0; i < wbsDetailCaches.size(); i++) {
                WbsTemplateRuleDetailCache wbsDetailCache = wbsDetailCaches.get(i);
                Row row = sheet2.getRow(i + 1);
                if (row == null) row = sheet2.createRow(i + 1);
                Cell cell3 = row.createCell(j * 3);
                cell3.setCellValue(wbsDetailCache.getCode());
                cell3.setCellStyle(style);
                Cell cell4 = row.createCell(j * 3 + 1);
                cell4.setCellValue(wbsDetailCache.getDescription());
                cell4.setCellStyle(style);
            }
        }

        //设置列宽
        for (int i = 0; i < wbsCaches.size() * 3; i++) {
            if ((i + 1) % 3 == 0) {
                continue;
            }
            sheet2.setColumnWidth(i, 5000);
        }
    }

    @ApiOperation(value = "导入模板", notes = "场景：WBS变更")
    @PostMapping("/wbsChange/checkTemplate")
    public Response checkTemplateWbsChange(@RequestParam MultipartFile file, @RequestParam Long projectId, Long projectWbsReceiptsId) {
        DataResponse<Map<String, Object>> response = Response.dataResponse();

        /* 查询项目 */
        Map<String, Object> projectParamMap = new HashMap();
        projectParamMap.put("id", projectId);
        String url1 = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/findOnlyProjectById", projectParamMap);
        String res1 = restTemplate.getForEntity(url1, String.class).getBody();
        DataResponse<ProjectDto> projectResponse = JSON.parseObject(res1, new TypeReference<DataResponse<ProjectDto>>() {
        });
        ProjectDto projectDto = projectResponse.getData();

        // 获取有效的wbs动态列
        List<WbsTemplateRuleCache> wbsTemplateRuleCaches = ProjectWbsBudgetUtils.getEligibilityWbsCache(projectDto.getWbsTemplateInfoId());

        List<List<String>> wbsExcelRows = new ArrayList<>();
        try {
            // 从excel第2行开始读，读取行数：wbs动态列数量 + 7固定列
            wbsExcelRows = FileUtil.importExcel(file, 0, 1, wbsTemplateRuleCaches.size() + 7);
            // 校验wbs模板导入title有效性
            validityImportWbsBudgetTitle(wbsExcelRows.get(0), wbsTemplateRuleCaches);
        } catch (BizException bizException) {
            // 自定义内容不能被Exception拦截
            throw bizException;
        } catch (Exception e) {
            logger.error("wbs变更导入异常:" + e.getMessage(), e);
            throw new BizException(ErrorCode.ERROR, "wbs变更导入异常");
        }

        List<List<String>> validExcelRows = wbsExcelRows.stream().filter(s -> Objects.equals(s.get(1), "New") || Objects.equals(s.get(1), "Set0")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(validExcelRows)) {
            throw new BizException(Code.ERROR, "当前导入Excel模板中无法匹配有效的记录，请检查");
        }

        ProjectWbsReceiptsDto dto = new ProjectWbsReceiptsDto();
        dto.setProjectId(projectId);
        dto.setWbsExcelRows(wbsExcelRows);
        dto.setProjectWbsReceiptsId(projectWbsReceiptsId);
        final String url = String.format("%sprojectWbsReceipts/wbsChange/checkTemplate", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });
        if (dataResponse == null || Objects.equals(dataResponse.getCode(), Code.ERROR.getCode())) {
            throw new BizException(ErrorCode.ERROR, "wbs变更导入数据校验出现异常");
        }
        return response.setData(dataResponse.getData());
    }

    /**
     * 校验WBS变更导入模板title有效性
     *
     * @param wbsExcelRows
     * @param wbsTemplateRuleCaches
     */
    public static void validityImportWbsBudgetTitle(List<String> wbsExcelRows, List<WbsTemplateRuleCache> wbsTemplateRuleCaches) {
        List<String> titleExcelRows = wbsExcelRows.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(titleExcelRows) || titleExcelRows.size() != wbsTemplateRuleCaches.size() + 7) {
            throw new BizException(Code.ERROR, "导入模板有误，请下载最新模板");
        }
        for (int i = 0; i < wbsTemplateRuleCaches.size(); i++) {
            if (!org.apache.commons.lang3.StringUtils.equals(wbsTemplateRuleCaches.get(i).getRuleName(), titleExcelRows.get(i + 2))) {
                throw new BizException(Code.ERROR, "导入模板有误，请下载最新模板");
            }
        }
    }

    @ApiOperation(value = "保存WBS变更单据", notes = "场景：WBS变更")
    @PostMapping("saveWbsChangeReceipts")
    public Response saveWbsChangeReceipts(@RequestBody ProjectWbsReceiptsDto dto) {
        String url = String.format("%sprojectWbsReceipts/saveWbsChangeReceipts", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<ProjectWbsReceiptsDto>>() {
        });
    }

    @ApiOperation(value = "查询WBS变更单据", notes = "场景：WBS变更")
    @GetMapping("findWbsChangeDetail")
    public Response findWbsChangeDetail(@ApiParam("详细设计单据id") @RequestParam Long id) {
        final String url = String.format("%sprojectWbsReceipts/findWbsChangeDetail?id=%s", ModelsEnum.CTC.getBaseUrl(), id);
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(600 * 1000);
        httpRequestFactory.setConnectTimeout(600 * 1000);
        httpRequestFactory.setReadTimeout(600 * 1000);
        restTemplate.setRequestFactory(httpRequestFactory);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectWbsReceiptsDto>>() {
        });
    }

    @ApiOperation(value = "撤回")
    @PutMapping("common/reject/{id}")
    public Response commonReject(@ApiParam("详细设计单据id") @PathVariable Long id) {
        String url = String.format("%sprojectWbsReceipts/common/reject/" + id, ModelsEnum.CTC.getBaseUrl());
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "作废")
    @PutMapping("common/cancel/{id}")
    public Response commonCancel(@ApiParam("详细设计单据id") @PathVariable Long id) {
        String url = String.format("%sprojectWbsReceipts/common/cancel/" + id, ModelsEnum.CTC.getBaseUrl());
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }
}
