package com.midea.pam.gateway.ctc.web;

import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

@Api("项目变更审批回调")
@RestController
@RequestMapping("projectBaseInfoChangeCallBack")
public class ProjectBaseInfoChangeCallBackController {
    @Resource
    private RestTemplate restTemplate;

    /**
     * 流程审批中回调方法.
     *
     * @param formInstanceId 表单实例id
     */
    @ApiOperation(value = "审批中")
    @PutMapping("approvaling/skipSecurityInterceptor")
    public Response approvaling(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                                @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%sprojectBaseInfoChangeCallBack/approvaling/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    /**
     * 流程审批完成回调方法。
     *
     * @param formInstanceId 表单实例id
     */
    @ApiOperation(value = "审批通过")
    @PutMapping("approved/skipSecurityInterceptor")
    public Response approved(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                             @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                             @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                             @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%sprojectBaseInfoChangeCallBack/approved/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    /**
     * 流程审批驳回回调方法.
     *
     * @param formInstanceId 表单实例id
     */
    @ApiOperation(value = "驳回")
    @PutMapping("refused/skipSecurityInterceptor")
    public Response refused(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                            @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                            @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                            @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%sprojectBaseInfoChangeCallBack/refused/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    /**
     * 流程审批撤回回调方法.
     *
     * @param formInstanceId 表单实例id
     */
    @ApiOperation(value = "撤回")
    @PutMapping("return/skipSecurityInterceptor")
    public Response returned(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                             @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                             @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                             @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%sprojectBaseInfoChangeCallBack/return/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "项目基本信息变更流程作废")
    @PutMapping("updateStatusAbandonForProject/skipSecurityInterceptor")
    public Response updateStatusAbandonForProject(@RequestParam(required = false) Long formInstanceId,
                                                  @RequestParam(required = false) String fdInstanceId,
                                                  @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                                  @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                                                  @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sprojectBaseInfoChangeCallBack/updateStatusAbandonForProject/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "项目基本信息变更流程删除")
    @PutMapping("updateStatusDeleteForProject/skipSecurityInterceptor")
    public Response updateStatusDeleteForProject(@RequestParam(required = false) Long formInstanceId,
                                                 @RequestParam(required = false) String fdInstanceId,
                                                 @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                                 @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                                                 @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sprojectBaseInfoChangeCallBack/updateStatusDeleteForProject/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "处理人通过")
    @PutMapping("agree/skipSecurityInterceptor")
    public Response agree(@RequestParam(required = false) Long formInstanceId,
                          @RequestParam(required = false) String fdInstanceId,
                          @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                          @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                          @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sprojectBaseInfoChangeCallBack/agree/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

}
