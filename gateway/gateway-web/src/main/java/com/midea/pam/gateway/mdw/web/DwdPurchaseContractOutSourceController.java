package com.midea.pam.gateway.mdw.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @PackageClassName: com.midea.pam.gateway.mdw.web.DwdPurchaseContractOutSourceController
 * @Description: 采购合同支付明细(外包)
 * @Author: JerryH
 * @Date: 2022-12-28, 0028 上午 09:12:49
 */
@Api("采购合同支付明细(外包)")
@RestController
@RequestMapping(value = {"/dwd/purchaseContractOutSource"})
public class DwdPurchaseContractOutSourceController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "手动拉取项目已确认收入&成本数据")
    @GetMapping("saveOrUpdatePurchaseContractOutSource")
    public Response saveOrUpdatePurchaseContractOutSource(@RequestParam(required = false) Long projectId) {
        Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        final String url = buildGetUrl(ModelsEnum.MDW.getBaseUrl(), "dwd/purchaseContractOutSource/saveOrUpdatePurchaseContractOutSource", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
    }
}
