package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.CustomerTransferDto;
import com.midea.pam.common.ctc.excelVo.CustomerTransferExcelVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("客户间转款")
@RestController
@RequestMapping("statistics/customerTransfer")
public class CustomerTransferStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "查询客户间转款列表")
    @GetMapping("page")
    public Response list(CustomerTransferDto customerTransferDto,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(customerTransferDto);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/customerTransfer/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<CustomerTransferDto>>>() {
        });
    }

    
    private Map buildParam(CustomerTransferDto customerTransferDto) {
        final Map<String, Object> params = new HashMap<>();
        params.put("transferCode", customerTransferDto.getTransferCode());
        params.put("transferOutCustomerName", customerTransferDto.getTransferOutCustomerName());
        params.put("transferInCustomerName", customerTransferDto.getTransferInCustomerName());
        params.put("originReceiptCode", customerTransferDto.getOriginReceiptCode());
        params.put("transferStatusStr", customerTransferDto.getTransferStatusStr());
        params.put("transferTypeStr", customerTransferDto.getTransferTypeStr());
        params.put("erpStatusStr", customerTransferDto.getErpStatusStr());
        params.put("currencyStr", customerTransferDto.getCurrencyStr());
        params.put("startTimeStr", customerTransferDto.getStartTimeStr());
        if(StringUtils.isNotEmpty(customerTransferDto.getEndTimeStr())){
            params.put("endTimeStr", customerTransferDto.getEndTimeStr() + " 23:59:59");
        }
        params.put("auditStartTimeStr", customerTransferDto.getAuditStartTimeStr());
        if(StringUtils.isNotEmpty(customerTransferDto.getAuditEndTimeStr())){
            params.put("auditEndTimeStr", customerTransferDto.getAuditEndTimeStr() + " 23:59:59");
        }
        return params;
    }


    @ApiOperation(value = "客户间转款列表导出", response = ResponseMap.class)
    @GetMapping("export")
    public void listExport(HttpServletResponse response, CustomerTransferDto customerTransferDto) {
        final Map<String, Object> params = buildParam(customerTransferDto);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/customerTransfer/v1/export", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("客户间转款_");
        fileName.append(DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");

        JSONArray customerTransferArr = (JSONArray) resultMap.get("customerTransfers");

        List<CustomerTransferExcelVo> customerTransferExcelVos =
                JSONObject.parseArray(customerTransferArr.toJSONString(), CustomerTransferExcelVo.class);
        for (int i = 0; i < customerTransferExcelVos.size(); i++) {
            CustomerTransferExcelVo customerTransferExcelVO = customerTransferExcelVos.get(i);
            customerTransferExcelVO.setNum(i+1);
        }
        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(customerTransferExcelVos,
                CustomerTransferExcelVo.class, null, "客户间转款", true);
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

}
