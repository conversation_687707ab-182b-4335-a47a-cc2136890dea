package com.midea.pam.gateway.ctc.web;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.ProjectMilepostDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

@Api("项目里程碑")
@RestController
@RequestMapping("projectMilepost")
public class ProjectMilepostController extends ControllerHelper {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "更新里程碑实际开始时间")
    @PostMapping("updateActualStartTime")
    public Response updateActualStartTime(@RequestBody ProjectMilepostDto dto) {
        String url = String.format("%sprojectMilepost/updateActualStartTime", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "初始化里程碑（交付标准-附件表）")
    @PostMapping("initMilepostAttach")
    public Response initMilepostAttach(@RequestBody ProjectDto oaram) {
        String url = String.format("%sprojectMilepost/initMilepostAttach", ModelsEnum.CTC.getBaseUrl());
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(6000 * 1000);
        httpRequestFactory.setConnectTimeout(6000 * 1000);
        httpRequestFactory.setReadTimeout(6000 * 1000);
        restTemplate.setRequestFactory(httpRequestFactory);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, oaram, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
    }
}
