package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.BomDto;
import com.midea.pam.common.basedata.entity.Bom;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.PageResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.common.utils.ExportExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("bom信息")
@RestController
@RequestMapping("bom")
public class BomController extends ControllerHelper {
	@Autowired
	private RestTemplate restTemplate;

	@ApiOperation(value = "查询bom基础列表", response = BomDto.class)
	@GetMapping("getBomList")
	public Response listInfo(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
							 @RequestParam(required = false, defaultValue = "10") Integer pageSize,
							 @RequestParam(required = false) Long id,
							 @RequestParam(required = false) Long parentId,
							 @RequestParam(required = false) String levelNo,
							 @RequestParam(required = false) String itemInfo,
							 @RequestParam(required = false) String itemSeqFrom,
							 @RequestParam(required = false) String itemSeqTo,
							 @RequestParam(required = false) String assembleCodeFrom,
							 @RequestParam(required = false) String assembleCodeTo,
							 @RequestParam(required = false) String organizationName,
							 @RequestParam(required = false) String assembleInfo,
							 @RequestParam(required = false) String componentCodeFrom,
							 @RequestParam(required = false) String componentCodeTo,
							 @RequestParam(required = false) String componentInfo) throws Exception {
		final Map<String, Object> param = new HashMap<>();
		param.put("pageNum", pageNum);
		param.put("pageSize", pageSize);
		param.put("organizationName", organizationName);
		param.put("id", id);
		param.put("parentId", parentId);
		param.put("levelNo", levelNo);
		param.put("itemInfo", itemInfo);
		param.put("itemSeqFrom", itemSeqFrom);
		param.put("itemSeqTo", itemSeqTo);
		param.put("assembleCodeFrom", assembleCodeFrom);
		param.put("assembleCodeTo", assembleCodeTo);
		param.put("assembleInfo", assembleInfo);
		param.put("componentCodeFrom", componentCodeFrom);
		param.put("componentCodeTo", componentCodeTo);
		param.put("componentInfo", componentInfo);
		String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/bom/getBomList", param);
		String res = restTemplate.getForObject(url, String.class);
		res = cleanStr(res);
		PageInfo<Map<String, Object>> data = JSON.parseObject(res, new TypeReference<PageInfo<Map<String, Object>>>() {
		});
		PageResponse<Map<String, Object>> response = Response.pageResponse();
		return response.convert(data);
	}

	@ApiOperation(value = "查询Bom子信息列表", response = BomDto.class)
	@GetMapping("getSonList/{componentCode}")
	public Response getSonList(@PathVariable String componentCode, @RequestParam(required = false) String organizationName) {
		final Map<String, Object> param = new HashMap<>();
		param.put("organizationName", organizationName);
		String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/bom/getSonList/" + componentCode, param);

		String res = restTemplate.getForObject(url, String.class);
		res = cleanStr(res);
		JSONArray data = JSON.parseArray(res);
		DataResponse<JSONArray> response = Response.dataResponse();
		return response.setData(data);
	}

	@ApiOperation(value = "导出Bom子信息列表")
	@GetMapping("export/{componentCode}")
	public void export(@PathVariable String componentCode, @RequestParam(required = false) String organizationName, HttpServletResponse response) throws Exception {
		final Map<String, Object> param = new HashMap<>();
		param.put("organizationName", organizationName);
		String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/bom/getSonList/" + componentCode, param);

		String res = restTemplate.getForObject(url, String.class);
		res = cleanStr(res);
		//JSONArray data = JSON.parseArray(res);
		List<BomDto> dataList = JSONArray.parseArray(res, BomDto.class);
//		DataResponse<List<BomDto>> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<List<BomDto>>>() {
//		});

		//List<BomDto> dataList = dataResponse.getData();
		if (CollectionUtils.isEmpty(dataList)) {
			throw new Exception("没有数据");
		}

//		List<BomDto> excelVos = ListUtil.map(dataList, new ListUtil.IteratorTask<BomDto, BomDto>() {
//			@Override
//			public LeadExcelVo getValue(LeadDto item) {
//				LeadExcelVo wifi = new LeadExcelVo();
//				BeanUtils.copyProperties(item, wifi);
//				return wifi;
//			}
//		});

		//导出操作
		ExportExcelUtil.exportExcel(dataList, null, "Sheet1", BomDto.class, "Bom_" + componentCode + ".xls", response);
	}

	@ApiOperation(value = "新增bom")
	@PostMapping("add")
	public Response add(@RequestBody Bom bom) {
		String url = String.format("%sbom/add", ModelsEnum.BASEDATA.getBaseUrl());
		ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, bom, String.class);
		String res = cleanStr(responseEntity.getBody());
		DataResponse<String> response = Response.dataResponse();
		return response.setData(res);
	}

	@ApiOperation(value = "修改bom")
	@PutMapping("update")
	public Response update(@RequestBody Bom bom) {
		String url = String.format("%sbom/update", ModelsEnum.BASEDATA.getBaseUrl());
		ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, new HttpEntity<Bom>(bom), String.class);
		String res = cleanStr(responseEntity.getBody());
		DataResponse<String> response = Response.dataResponse();
		return response.setData(res);
	}

	@ApiOperation(value = "删除bom")
	@DeleteMapping("{id}")
	public Response delete(@PathVariable Long id) {
		String url = String.format("%sbom/" + id, ModelsEnum.BASEDATA.getBaseUrl());
		ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.DELETE, null, String.class);
		String res = cleanStr(responseEntity.getBody());
		DataResponse<String> response = Response.dataResponse();
		return response.setData(res);
	}

	@ApiOperation("手工同步bom数据")
	@GetMapping("getBomFromErp")
	public Response getBomFromErp(@RequestParam(required = false) String lastUpdateDate,
								  @RequestParam(required = false) String lastUpdateDateEnd) throws Exception {
		final Map<String, Object> param = new HashMap<>();
		param.put("lastUpdateDate", lastUpdateDate);
		param.put("lastUpdateDateEnd", lastUpdateDateEnd);

		String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/bom/getBomFromErp", param);
		String res = restTemplate.getForObject(url, String.class);
		res = cleanStr(res);

		DataResponse<String> response = Response.dataResponse();
		return response.setData(res);
	}

}
