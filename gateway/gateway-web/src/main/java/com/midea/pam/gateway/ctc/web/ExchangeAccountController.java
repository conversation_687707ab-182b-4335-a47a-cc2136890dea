package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.ExchangeAccountDealRecordDto;
import com.midea.pam.common.ctc.dto.ExchangeAccountDetailDto;
import com.midea.pam.common.ctc.dto.ExchangeAccountHeadDto;
import com.midea.pam.common.ctc.entity.ExchangeAccountDealRecord;
import com.midea.pam.common.ctc.excelVo.ExchangeAccountHeadExcelVo;
import com.midea.pam.common.enums.ExchangeAccountType;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Api("汇兑损益入帐单")
@RestController
@RequestMapping("exchangeAccount")
public class ExchangeAccountController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "分页查询")
    @PostMapping("page")
    public Response page(@RequestBody ExchangeAccountHeadDto query) {
        String url = String.format("%sexchangeAccount/page", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, query, String.class).getBody();
        DataResponse<PageInfo<ExchangeAccountHeadDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<ExchangeAccountHeadDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "新建")
    @PostMapping("create")
    public Response createExchangeAccount(@RequestBody ExchangeAccountDealRecord record) {
        String url = String.format("%sexchangeAccount/create", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, record, String.class).getBody();
        DataResponse<ExchangeAccountDealRecord> response = JSON.parseObject(res, new TypeReference<DataResponse<ExchangeAccountDealRecord>>() {
        });
        return response;
    }

    @ApiOperation(value = "单据详情")
    @GetMapping("detail/{id}")
    public Response queryDetail(@PathVariable final Long id) {
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "exchangeAccount/detail/" + id, null);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<ExchangeAccountHeadDto> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<ExchangeAccountHeadDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "冲销")
    @PostMapping("reverse")
    public Response reverse(@RequestBody ExchangeAccountDealRecordDto record) {
        String url = String.format("%sexchangeAccount/reverse", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, record, String.class).getBody();
        DataResponse<Integer> response = JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
        return response;
    }

    @ApiOperation(value = "同步")
    @GetMapping("sync")
    public Response sync(@RequestParam(required = true) final Long id) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "exchangeAccount/sync", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "查询业务实体下销售合同涉及的外币币种")
    @GetMapping("queryContractForeignCurrency")
    public Response queryContractForeignCurrency(@RequestParam Long ouId) {
        Map<String, Object> param = new HashMap<>();
        param.put("ouId", ouId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "exchangeAccount/queryContractForeignCurrency", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<String>>>() {
        });
    }

    @ApiOperation(value = "项目列表自定义字段初始化", notes = "外币合同功能专用")
    @GetMapping("initProjectTableTemplate")
    public Response initProjectTableTemplate() {
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "resource/user/initProjectTableTemplate", null);
        restTemplate.getForEntity(url, String.class);
        return new DataResponse<>();
    }

    @ApiOperation(value = "列表导出")
    @PostMapping("export")
    public void export(@RequestBody ExchangeAccountHeadDto query, HttpServletResponse response) throws Exception {
        String url = String.format("%sexchangeAccount/export", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, query, String.class).getBody();
        DataResponse<List<ExchangeAccountHeadExcelVo>> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<List<ExchangeAccountHeadExcelVo>>>() {
        });
        List<ExchangeAccountHeadExcelVo> excelVoList = dataResponse.getData();
        if (CollectionUtils.isEmpty(excelVoList)) {
            throw new ApplicationBizException("导出数据为空");
        }
        //导出操作
        Workbook workbook = ExportExcelUtil.buildDefaultSheet(excelVoList, ExchangeAccountHeadExcelVo.class, null, "汇兑损益列表", true);
        ExportExcelUtil.downLoadExcel("汇兑损益列表_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls", response, workbook);
    }

    @ApiOperation(value = "项目收入开票明细明细导出")
    @GetMapping("exportDetail")
    public void exportDetail(@RequestParam Long exchangeAccountHeadId, HttpServletResponse response) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("exchangeAccountHeadId", exchangeAccountHeadId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "exchangeAccount/exportDetail", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        final DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });
        Map<String, Object> resultMap = dataResponse.getData();
        JSONArray exchangeAccountDetailArr = (JSONArray) resultMap.get("detailList");
        List<ExchangeAccountDetailDto> exchangeAccountDetailList = new ArrayList<>();
        if (exchangeAccountDetailArr != null) {
            exchangeAccountDetailList = JSONObject.parseArray(exchangeAccountDetailArr.toJSONString(), ExchangeAccountDetailDto.class);
        }
        if (CollectionUtils.isEmpty(exchangeAccountDetailList)) {
            throw new ApplicationBizException("导出数据为空");
        }

        // 创建新的Excel工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("汇兑损益详情");
        setColumnWidth(sheet);

        // 设置字体
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setBoldweight(XSSFFont.BOLDWEIGHT_BOLD);
        font.setFontHeightInPoints((short) 10);

        // 设置表头样式
        HSSFCellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setFont(font);

        String[] header = {"入账单号", "入账期间", "业务实体", "业务类型", "项目号", "项目类型名称", "项目客户CRM编码", "项目客户名称", "项目的预开票入账科目",
                "项目合同币种", "合同累计不含税金额-原币", "累计开票不含税-原币", "累计收入-原币", "重估汇率", "累计开票不含税-本位币", "累计收入-本位币", "汇兑损益金额"};

        //表头
        HSSFRow row0 = sheet.createRow(0);
        for (int i = 0; i < 17; i++) {
            ExportExcelUtil.createCell(row0, header[i], i, headerStyle);
        }

        //明细数据
        for (int j = 0; j < exchangeAccountDetailList.size(); j++) {
            ExchangeAccountDetailDto detail = exchangeAccountDetailList.get(j);
            HSSFRow row = sheet.createRow(j + 1);
            ExportExcelUtil.createCell(row, detail.getAccountNum(), 0);
            ExportExcelUtil.createCell(row, detail.getGlPeriod(), 1);
            ExportExcelUtil.createCell(row, detail.getOuName(), 2);
            ExportExcelUtil.createCell(row, ExchangeAccountType.getNameByCode(detail.getAccountType()), 3);
            ExportExcelUtil.createCell(row, detail.getProjectCode(), 4);
            ExportExcelUtil.createCell(row, detail.getProjectTypeName(), 5);
            ExportExcelUtil.createCell(row, detail.getCustomerCrmCode(), 6);
            ExportExcelUtil.createCell(row, detail.getCustomerName(), 7);
            ExportExcelUtil.createCell(row, detail.getPreInvoiceSubject(), 8);
            ExportExcelUtil.createCell(row, detail.getCurrency(), 9);
            ExportExcelUtil.createCell(row, bigDecimalRoundFormat(detail.getProjectContractAmount()), 10);
            ExportExcelUtil.createCell(row, bigDecimalRoundFormat(detail.getCumulativeInvoiceTotal()), 11);
            ExportExcelUtil.createCell(row, bigDecimalRoundFormat(detail.getCumulativeIncomeTotal()), 12);
            ExportExcelUtil.createCell(row, bigDecimalFormat(detail.getEvaluateRate()), 13);
            ExportExcelUtil.createCell(row, bigDecimalRoundFormat(detail.getStandardCumulativeInvoiceTotal()), 14);
            ExportExcelUtil.createCell(row, bigDecimalRoundFormat(detail.getStandardCumulativeIncomeTotal()), 15);
            ExportExcelUtil.createCell(row, bigDecimalRoundFormat(detail.getExchangeAmount()), 16);
        }

        //合计
        BigDecimal total = exchangeAccountDetailList.stream().map(ExchangeAccountDetailDto::getExchangeAmount).reduce(BigDecimal.ZERO, BigDecimalUtils::add);
        HSSFRow lastRow = sheet.createRow(sheet.getLastRowNum() + 1);
        ExportExcelUtil.createCell(lastRow, "合计", 0, headerStyle);
        ExportExcelUtil.createCell(lastRow, bigDecimalRoundFormat(total), 16, headerStyle);

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("汇兑损益详情_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    private void setColumnWidth(HSSFSheet sheet) {
        sheet.setColumnWidth(0, 6000);
        sheet.setColumnWidth(1, 4000);
        sheet.setColumnWidth(2, 6000);
        sheet.setColumnWidth(3, 6000);
        sheet.setColumnWidth(4, 6000);
        sheet.setColumnWidth(5, 6000);
        sheet.setColumnWidth(6, 6000);
        sheet.setColumnWidth(7, 10000);
        sheet.setColumnWidth(8, 10000);
        sheet.setColumnWidth(9, 6000);
        sheet.setColumnWidth(10, 6000);
        sheet.setColumnWidth(11, 6000);
        sheet.setColumnWidth(12, 6000);
        sheet.setColumnWidth(13, 6000);
        sheet.setColumnWidth(14, 6000);
        sheet.setColumnWidth(15, 6000);
        sheet.setColumnWidth(16, 6000);
    }

    private String bigDecimalFormat(BigDecimal b) {
        if (b == null) {
            return "-";
        } else {
            return b.toString();
        }
    }

    private String bigDecimalRoundFormat(BigDecimal b) {
        if (b == null) {
            return "-";
        } else {
            return b.setScale(2, BigDecimal.ROUND_HALF_UP).toString();
        }
    }
}
