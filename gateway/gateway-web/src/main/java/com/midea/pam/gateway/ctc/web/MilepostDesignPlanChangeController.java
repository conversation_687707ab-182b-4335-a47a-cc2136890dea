package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.MaterialCostDto;
import com.midea.pam.common.ctc.dto.DesignPlanChangeReportDTO;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanChangeRecordDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailChangeDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDto;
import com.midea.pam.common.ctc.entity.AsyncRequestResult;
import com.midea.pam.common.ctc.excelVo.MilepostDesignPlanDetailApprovedExportVo;
import com.midea.pam.common.ctc.excelVo.MilepostDesignPlanDetailChangeExportVo;
import com.midea.pam.common.ctc.vo.DesignPlanChangeExcelVo;
import com.midea.pam.common.ctc.vo.DesignPlanDetailChangeHistoryVO;
import com.midea.pam.common.ctc.vo.MilepostDesignPlanDetailApprovedVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.utils.BeanConverter;
import com.midea.pam.gateway.common.utils.DateUtil;
import com.midea.pam.gateway.common.utils.ExportExcelUtil;
import com.midea.pam.gateway.service.OssService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.entity.ContentType;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("里程碑详设变更")
@RestController
@RequestMapping("ctc/milepostDesignPlanChange")
public class MilepostDesignPlanChangeController extends ControllerHelper {

    private static final Logger logger = LoggerFactory.getLogger(MilepostDesignPlanChangeController.class);

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private OssService ossService;

    @ApiOperation(value = "根据模组id获取详细设计方案", response = MilepostDesignPlanDto.class)
    @GetMapping({"getModelWithSon"})
    public Response getModelWithSon(@RequestParam @ApiParam(value = "项目id") Long projectId,
                                    @RequestParam @ApiParam(value = "模组id") Long modelDesignPlanDetailId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        param.put("modelDesignPlanDetailId", modelDesignPlanDetailId);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/ctc/milepostDesignPlanChange/getModelWithSon", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<MilepostDesignPlanDto> response = JSON.parseObject(res, new TypeReference<DataResponse<MilepostDesignPlanDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "根据模组id获取详细设计方案", response = MilepostDesignPlanDto.class)
    @GetMapping({"getModelWithSonNew"})
    public Response getModelWithSonNew(@RequestParam @ApiParam(value = "项目id") Long projectId,
                                       @RequestParam @ApiParam(value = "模组id") Long modelDesignPlanDetailId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        param.put("modelDesignPlanDetailId", modelDesignPlanDetailId);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/ctc/milepostDesignPlanChange/getModelWithSonNew", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<MilepostDesignPlanDto> response = JSON.parseObject(res, new TypeReference<DataResponse<MilepostDesignPlanDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "根据模组id获取详细设计方案(wbs柔性)", response = MilepostDesignPlanDto.class)
    @GetMapping({"getModelWithSonNewWbs"})
    public Response getModelWithSonNewWbs(@RequestParam @ApiParam(value = "项目id") Long projectId,
                                          @RequestParam @ApiParam(value = "模组id") Long modelDesignPlanDetailId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        param.put("modelDesignPlanDetailId", modelDesignPlanDetailId);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/ctc/milepostDesignPlanChange/getModelWithSonNewWbs", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<MilepostDesignPlanDto> response = JSON.parseObject(res, new TypeReference<DataResponse<MilepostDesignPlanDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "提交变更", response = MilepostDesignPlanChangeRecordDto.class)
    @PostMapping({"saveChangeRecordWithDetail"})
    public Response saveChangeRecordWithDetail(@RequestBody MilepostDesignPlanChangeRecordDto dto) {
        String url = String.format("%sctc/milepostDesignPlanChange/saveChangeRecordWithDetail", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, dto, String.class).getBody();
        DataResponse<MilepostDesignPlanChangeRecordDto> response = JSON.parseObject(res,
                new TypeReference<DataResponse<MilepostDesignPlanChangeRecordDto>>() {
                });
        return response;
    }

    @ApiOperation(value = "提交变更前的校验", response = AsyncRequestResult.class)
    @PostMapping({"saveChangeRecordWithDetailAsyncBefore"})
    public Response saveChangeRecordWithDetailAsyncBefore(@RequestBody MilepostDesignPlanChangeRecordDto dto) {
        String url = String.format("%sctc/milepostDesignPlanChange/saveChangeRecordWithDetailAsyncBefore", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, dto, String.class).getBody();
        DataResponse<List<String>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<String>>>() {
        });
        return response;
    }

    @ApiOperation(value = "提交变更(异步)", response = AsyncRequestResult.class)
    @PostMapping({"saveChangeRecordWithDetailAsync"})
    public Response saveChangeRecordWithDetailAsync(@RequestBody MilepostDesignPlanChangeRecordDto dto) {
        String url = String.format("%sctc/milepostDesignPlanChange/saveChangeRecordWithDetailAsync", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, dto, String.class).getBody();
        DataResponse<AsyncRequestResult> response = JSON.parseObject(res, new TypeReference<DataResponse<AsyncRequestResult>>() {
        });
        return response;
    }

    @ApiOperation(value = "获取变更记录", response = MilepostDesignPlanChangeRecordDto.class)
    @GetMapping({"getDetailById"})
    public Response getDetailById(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/ctc/milepostDesignPlanChange/getDetailById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<MilepostDesignPlanChangeRecordDto> response = JSON.parseObject(res,
                new TypeReference<DataResponse<MilepostDesignPlanChangeRecordDto>>() {
                });
        return response;
    }

    @ApiOperation(value = "导出物料成本(变更记录)", response = MaterialCostDto.class)
    @GetMapping("exportMaterialCostForChangeRecord")
    public void exportMaterialCostForChangeRecord(HttpServletResponse response, @RequestParam @ApiParam("变更记录id") Long changeRecordId) throws Exception {
    }


    @ApiOperation(value = "发起变更审批")
    @PutMapping("updateStatusChecking/skipSecurityInterceptor")
    public Response updateStatusChecking(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                         @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                         @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                                         @RequestParam(required = false) Long createUserId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", formInstanceId);
        String url1 = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/ctc/milepostDesignPlanChange/getApprovalChangeDetail", param);
        String res1 = restTemplate.getForEntity(url1, String.class).getBody();
        DataResponse<MilepostDesignPlanChangeRecordDto> response1 = JSON.parseObject(res1,
                new TypeReference<DataResponse<MilepostDesignPlanChangeRecordDto>>() {
                });
        //生成附件
        MilepostDesignPlanChangeRecordDto milepostDesignPlanChangeRecordDto = response1.getData();
        MultipartFile multipartFile = null;
        if (!ObjectUtils.isEmpty(milepostDesignPlanChangeRecordDto)) {
            DesignPlanDetailChangeHistoryVO designPlanDetailChangeHistoryVO = milepostDesignPlanChangeRecordDto.getDesignPlanDetailChangeHistoryVO();
            if (!ObjectUtils.isEmpty(designPlanDetailChangeHistoryVO) && !designPlanDetailChangeHistoryVO.getType().equals(4)) {
                multipartFile = createAnnex(milepostDesignPlanChangeRecordDto);
            }
        }

        JSONObject jsonObject = new JSONObject();
        try {
            JSONArray result = ossService.upload(multipartFile);
            if (!ObjectUtils.isEmpty(result)) {
                jsonObject = result.getJSONObject(0);
            }
        } catch (Exception e) {
            logger.error("详细设计变更生成文件，上传失败", e);
        }
        //提交审批
        String fileId = jsonObject.getString("fileId");
        String fileName = jsonObject.getString("fileName");
        String fileSize = jsonObject.getString("fileSize");

        DataResponse<String> response = Response.dataResponse();
        String url = String.format("%sctc/milepostDesignPlanChange/updateStatusChecking/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s" +
                        "&fileId=%s" +
                        "&fileName=%s" +
                        "&fileSize=%s",
                ModelsEnum.CTC.getBaseUrl(),
                formInstanceId,
                fdInstanceId,
                formUrl,
                eventName,
                handlerId,
                companyId,
                createUserId,
                fileId,
                fileName,
                fileSize);
        restTemplate.put(url, String.class);
        return response;
    }

    @ApiOperation(value = "变更通过")
    @PutMapping("updateStatusPassed/skipSecurityInterceptor")
    public Response updateStatusPassed(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                       @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                       @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                                       @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sctc/milepostDesignPlanChange/updateStatusPassed/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "变更驳回")
    @PutMapping("updateStatusReject/skipSecurityInterceptor")
    public Response updateStatusReject(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                       @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                       @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                                       @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sctc/milepostDesignPlanChange/updateStatusReject/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "变更撤回")
    @PutMapping("updateStatusReturn/skipSecurityInterceptor")
    public Response updateStatusReturn(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                       @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                       @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                                       @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sctc/milepostDesignPlanChange/updateStatusReturn/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "项目详设变更流程作废")
    @PutMapping("updateStatusAbandonForMilepostDesignPlan/skipSecurityInterceptor")
    public Response updateStatusAbandonForMilepostDesignPlan(@RequestParam(required = false) Long formInstanceId,
                                                             @RequestParam(required = false) String fdInstanceId,
                                                             @RequestParam(required = false) String formUrl,
                                                             @RequestParam(required = false) String eventName,
                                                             @RequestParam(required = false) String handlerId,
                                                             @RequestParam(required = false) Long companyId,
                                                             @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sctc/milepostDesignPlanChange/updateStatusAbandonForMilepostDesignPlan/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "项目详设变更流程删除")
    @PutMapping("updateStatusDeleteForMilepostDesignPlan/skipSecurityInterceptor")
    public Response updateStatusDeleteForMilepostDesignPlan(@RequestParam(required = false) Long formInstanceId,
                                                            @RequestParam(required = false) String fdInstanceId,
                                                            @RequestParam(required = false) String formUrl,
                                                            @RequestParam(required = false) String eventName,
                                                            @RequestParam(required = false) String handlerId,
                                                            @RequestParam(required = false) Long companyId,
                                                            @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sctc/milepostDesignPlanChange/updateStatusDeleteForMilepostDesignPlan/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "处理人通过")
    @PutMapping("agree/skipSecurityInterceptor")
    public Response agree(@RequestParam(required = false) Long formInstanceId,
                          @RequestParam(required = false) String fdInstanceId,
                          @RequestParam(required = false) String formUrl,
                          @RequestParam(required = false) String eventName,
                          @RequestParam(required = false) String handlerId,
                          @RequestParam(required = false) Long companyId,
                          @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sctc/milepostDesignPlanChange/agree/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "获取变更审批记录", response = MilepostDesignPlanChangeRecordDto.class)
    @GetMapping({"getApprovalChangeDetail"})
    public Response getApprovalChangeDetail(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/ctc/milepostDesignPlanChange/getApprovalChangeDetail", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<MilepostDesignPlanChangeRecordDto> response = JSON.parseObject(res,
                new TypeReference<DataResponse<MilepostDesignPlanChangeRecordDto>>() {
                });
        return response;
    }


    //根据数据产生Excel
    private MultipartFile createAnnex(MilepostDesignPlanChangeRecordDto milepostDesignPlanChangeRecordDto) {
        // 附件名称为：项目编号+“详设变更明细”+“_”+YYYYMMDD MM:HH:SS;举例：IA19108详设变更明细_20191127 14:09:01.xlsx
        MultipartFile multipartFile = null;
        String filePath = milepostDesignPlanChangeRecordDto.getProjectCode() + "详设变更明细_" + DateUtils.format(new Date(), "yyyyMMdd HH:mm:ss") + ".xls";
        try {
            ExportParams exportParams = new ExportParams(filePath, "Sheet1");
            DesignPlanDetailChangeHistoryVO designPlanDetailChangeHistoryVO = milepostDesignPlanChangeRecordDto.getDesignPlanDetailChangeHistoryVO();
            if (!ObjectUtils.isEmpty(designPlanDetailChangeHistoryVO)) {
                List<MilepostDesignPlanDetailChangeExportVo> excelVos = this.buildExcelVos(designPlanDetailChangeHistoryVO);
                Workbook workbook = ExcelExportUtil.exportExcel(exportParams, MilepostDesignPlanDetailChangeExportVo.class, excelVos);
                File pdfFile = new File("/apps/pam/gateway/file/" + filePath);
                try (OutputStream out = Files.newOutputStream(Paths.get("/apps/pam/gateway/file/" + filePath)); FileInputStream fileInputStream =
                        new FileInputStream(pdfFile);) {
                    workbook.write(out);
                    multipartFile = new MockMultipartFile(pdfFile.getName(), pdfFile.getName(), ContentType.APPLICATION_OCTET_STREAM.toString(),
                            fileInputStream);
                } catch (IOException e) {
                    logger.error("详细设计变更生成文件，上传失败", e);
                }
            }
        } catch (Exception e) {
            logger.error("详细设计变更生成文件，上传失败", e);
        }
        return multipartFile;
    }

    private List<MilepostDesignPlanDetailChangeExportVo> buildExcelVos(DesignPlanDetailChangeHistoryVO designPlanDetailChangeHistoryVO) {
        List<MilepostDesignPlanDetailChangeExportVo> exportListVo = new ArrayList<>();
        MilepostDesignPlanDetailChangeExportVo exportVo = new MilepostDesignPlanDetailChangeExportVo();
        // 主模组
        MilepostDesignPlanDetailChangeDto history = designPlanDetailChangeHistoryVO.getHistory();
        MilepostDesignPlanDetailChangeDto change = designPlanDetailChangeHistoryVO.getChange();
        if (!ObjectUtils.isEmpty(history) && !ObjectUtils.isEmpty(change)) {
            BeanConverter.copy(history, exportVo);
            Integer type = designPlanDetailChangeHistoryVO.getType();
            switch (type) {
                case 1:
                    exportVo.setChangeType("新增");
                    break;
                case 2:
                    exportVo.setChangeType("删除");
                    break;
                case 3:
                    exportVo.setChangeType("修改");
                    break;
                case 4:
                    exportVo.setChangeType("未更改");
                    break;
                default:
                    break;
            }
            exportVo.setSerialNumber("1");
            exportListVo.add(exportVo);
        }

        List<DesignPlanDetailChangeHistoryVO> sonVos = designPlanDetailChangeHistoryVO.getSonVos();
        if (CollectionUtils.isNotEmpty(sonVos)) {
            int i = 0;
            for (DesignPlanDetailChangeHistoryVO sonVo : sonVos) {
                i = i + 1;
                MilepostDesignPlanDetailChangeExportVo exportsonVo = new MilepostDesignPlanDetailChangeExportVo();
                MilepostDesignPlanDetailChangeDto sonVoChange = sonVo.getChange();
                if (!ObjectUtils.isEmpty(sonVoChange)) {
                    BeanConverter.copy(sonVoChange, exportsonVo);
                    Integer sonType = sonVo.getType();
                    switch (sonType) {
                        case 1:
                            exportsonVo.setChangeType("新增");
                            break;
                        case 2:
                            exportsonVo.setChangeType("删除");
                            break;
                        case 3:
                            exportsonVo.setChangeType("修改");
                            break;
                        case 4:
                            exportsonVo.setChangeType("未更改");
                            break;
                        default:
                            break;
                    }
                    exportsonVo.setSerialNumber("1." + i);
                    exportListVo.add(exportsonVo);
                }
            }
        }
        return exportListVo;
    }

    @ApiOperation(value = "导出审批中的变更详细设计BOM")
    @GetMapping("exportApprovalingDesignPlanDetails")
    public void exportApprovalingDesignPlanDetails(HttpServletResponse response, @RequestParam Long id) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlanChange/findApprovalingDesignPlanDetail", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<MilepostDesignPlanDetailApprovedVO> dataResponse = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<MilepostDesignPlanDetailApprovedVO>>() {
                });
        MilepostDesignPlanDetailApprovedVO data = dataResponse.getData();
        List<MilepostDesignPlanDetailDto> dataList = data.getDesignPlanDetailDtos();
        if (org.springframework.util.CollectionUtils.isEmpty(dataList)) {
            throw new Exception("没有数据");
        }
        List<MilepostDesignPlanDetailApprovedExportVo> excelVos = BeanConverter.copy(dataList, MilepostDesignPlanDetailApprovedExportVo.class);
        //导出操作
        ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", MilepostDesignPlanDetailApprovedExportVo.class,
                this.buildExcelName(data.getProjectCode()), response);
    }

    private String buildExcelName(String projectCode) {
        return projectCode + "-详细设计方案-" + DateUtil.format(DateUtil.getCurrentDate(), DateUtil.DATE_INTEGER_PATTERN) + ".xls";
    }

    @ApiOperation(value = " 详细设计@Excel(name = ")
    @PostMapping("designPlanChangeReport")
    public Response designPlanChangeReport(@RequestBody List<MilepostDesignPlanDetailDto> dto) {
        String url = String.format("%sctc/milepostDesignPlanChange/designPlanChangeReport", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, dto, String.class).getBody();
        DataResponse<List<DesignPlanChangeReportDTO>> response = JSON.parseObject(res,
                new TypeReference<DataResponse<List<DesignPlanChangeReportDTO>>>() {
                });
        return response;
    }

    @ApiOperation(value = " 详细设计变更报告")
    @PostMapping("designPlanChangeReportExport")
    public void designPlanChangeReportExport(@RequestBody List<MilepostDesignPlanDetailDto> dto) throws Exception {
        String url = String.format("%sctc/milepostDesignPlanChange/designPlanChangeReport", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, dto, String.class).getBody();
        DataResponse<List<DesignPlanChangeReportDTO>> dataResponse = JSON.parseObject(res,
                new TypeReference<DataResponse<List<DesignPlanChangeReportDTO>>>() {
                });
        List<DesignPlanChangeExcelVo> excelVos = BeanConverter.copy(dataResponse.getData(), DesignPlanChangeExcelVo.class);
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletResponse response = attributes.getResponse();
        ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", DesignPlanChangeExcelVo.class, "详细设计变更报告.xls", response);
    }
}
