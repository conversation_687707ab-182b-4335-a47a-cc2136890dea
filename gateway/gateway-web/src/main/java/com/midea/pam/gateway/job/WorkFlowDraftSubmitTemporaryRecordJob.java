package com.midea.pam.gateway.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.mcomponent.core.util.Assert;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.WorkflowOperationType;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.gateway.entity.CurrentContext;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.FormInstanceExample;
import com.midea.pam.common.gateway.entity.FormTemplate;
import com.midea.pam.common.gateway.entity.FormTemplateExample;
import com.midea.pam.common.gateway.entity.WorkFlowDraftSubmitTemporaryRecord;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.gateway.common.base.Code;
import com.midea.pam.gateway.common.constants.WorkflowResultCode;
import com.midea.pam.gateway.contract.service.PurchaseOrderService;
import com.midea.pam.gateway.service.FormInstanceService;
import com.midea.pam.gateway.service.FormTemplateService;
import com.midea.pam.gateway.service.MipWorkflowService;
import com.midea.pam.gateway.service.WorkFlowDraftSubmitTemporaryRecordService;
import com.midea.pam.gateway.service.impl.MipWorkflowBranchHelper;
import com.midea.pam.gateway.vo.NodeHandler;
import com.midea.pam.gateway.vo.WorkflowDraftForm;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description
 * Created by liuqing85
 * Date 2022/9/26 10:56
 */
@Component
public class WorkFlowDraftSubmitTemporaryRecordJob {

    private static final Logger logger = LoggerFactory.getLogger(WorkFlowDraftSubmitTemporaryRecordJob.class);

    @Resource
    private WorkFlowDraftSubmitTemporaryRecordService workFlowDraftSubmitTemporaryRecordService;

    @Resource
    private MipWorkflowService mipWorkflowService;

    @Resource
    private FormTemplateService formTemplateService;

    @Resource
    private FormInstanceService formInstanceService;

    @Resource
    private PurchaseOrderService purchaseOrderService;

    @XxlJob("workFlowDraftSubmitTemporaryRecordJob")
    public ReturnT<String> execute(String param) {
        logger.info("*************** WorkFlowDraftSubmitTemporaryRecordJob处理任务开始 ***************");
        List<WorkFlowDraftSubmitTemporaryRecord> allSubmittedRecordList = workFlowDraftSubmitTemporaryRecordService.getAllSubmittedRecord();
        if (CollectionUtils.isEmpty(allSubmittedRecordList)) {
            logger.info("*************** WorkFlowDraftSubmitTemporaryRecordJob没有找到可以处理的任务 ***************");
            return ReturnT.SUCCESS;
        }
        logger.info("WorkFlowDraftSubmitTemporaryRecordJob可以处理的任务：{}", JSONObject.toJSONString(allSubmittedRecordList));

        List<Long> idList = new ArrayList<>();
        for (WorkFlowDraftSubmitTemporaryRecord record : allSubmittedRecordList) {
            String formUrl = record.getFormUrl();
            Long formInstanceId = record.getFormInstanceId();
            String submitParam = record.getSubmitParam();
            String userName = record.getUserName();
            WorkflowDraftForm form = JSONObject.parseObject(submitParam, WorkflowDraftForm.class);

            try {
                String fdId = getInstanceId(formUrl, formInstanceId);
                FormTemplate template = checkFormTemplate(formUrl, userName);
                if (form.getOpt() == null) {
                    form.setOpt(getOpt(formUrl, formInstanceId, WorkflowOperationType.DRAFT_SUBMIT.getName(), userName));
                }
                // 更新订单审批人信息
                if ("purchaseOrderChangeApp".equals(formUrl)) {
                    updateApproveInfoByPurchaseOrderReceiptsId(formInstanceId, form);
                }
                JSONObject formData = form.getFormData();
                // 流程分支参数处理
                formData = MipWorkflowBranchHelper.buildFormData(formUrl, formData);

                form.setSubject(template.getModelName() + "：" + form.getSubject());
                form.setFdTemplateId(template.getFdTemplateId());
                JSONObject res = mipWorkflowService.draftSubmit(form.getOpt(), record.getUserName(), fdId, form.getAuditNote(),
                        JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeHandler())),
                        JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeFormUrls())),
                        form.getSubject(), form.getFdTemplateId(), form.getFdUrl(),
                        formUrl, String.valueOf(formInstanceId), formData, form.getAuditFileDocIdList());

                checkResultAndThrowsException(res);
                idList.add(record.getId());
            } catch (Exception e) {
                logger.error(String.format("WorkFlowDraftSubmitTemporaryRecordJob处理任务失败，失败的记录id：%d", record.getId()), e);
            }
        }
        if (CollectionUtils.isNotEmpty(idList)) {
            // 先对查询出来的数据分别进行提交工作流，再删除，避免重复提交工作流
            workFlowDraftSubmitTemporaryRecordService.deleteByIdList(idList);
        }

        logger.info("*************** WorkFlowDraftSubmitTemporaryRecordJob处理任务结束 ***************");
        return ReturnT.SUCCESS;
    }

    /**
     * 获取流程实例.
     *
     * @param formInstanceId 表单实例
     * @return 流程实例
     */
    private String getInstanceId(String templateId, Long formInstanceId) {
        String fdId = getInstanceId2(templateId, formInstanceId);
        if (StringUtils.isBlank(fdId)) {
            throw new MipException("流程实例不存在");
        }

        return fdId;
    }

    private String getInstanceId2(String templateId, Long formInstanceId) {
        final FormInstance instance = getInstance(templateId, formInstanceId);
        if (instance != null) {
            return instance.getFdInstanceId();
        } else {
            throw new MipException("流程实例不存在");
        }
    }

    private FormInstance getInstance(String templateId, Long formInstanceId) {
        // 使用单位
        FormInstanceExample condition = new FormInstanceExample();
        condition.createCriteria().andFormUrlEqualTo(templateId).andFormInstanceIdEqualTo(formInstanceId)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<FormInstance> instances = formInstanceService.selectByExample(condition);

        if (instances.size() > 0) {
            instances.sort(Comparator.comparing(FormInstance::getId).reversed());
            return instances.get(0);
        } else {
            return null;
        }
    }

    /**
     * 获取表单模板id
     *
     * @param formUrl fromUrl
     * @return 表单模板
     */
    private FormTemplate checkFormTemplate(String formUrl, String userName) {
        CurrentContext currentContext = CacheDataUtils.getContextByMip(userName);
        final Long companyId = currentContext.getCurrentOrgId();
        // 使用单位
        FormTemplateExample cond = new FormTemplateExample();
        cond.createCriteria().andFormUrlEqualTo(formUrl).andCompanyIdEqualTo(companyId);
        List<FormTemplate> forms = formTemplateService.selectByExample(cond);
        Assert.notEmpty(forms, "表单模板未配置");
        return forms.get(0);
    }

    private JSONObject getOpt(String formTemplateId, Long formInstanceId, String optName, String userName) {
        if (optName == null) {
            return null;
        }

        checkFormTemplate(formTemplateId, userName);
        String fdId = getInstanceId(formTemplateId, formInstanceId);

        JSONObject res = mipWorkflowService.getOperationList(userName, fdId);
        logger.info("getOpt res:{}", res.toJSONString());
        JSONObject body = res.getJSONObject("body");
        JSONArray datas = body.getJSONArray("data");

        for (int i = 0; i < datas.size(); i++) {
            JSONObject data = datas.getJSONObject(i);
            JSONArray operationList = data.getJSONArray("operationList");
            for (int j = 0; j < operationList.size(); j++) {
                JSONObject opt = operationList.getJSONObject(j);
                if (optName.equals(opt.getString("name"))) {
                    return opt;
                }
            }
        }

        // 处理流程重复操作，导致操作不成功场景
        // 无对应操作权限，
        throw new BizException(ErrorCode.ERROR.getCode(), "操作失败，流程状态已变更，请刷新页面确认你的操作权限");
    }

    private void updateApproveInfoByPurchaseOrderReceiptsId(Long receiptsId, WorkflowDraftForm form) {
        List<NodeHandler> changeNodeHandler = form.getChangeNodeHandler();
        if (ListUtils.isEmpty(changeNodeHandler)) {
            return;
        }
        List<String> approveNodes = changeNodeHandler.stream().filter(o -> Objects.equals("approveNode", o.getActivityType())).
                map(entity -> entity.getNodeId() + "@" + entity.getHandlerIds()).collect(Collectors.toList());
        String approveInfo = StringUtils.join(approveNodes, ",");
        if (StringUtils.isNotBlank(approveInfo)) {
            //保存审批节点审批人信息
            purchaseOrderService.updateApproveInfoByPurchaseOrderReceiptsId(receiptsId, approveInfo);
        }
    }

    private void checkResultAndThrowsException(JSONObject res) {
        if (Objects.nonNull(res)) {
            JSONObject body = res.getJSONObject("body");
            if (Objects.nonNull(body)) {
                String resultCode = body.getString("resultCode");
                if (!WorkflowResultCode.SUCCESS.getCode().equals(resultCode)) {
                    throw new BizException(Code.ERROR.getCode(), body.getString("resultMsg"));
                }
            }
        }
    }
}
