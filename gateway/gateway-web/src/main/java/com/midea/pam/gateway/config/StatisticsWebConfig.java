package com.midea.pam.gateway.config;

import com.midea.pam.gateway.ctc.web.ProjectBudgetPushEmsErrorController;
import com.midea.pam.gateway.mdw.web.DmSubjectStandardController;
import com.midea.pam.gateway.mdw.web.DmSubjectUnitController;
import com.midea.pam.gateway.mdw.web.DwdCarryoverBillIncomeCostDetailStatisticsController;
import com.midea.pam.gateway.mdw.web.DwdContractReceiptDetailStatisticsController;
import com.midea.pam.gateway.mdw.web.DwdIhrAttendDetailController;
import com.midea.pam.gateway.mdw.web.DwdInsideHoursEmsController;
import com.midea.pam.gateway.mdw.web.DwdInternalHoursController;
import com.midea.pam.gateway.mdw.web.DwdInvoiceSaleContractDetailStatisticsController;
import com.midea.pam.gateway.mdw.web.DwdOrgUserController;
import com.midea.pam.gateway.mdw.web.DwdPaymentRefundController;
import com.midea.pam.gateway.mdw.web.DwdProjectBudgetChangeController;
import com.midea.pam.gateway.mdw.web.DwdProjectCostPaymentController;
import com.midea.pam.gateway.mdw.web.DwdProjectCustomerSatisfactionController;
import com.midea.pam.gateway.mdw.web.DwdProjectIncomeSummaryController;
import com.midea.pam.gateway.mdw.web.DwdProjectProblemController;
import com.midea.pam.gateway.mdw.web.DwdProjectSecurityIncidentController;
import com.midea.pam.gateway.mdw.web.DwdPurchaseContractOutSourceController;
import com.midea.pam.gateway.mdw.web.DwdPurchaseContractPaymentController;
import com.midea.pam.gateway.mdw.web.DwdToandfroMaterialController;
import com.midea.pam.gateway.mdw.web.DwdWorkingHourController;
import com.midea.pam.gateway.mdw.web.DwsReceiptExclusiveTaxBalanceController;
import com.midea.pam.gateway.mdw.web.DwsWorkingHourIhrAttendController;
import com.midea.pam.gateway.mdw.web.LabelUserUnitOuController;
import com.midea.pam.gateway.mdw.web.QbiController;
import com.midea.pam.gateway.statistics.web.AssetDeprnAccountingStatisticsController;
import com.midea.pam.gateway.statistics.web.AssetStatisticsController;
import com.midea.pam.gateway.statistics.web.BusinessStatisticsController;
import com.midea.pam.gateway.statistics.web.CarryoverBillStatisticsController;
import com.midea.pam.gateway.statistics.web.CostCollectionStatisticsController;
import com.midea.pam.gateway.statistics.web.CustomerStatisticsController;
import com.midea.pam.gateway.statistics.web.InnerSwapApplyStatisticsContoller;
import com.midea.pam.gateway.statistics.web.InvoiceApplyStaController;
import com.midea.pam.gateway.statistics.web.InvoiceReceivableStaController;
import com.midea.pam.gateway.statistics.web.LeadStatisticsController;
import com.midea.pam.gateway.statistics.web.MaterialCostTransferStatisticsController;
import com.midea.pam.gateway.statistics.web.MaterialGetStatisticsController;
import com.midea.pam.gateway.statistics.web.MaterialPriceReceiptStatisticsController;
import com.midea.pam.gateway.statistics.web.MaterialReturnStatisticsController;
import com.midea.pam.gateway.statistics.web.MaterialStatisticsController;
import com.midea.pam.gateway.statistics.web.MaterialTransferStatisticsController;
import com.midea.pam.gateway.statistics.web.OperatingStatisticsController;
import com.midea.pam.gateway.statistics.web.PaymentApplyStatisticsController;
import com.midea.pam.gateway.statistics.web.PaymentInvoiceDetailStatisticsController;
import com.midea.pam.gateway.statistics.web.PaymentInvoiceStatisticsController;
import com.midea.pam.gateway.statistics.web.PaymentPlanStatisticsController;
import com.midea.pam.gateway.statistics.web.PaymentWriteOffStatisticsController;
import com.midea.pam.gateway.statistics.web.PlanStatisticsController;
import com.midea.pam.gateway.statistics.web.PortalReportController;
import com.midea.pam.gateway.statistics.web.ProjectStatisticsController;
import com.midea.pam.gateway.statistics.web.ProjectStatisticsPendingCloseController;
import com.midea.pam.gateway.statistics.web.ProjectStatisticsTransferQualityController;
import com.midea.pam.gateway.statistics.web.PurchaseContractMaterialBudgetController;
import com.midea.pam.gateway.statistics.web.ReceiptClaimStatisticsController;
import com.midea.pam.gateway.statistics.web.RefundApplyStatisticsController;
import com.midea.pam.gateway.statistics.web.ReportController;
import com.midea.pam.gateway.statistics.web.RevenueCostOrderStatisticsController;
import com.midea.pam.gateway.statistics.web.StorageStatisticsController;
import com.midea.pam.gateway.statistics.web.SwapExecuteStatisticsController;
import com.midea.pam.gateway.statistics.web.TicketTasksStatisticsController;
import com.midea.pam.gateway.statistics.web.WorkingHourCostChangeHeaderStatisticsController;
import com.midea.pam.gateway.statistics.web.WriteOffStatisticsController;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2020/2/12
 * @description
 */
@Configuration
public class StatisticsWebConfig {

    @Bean
    public ProjectStatisticsController projectStatisticsController() {
        return new ProjectStatisticsController();
    }

    @Bean
    public CustomerStatisticsController customerStatisticsController() {
        return new CustomerStatisticsController();
    }

    @Bean
    public BusinessStatisticsController businessStatisticsController() {
        return new BusinessStatisticsController();
    }

    @Bean
    public PlanStatisticsController planStatisticsController() {
        return new PlanStatisticsController();
    }

    @Bean
    public OperatingStatisticsController operatingStatisticsController() {
        return new OperatingStatisticsController();
    }

    @Bean
    public LeadStatisticsController leadStatisticsController() {
        return new LeadStatisticsController();
    }

    @Bean
    public ReceiptClaimStatisticsController receiptClaimStatisticsController() {
        return new ReceiptClaimStatisticsController();
    }

    @Bean
    public WriteOffStatisticsController writeOffStatisticsController() {
        return new WriteOffStatisticsController();
    }

    @Bean
    public RevenueCostOrderStatisticsController revenueCostOrderStatisticsController() {
        return new RevenueCostOrderStatisticsController();
    }

    @Bean
    public CarryoverBillStatisticsController carryoverBillStatisticsController() {
        return new CarryoverBillStatisticsController();
    }

    @Bean
    public MaterialGetStatisticsController materialGetStatisticsController() {
        return new MaterialGetStatisticsController();
    }

    @Bean
    public MaterialCostTransferStatisticsController materialCostTransferStatisticsController(){
        return new MaterialCostTransferStatisticsController();
    }

    @Bean
    public WorkingHourCostChangeHeaderStatisticsController workingHourCostChangeHeaderStatisticsController() {
        return new WorkingHourCostChangeHeaderStatisticsController();
    }

    @Bean
    public MaterialReturnStatisticsController materialReturnStatisticsController() {
        return new MaterialReturnStatisticsController();
    }

    @Bean
    public MaterialTransferStatisticsController materialTransferStatisticsController() {
        return new MaterialTransferStatisticsController();
    }

    @Bean
    public MaterialStatisticsController materialStatisticsController() {
        return new MaterialStatisticsController();
    }

    @Bean
    public StorageStatisticsController storageStatisticsController() {
        return new StorageStatisticsController();
    }

    @Bean
    public InvoiceApplyStaController invoiceApplyStaController() {
        return new InvoiceApplyStaController();
    }

    @Bean
    public PaymentPlanStatisticsController paymentPlanStatisticsController() {
        return new PaymentPlanStatisticsController();
    }

    @Bean
    public PaymentApplyStatisticsController paymentApplyStatisticsController() {
        return new PaymentApplyStatisticsController();
    }

    @Bean
    public InvoiceReceivableStaController invoiceReceivableStaController() {
        return new InvoiceReceivableStaController();
    }

    @Bean
    public PaymentInvoiceDetailStatisticsController paymentInvoiceDetailStatisticsController() {
        return new PaymentInvoiceDetailStatisticsController();
    }

    @Bean
    public PaymentWriteOffStatisticsController paymentWriteOffStatisticsController() {
        return new PaymentWriteOffStatisticsController();
    }

    @Bean
    public CostCollectionStatisticsController costCollectionStatisticsController() {
        return new CostCollectionStatisticsController();
    }

    @Bean
    public ReportController reportController() {
        return new ReportController();
    }

    @Bean
    public PortalReportController portalReportController() {
        return new PortalReportController();
    }

    @Bean
    public RefundApplyStatisticsController refundApplyStatisticsController() {
        return new RefundApplyStatisticsController();
    }

    @Bean
    public PaymentInvoiceStatisticsController paymentInvoiceStatisticsController() {
        return new PaymentInvoiceStatisticsController();
    }

    @Bean
    public ProjectBudgetPushEmsErrorController projectBudgetPushEmsErrorController() {
        return new ProjectBudgetPushEmsErrorController();
    }

    @Bean
    public InnerSwapApplyStatisticsContoller innerSwapApplyStatisticsContoller() {
        return new InnerSwapApplyStatisticsContoller();
    }

    @Bean
    public TicketTasksStatisticsController ticketTasksStatisticsController() {
        return new TicketTasksStatisticsController();
    }

    @Bean
    public SwapExecuteStatisticsController swapExecuteStatisticsController() {
        return new SwapExecuteStatisticsController();
    }

    @Bean
    public PurchaseContractMaterialBudgetController purchaseContractMaterialBudgetController() {
        return new PurchaseContractMaterialBudgetController();
    }

    @Bean
    public ProjectStatisticsTransferQualityController projectStatisticsTransferQualityController() {
        return new ProjectStatisticsTransferQualityController();
    }

    @Bean
    public ProjectStatisticsPendingCloseController projectStatisticsPendingCloseController() {
        return new ProjectStatisticsPendingCloseController();
    }

    @Bean
    public DwdCarryoverBillIncomeCostDetailStatisticsController carryoverBillIncomeCostDetailStatisticsController() {
        return new DwdCarryoverBillIncomeCostDetailStatisticsController();
    }

    @Bean
    public DwdContractReceiptDetailStatisticsController contractReceiptDetailStatisticsController() {
        return new DwdContractReceiptDetailStatisticsController();
    }

    @Bean
    public DwdInvoiceSaleContractDetailStatisticsController dwdInvoiceSaleContractDetailStatisticsController() {
        return new DwdInvoiceSaleContractDetailStatisticsController();
    }

    @Bean
    public DwdInsideHoursEmsController dwdInsideHoursEmsController() {
        return new DwdInsideHoursEmsController();
    }

    @Bean
    public DwdInternalHoursController dwdInternalHoursController() {
        return new DwdInternalHoursController();
    }

    @Bean
    public DwdProjectCostPaymentController dwdProjectCostPaymentController() {
        return new DwdProjectCostPaymentController();
    }

    @Bean
    public DwdPurchaseContractPaymentController dwdPurchaseContractPaymentController() {
        return new DwdPurchaseContractPaymentController();
    }

    @Bean
    public DwdToandfroMaterialController dwdToandfroMaterialController() {
        return new DwdToandfroMaterialController();
    }

    @Bean
    public DwdPaymentRefundController dwdPaymentRefundController() {
        return new DwdPaymentRefundController();
    }

    @Bean
    public DwdProjectIncomeSummaryController dwdProjectIncomeSummaryController() {
        return new DwdProjectIncomeSummaryController();
    }

    @Bean
    public DwsReceiptExclusiveTaxBalanceController dwsReceiptExclusiveTaxBalanceController() {
        return new DwsReceiptExclusiveTaxBalanceController();
    }

    @Bean
    public DwdProjectBudgetChangeController dwdProjectBudgetChangeController() {
        return new DwdProjectBudgetChangeController();
    }

    @Bean
    public DwdPurchaseContractOutSourceController dwdPurchaseContractOutSourceController() {
        return new DwdPurchaseContractOutSourceController();
    }

    @Bean
    public DmSubjectUnitController dmSubjectUnitController() {
        return new DmSubjectUnitController();
    }

    @Bean
    public DmSubjectStandardController dmSubjectStandardController() {
        return new DmSubjectStandardController();
    }

    @Bean
    public DwdProjectCustomerSatisfactionController dwdProjectCustomerSatisfactionController() {
        return new DwdProjectCustomerSatisfactionController();
    }

    @Bean
    public DwdProjectSecurityIncidentController dwdProjectSecurityIncidentController() {
        return new DwdProjectSecurityIncidentController();
    }

    @Bean
    public DwdProjectProblemController dwdProjectProblemController() {
        return new DwdProjectProblemController();
    }

    @Bean
    public DwdWorkingHourController dwdWorkingHourController() {
        return new DwdWorkingHourController();
    }

    @Bean
    public DwdIhrAttendDetailController dwdIhrAttendDetailController() {
        return new DwdIhrAttendDetailController();
    }

    @Bean
    public DwdOrgUserController dwdOrgUserController() {
        return new DwdOrgUserController();
    }

    @Bean
    public QbiController qbiController() {
        return new QbiController();
    }

    @Bean
    public LabelUserUnitOuController labelUserUnitOuController() {
        return new LabelUserUnitOuController();
    }

    @Bean
    public DwsWorkingHourIhrAttendController dwsWorkingHourIhrAttendController() {
        return new DwsWorkingHourIhrAttendController();
    }

    @Bean
    public MaterialPriceReceiptStatisticsController materialPriceReceiptStatisticsController() {
        return new MaterialPriceReceiptStatisticsController();
    }

    @Bean
    public AssetStatisticsController assetStatisticsController() {
        return new AssetStatisticsController();
    }

    @Bean
    public AssetDeprnAccountingStatisticsController assetDeprnAccountingStatisticsController() {
        return new AssetDeprnAccountingStatisticsController();
    }
}
