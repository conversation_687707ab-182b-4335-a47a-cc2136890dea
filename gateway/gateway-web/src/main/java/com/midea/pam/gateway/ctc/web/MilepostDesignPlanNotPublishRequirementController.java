package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanNotPublishRequirement;
import com.midea.pam.common.ctc.excelVo.MilepostDesignPlanNotPublishRequirementExcelVO;
import com.midea.pam.common.ctc.query.MilepostDesignPlanNotPublishRequirementQuery;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.common.utils.BeanConverter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Api("详设未发布需求")
@RestController
@RequestMapping("milepostDesignPlanNotPublishRequirement")
public class MilepostDesignPlanNotPublishRequirementController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查询列表")
    @PostMapping("page")
    public Response page(@RequestBody MilepostDesignPlanNotPublishRequirementQuery query) {
        Guard.notNull(query, "查询列表条件为空");
        String url = String.format("%smilepostDesignPlanNotPublishRequirement/page", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, query, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<MilepostDesignPlanNotPublishRequirement>>>() {
        });
    }

    @ApiOperation(value = "列表导出")
    @PostMapping("export")
    public void listExport(HttpServletResponse response, @RequestBody MilepostDesignPlanNotPublishRequirementQuery query) {
        Guard.notNull(query, "列表导出条件为空");
        String url = String.format("%smilepostDesignPlanNotPublishRequirement/export", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, query, String.class);

        DataResponse<List<MilepostDesignPlanNotPublishRequirement>> dataResponse = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<List<MilepostDesignPlanNotPublishRequirement>>>() {
                });
        List<MilepostDesignPlanNotPublishRequirement> requirementList = dataResponse.getData();

        List<MilepostDesignPlanNotPublishRequirementExcelVO> excelVos = BeanConverter.copy(requirementList,
                MilepostDesignPlanNotPublishRequirementExcelVO.class);
        //导出操作
        ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", MilepostDesignPlanNotPublishRequirementExcelVO.class,
                "详设未发布需求列表.xls", response);
    }

    @ApiOperation(value = "手动执行刷新数据定时任务")
    @PostMapping("refresh")
    public Response refresh() {
        String url = String.format("%smilepostDesignPlanNotPublishRequirement/refresh", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, null, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }
}
