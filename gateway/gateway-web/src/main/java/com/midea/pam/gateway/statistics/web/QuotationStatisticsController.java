package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.mcomponent.core.util.Assert;
import com.midea.mframework.sdk.mform.utills.DownLoadUtils;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.IdEntity;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.crm.dto.BusinessDto;
import com.midea.pam.common.crm.dto.PlanDto;
import com.midea.pam.common.crm.dto.PlanHumanCostDto;
import com.midea.pam.common.crm.dto.PlanOtherCostDto;
import com.midea.pam.common.crm.dto.PlanProductCostDto;
import com.midea.pam.common.crm.dto.PlanTripCostDto;
import com.midea.pam.common.crm.dto.QuotationDetailDto;
import com.midea.pam.common.crm.dto.QuotationDto;
import com.midea.pam.common.crm.dto.QuotationExportDto;
import com.midea.pam.common.crm.entity.QuotationDetail;
import com.midea.pam.common.crm.excelVo.QuotationExcelVO;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.gateway.entity.FileInfo;
import com.midea.pam.common.util.BeanConverter;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.utils.DateUtil;
import com.midea.pam.gateway.config.OssServiceProperties;
import com.midea.pam.gateway.service.OssService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFClientAnchor;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFPatriarch;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/2
 * @description
 */
@Api("报价单")
@RestController
@RequestMapping("statistics/quotation")
public class QuotationStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private OssService ossService;

    @Resource
    private OssServiceProperties ossServiceProperties;


    @ApiOperation(value = "报价单列表")
    @GetMapping("selectList")
    public Response selectList(@RequestParam Boolean isMe,
                               @RequestParam(required = false) @ApiParam(value = "报价单号") String quotationNum,
                               @RequestParam(required = false) @ApiParam(value = "报价单名称") String quotationName,
                               @RequestParam(required = false) @ApiParam(value = "商机名称") String businessName,
                               @RequestParam(required = false) @ApiParam(value = "客户名称") String customerName,
                               @RequestParam(required = false) @ApiParam(value = "销售经理名称") String salesManagerName,
                               @RequestParam(required = false) @ApiParam(value = "报价单类型") Boolean orNotSimple,
                               @RequestParam(required = false) @ApiParam(value = "创建人名称") String createByName,
                               @RequestParam(required = false) @ApiParam(value = "状态(多选)") String statusStr,
                               @RequestParam(required = false) @ApiParam(value = "创建日期开始") String createAtStart,
                               @RequestParam(required = false) @ApiParam(value = "创建日期结束") String createAtEnd,
                               @RequestParam(required = false) @ApiParam(value = "审批通过日期开始") String checkAtStart,
                               @RequestParam(required = false) @ApiParam(value = "审批通过日期结束") String checkAtEnd,
                               @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                               @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> param = new HashMap<>(16);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("me", isMe);
        param.put("num", quotationNum);
        param.put("name", quotationName);
        param.put("businessName", businessName);
        param.put("customerName", customerName);
        param.put("statusStr", statusStr);
        param.put("createAtStart", createAtStart);
        param.put("createAtEnd", createAtEnd);
        param.put("checkAtStart", checkAtStart);
        param.put("checkAtEnd", checkAtEnd);
        param.put("salesManagerName", salesManagerName);
        param.put("createByName", createByName);
        param.put("orNotSimple", orNotSimple);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/quotation/selectList", param);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<QuotationDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<QuotationDto>>>() {
        });

        return response;
    }

    @ApiOperation(value = "导出报价单")
    @GetMapping("export")
    public void export(HttpServletResponse response,
                       @RequestParam Boolean isMe,
                       @RequestParam(required = false) @ApiParam(value = "报价单号") String quotationNum,
                       @RequestParam(required = false) @ApiParam(value = "报价单名称") String quotationName,
                       @RequestParam(required = false) @ApiParam(value = "商机名称") String businessName,
                       @RequestParam(required = false) @ApiParam(value = "客户名称") String customerName,
                       @RequestParam(required = false) @ApiParam(value = "销售经理名称") String salesManagerName,
                       @RequestParam(required = false) @ApiParam(value = "报价单类型") Boolean orNotSimple,
                       @RequestParam(required = false) @ApiParam(value = "创建人名称") String createByName,
                       @RequestParam(required = false) @ApiParam(value = "状态(多选)") String statusStr,
                       @RequestParam(required = false) @ApiParam(value = "创建日期开始") String createAtStart,
                       @RequestParam(required = false) @ApiParam(value = "创建日期结束") String createAtEnd,
                       @RequestParam(required = false) @ApiParam(value = "审批通过日期开始") String checkAtStart,
                       @RequestParam(required = false) @ApiParam(value = "审批通过日期结束") String checkAtEnd) {
        final Map<String, Object> param = new HashMap<>(16);
        param.put("pageNum", 1);
        param.put("pageSize", Integer.MAX_VALUE);
        param.put("me", isMe);
        param.put("num", quotationNum);
        param.put("name", quotationName);
        param.put("businessName", businessName);
        param.put("customerName", customerName);
        param.put("statusStr", statusStr);
        param.put("createAtStart", createAtStart);
        param.put("createAtEnd", createAtEnd);
        param.put("checkAtStart", checkAtStart);
        param.put("checkAtEnd", checkAtEnd);
        param.put("salesManagerName", salesManagerName);
        param.put("createByName", createByName);
        param.put("orNotSimple", orNotSimple);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/quotation/selectList", param);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<QuotationDto>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<QuotationDto>>>() {
        });
        List<QuotationDto> quotationDtoList = dataResponse.getData().getList();
        List<QuotationExcelVO> excelVoList = BeanConverter.copy(quotationDtoList, QuotationExcelVO.class);
        ExportExcelUtil.exportExcel(excelVoList, null, "报价单列表", QuotationExcelVO.class, "报价单列表_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls", response);
    }

    @ApiOperation(value = "报价单-详情导出")
    @GetMapping("detail/export/{id}")
    public void detailExport(HttpServletResponse response, @PathVariable @ApiParam("报价单id") Long id) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.CRM.getBaseUrl(), "/quotation/getExportDataById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<QuotationExportDto> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<QuotationExportDto>>() {
        });
        assert dataResponse != null;
        Assert.isTrue(Objects.equals(0, dataResponse.getCode()), dataResponse.getMsg());
        Assert.notNull(dataResponse.getData(), "查询报价单-详情失败!");

        QuotationExportDto dto = dataResponse.getData();
        // 创建新的Excel 工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();
        this.createSheet(workbook, dto);

        //导出操作
        StringBuilder fileName = new StringBuilder();
        fileName.append(dto.getName()).append("-").append(DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);

    }

    /**
     * @param workbook
     * @param po
     */
    private void createSheet(HSSFWorkbook workbook, QuotationExportDto po) {

        HSSFSheet sheet = workbook.createSheet("报价单");
        HSSFCellStyle headStyle = this.getHeadStyle(workbook);
        HSSFCellStyle descLeftStyle = this.getDescLeftStyle(workbook);
        HSSFCellStyle descRightStyle = this.getDescRightStyle(workbook);
        HSSFCellStyle classStyle = this.getClassStyle(workbook, 14);
        HSSFCellStyle classStyle2 = this.getClassStyle(workbook, 12);
        HSSFCellStyle titleStyle = this.getTitleStyle(workbook);
        HSSFCellStyle contentStyle = this.getContentStyle(workbook, XSSFCellStyle.ALIGN_CENTER, false);
        HSSFCellStyle contentBoldStyle = this.getContentStyle(workbook, XSSFCellStyle.ALIGN_CENTER, true);
        HSSFCellStyle contentLeftStyle = this.getContentStyle(workbook, XSSFCellStyle.ALIGN_LEFT, false);
        HSSFCellStyle contentRightStyle = this.getContentStyle(workbook, XSSFCellStyle.ALIGN_RIGHT, false);
        HSSFCellStyle totalStyle = this.getTotalStyle(workbook);
        HSSFCellStyle planTotalStyle = this.getPlanTotalStyle(workbook);
        HSSFCellStyle quotationTotalStyle = this.getQuotationTotalStyle(workbook);
        HSSFCellStyle remarkStyle = this.getRemarkStyle(workbook);
        sheet.setColumnWidth(0, 7200);
        sheet.setColumnWidth(1, 10000);
        sheet.setColumnWidth(2, 10000);
        sheet.setColumnWidth(3, 4000);
        sheet.setColumnWidth(4, 4000);
        sheet.setColumnWidth(5, 10000);
        sheet.setColumnWidth(6, 6500);
        if (Objects.equals(po.getUnitId(), Long.parseLong("884455544528568320"))) { //上海瑞仕格医疗科技有限公司
            this.addLogo(workbook);
        }
        // 设置需要合并的单元格
        CellRangeAddress mergedRegion = new CellRangeAddress(0, 0, 0, 5);
        sheet.addMergedRegion(mergedRegion);
        HSSFRow row0 = ExportExcelUtil.createCell(sheet, po.getName(), 0, 0, headStyle);
        row0.setHeightInPoints(40);
        BusinessDto business = po.getBusiness();
        PlanDto planDto = po.getPlanDto();
        List<QuotationDetailDto> detailDtos = po.getDetailDtos();
        Map<String, BigDecimal> planTotalMap = detailDtos.stream().map(e -> {
                    setDetailType(e);
                    return e;
                })
                .collect(Collectors.groupingBy(QuotationDetail::getDetailType
                        , Collectors.collectingAndThen(Collectors.toList(), m -> m.stream()
                                .map(QuotationDetail::getPrice).reduce(BigDecimal.ZERO, BigDecimalUtils::add))));
        Map<String, BigDecimal> quotationTotalMap = detailDtos.stream().map(e -> {
                    setDetailType(e);
                    return e;
                })
                .collect(Collectors.groupingBy(QuotationDetail::getDetailType
                        , Collectors.collectingAndThen(Collectors.toList(), m -> m.stream()
                                .map(QuotationDetail::getAfterTaxDiscount).reduce(BigDecimal.ZERO, BigDecimalUtils::add))));
        HSSFRow row1 = ExportExcelUtil.createCell(sheet, "Customer Name 客户名：", 1, 0, descRightStyle);
        addMergedRegionHead(sheet, 1);
        ExportExcelUtil.createCell(row1, "Project Name 项目名称：", 3, descRightStyle);
        ExportExcelUtil.createCell(row1, business.getCustomerName(), 1, descLeftStyle);
        ExportExcelUtil.createCell(row1, business.getName(), 5, descLeftStyle);

        HSSFRow row2 = ExportExcelUtil.createCell(sheet, "Quotation Date 报价日期：", 2, 0, descRightStyle);
        addMergedRegionHead(sheet, 2);
        ExportExcelUtil.createCell(row2, "Product Intention 产品意向：", 3, descRightStyle);
        ExportExcelUtil.createCell(row2, DateUtil.format(po.getCreateAt(), DateUtil.TIMESTAMP_PATTERN), 1, descLeftStyle);
        ExportExcelUtil.createCell(row2, business.getProductIntentionName(), 5, descLeftStyle);

        HSSFRow row3 = ExportExcelUtil.createCell(sheet, "Valid Until 报价单有效期至：", 3, 0, descRightStyle);
        addMergedRegionHead(sheet, 3);
        ExportExcelUtil.createCell(row3, "QuotCtion  Version 报价单版本：", 3, descRightStyle);

        HSSFRow row4 = ExportExcelUtil.createCell(sheet, "Contacts 联系人：", 4, 0, descRightStyle);
        addMergedRegionHead(sheet, 4);
        ExportExcelUtil.createCell(row4, "Delivery DCte交货期：", 3, descRightStyle);
        if (business.getCreateBy() != null) {
            UserInfo userInfo = CacheDataUtils.findUserById(business.getCreateBy());
            if (userInfo != null) {
                ExportExcelUtil.createCell(row4, userInfo.getName(), 1, descLeftStyle);
            }
        }

        HSSFRow row5 = ExportExcelUtil.createCell(sheet, "Exchange Rate 汇率：", 5, 0, descRightStyle);
        addMergedRegionHead(sheet, 5);
        ExportExcelUtil.createCell(row5, "Warrenty 质保期：", 3, descRightStyle);
        ExportExcelUtil.createCell(row5, po.getExchangeRate().stripTrailingZeros().toPlainString(), 1, descLeftStyle);
        ExportExcelUtil.createCell(row5, planDto.getGuaranteePeriod(), 5, descLeftStyle);

        int rowNum = 7;
        List<PlanProductCostDto> planProductCostDtos = po.getPlanProductCostDtos();
        if (ListUtils.isNotEmpty(planProductCostDtos)) {
            ExportExcelUtil.createCell(sheet, "产品/物料报价", rowNum, 0, classStyle);
            HSSFRow row_10 = ExportExcelUtil.createCell(sheet, "序号", ++rowNum, 0, titleStyle);
            ExportExcelUtil.createCell(row_10, "ERP编码", 1, titleStyle);
            ExportExcelUtil.createCell(row_10, "中文名称", 2, titleStyle);
            ExportExcelUtil.createCell(row_10, "数量", 3, titleStyle);
            ExportExcelUtil.createCell(row_10, "单位", 4, titleStyle);
            ExportExcelUtil.createCell(row_10, "销售价格 ", 5, titleStyle);
            ExportExcelUtil.createCell(row_10, "总价", 6, titleStyle);
            List<PlanProductCostDto> newDto = planProductCostDtos.stream().sorted(Comparator.comparing(IdEntity::getId)).collect(Collectors.toList());
            for (int i = 0; i < newDto.size(); i++) {
                PlanProductCostDto planProductCostDto = newDto.get(i);
                HSSFRow row_i = ExportExcelUtil.createCell(sheet, planProductCostDto.getOrderNo(), ++rowNum, 0, contentLeftStyle);
                ExportExcelUtil.createCell(row_i, planProductCostDto.getErpCode(), 1, contentLeftStyle);
                ExportExcelUtil.createCell(row_i, planProductCostDto.getName(), 2, contentLeftStyle);
                ExportExcelUtil.createCell(row_i, bigDecimalRoundFormat(planProductCostDto.getAmount(), 2), 3, contentStyle);
                ExportExcelUtil.createCell(row_i, planProductCostDto.getUnit(), 4, contentStyle);
                ExportExcelUtil.createCell(row_i, bigDecimalRoundFormat(planProductCostDto.getUnitPrice(), 6), 5, contentRightStyle);
                ExportExcelUtil.createCell(row_i, bigDecimalRoundFormat(planProductCostDto.getSubtotal(), 2), 6, contentRightStyle);
            }

            HSSFRow row6 = ExportExcelUtil.createCell(sheet, "总价：", ++rowNum, 4, totalStyle);
            ExportExcelUtil.createCell(row6, planDto.getCurrencyCode(), 5, planTotalStyle);
            ExportExcelUtil.createCell(row6, bigDecimalRoundFormat(planTotalMap.getOrDefault("1", BigDecimal.ZERO), 2), 6, planTotalStyle);
            HSSFRow row7 = ExportExcelUtil.createCell(sheet, "最终产品物料总价：", ++rowNum, 4, totalStyle);
            ExportExcelUtil.createCell(row7, po.getToCurrency(), 5, quotationTotalStyle);
            ExportExcelUtil.createCell(row7, bigDecimalRoundFormat(quotationTotalMap.getOrDefault("1", BigDecimal.ZERO), 2), 6, quotationTotalStyle);
            ++rowNum;
            ++rowNum;
            ++rowNum;
        }
        //人力成本
        List<PlanHumanCostDto> planHumanCostDtos = po.getPlanHumanCostDtos();
        //差旅成本
        List<PlanTripCostDto> planTripCostDtos = po.getPlanTripCostDtos();
        if (ListUtils.isNotEmpty(planHumanCostDtos) || ListUtils.isNotEmpty(planTripCostDtos)) {
            ExportExcelUtil.createCell(sheet, "管理费用报价", rowNum, 0, classStyle);
            ExportExcelUtil.createCell(sheet, "管理费用-人力报价", ++rowNum, 0, classStyle2);
            HSSFRow row_10 = ExportExcelUtil.createCell(sheet, "序号", ++rowNum, 0, titleStyle);
            addMergedRegion(sheet, rowNum, 3, 4);
            ExportExcelUtil.createCell(row_10, "职级/角色/层级/名称", 1, titleStyle);
            ExportExcelUtil.createCell(row_10, "投入人数", 2, titleStyle);
            ExportExcelUtil.createCell(row_10, "单人投入天数", 3, titleStyle);
            ExportExcelUtil.createCell(row_10, "人力单价", 5, titleStyle);
            ExportExcelUtil.createCell(row_10, "总价 ", 6, titleStyle);
            int size = planHumanCostDtos.size();
            for (int i = 0; i < size; i++) {
                PlanHumanCostDto planHumanCostDto = planHumanCostDtos.get(i);
                HSSFRow row_i = ExportExcelUtil.createCell(sheet, String.valueOf(i + 1), ++rowNum, 0, contentStyle);
                addMergedRegion(sheet, rowNum, 3, 4);
                ExportExcelUtil.createCell(row_i, getHumanCostName(planHumanCostDto), 1, contentStyle);
                ExportExcelUtil.createCell(row_i, String.valueOf(planHumanCostDto.getNumberPerson()), 2, contentStyle);
                ExportExcelUtil.createCell(row_i, bigDecimalRoundFormat(planHumanCostDto.getSinglePersonInvestmentDays(), 3), 3, contentStyle);
                ExportExcelUtil.createCell(row_i, bigDecimalRoundFormat(planHumanCostDto.getPersonDailyPrice(), 6), 5, contentRightStyle);
                ExportExcelUtil.createCell(row_i, bigDecimalRoundFormat(planHumanCostDto.getTotalPrice(), 2), 6, contentRightStyle);
            }
            for (int i = 0; i < planTripCostDtos.size(); i++) {
                PlanTripCostDto planTripCostDto = planTripCostDtos.get(i);
                HSSFRow row_i = ExportExcelUtil.createCell(sheet, String.valueOf(i + size + 1), ++rowNum, 0, contentStyle);
                addMergedRegion(sheet, rowNum, 3, 4);
                ExportExcelUtil.createCell(row_i, getTripCostName(planTripCostDto), 1, contentStyle);
                ExportExcelUtil.createCell(row_i, "1", 2, contentStyle);
                ExportExcelUtil.createCell(row_i, String.valueOf(planTripCostDto.getBusinessTripDays()), 3, contentStyle);
                ExportExcelUtil.createCell(row_i, bigDecimalRoundFormat(planTripCostDto.getOpenPrice().divide(new BigDecimal(planTripCostDto.getBusinessTripDays()), 4, RoundingMode.HALF_UP), 4), 5, contentStyle);
                ExportExcelUtil.createCell(row_i, bigDecimalRoundFormat(planTripCostDto.getOpenPrice(), 2), 6, contentStyle);
            }

            HSSFRow row6 = ExportExcelUtil.createCell(sheet, "总价：", ++rowNum, 4, totalStyle);
            ExportExcelUtil.createCell(row6, planDto.getCurrencyCode(), 5, planTotalStyle);
            ExportExcelUtil.createCell(row6, bigDecimalRoundFormat(planTotalMap.getOrDefault("2", BigDecimal.ZERO).add(planTotalMap.getOrDefault("3", BigDecimal.ZERO)), 2), 6, planTotalStyle);
            HSSFRow row7 = ExportExcelUtil.createCell(sheet, "最终人力总价：", ++rowNum, 4, totalStyle);
            ExportExcelUtil.createCell(row7, po.getToCurrency(), 5, quotationTotalStyle);
            ExportExcelUtil.createCell(row7, bigDecimalRoundFormat(quotationTotalMap.getOrDefault("2", BigDecimal.ZERO).add(quotationTotalMap.getOrDefault("3", BigDecimal.ZERO)), 2), 6, quotationTotalStyle);
            ++rowNum;
            ++rowNum;
            ++rowNum;
        }
        //其他成本
        List<PlanOtherCostDto> planOtherCostDtos = po.getPlanOtherCostDtos();
        if (ListUtils.isNotEmpty(planOtherCostDtos)) {
            ExportExcelUtil.createCell(sheet, "管理费用-其他费用报价", rowNum, 0, classStyle2);
            HSSFRow row_10 = ExportExcelUtil.createCell(sheet, "序号", ++rowNum, 0, titleStyle);
            addMergedRegion(sheet, rowNum, 2, 5);
            ExportExcelUtil.createCell(row_10, "费用类型", 1, titleStyle);
            for (int i = 2; i <= 5; i++) {
                ExportExcelUtil.createCell(row_10, null, i, titleStyle);
            }
            ExportExcelUtil.createCell(row_10, "总价", 6, titleStyle);
//            for (int i = 0; i < planOtherCostDtos.size(); i++) {
//                PlanOtherCostDto planOtherCostDto = planOtherCostDtos.get(i);
//                BigDecimal openPrice = planOtherCostDto.getOpenPrice();
//                HSSFRow row_i = ExportExcelUtil.createCell(sheet, String.valueOf(i + 1), ++rowNum, 0, contentStyle);
//                ExportExcelUtil.createCell(row_i, planOtherCostDto.getFeeItemName(), 1, contentStyle);
//                ExportExcelUtil.createCell(row_i, bigDecimalRoundFormat(openPrice, 2), 2, contentStyle);
//                ExportExcelUtil.createCell(row_i, "", 3, contentStyle);
//                CellRangeAddress mergedRegion3 = new CellRangeAddress(rowNum, rowNum, 2, 3);
//                sheet.addMergedRegion(mergedRegion3);
//            }
            HSSFRow row_i = ExportExcelUtil.createCell(sheet, "1", ++rowNum, 0, contentStyle);
            addMergedRegion(sheet, rowNum, 2, 5);
            ExportExcelUtil.createCell(row_i, "其他费用", 1, contentStyle);
            ExportExcelUtil.createCell(row_i, bigDecimalRoundFormat(planTotalMap.getOrDefault("4", BigDecimal.ZERO), 2), 6, contentStyle);
            HSSFRow row6 = ExportExcelUtil.createCell(sheet, "总价：", ++rowNum, 4, totalStyle);
            ExportExcelUtil.createCell(row6, planDto.getCurrencyCode(), 5, planTotalStyle);
            ExportExcelUtil.createCell(row6, bigDecimalRoundFormat(planTotalMap.getOrDefault("4", BigDecimal.ZERO), 2), 6, planTotalStyle);
            HSSFRow row7 = ExportExcelUtil.createCell(sheet, "最终其他费用报价：", ++rowNum, 4, totalStyle);
            ExportExcelUtil.createCell(row7, po.getToCurrency(), 5, quotationTotalStyle);
            ExportExcelUtil.createCell(row7, bigDecimalRoundFormat(quotationTotalMap.getOrDefault("4", BigDecimal.ZERO), 2), 6, quotationTotalStyle);
            ++rowNum;
            HSSFRow row8 = ExportExcelUtil.createCell(sheet, "管理费用总价：", ++rowNum, 4, totalStyle);
            ExportExcelUtil.createCell(row8, planDto.getCurrencyCode(), 5, planTotalStyle);
            ExportExcelUtil.createCell(row8, bigDecimalRoundFormat(BigDecimalUtils.add(planTotalMap.get("2"), planTotalMap.get("4")), 2), 6, planTotalStyle);
            HSSFRow row9 = ExportExcelUtil.createCell(sheet, "最终管理费用总价：", ++rowNum, 4, totalStyle);
            ExportExcelUtil.createCell(row9, po.getToCurrency(), 5, quotationTotalStyle);
            ExportExcelUtil.createCell(row9, bigDecimalRoundFormat(BigDecimalUtils.add(quotationTotalMap.get("2"), quotationTotalMap.get("4")), 2), 6, quotationTotalStyle);
            ++rowNum;
            ++rowNum;
            ++rowNum;
        }
        ExportExcelUtil.createCell(sheet, "最终报价", rowNum, 0, classStyle);
        HSSFRow row_10 = ExportExcelUtil.createCell(sheet, "本位币报价金额（含税）", ++rowNum, 0, titleStyle);
        addMergedRegion(sheet, rowNum, 2, 3);
        addMergedRegion(sheet, rowNum, 4, 6);
        ExportExcelUtil.createCell(row_10, "本位币报价金额（不含税）", 1, titleStyle);
        ExportExcelUtil.createCell(row_10, "报价币种金额（含税）", 2, titleStyle);
        ExportExcelUtil.createCell(row_10, "报价币种金额（不含税）", 4, titleStyle);
        HSSFRow row_11 = ExportExcelUtil.createCell(sheet, bigDecimalRoundFormat(po.getContractAmount(), 2), ++rowNum, 0, contentBoldStyle);
        addMergedRegion(sheet, rowNum, 2, 3);
        addMergedRegion(sheet, rowNum, 4, 6);
        ExportExcelUtil.createCell(row_11, bigDecimalRoundFormat(po.getQuoteWithoutTax(), 2), 1, contentBoldStyle);
        ExportExcelUtil.createCell(row_11, bigDecimalRoundFormat(po.getContractAmount(), 2), 2, contentBoldStyle);
        ExportExcelUtil.createCell(row_11, bigDecimalRoundFormat(po.getQuoteWithoutTax(), 2), 4, contentBoldStyle);
        ++rowNum;
        ++rowNum;
        ++rowNum;
        ExportExcelUtil.createCell(sheet, "备注：", ++rowNum, 0, remarkStyle);
        ExportExcelUtil.createCell(sheet, "1、以上为含税价格，税率13%；", ++rowNum, 0, remarkStyle);
        ExportExcelUtil.createCell(sheet, "2、以上材料按图纸实际测量，不包含项目现场变更；", ++rowNum, 0, remarkStyle);
        ExportExcelUtil.createCell(sheet, "3、以上报价含1年质保；", ++rowNum, 0, remarkStyle);
        ExportExcelUtil.createCell(sheet, "4、超出部分以报价清单内单价结算；", ++rowNum, 0, remarkStyle);
        ExportExcelUtil.createCell(sheet, "5、汇率上下浮动超过5%需更新报价；", ++rowNum, 0, remarkStyle);
        ExportExcelUtil.createCell(sheet, "6、报价不含BIM图工作，如有需求需调整报价；", ++rowNum, 0, remarkStyle);
    }

    /**
     * 合并单元格（表头）
     *
     * @param sheet
     * @param rowNum
     */
    private void addMergedRegionHead(HSSFSheet sheet, int rowNum) {
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 3, 4));
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 5, 6));
        HSSFRow row = sheet.getRow(rowNum);
        if (row != null) {
            // 设置下边框
            HSSFCellStyle style = sheet.getWorkbook().createCellStyle();
            style.setBorderBottom(HSSFCellStyle.BORDER_DASHED);
            for (int i = 3; i <= 6; i++) {
                // 创建单元格
                HSSFCell cell = row.createCell(i);
                cell.setCellStyle(style);
            }
        }
    }

    /**
     * 合并单元格
     *
     * @param sheet
     * @param rowNum
     * @param firCol
     * @param lastCol
     */
    private void addMergedRegion(HSSFSheet sheet, int rowNum, int firCol, int lastCol) {
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, firCol, lastCol));
        HSSFRow row = sheet.getRow(rowNum);
        if (row != null) {
            // 设置边框
            HSSFCellStyle style = sheet.getWorkbook().createCellStyle();
            style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
            style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
            style.setBorderRight(HSSFCellStyle.BORDER_THIN);
            style.setBorderTop(HSSFCellStyle.BORDER_THIN);
            for (int i = firCol; i <= lastCol; i++) {
                // 创建单元格
                HSSFCell cell = row.createCell(i);
                cell.setCellStyle(style);
            }
        }
    }

    private void setDetailType(QuotationDetailDto dto) {
        // 获取 行对应分类
        if (Objects.nonNull(dto.getFeeName()) && dto.getFeeName().contains("产品")) {
            dto.setDetailType("1");
        } else if (Objects.nonNull(dto.getFeeName()) && dto.getFeeName().contains("人力")) {
            dto.setDetailType("2");
        } else if (Objects.nonNull(dto.getFeeName()) && dto.getFeeName().contains("差旅")) {
            dto.setDetailType("3");
        } else {
            dto.setDetailType("4");
        }
    }

    private void addLogo(HSSFWorkbook workbook) {
        // 获取第一个工作表
        HSSFSheet sheet = workbook.getSheetAt(0);
        //插入图片
        //给标题插入图片
        // 先把读进来的图片放到一个ByteArrayOutputStream中，以便产生ByteArray
        ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
        BufferedImage bufferImg = null;
        try {
            //根据组织参数获取logo文件id
            String fileId = getFileId();
            InputStream inputStream = logoDownload(Long.valueOf(fileId));
            bufferImg = ImageIO.read(inputStream);
            // 将图片写入流中
            ImageIO.write(bufferImg, "png", byteArrayOut);
            // 利用HSSFPatriarch将图片写入EXCEL
            HSSFPatriarch patriarch = sheet.createDrawingPatriarch();
            /**
             * 该构造函数有8个参数
             * int dx1, int dy1, int dx2, int dy2,
             * short col1, int row1, short col2, int row2
             * excel中的cellNum和rowNum的index都是从0开始的
             */
            //图片一导出到单元格B2中
            HSSFClientAnchor anchor = new HSSFClientAnchor(10, 10, 10, 10,
                    (short) 6, 0, (short) 7, 1);
            // 插入图片
            patriarch.createPicture(anchor, workbook.addPicture(byteArrayOut
                    .toByteArray(), HSSFWorkbook.PICTURE_TYPE_PNG));

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private InputStream logoDownload(Long fileId) {
        FileInfo download = ossService.download(fileId);
        if (Objects.isNull(download) || StringUtils.isEmpty(download.getDocId())) {
            throw new BizException(Code.ERROR, "Logo文件不存在");
        }
        String docId = download.getDocId();
        String downLoadRestUrl = ossServiceProperties.getUploadRestUrlHeader() + ossServiceProperties.getDownLoadServiceName();
        return DownLoadUtils.restDownloadFile(downLoadRestUrl, docId);
    }

    private String getFileId() {
        Map<String, Object> param = new HashMap<>();
        param.put("orgId", "884455544528568320");
        param.put("orgFrom", "company");
        param.put("name", "报价单导出logo");
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/organizationCustomDict/queryByName", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Set<String>> response = JSON.parseObject(res, new TypeReference<DataResponse<Set<String>>>() {
        });
        assert response != null;
        if (response.getData().isEmpty()) {
            throw new BizException(Code.ERROR, "根据组织参数配置获取logo失败");
        }
        return response.getData().iterator().next();
    }

    /**
     * 报价单名称--样式
     *
     * @param workbook
     * @return
     */
    private HSSFCellStyle getHeadStyle(HSSFWorkbook workbook) {
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD); // 字体增粗
        font.setFontHeightInPoints((short) 20);
        // 设置head样式
        HSSFCellStyle style = workbook.createCellStyle();
        style.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        style.setAlignment(XSSFCellStyle.ALIGN_CENTER);
        style.setFont(font);
        return style;
    }

    /**
     * 说明左对齐--样式
     *
     * @param workbook
     * @return
     */
    private HSSFCellStyle getDescLeftStyle(HSSFWorkbook workbook) {
        // 设置字体
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 10);
        // 设置样式
        HSSFCellStyle style = workbook.createCellStyle();
        style.setVerticalAlignment(CellStyle.VERTICAL_CENTER);// 上下居中
        style.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        style.setFont(font);
        // 设置下边框
        style.setBorderBottom(HSSFCellStyle.BORDER_DASHED);
        return style;
    }

    /**
     * 说明右对齐--样式
     *
     * @param workbook
     * @return
     */
    private HSSFCellStyle getDescRightStyle(HSSFWorkbook workbook) {
        // 设置字体
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 10);
        // 设置样式
        HSSFCellStyle style = workbook.createCellStyle();
        style.setVerticalAlignment(CellStyle.VERTICAL_CENTER);// 上下居中
        style.setAlignment(XSSFCellStyle.ALIGN_RIGHT);
        style.setFont(font);
        // 设置下边框
        style.setBorderBottom(HSSFCellStyle.BORDER_DASHED);
        return style;
    }

    /**
     * 分类--样式：物料、人力等
     *
     * @param workbook
     * @return
     */
    private HSSFCellStyle getClassStyle(HSSFWorkbook workbook, int fontSize) {
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD); // 字体增粗
        font.setFontHeightInPoints((short) fontSize);
        // 设置head样式
        HSSFCellStyle style = workbook.createCellStyle();
        style.setVerticalAlignment(CellStyle.VERTICAL_CENTER);// 上下居中
        style.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        style.setFont(font);
        return style;
    }

    /**
     * 标题--样式
     *
     * @param workbook
     * @return
     */
    private HSSFCellStyle getTitleStyle(HSSFWorkbook workbook) {
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD); // 字体增粗
        font.setFontHeightInPoints((short) 12);
        // 设置head样式
        HSSFCellStyle style = workbook.createCellStyle();
        style.setVerticalAlignment(CellStyle.VERTICAL_CENTER);// 上下居中
        style.setAlignment(XSSFCellStyle.ALIGN_CENTER);
        style.setFont(font);
        style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        style.setFillForegroundColor(HSSFColor.GREY_25_PERCENT.index); //设置单元格颜色
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        return style;
    }

    /**
     * 内容--样式
     *
     * @param workbook
     * @return
     */
    private HSSFCellStyle getContentStyle(HSSFWorkbook workbook, short alignment, boolean bold) {
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        if (bold) {
            font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD); // 字体增粗
        }
        font.setFontHeightInPoints((short) 10);
        // 设置head样式
        HSSFCellStyle style = workbook.createCellStyle();
        style.setVerticalAlignment(CellStyle.VERTICAL_CENTER);// 上下居中
        style.setAlignment(alignment);
        style.setFont(font);
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        return style;
    }

    private HSSFCellStyle getTotalStyle(HSSFWorkbook workbook) {
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD); // 字体增粗
        font.setFontHeightInPoints((short) 12);
        // 设置head样式
        HSSFCellStyle style = workbook.createCellStyle();
        style.setVerticalAlignment(CellStyle.VERTICAL_CENTER);// 上下居中
        style.setAlignment(XSSFCellStyle.ALIGN_RIGHT);
        style.setFont(font);
        return style;
    }

    private HSSFCellStyle getPlanTotalStyle(HSSFWorkbook workbook) {
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD); // 字体增粗
        font.setFontHeightInPoints((short) 12);
        // 设置head样式
        HSSFCellStyle style = workbook.createCellStyle();
        style.setVerticalAlignment(CellStyle.VERTICAL_CENTER);// 上下居中
        style.setAlignment(XSSFCellStyle.ALIGN_RIGHT);
        style.setFont(font);
        style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        style.setFillForegroundColor(HSSFColor.LIGHT_ORANGE.index);
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        return style;
    }

    private HSSFCellStyle getQuotationTotalStyle(HSSFWorkbook workbook) {
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD); // 字体增粗
        font.setFontHeightInPoints((short) 12);
        // 设置head样式
        HSSFCellStyle style = workbook.createCellStyle();
        style.setVerticalAlignment(CellStyle.VERTICAL_CENTER);// 上下居中
        style.setAlignment(XSSFCellStyle.ALIGN_RIGHT);
        style.setFont(font);
        style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        style.setFillForegroundColor(HSSFColor.LIME.index);
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        return style;
    }

    private HSSFCellStyle getRemarkStyle(HSSFWorkbook workbook) {
        // 设置字体
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 10);
        // 设置样式
        HSSFCellStyle style = workbook.createCellStyle();
        style.setVerticalAlignment(CellStyle.VERTICAL_CENTER);// 上下居中
        style.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        style.setFont(font);
        return style;
    }

    private String bigDecimalRoundFormat(BigDecimal b, int scale) {
        if (Objects.isNull(b)) {
            return "-";
        } else {
            return b.setScale(scale, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
        }
    }

    public String getHumanCostName(PlanHumanCostDto humanCostDto) {
        if (Objects.nonNull(humanCostDto.getRoleName())) {
            return humanCostDto.getRoleName();
        } else if (Objects.nonNull(humanCostDto.getLevelName())) {
            return humanCostDto.getLevelName();
        } else {
            return "-";
        }
    }

    public String getTripCostName(PlanTripCostDto tripCostDto) {
        if (Objects.nonNull(tripCostDto.getRoleName())) {
            return tripCostDto.getRoleName();
        } else if (Objects.nonNull(tripCostDto.getLevelName())) {
            return tripCostDto.getLevelName();
        } else {
            return "-";
        }
    }
}
