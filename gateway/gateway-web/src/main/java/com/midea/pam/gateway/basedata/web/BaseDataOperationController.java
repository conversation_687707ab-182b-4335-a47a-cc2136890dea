package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-7-29
 * @description 基础数据运维接口
 */
@Api("基础数据运维接口")
@RestController
@RequestMapping("operation")
public class BaseDataOperationController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "基础数据缓存更新")
    @GetMapping("cache/sync")
    public DataResponse cacheManage(@ApiParam("缓存类型") @RequestParam String type,
                                    @ApiParam("是否强制更新") @RequestParam(required = false) String forceUpdate,
                                    @ApiParam("是否强制更新") @RequestParam(required = false) String isAll,
                                    @ApiParam("认证") @RequestParam String auth) {
        final Map<String, Object> param = new HashMap<>();
        param.put("type", type);
        param.put("forceUpdate", forceUpdate);
        param.put("isAll", isAll);
        param.put("auth", auth);

        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/operation/cache/sync", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<String> response = JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
        return response;
    }

    @ApiOperation(value = "基础数据缓存更新")
    @GetMapping("cache/value")
    public DataResponse find(@ApiParam("缓存类型") @RequestParam String type,
                             @ApiParam("查询值") @RequestParam(required = false) String key,
                             @ApiParam("认证") @RequestParam String auth) {
        final Map<String, Object> param = new HashMap<>();
        param.put("type", type);
        param.put("key", key);
        param.put("auth", auth);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/operation/cache/value", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Object> response = JSON.parseObject(res, new TypeReference<DataResponse<Object>>() {
        });
        return response;
    }

}
