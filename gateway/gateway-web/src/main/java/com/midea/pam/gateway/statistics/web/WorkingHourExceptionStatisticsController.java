package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.WorkingHourAccountingDto;
import com.midea.pam.common.ctc.dto.WorkingHourExceptionDto;
import com.midea.pam.common.ctc.excelVo.RDMWorkingHourTransformErrorExcelVo;
import com.midea.pam.common.ctc.excelVo.WorkingHourAccountingErrorExcelVo;
import com.midea.pam.common.ctc.excelVo.WorkingHourCollectionErrorExcelVo;
import com.midea.pam.common.ctc.excelVo.WorkingHourErrorExcelVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.statistics.entity.WorkingHourCollectionErrorTemporary;
import com.midea.pam.common.statistics.entity.WorkingHourExceptionAccounting;
import com.midea.pam.common.statistics.entity.WorkingHourExceptionRdm;
import com.midea.pam.common.statistics.entity.WorkingHourSubmitErrorTemporary;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

@Api("工时异常数据")
@RestController
@RequestMapping("statistics/workingHourException")
public class WorkingHourExceptionStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "工时填报异常数据导出", response = WorkingHourAccountingDto.class)
    @GetMapping({"exprotWorkingHourError"})
    public void exprotWorkingHourError(HttpServletResponse response,
                                       @RequestParam(required = false) @ApiParam(value = "项目编号") String projectCode,
                                       @RequestParam(required = false) @ApiParam(value = "项目名称") String projectName,
                                       @RequestParam(required = false) @ApiParam(value = "填报人姓名") String userName,
                                       @RequestParam(required = false) @ApiParam(value = "填报人mip") String userMip,
                                       @RequestParam(required = false) @ApiParam(value = "出勤日期开始") String applyDateStart,
                                       @RequestParam(required = false) @ApiParam(value = "出勤日期结束") String applyDateEnd,
                                       @RequestParam(required = false) @ApiParam(value = "填报日期开始") String createAtStart,
                                       @RequestParam(required = false) @ApiParam(value = "填报日期结束") String createAtEnd,
                                       @RequestParam(required = false) @ApiParam(value = "工时状态") String statusStr,
                                       @RequestParam(required = false) @ApiParam(value = "工时变id") Long workingHourId,
                                       @RequestParam(required = false) @ApiParam(value = "是否开票") Integer invoiceApplyFlag,
                                       @RequestParam(required = false) @ApiParam(value = "是否结转") Integer costCollectionFlag,
                                       @RequestParam(required = false) @ApiParam(value = "业务实体") String ouIdStr) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectCode", projectCode);
        param.put("projectName", projectName);
        param.put("userName", userName);
        param.put("userMip", userMip);
        param.put("applyDateStart", applyDateStart);
        param.put("applyDateEnd", applyDateEnd);
        param.put("createAtStart", createAtStart);
        param.put("createAtEnd", createAtEnd);
        param.put("statusStr", statusStr);
        param.put("workingHourId", workingHourId);
        param.put("invoiceApplyFlag", invoiceApplyFlag);
        param.put("costCollectionFlag", costCollectionFlag);
        param.put("ouIdStr", ouIdStr);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/workingHourException/exprotWorkingHourError", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        final DataResponse<Map<String, Object>> dataResponse =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
                });
        Map<String, Object> resultMap = dataResponse.getData();
        JSONArray accountingArr = (JSONArray) resultMap.get("workingHourErrorList");

        List<WorkingHourErrorExcelVo> workingHourErrorExcelVoArrayList = new ArrayList<>();

        if (accountingArr != null) {
            workingHourErrorExcelVoArrayList = JSONObject.parseArray(accountingArr.toJSONString(), WorkingHourErrorExcelVo.class);
        }

        Workbook workbook = ExportExcelUtil.buildDefaultSheet(workingHourErrorExcelVoArrayList, WorkingHourErrorExcelVo.class, null, "sheet1", true);
        ExportExcelUtil.downLoadExcel("工时填报异常_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls", response, workbook);
    }

    @ApiOperation(value = "工时归集异常数据导出", response = WorkingHourAccountingDto.class)
    @GetMapping({"exprotWorkingHourCollectionError"})
    public void exprotWorkingHourCollectionError(HttpServletResponse response,
                                                 @RequestParam(required = false) @ApiParam(value = "归集日期开始") String collectionDateStart,
                                                 @RequestParam(required = false) @ApiParam(value = "归集日期结束") String collectionDateEnd,
                                                 @RequestParam(required = false) @ApiParam(value = "成本发生日期开始") String costDateStart,
                                                 @RequestParam(required = false) @ApiParam(value = "成本发生日期结束") String costDateEnd,
                                                 @RequestParam(required = false) @ApiParam(value = "结转状态") String carryStatus,
                                                 @RequestParam(required = false) @ApiParam(value = "结转期间") String carryGlPeriod,
                                                 @RequestParam(required = false) @ApiParam(value = "项目编号") String projectCode,
                                                 @RequestParam(required = false) @ApiParam(value = "项目名称") String projectName,
                                                 @RequestParam(required = false) @ApiParam(value = "项目类型") String projectType,
                                                 @RequestParam(required = false) @ApiParam(value = "币种") String currency,
                                                 @RequestParam(required = false) @ApiParam(value = "物料实际成本") BigDecimal materialActualCost,
                                                 @RequestParam(required = false) @ApiParam(value = "物料外包成本") BigDecimal materialOutsourceCost,
                                                 @RequestParam(required = false) @ApiParam(value = "人工成本（内部）") BigDecimal innerLaborCost,
                                                 @RequestParam(required = false) @ApiParam(value = "人工成本（外部）") BigDecimal outerLaborCost,
                                                 @RequestParam(required = false) @ApiParam(value = "费用成本") BigDecimal feeCost,
                                                 @RequestParam(required = false) @ApiParam(value = "业务实体") String ouIdStr) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("collectionDateStart", collectionDateStart);
        param.put("collectionDateEnd", collectionDateEnd);
        param.put("costDateStart", costDateStart);
        param.put("costDateEnd", costDateEnd);
        param.put("carryStatus", carryStatus);
        param.put("carryGlPeriod", carryGlPeriod);
        param.put("projectCode", projectCode);
        param.put("projectName", projectName);
        param.put("projectType", projectType);
        param.put("currency", currency);
        param.put("materialActualCost", materialActualCost);
        param.put("materialOutsourceCost", materialOutsourceCost);
        param.put("innerLaborCost", innerLaborCost);
        param.put("outerLaborCost", outerLaborCost);
        param.put("feeCost", feeCost);
        param.put("ouIdStr", ouIdStr);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/workingHourException/exprotWorkingHourCollectionError", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        final DataResponse<Map<String, Object>> dataResponse =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
                });
        Map<String, Object> resultMap = dataResponse.getData();
        JSONArray accountingArr = (JSONArray) resultMap.get("workingHourCollectionErrorList");

        List<WorkingHourCollectionErrorExcelVo> WorkingHourCollectionErrorExcelVoArrayList = new ArrayList<>();

        if (accountingArr != null) {
            WorkingHourCollectionErrorExcelVoArrayList = JSONObject.parseArray(accountingArr.toJSONString(), WorkingHourCollectionErrorExcelVo.class);
        }

        Workbook workbook = ExportExcelUtil.buildDefaultSheet(WorkingHourCollectionErrorExcelVoArrayList, WorkingHourCollectionErrorExcelVo.class, null, "sheet1", true);
        ExportExcelUtil.downLoadExcel("工时归集异常_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls", response, workbook);
    }

    @ApiOperation(value = "RDM工时转换表异常数据导出", response = WorkingHourAccountingDto.class)
    @GetMapping({"exprotRDMWorkingHourTransformError"})
    public void exprotRDMWorkingHourTransformError(HttpServletResponse response,
                                                       @RequestParam(required = false) @ApiParam(value = "主键") Long key,
                                                       @RequestParam(required = false) @ApiParam(value = "姓名") String userName,
                                                       @RequestParam(required = false) @ApiParam(value = "MIP") String userMip,
                                                       @RequestParam(required = false) @ApiParam(value = "考勤时长") String attHour,
                                                       @RequestParam(required = false) @ApiParam(value = "考勤日期开始") String attDateStart,
                                                       @RequestParam(required = false) @ApiParam(value = "考勤日期结束") String attDateEnd,
                                                       @RequestParam(required = false) @ApiParam(value = "顾问填报时长") String reportHour,
                                                       @RequestParam(required = false) @ApiParam(value = "顾问填报日期开始") String reportDateStart,
                                                       @RequestParam(required = false) @ApiParam(value = "顾问填报日期结束") String reportDateEnd,
                                                       @RequestParam(required = false) @ApiParam(value = "项目ID") Long projectId,
                                                       @RequestParam(required = false) @ApiParam(value = "工时来源ID") Long workingHourId,
                                                       @RequestParam(required = false) @ApiParam(value = "接口确认时长") String confirmHour,
                                                       @RequestParam(required = false) @ApiParam(value = "接口人确认日期开始") String confirmDateStart,
                                                       @RequestParam(required = false) @ApiParam(value = "接口人确认日期结束") String confirmDateEnd) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("key", key);
        param.put("userName", userName);
        param.put("userMip", userMip);
        param.put("attHour", attHour);
        param.put("attDateStart", attDateStart);
        param.put("attDateEnd", attDateEnd);
        param.put("reportHour", reportHour);
        param.put("reportDateStart", reportDateStart);
        param.put("reportDateEnd", reportDateEnd);
        param.put("projectId", projectId);
        param.put("workingHourId", workingHourId);
        param.put("confirmHour", confirmHour);
        param.put("confirmDateStart", confirmDateStart);
        param.put("confirmDateEnd", confirmDateEnd);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/workingHourException/exprotRDMWorkingHourTransformError", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        final DataResponse<Map<String, Object>> dataResponse =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
                });
        Map<String, Object> resultMap = dataResponse.getData();
        JSONArray accountingArr = (JSONArray) resultMap.get("rdmWorkingHourTransformErrorList");

        List<RDMWorkingHourTransformErrorExcelVo> rdmWorkingHourTransformErrorExcelVoArrayList = new ArrayList<>();

        if (accountingArr != null) {
            rdmWorkingHourTransformErrorExcelVoArrayList = JSONObject.parseArray(accountingArr.toJSONString(), RDMWorkingHourTransformErrorExcelVo.class);
        }

        Workbook workbook = ExportExcelUtil.buildDefaultSheet(rdmWorkingHourTransformErrorExcelVoArrayList, RDMWorkingHourTransformErrorExcelVo.class, null, "sheet1", true);
        ExportExcelUtil.downLoadExcel("RDM工时转换表异常_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls", response, workbook);
    }


    @ApiOperation(value = "工时入账单异常数据导出", response = WorkingHourAccountingDto.class)
    @GetMapping({"exprotWorkingHourAccountingError"})
    public void exprotWorkingHourAccountingError(HttpServletResponse response,
                                                 @RequestParam(required = false) @ApiParam(value = "入账单号") String accountingCode,
                                                 @RequestParam(required = false) @ApiParam(value = "状态") String accountingStatus,
                                                 @RequestParam(required = false) @ApiParam(value = "出勤月份") String applyMonth,
                                                 @RequestParam(required = false) @ApiParam(value = "工时合计（H）") String totalWorkingHour,
                                                 @RequestParam(required = false) @ApiParam(value = "工时成本") String totalLaborCost,
                                                 @RequestParam(required = false) @ApiParam(value = "会计期间") String glPeriod,
                                                 @RequestParam(required = false) @ApiParam(value = "入账日期开始") String glDateStart,
                                                 @RequestParam(required = false) @ApiParam(value = "入账日期结束") Long glDateEnd,
                                                 @RequestParam(required = false) @ApiParam(value = "币种") String currency,
                                                 @RequestParam(required = false) @ApiParam(value = "同步状态") String erpStatus,
                                                 @RequestParam(required = false) @ApiParam(value = "同步消息") String erpMessage,
                                                 @RequestParam(required = false) @ApiParam(value = "冲销状态") String writeStatus,
                                                 @RequestParam(required = false) @ApiParam(value = "业务实体") String ouIdStr,
                                                 @RequestParam(required = false) @ApiParam(value = "创建日期") String createAt)  throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("accountingCode", accountingCode);
        param.put("accountingStatus", accountingStatus);
        param.put("applyMonth", applyMonth);
        param.put("totalWorkingHour", totalWorkingHour);
        param.put("totalLaborCost", totalLaborCost);
        param.put("glPeriod", glPeriod);
        param.put("glDateStart", glDateStart);
        param.put("glDateEnd", glDateEnd);
        param.put("currency", currency);
        param.put("erpStatus", erpStatus);
        param.put("erpMessage", erpMessage);
        param.put("writeStatus", writeStatus);
        param.put("ouIdStr", ouIdStr);
        param.put("createAt", createAt);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/workingHourException/exprotWorkingHourAccountingError", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        final DataResponse<Map<String, Object>> dataResponse =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
                });
        Map<String, Object> resultMap = dataResponse.getData();
        JSONArray accountingArr = (JSONArray) resultMap.get("workingHourAccountingErrorList");

        List<WorkingHourAccountingErrorExcelVo> workingHourAccountingErrorExcelVos = new ArrayList<>();

        if (accountingArr != null) {
            workingHourAccountingErrorExcelVos = JSONObject.parseArray(accountingArr.toJSONString(), WorkingHourAccountingErrorExcelVo.class);
        }

        Workbook workbook = ExportExcelUtil.buildDefaultSheet(workingHourAccountingErrorExcelVos, WorkingHourAccountingErrorExcelVo.class, null, "sheet1", true);
        ExportExcelUtil.downLoadExcel("工时入账单异常_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls", response, workbook);
    }

    @ApiOperation(value = "工时填报异常数据分页查询", response = WorkingHourAccountingDto.class)
    @GetMapping({"pageWorkingHourError"})
    public Response pageWorkingHourError(
            @RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize,
            @RequestParam(required = false) @ApiParam(value = "项目编号") String projectCode,
            @RequestParam(required = false) @ApiParam(value = "项目名称") String projectName,
            @RequestParam(required = false) @ApiParam(value = "填报人姓名") String userName,
            @RequestParam(required = false) @ApiParam(value = "填报人mip") String userMip,
            @RequestParam(required = false) @ApiParam(value = "出勤日期开始") String applyDateStart,
            @RequestParam(required = false) @ApiParam(value = "出勤日期结束") String applyDateEnd,
            @RequestParam(required = false) @ApiParam(value = "填报日期开始") String createAtStart,
            @RequestParam(required = false) @ApiParam(value = "填报日期结束") String createAtEnd,
            @RequestParam(required = false) @ApiParam(value = "工时状态") String statusStr,
            @RequestParam(required = false) @ApiParam(value = "工时变id") Long id,
            @RequestParam(required = false) @ApiParam(value = "是否开票") Integer invoiceApplyFlag,
            @RequestParam(required = false) @ApiParam(value = "是否结转") Integer costCollectionFlag,
            @RequestParam(required = false) @ApiParam(value = "业务实体") String ouIdStr) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("projectCode", projectCode);
        param.put("projectName", projectName);
        param.put("userName", userName);
        param.put("userMip", userMip);
        param.put("applyDateStart", applyDateStart);
        param.put("applyDateEnd", applyDateEnd);
        param.put("createAtStart", createAtStart);
        param.put("createAtEnd", createAtEnd);
        param.put("statusStr", statusStr);
        param.put("id", id);
        param.put("invoiceApplyFlag", invoiceApplyFlag);
        param.put("costCollectionFlag", costCollectionFlag);
        param.put("ouIdStr", ouIdStr);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/workingHourException/pageWorkingHourError", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<WorkingHourExceptionDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<WorkingHourExceptionDto>>>() {
        });
        return response;
    }

    /**
     * ====================================================新更改--start===================================================
     */
    /**
     * PengBo
     */
    @ApiOperation(value = "工时填报异常数据分页查询--新更改", response = WorkingHourAccountingDto.class)
    @GetMapping({"pageWorkingHourSubmitTemporary"})
    public Response pageWorkingHourSubmitTemporary(  @RequestParam(required = false) @ApiParam(value = "项目编号") String projectCode,
                                           @RequestParam(required = false) @ApiParam(value = "项目名称") String projectName,
                                           @RequestParam(required = false) @ApiParam(value = "填报人姓名") String userName,
                                           @RequestParam(required = false) @ApiParam(value = "id") Long id,
                                           @RequestParam(required = false) @ApiParam(value = "填报人mip") String userMip,
                                           @RequestParam(required = false) @ApiParam(value = "出勤日期开始") String applyDateStart,
                                           @RequestParam(required = false) @ApiParam(value = "出勤日期结束") String applyDateEnd,
                                           @RequestParam(required = false) @ApiParam(value = "填报日期开始") String createAtStart,
                                           @RequestParam(required = false) @ApiParam(value = "填报日期结束") String createAtEnd,
                                           @RequestParam(required = false) @ApiParam(value = "工时状态") String statusStr,
                                           @RequestParam(required = false) @ApiParam(value = "工时变id") Long workingHourId,
                                           @RequestParam(required = false) @ApiParam(value = "是否开票") Integer invoiceApplyFlag,
                                           @RequestParam(required = false) @ApiParam(value = "是否结转") Integer costCollectionFlag,
                                           @RequestParam(required = false) @ApiParam(value = "业务实体") String ouIdStr,
                                           @RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                           @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize){
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("id", id);
        param.put("projectCode", projectCode);
        param.put("projectName", projectName);
        param.put("userName", userName);
        param.put("userMip", userMip);
        param.put("applyDateStart", applyDateStart);
        param.put("applyDateEnd", applyDateEnd);
        param.put("createAtStart", createAtStart);
        param.put("createAtEnd", createAtEnd);
        param.put("statusStr", statusStr);
        param.put("workingHourId", workingHourId);
        param.put("invoiceApplyFlag", invoiceApplyFlag);
        param.put("costCollectionFlag", costCollectionFlag);
        param.put("ouIdStr", ouIdStr);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/workingHourException/pageWorkingHourSubmitTemporary", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<WorkingHourSubmitErrorTemporary>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<WorkingHourSubmitErrorTemporary>>>() {
        });
        return response;
    }

    /**
     *
     * @return
     */
    @ApiOperation(value = "更新工时填报异常数据--新更改", response = WorkingHourAccountingDto.class)
    @GetMapping({"updateWorkingHourSubmitTemporary"})
    public String updateWorkingHourSubmitTemporary(){
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/workingHourException/updateWorkingHourSubmitTemporary", null);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return res;
    }







    @ApiOperation(value = "工时归集异常数据分页查询--新更改", response = WorkingHourCollectionErrorTemporary.class)
    @GetMapping({"pageWorkingHourCollectionTemporary"})
    public Response pageWorkingHourCollectionTemporary(
            @RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize,
            @RequestParam(required = false) @ApiParam(value = "id") Long id,
            @RequestParam(required = false) @ApiParam(value = "归集日期开始") String collectionDateStart,
            @RequestParam(required = false) @ApiParam(value = "归集日期结束") String collectionDateEnd,
            @RequestParam(required = false) @ApiParam(value = "成本发生日期开始") String costDateStart,
            @RequestParam(required = false) @ApiParam(value = "成本发生日期结束") String costDateEnd,
            @RequestParam(required = false) @ApiParam(value = "结转状态") String carryStatus,
            @RequestParam(required = false) @ApiParam(value = "结转期间") String carryGlPeriod,
            @RequestParam(required = false) @ApiParam(value = "项目编号") String projectCode,
            @RequestParam(required = false) @ApiParam(value = "项目名称") String projectName,
            @RequestParam(required = false) @ApiParam(value = "项目类型") String projectType,
            @RequestParam(required = false) @ApiParam(value = "币种") String currency,
            @RequestParam(required = false) @ApiParam(value = "费用成本") BigDecimal feeCost,
            @RequestParam(required = false) @ApiParam(value = "业务实体") String ouIdStr) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("id", id);
        param.put("collectionDateStart", collectionDateStart);
        param.put("collectionDateEnd", collectionDateEnd);
        param.put("costDateStart", costDateStart);
        param.put("costDateEnd", costDateEnd);
        param.put("carryStatus", carryStatus);
        param.put("carryGlPeriod", carryGlPeriod);
        param.put("projectCode", projectCode);
        param.put("projectName", projectName);
        param.put("projectType", projectType);
        param.put("currency", currency);
        param.put("moneyCost", feeCost);
        param.put("ouIdStr", ouIdStr);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/workingHourException/pageWorkingHourCollectionTemporary", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<WorkingHourCollectionErrorTemporary>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<WorkingHourCollectionErrorTemporary>>>() {
        });
        return response;
    }

    @ApiOperation(value ="更新工时归集异常数据--新更改")
    @GetMapping({"updateWorkingHourCollectionTemporary"})
    public String updateWorkingHourCollectionTemporary() {
        final Map<String, Object> param = new HashMap<>();
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/workingHourException/updateWorkingHourCollectionTemporary", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return res;
    }










    /**
     *-------------------------------------------------------新更改--end--------------------------------------------------------------
     */


    @ApiOperation(value = "工时归集异常数据分页查询", response = WorkingHourAccountingDto.class)
    @GetMapping({"pageWorkingHourCollectionError"})
    public Response pageWorkingHourCollectionError(
            @RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize,
            @RequestParam(required = false) @ApiParam(value = "归集日期开始") String collectionDateStart,
            @RequestParam(required = false) @ApiParam(value = "归集日期结束") String collectionDateEnd,
            @RequestParam(required = false) @ApiParam(value = "成本发生日期开始") String costDateStart,
            @RequestParam(required = false) @ApiParam(value = "成本发生日期结束") String costDateEnd,
            @RequestParam(required = false) @ApiParam(value = "结转状态") String carryStatus,
            @RequestParam(required = false) @ApiParam(value = "结转期间") String carryGlPeriod,
            @RequestParam(required = false) @ApiParam(value = "项目编号") String projectCode,
            @RequestParam(required = false) @ApiParam(value = "项目名称") String projectName,
            @RequestParam(required = false) @ApiParam(value = "项目类型") String projectType,
            @RequestParam(required = false) @ApiParam(value = "币种") String currency,
            @RequestParam(required = false) @ApiParam(value = "物料实际成本") BigDecimal materialActualCost,
            @RequestParam(required = false) @ApiParam(value = "物料外包成本") BigDecimal materialOutsourceCost,
            @RequestParam(required = false) @ApiParam(value = "人工成本（内部）") BigDecimal innerLaborCost,
            @RequestParam(required = false) @ApiParam(value = "人工成本（外部）") BigDecimal outerLaborCost,
            @RequestParam(required = false) @ApiParam(value = "费用成本") BigDecimal feeCost,
            @RequestParam(required = false) @ApiParam(value = "业务实体") String ouIdStr) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("collectionDateStart", collectionDateStart);
        param.put("collectionDateEnd", collectionDateEnd);
        param.put("costDateStart", costDateStart);
        param.put("costDateEnd", costDateEnd);
        param.put("carryStatus", carryStatus);
        param.put("carryGlPeriod", carryGlPeriod);
        param.put("projectCode", projectCode);
        param.put("projectName", projectName);
        param.put("projectType", projectType);
        param.put("currency", currency);
        param.put("materielActualCost", materialActualCost);
        param.put("materialOutsourceCost", materialOutsourceCost);
        param.put("innerLaborCost", innerLaborCost);
        param.put("outerLaborCost", outerLaborCost);
        param.put("moneyCost", feeCost);
        param.put("ouIdStr", ouIdStr);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/workingHourException/pageWorkingHourCollectionError", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<WorkingHourExceptionDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<WorkingHourExceptionDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "更新RDM工时转换表异常数据", response = WorkingHourAccountingDto.class)
    @GetMapping({"updateRDMWorkingHourTransformError"})
    public Response updateRDMWorkingHourTransformError() {
        final Map<String, Object> param = new HashMap<>();
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/workingHourException/updateRDMWorkingHourTransformError", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "更新工时入账单异常数据", response = WorkingHourAccountingDto.class)
    @GetMapping({"updateWorkingHourAccountingError"})
    public Response updateWorkingHourAccountingError() {
        final Map<String, Object> param = new HashMap<>();
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/workingHourException/updateWorkingHourAccountingError", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "RDM工时转换表异常数据分页查询", response = WorkingHourAccountingDto.class)
    @GetMapping({"pageRDMWorkingHourTransformError"})
    public Response pageRDMWorkingHourTransformError(
            @RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize,
            @RequestParam(required = false) @ApiParam(value = "主键") Long key,
            @RequestParam(required = false) @ApiParam(value = "姓名") String userName,
            @RequestParam(required = false) @ApiParam(value = "MIP") String userMip,
            @RequestParam(required = false) @ApiParam(value = "考勤时长") String attHour,
            @RequestParam(required = false) @ApiParam(value = "考勤日期开始") String attDateStart,
            @RequestParam(required = false) @ApiParam(value = "考勤日期结束") String attDateEnd,
            @RequestParam(required = false) @ApiParam(value = "顾问填报时长") String reportHour,
            @RequestParam(required = false) @ApiParam(value = "顾问填报日期开始") String reportDateStart,
            @RequestParam(required = false) @ApiParam(value = "顾问填报日期结束") String reportDateEnd,
            @RequestParam(required = false) @ApiParam(value = "项目ID") Long projectId,
            @RequestParam(required = false) @ApiParam(value = "工时来源ID") Long workingHourId,
            @RequestParam(required = false) @ApiParam(value = "接口确认时长") String confirmHour,
            @RequestParam(required = false) @ApiParam(value = "接口人确认日期开始") String confirmDateStart,
            @RequestParam(required = false) @ApiParam(value = "接口人确认日期结束") String confirmDateEnd) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("key", key);
        param.put("userName", userName);
        param.put("userMip", userMip);
        param.put("attHour", attHour);
        param.put("attDateStart", attDateStart);
        param.put("attDateEnd", attDateEnd);
        param.put("reportHour", reportHour);
        param.put("reportDateStart", reportDateStart);
        param.put("reportDateEnd", reportDateEnd);
        param.put("projectId", projectId);
        param.put("workingHourId", workingHourId);
        param.put("confirmHour", confirmHour);
        param.put("confirmDateStart", confirmDateStart);
        param.put("confirmDateEnd", confirmDateEnd);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/workingHourException/pageRDMWorkingHourTransformError", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<WorkingHourExceptionRdm>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<WorkingHourExceptionRdm>>>() {
        });
        return response;
    }

    @ApiOperation(value = "工时入账单异常数据分页查询", response = WorkingHourAccountingDto.class)
    @GetMapping({"pageWorkingHourAccountingError"})
    public Response pageWorkingHourAccountingError(
            @RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize,
            @RequestParam(required = false) @ApiParam(value = "入账单号") String accountingCode,
            @RequestParam(required = false) @ApiParam(value = "状态") String accountingStatus,
            @RequestParam(required = false) @ApiParam(value = "出勤月份") String applyMonth,
            @RequestParam(required = false) @ApiParam(value = "工时合计（H）") String totalWorkingHour,
            @RequestParam(required = false) @ApiParam(value = "工时成本") String totalLaborCost,
            @RequestParam(required = false) @ApiParam(value = "会计期间") String glPeriod,
            @RequestParam(required = false) @ApiParam(value = "入账日期开始") String glDateStart,
            @RequestParam(required = false) @ApiParam(value = "入账日期结束") Long glDateEnd,
            @RequestParam(required = false) @ApiParam(value = "币种") String currency,
            @RequestParam(required = false) @ApiParam(value = "同步状态") String erpStatusStr,
            @RequestParam(required = false) @ApiParam(value = "同步消息") String erpMessage,
            @RequestParam(required = false) @ApiParam(value = "冲销状态") String writeOffStatusStr,
            @RequestParam(required = false) @ApiParam(value = "业务实体") String ouIdStr,
            @RequestParam(required = false) @ApiParam(value = "创建日期") String createAt) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("accountingCode", accountingCode);
        param.put("accountingStatus", accountingStatus);
        param.put("applyMonth", applyMonth);
        param.put("totalWorkingHour", totalWorkingHour);
        param.put("totalLaborCost", totalLaborCost);
        param.put("glPeriod", glPeriod);
        param.put("glDateStart", glDateStart);
        param.put("glDateEnd", glDateEnd);
        param.put("currency", currency);
        param.put("erpStatusStr", erpStatusStr);
        param.put("erpMessage", erpMessage);
        param.put("writeOffStatusStr", writeOffStatusStr);
        param.put("ouIdStr", ouIdStr);
        param.put("createAt", createAt);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/workingHourException/pageWorkingHourAccountingError", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<WorkingHourExceptionAccounting>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<WorkingHourExceptionAccounting>>>() {
        });
        return response;
    }
}
