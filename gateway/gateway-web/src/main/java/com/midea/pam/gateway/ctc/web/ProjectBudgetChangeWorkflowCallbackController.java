package com.midea.pam.gateway.ctc.web;

import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2019-6-27
 * @description 项目预算变更流程回调
 */
@Api("项目预算变更流程回调")
@RestController
@RequestMapping(value = {"project/budgetChange/workflow/callback", "budgetChange/workflow/callback"})
public class ProjectBudgetChangeWorkflowCallbackController {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "发起审批")
    @PutMapping("draftSubmit/skipSecurityInterceptor")
    public Response draftSubmitCallback(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                        @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                        @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                                        @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%sbudgetChange/workflow/callback/draftSubmit/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "驳回")
    @PutMapping("refuse/skipSecurityInterceptor")
    public Response refuseCallback(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                   @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                   @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                                   @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%sbudgetChange/workflow/callback/refuse/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "通过")
    @PutMapping("pass/skipSecurityInterceptor")
    public Response passCallback(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                 @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                 @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                                 @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%sbudgetChange/workflow/callback/pass/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "撤回")
    @PutMapping("draftReturn/skipSecurityInterceptor")
    public Response draftReturnCallback(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                        @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                        @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                                        @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%sbudgetChange/workflow/callback/draftReturn/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "作废")
    @PutMapping("abandon/skipSecurityInterceptor")
    public Response abandonCallback(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                    @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                    @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                                    @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%sbudgetChange/workflow/callback/abandon/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "删除")
    @PutMapping("delete/skipSecurityInterceptor")
    public Response deleteCallback(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                   @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                   @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                                   @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%sbudgetChange/workflow/callback/delete/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "处理人通过")
    @PutMapping("agree/skipSecurityInterceptor")
    public Response agree(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                          @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                          @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                          @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%sbudgetChange/workflow/callback/agree/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

}
