package com.midea.pam.gateway;

import com.github.pagehelper.PageInfo;
import com.midea.pam.common.gateway.dto.RocketMQMsgConsumerRecordDTO;
import com.midea.pam.common.gateway.query.RocketMQMsgConsumerRecordQuery;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.service.RocketMQMsgConsumerRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api("RocketMQ消息消费成功记录")
@RestController
@RequestMapping("rocketMQMsgConsumerRecord")
public class RocketMQMsgConsumerRecordController {

    @Resource
    private RocketMQMsgConsumerRecordService rocketMQMsgConsumerRecordService;

    @ApiOperation("获取 pam_system.rocketmq_msg_consumer_record 表中的数据")
    @PostMapping("getList")
    public Response getList(@RequestBody RocketMQMsgConsumerRecordQuery query) {
        Guard.notNull(query, "查询参数为空");
        Guard.notNull(query.getPageNum(), "分页当前页参数为空");
        Guard.notNull(query.getPageSize(), "分页每页数量参数为空");

        DataResponse<PageInfo<RocketMQMsgConsumerRecordDTO>> dataResponse = Response.dataResponse();
        PageInfo<RocketMQMsgConsumerRecordDTO> list = rocketMQMsgConsumerRecordService.page(query);
        return dataResponse.setData(list);
    }
}
