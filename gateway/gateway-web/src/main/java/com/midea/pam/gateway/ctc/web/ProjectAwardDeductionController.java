package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.ProjectAwardDeductionDto;
import com.midea.pam.common.ctc.entity.ProjectAwardDeduction;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("项目扣回金额明细")
@RestController
@RequestMapping("projectAwardDeduction")
public class ProjectAwardDeductionController extends ControllerHelper {
    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "按项目扣回金额明细ID删除")
    @GetMapping("deleteById")
    public Response deleteById(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectAwardDeduction/deleteById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
    }


    @ApiOperation(value = "新增/修改项目扣回金额明细")
    @PostMapping("save")
    public Response save(@RequestBody ProjectAwardDeduction projectAwardDeduction) {
        final String url = String.format("%sprojectAwardDeduction/save", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, projectAwardDeduction, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Long>>() {
        });
    }


    @ApiOperation(value = "按id查询项目扣回金额明细")
    @GetMapping("findById/{id}")
    public Response findById(@PathVariable Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectAwardDeduction/findById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectAwardDeduction>>() {
        });


    }

    @ApiOperation(value = " 分页查询项目扣回金额明细")
    @GetMapping("page")
    public Response page(@RequestParam(required = false) final String projectCode,
                         @RequestParam(required = false) final Long projectAwardId,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "100") final Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectCode", projectCode);
        param.put("projectAwardId", projectAwardId);
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectAwardDeduction/page", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<ProjectAwardDeductionDto>>>() {
        });
    }

    @ApiOperation(value = "根据项目奖查询所有项目扣回金额明细")
    @GetMapping("findAll")
    public Response findAll() {
        final Map<String, Object> param = new HashMap<>();
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectAwardDeduction/findAll", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<ProjectAwardDeduction>>>() {
        });
    }
}
