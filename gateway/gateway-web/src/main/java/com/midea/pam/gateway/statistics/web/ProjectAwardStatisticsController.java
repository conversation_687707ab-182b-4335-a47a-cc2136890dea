package com.midea.pam.gateway.statistics.web;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.ProjectAwardDeductionDto;
import com.midea.pam.common.ctc.dto.ProjectAwardDto;
import com.midea.pam.common.ctc.dto.ProjectAwardTargetDetailDTO;
import com.midea.pam.common.ctc.query.ProjectAwardQuery;
import com.midea.pam.common.ctc.vo.ProjectAwardDepartmentExcelVO;
import com.midea.pam.common.ctc.vo.ProjectAwardTargetDetailExcelVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.PageResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Api("项目奖管理")
@RestController
@RequestMapping("statistics/projectAward")
public class ProjectAwardStatisticsController extends ControllerHelper {


    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "项目奖管理页面分页查询")
    @PostMapping("selectPage")
    public Response selectPage(@RequestBody(required = false) ProjectAwardQuery query) throws Exception {
        String url = String.format("%sstatistics/projectAward/selectPage", ModelsEnum.STATISTICS.getBaseUrl());
        String res = cleanStr(restTemplate.postForObject(url, query, String.class));
        PageInfo<Map<String, Object>> data = JSON.parseObject(res, new TypeReference<PageInfo<Map<String, Object>>>() {
        });
        PageResponse<Map<String, Object>> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation(value = "按id查询项目奖管理(事业部总确认页面)")
    @GetMapping("findByDepartment/{id}")
    public com.midea.pam.common.base.Response findByDepartment(@PathVariable Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/statistics/projectAward/findByDepartment", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectAwardDto>>() {
        });

    }

    @ApiOperation(value = "项目奖管理页面分页查询")
    @GetMapping("selectProjectAwardDeduction")
    public com.midea.pam.common.base.Response selectProjectAwardDeduction(@RequestParam(required = false) final String projectCode,
                                                                          @RequestParam(required = false) final Long projectAwardId,
                                                                          @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                                                          @RequestParam(required = false, defaultValue = "100") final Integer pageSize) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectCode", projectCode);
        param.put("projectAwardId", projectAwardId);
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectAward/selectProjectAwardDeduction", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<ProjectAwardDeductionDto>>>() {
        });
    }

    @ApiOperation(value = "项目奖指标统计")
    @GetMapping("generateProjectAwardTargetDetail")
    public com.midea.pam.common.base.Response generateProjectAwardTargetDetail() throws Exception {
        final Map<String, Object> param = new HashMap<>();
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectAward/generateProjectAwardTargetDetail", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Long>>() {
        });
    }

    @ApiOperation(value = "实时统计项目奖指标明细")
    @GetMapping("getLatestTargetDetail")
    public com.midea.pam.common.base.Response getLatestTargetDetail(@RequestParam(required = false) final Long projectAwardId,
                                                                    @RequestParam(required = false) final String projectCode,
                                                                    @RequestParam(required = false) final String projectName,
                                                                    @RequestParam(required = false) final String departmentCodeStr,
                                                                    @RequestParam(required = false) final String unitIdStr,
                                                                    @RequestParam(required = false) final String projectTypeStr,
                                                                    @RequestParam(required = false) final String projectStatusStr,
                                                                    @RequestParam(required = false) final String priceTypeStr,
                                                                    @RequestParam(required = false) final String projectManagerName,
                                                                    @RequestParam(required = false) final String awardStatusStr,
                                                                    @RequestParam(required = false) final Boolean objectiveFlag,
                                                                    @RequestParam(required = false) final Boolean pauseFlag,
                                                                    @RequestParam(required = false) final Boolean beyondBudgetAmountFlag,
                                                                    @RequestParam(required = false) final String startDate,
                                                                    @RequestParam(required = false) final String endDate,
                                                                    @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                                                    @RequestParam(required = false, defaultValue = "100") final Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectAwardId", projectAwardId);
        param.put("projectCode", projectCode);
        param.put("projectName", projectName);
        param.put("departmentCodeStr", departmentCodeStr);
        param.put("unitIdStr", unitIdStr);
        param.put("projectTypeStr", projectTypeStr);
        param.put("projectStatusStr", projectStatusStr);
        param.put("priceTypeStr", priceTypeStr);
        param.put("projectManagerName", projectManagerName);
        param.put("awardStatusStr", awardStatusStr);
        param.put("objectiveFlag", objectiveFlag);
        param.put("pauseFlag", pauseFlag);
        param.put("beyondBudgetAmountFlag", beyondBudgetAmountFlag);
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectAward/getLatestTargetDetail", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<ProjectAwardTargetDetailDTO>>>() {
        });
    }

    @ApiOperation(value = "获取最新项目奖指标明细")
    @GetMapping("queryLatestTargetDetail")
    public com.midea.pam.common.base.Response queryLatestTargetDetail(@RequestParam(required = false) final Long projectAwardId,
                                                                    @RequestParam(required = false) final String projectCode,
                                                                    @RequestParam(required = false) final String projectName,
                                                                    @RequestParam(required = false) final String departmentCodeStr,
                                                                    @RequestParam(required = false) final String unitIdStr,
                                                                    @RequestParam(required = false) final String projectTypeStr,
                                                                    @RequestParam(required = false) final String projectStatusStr,
                                                                    @RequestParam(required = false) final String priceTypeStr,
                                                                    @RequestParam(required = false) final String projectManagerName,
                                                                    @RequestParam(required = false) final String awardStatusStr,
                                                                    @RequestParam(required = false) final Boolean objectiveFlag,
                                                                    @RequestParam(required = false) final Boolean pauseFlag,
                                                                    @RequestParam(required = false) final Boolean beyondBudgetAmountFlag,
                                                                    @RequestParam(required = false) final String startDate,
                                                                    @RequestParam(required = false) final String endDate,
                                                                    @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                                                    @RequestParam(required = false, defaultValue = "100") final Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectAwardId", projectAwardId);
        param.put("projectCode", projectCode);
        param.put("projectName", projectName);
        param.put("departmentCodeStr", departmentCodeStr);
        param.put("unitIdStr", unitIdStr);
        param.put("projectTypeStr", projectTypeStr);
        param.put("projectStatusStr", projectStatusStr);
        param.put("priceTypeStr", priceTypeStr);
        param.put("projectManagerName", projectManagerName);
        param.put("awardStatusStr", awardStatusStr);
        param.put("objectiveFlag", objectiveFlag);
        param.put("pauseFlag", pauseFlag);
        param.put("beyondBudgetAmountFlag", beyondBudgetAmountFlag);
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectAward/queryLatestTargetDetail", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<ProjectAwardTargetDetailDTO>>>() {
        });
    }

    @ApiOperation(value = "获取最新项目奖指标明细")
    @GetMapping("getLatestTargetDetailByDepartment")
    public com.midea.pam.common.base.Response getLatestTargetDetailByDepartment(@RequestParam(required = false) final Long projectAwardId,
                                                                    @RequestParam(required = false) final String projectCode,
                                                                    @RequestParam(required = false) final String projectName,
                                                                    @RequestParam(required = false) final String departmentCodeStr,
                                                                    @RequestParam(required = false) final String unitIdStr,
                                                                    @RequestParam(required = false) final String projectTypeStr,
                                                                    @RequestParam(required = false) final String projectStatusStr,
                                                                    @RequestParam(required = false) final String priceTypeStr,
                                                                    @RequestParam(required = false) final String projectManagerName,
                                                                    @RequestParam(required = false) final String awardStatusStr,
                                                                    @RequestParam(required = false) final Boolean objectiveFlag,
                                                                    @RequestParam(required = false) final Boolean pauseFlag,
                                                                    @RequestParam(required = false) final Boolean beyondBudgetAmountFlag,
                                                                    @RequestParam(required = false) final String startDate,
                                                                    @RequestParam(required = false) final String endDate,
                                                                    @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                                                    @RequestParam(required = false, defaultValue = "100") final Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectAwardId", projectAwardId);
        param.put("projectCode", projectCode);
        param.put("projectName", projectName);
        param.put("departmentCodeStr", departmentCodeStr);
        param.put("unitIdStr", unitIdStr);
        param.put("projectTypeStr", projectTypeStr);
        param.put("projectStatusStr", projectStatusStr);
        param.put("priceTypeStr", priceTypeStr);
        param.put("projectManagerName", projectManagerName);
        param.put("awardStatusStr", awardStatusStr);
        param.put("objectiveFlag", objectiveFlag);
        param.put("pauseFlag", pauseFlag);
        param.put("beyondBudgetAmountFlag", beyondBudgetAmountFlag);
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectAward/getLatestTargetDetailByDepartment", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<ProjectAwardTargetDetailDTO>>>() {
        });
    }


    @ApiOperation(value = "查询项目总成本", notes = "map【key:项目id，value：项目总成本】")
    @GetMapping("getProjectTotalCost")
    public com.midea.pam.common.base.Response getProjectTotalCost(@ApiParam("项目id列表（,拼接）") @RequestParam String projectIdList,
                                                                  @ApiParam("年度") @RequestParam Integer year,
                                                                  @ApiParam("季度") @RequestParam Integer quarter) {
        Map<String, Object> param = new HashMap<>(3);
        param.put("projectIdList", projectIdList);
        param.put("year", year);
        param.put("quarter", quarter);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectAward/getProjectTotalCost", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Map<Long, BigDecimal>>>() {
        });
    }

    //新增项目奖的页面导出
    @ApiOperation(value = "导出项目奖明细Excel")
    @GetMapping("exportProjectAwardDetailExcel")
    public void exportProjectAwardDetailExcel(@RequestParam(required = false) final Long projectAwardId,
                                              @RequestParam(required = false) final String projectCode,
                                              @RequestParam(required = false) final String projectName,
                                              @RequestParam(required = false) final String departmentCodeStr,
                                              @RequestParam(required = false) final String unitIdStr,
                                              @RequestParam(required = false) final String projectTypeStr,
                                              @RequestParam(required = false) final String projectStatusStr,
                                              @RequestParam(required = false) final String priceTypeStr,
                                              @RequestParam(required = false) final String projectManagerName,
                                              @RequestParam(required = false) final String awardStatusStr,
                                              @RequestParam(required = false) final Boolean objectiveFlag,
                                              @RequestParam(required = false) final Boolean pauseFlag,
                                              @RequestParam(required = false) final Boolean beyondBudgetAmountFlag,
                                              @RequestParam(required = false) final String startDate,
                                              @RequestParam(required = false) final String endDate,
                                              @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                              @RequestParam(required = false, defaultValue = "9999") final Integer pageSize,
                                              HttpServletResponse response) throws Exception {
        Map<String, Object> param = new HashMap<>();
        param.put("projectAwardId", projectAwardId);
        param.put("projectCode", projectCode);
        param.put("projectName", projectName);
        param.put("departmentCodeStr", departmentCodeStr);
        param.put("unitIdStr", unitIdStr);
        param.put("projectTypeStr", projectTypeStr);
        param.put("projectStatusStr", projectStatusStr);
        param.put("priceTypeStr", priceTypeStr);
        param.put("projectManagerName", projectManagerName);
        param.put("awardStatusStr", awardStatusStr);
        param.put("objectiveFlag", objectiveFlag);
        param.put("pauseFlag", pauseFlag);
        param.put("beyondBudgetAmountFlag", beyondBudgetAmountFlag);
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectAwardExportExcel/exportDetailExcel", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        List<ProjectAwardTargetDetailExcelVO> excelVOList = JSON.parseArray(res, ProjectAwardTargetDetailExcelVO.class);
        ExportExcelUtil.exportExcel(excelVOList, null, "Sheet1", ProjectAwardTargetDetailExcelVO.class, "项目奖明细导出.xls", response);
    }


    @ApiOperation(value = "（非事业部总确认）全Excel")
    @GetMapping("exportProjectAwardDepartmentExcel")
    public void exportProjectAwardDepartmentExcel(@RequestParam(required = false) final Long projectAwardId,
                                                  @RequestParam(required = false) final String projectCode,
                                                  @RequestParam(required = false) final String projectName,
                                                  @RequestParam(required = false) final String departmentCodeStr,
                                                  @RequestParam(required = false) final String unitIdStr,
                                                  @RequestParam(required = false) final String projectTypeStr,
                                                  @RequestParam(required = false) final String projectStatusStr,
                                                  @RequestParam(required = false) final String priceTypeStr,
                                                  @RequestParam(required = false) final String projectManagerName,
                                                  @RequestParam(required = false) final String awardStatusStr,
                                                  @RequestParam(required = false) final Boolean objectiveFlag,
                                                  @RequestParam(required = false) final Boolean pauseFlag,
                                                  @RequestParam(required = false) final Boolean beyondBudgetAmountFlag,
                                                  @RequestParam(required = false) final String startDate,
                                                  @RequestParam(required = false) final String endDate,
                                                  @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                                  @RequestParam(required = false, defaultValue = "9999") final Integer pageSize,
                                                  HttpServletResponse response){ //注意，此方法过于“简单暴力”--从SQL直接返回到Excel

        //参数
        Map<String, Object> param = new HashMap<>();
        param.put("projectAwardId", projectAwardId);
        param.put("projectCode", projectCode);
        param.put("projectName", projectName);
        param.put("departmentCodeStr", departmentCodeStr);
        param.put("unitIdStr", unitIdStr);
        param.put("projectTypeStr", projectTypeStr);
        param.put("projectStatusStr", projectStatusStr);
        param.put("priceTypeStr", priceTypeStr);
        param.put("projectManagerName", projectManagerName);
        param.put("awardStatusStr", awardStatusStr);
        param.put("objectiveFlag", objectiveFlag);
        param.put("pauseFlag", pauseFlag);
        param.put("beyondBudgetAmountFlag", beyondBudgetAmountFlag);
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        //汇总sheet
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectAwardExportExcel/exportDepartmentExcel", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        List<ProjectAwardDepartmentExcelVO> e1 = JSON.parseArray(res, ProjectAwardDepartmentExcelVO.class);

        //明细sheet

        String url1 = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectAwardExportExcel/exportDetailExcel", param);
        String res1 = restTemplate.getForEntity(url1, String.class).getBody();
        List<ProjectAwardTargetDetailExcelVO> e2 = JSON.parseArray(res1, ProjectAwardTargetDetailExcelVO.class);


        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(e1, ProjectAwardDepartmentExcelVO.class, null, "项目奖汇总表", true);
        ExportExcelUtil.addSheet(workbook, e2,ProjectAwardTargetDetailExcelVO.class, null,"项目奖明细表",true);
        ExportExcelUtil.downLoadExcel("项目奖信息Excel.xls".toString(), response, workbook);
    }


    @ApiOperation(value = "（事业部总确认）全Excel")
    @GetMapping("exportProjectAwardDepartmentExcel_1")
    public void exportProjectAwardDepartmentExcel_1(@RequestParam(required = false) final Long projectAwardId,
                                                    @RequestParam(required = false) final String projectCode,
                                                    @RequestParam(required = false) final String projectName,
                                                    @RequestParam(required = false) final String departmentCodeStr,
                                                    @RequestParam(required = false) final String unitIdStr,
                                                    @RequestParam(required = false) final String projectTypeStr,
                                                    @RequestParam(required = false) final String projectStatusStr,
                                                    @RequestParam(required = false) final String priceTypeStr,
                                                    @RequestParam(required = false) final String projectManagerName,
                                                    @RequestParam(required = false) final String awardStatusStr,
                                                    @RequestParam(required = false) final Boolean objectiveFlag,
                                                    @RequestParam(required = false) final Boolean pauseFlag,
                                                    @RequestParam(required = false) final Boolean beyondBudgetAmountFlag,
                                                    @RequestParam(required = false) final String startDate,
                                                    @RequestParam(required = false) final String endDate,
                                                    @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                                    @RequestParam(required = false, defaultValue = "9999") final Integer pageSize,
                                                    HttpServletResponse response){

        //参数
        Map<String, Object> param = new HashMap<>();
        param.put("projectAwardId", projectAwardId);
        param.put("projectCode", projectCode);
        param.put("projectName", projectName);
        param.put("departmentCodeStr", departmentCodeStr);
        param.put("unitIdStr", unitIdStr);
        param.put("projectTypeStr", projectTypeStr);
        param.put("projectStatusStr", projectStatusStr);
        param.put("priceTypeStr", priceTypeStr);
        param.put("projectManagerName", projectManagerName);
        param.put("awardStatusStr", awardStatusStr);
        param.put("objectiveFlag", objectiveFlag);
        param.put("pauseFlag", pauseFlag);
        param.put("beyondBudgetAmountFlag", beyondBudgetAmountFlag);
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);

        //汇总sheet
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectAwardExportExcel/exportDepartmentExcel_1", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        List<ProjectAwardDepartmentExcelVO> e1 = JSON.parseArray(res, ProjectAwardDepartmentExcelVO.class);

        //明细sheet

        String url1 = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectAwardExportExcel/exportDetailExcel_1", param);
        String res1 = restTemplate.getForEntity(url1, String.class).getBody();
        List<ProjectAwardTargetDetailExcelVO> e2 = JSON.parseArray(res1, ProjectAwardTargetDetailExcelVO.class);


        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(e1, ProjectAwardDepartmentExcelVO.class, null, "项目奖汇总表", true);
        ExportExcelUtil.addSheet(workbook, e2,ProjectAwardTargetDetailExcelVO.class, null,"项目奖明细表",true);
        ExportExcelUtil.downLoadExcel("项目奖信息Excel.xls".toString(), response, workbook);
    }






    @ApiOperation(value = "PMO、HR项目奖管理页面分页查询")
    @PostMapping("pomOrHrProjectAwardPage")
    public Response pomOrHrProjectAwardPage(@RequestBody(required = false) ProjectAwardQuery query) throws Exception{
        String url = String.format("%sstatistics/projectAward/pomOrHrProjectAwardPage", ModelsEnum.STATISTICS.getBaseUrl());
        String res = cleanStr(restTemplate.postForObject(url, query, String.class));
        PageInfo<Map<String, Object>> data = JSON.parseObject(res, new TypeReference<PageInfo<Map<String, Object>>>() {
        });
        PageResponse<Map<String, Object>> response = Response.pageResponse();
        return response.convert(data);
    }







}
