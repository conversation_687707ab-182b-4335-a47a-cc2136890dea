package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.query.GlDailyRateQuery;
import com.midea.pam.common.util.Utils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.PageResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Api("汇率")
@RequestMapping({"glDailyRate"})
public class GlDailyRateController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "汇率页面分页查询")
    @PostMapping("selectPage")
    public Response selectPage(@RequestBody(required = false) GlDailyRateQuery query) throws Exception {
        String url = String.format("%sglDailyRate/selectPage", ModelsEnum.BASEDATA.getBaseUrl());
        String res = cleanStr(restTemplate.postForObject(url, query, String.class));
        PageInfo<Map<String, Object>> data = JSON.parseObject(res, new TypeReference<PageInfo<Map<String, Object>>>() {
        });
        PageResponse<Map<String, Object>> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation(value = "汇率折换类型列表")
    @GetMapping("exchangeTypeList")
    public Response exchangeTypeList() throws Exception {
        String url = String.format("%sglDailyRate/exchangeTypeList", ModelsEnum.BASEDATA.getBaseUrl());
        String res = cleanStr(restTemplate.getForObject(url, String.class));
        List<Map<String, Object>> dataList = Utils.jsonStr2List(res);
        DataResponse<List<Map<String, Object>>> response = Response.dataResponse();
        return response.setData(dataList);
    }

    @ApiOperation(value = "getDailyRateFromErp")
    @GetMapping("getDailyRateFromErp")
    public Response getDailyRateFromErp(@RequestParam(required = false) String lastUpdateDate,
                                        @RequestParam(required = false) String lastUpdateDateEnd){
        final Map<String, Object> param = new HashMap<>();
        param.put("lastUpdateDate", lastUpdateDate);
        param.put("lastUpdateDateEnd", lastUpdateDateEnd);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "glDailyRate/getDailyRateFromErp",param);
        restTemplate.getForObject(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        response.setData("0");
        return response;
    }

    @ApiOperation(value = "汇率折换类型列表")
    @PostMapping("selectDailyRate")
    public Response selectDailyRate(@RequestBody(required = false) GlDailyRateQuery query) throws Exception {
        String url = String.format("%sglDailyRate/selectDailyRate", ModelsEnum.BASEDATA.getBaseUrl());
        String res = cleanStr(restTemplate.postForObject(url, query, String.class));
        List<Map<String, Object>> dataList = Utils.jsonStr2List(res);
        DataResponse<List<Map<String, Object>>> response = Response.dataResponse();
        return response.setData(dataList);
    }

}
