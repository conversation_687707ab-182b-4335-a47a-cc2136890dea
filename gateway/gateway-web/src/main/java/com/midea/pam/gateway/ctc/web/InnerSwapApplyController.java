package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.InnerSwapApplyDTO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: common-module
 * @description: 内部资源调剂申请网关
 * @author:zhongpeng
 * @create:2020-12-15 14:18
 **/
@Api("内部资源调剂申请")
@RestController
@RequestMapping(value = {"innerSwapApply"})
public class InnerSwapApplyController extends ControllerHelper {
    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "分页查询")
    @GetMapping("selectPage")
    public Response selectPage(@RequestParam(required = false) String fuzzLike,
                               @RequestParam(required = false) String requireUserName,
                               @RequestParam(required = false) String provideUserName,
                               @RequestParam(required = false) String provideProductOrgName,
                               @RequestParam(required = false) String requireProductOrgName,
                               @RequestParam(required = false) String applyYear,
                               @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                               @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = new HashMap<>();
        params.put(Constants.Page.PAGE_NUM, pageNum);
        params.put(Constants.Page.PAGE_SIZE, pageSize);
        params.put("fuzzLike",fuzzLike);
        params.put("requireUserName",requireUserName);
        params.put("provideUserName",provideUserName);
        params.put("provideProductOrgName",provideProductOrgName);
        params.put("requireProductOrgName",requireProductOrgName);
        params.put("applyYear",applyYear);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/innerSwapApply/selectPage", params);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<InnerSwapApplyDTO>>>() {
        });
    }


    @ApiOperation(value = "查询调剂申请")
    @GetMapping("listInnerSwapApply")
    public Response listInnerSwapApply(@RequestParam(required = false) String fuzzLike,
                               @RequestParam(required = false) String requireUserName,
                               @RequestParam(required = false) String provideUserName) {
        final Map<String, Object> params = new HashMap<>();
        params.put("fuzzLike",fuzzLike);
        params.put("requireUserName",requireUserName);
        params.put("provideUserName",provideUserName);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/innerSwapApply/listInnerSwapApply", params);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<InnerSwapApplyDTO>>>() {
        });
    }


    @ApiOperation(value = "保存")
    @PostMapping("persistence")
    public Response save(@RequestBody InnerSwapApplyDTO innerSwapApplyDTO) {
        final String url = String.format("%sinnerSwapApply/persistence", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, innerSwapApplyDTO, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<InnerSwapApplyDTO>>() {
        });
    }


    @ApiOperation(value = "内部资源调剂申请详情")
    @GetMapping("getInnerSwapApplyDetail")
    public Response getInnerSwapApplyDetail(@RequestParam Long id) {
        String url = String.format("%sinnerSwapApply/getInnerSwapApplyDetail?id=%s", ModelsEnum.CTC.getBaseUrl(), id);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<InnerSwapApplyDTO> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<InnerSwapApplyDTO>>() {
        });
        return dataResponse;
    }





    @ApiOperation(value = "提交审批")
    @PutMapping("/updateStatusApproval/skipSecurityInterceptor")
    public Response updateStatusApproval(@RequestParam(required = false) Long formInstanceId,
                                         @RequestParam(required = false) String handlerId) {

        String url = String.format("%sinnerSwapApply/updateStatusApproval/skipSecurityInterceptor?formInstanceId=%s&handlerId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId, handlerId);
        restTemplate.put(url, String.class);
        DataResponse<Long> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "驳回")
    @PutMapping("/updateStatusRefuse/skipSecurityInterceptor")
    public Response updateStatusRefuse(@RequestParam(required = false) Long formInstanceId,
                                       @RequestParam(required = false) String handlerId) {
        String url = String.format("%sinnerSwapApply/updateStatusRefuse/skipSecurityInterceptor?formInstanceId=%s&handlerId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId, handlerId);
        restTemplate.put(url, String.class);
        DataResponse<Long> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "审批通过")
    @PutMapping("/updateStatusEnable/skipSecurityInterceptor")
    public Response handlerCommunicate(@RequestParam(required = false) Long formInstanceId,
                                       @RequestParam(required = false) String handlerId) {
        String url = String.format("%sinnerSwapApply/updateStatusEnable/skipSecurityInterceptor?formInstanceId=%s&handlerId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId, handlerId);
        restTemplate.put(url, String.class);
        DataResponse<Long> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "撤回")
    @PutMapping("/updateStatusReturn/skipSecurityInterceptor")
    public Response updateStatusReturn(@RequestParam(required = false) Long formInstanceId,
                                       @RequestParam(required = false) String handlerId) {
        String url = String.format("%sinnerSwapApply/updateStatusReturn/skipSecurityInterceptor?formInstanceId=%s&handlerId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId, handlerId);
        restTemplate.put(url, String.class);
        DataResponse<Long> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "作废")
    @PutMapping("/abandonChange/skipSecurityInterceptor")
    public Response abandonChange(@RequestParam(required = false) Long formInstanceId,
                                  @RequestParam(required = false) String handlerId) {
        String url = String.format("%sinnerSwapApply/abandonChange/skipSecurityInterceptor?formInstanceId=%s&handlerId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId, handlerId);
        restTemplate.put(url, String.class);
        DataResponse<Long> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "删除")
    @PutMapping("/deleteChange/skipSecurityInterceptor")
    public Response deleteChange(@RequestParam(required = false) Long formInstanceId,
                                 @RequestParam(required = false) String handlerId) {
        String url = String.format("%sinnerSwapApply/deleteChange/skipSecurityInterceptor?formInstanceId=%s&handlerId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId, handlerId);
        restTemplate.put(url, String.class);
        DataResponse<Long> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "内部资源调剂申请作废")
    @GetMapping("abandonSwapApply")
    public Response abandonSwapApply(@RequestParam Long id) {
        String url = String.format("%sinnerSwapApply/abandonSwapApply?id=%s", ModelsEnum.CTC.getBaseUrl(), id);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<Long> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<Long>>() {
        });
        return dataResponse;
    }

    @ApiOperation(value = "关闭")
    @GetMapping("cancelSwapApply")
    public Response cancelSwapApply(@RequestParam Long id) {
        String url = String.format("%sinnerSwapApply/cancelSwapApply?id=%s", ModelsEnum.CTC.getBaseUrl(), id);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<Long> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<Long>>() {
        });
        return dataResponse;
    }

}
