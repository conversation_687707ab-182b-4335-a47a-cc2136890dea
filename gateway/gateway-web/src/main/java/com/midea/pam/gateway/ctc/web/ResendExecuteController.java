package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.ResendExecuteDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.List;


/**
 * @program: pam
 * @description: ResendExecuteController
 * @author: gaojh1
 * @create: 2019-6-28 10:51
 **/
@Api("成本归集")
@RestController
@RequestMapping("ctc/resendExecute")
public class ResendExecuteController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "分页查询", response = ResendExecuteDto.class)
    @GetMapping("/page")
    public Response page(@RequestParam(required = false) @ApiParam(value = "接口类型") final String businessType,
                         @RequestParam(required = false) @ApiParam(value = "接口子类型") final String subBusinessType,
                         @RequestParam(required = false) @ApiParam(value = "ESB流水号") final String esbSerialNo,
                         @RequestParam(required = false) @ApiParam(value = "主单据ID") final String applyNo,
                         @RequestParam(required = false) @ApiParam(value = "子单据ID") final String subApplyNo,
                         @RequestParam(required = false) @ApiParam(value = "状态0:未发送，ESB1:已发送，2:发送异常") final Integer status,
                         @RequestParam(required = false) @ApiParam(value = "ESB返回码") final String responCode,
                         @RequestParam(required = false) @ApiParam(value = "ESB返回信息") final String responMsg,
                         @RequestParam(required = false) @ApiParam(value = "同步开始时间") final String createAtStart,
                         @RequestParam(required = false) @ApiParam(value = "同步结束时间") final String createAtEnd,
                         @RequestParam(required = true, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = true, defaultValue = "10") final Integer pageSize) {
        return null;
    }

    @ApiOperation(value = "批量更新")
    @PostMapping("/batchUpdate")
    public Response batchUpdate(@RequestBody List<ResendExecuteDto> resendExecuteDtoList) {
        return null;
    }

    @ApiOperation(value = "获取待推送条数")
    @GetMapping("/getTodoSize")
    public Response getTodoSize() {
        return null;
    }

    @ApiOperation(value = "重新发送")
    @PostMapping("/resend")
    public Response resend() {
        return null;
    }

    @ApiOperation(value = "批量初始化")
    @PostMapping("/batchInitialize")
    public Response batchInitialize(@RequestBody List<ResendExecuteDto> resendExecuteDtoList) {
        String url = String.format("%sctc/resendExecute/batchInitialize", ModelsEnum.CTC.getBaseUrl());
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(600 * 1000);
        httpRequestFactory.setConnectTimeout(600 * 1000);
        httpRequestFactory.setReadTimeout(600 * 1000);
        restTemplate.setRequestFactory(httpRequestFactory);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, resendExecuteDtoList, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
    }

}
