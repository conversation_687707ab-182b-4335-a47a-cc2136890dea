package com.midea.pam.gateway.externalsys.glegal;

import com.alibaba.fastjson.JSONObject;
import com.midea.pam.gateway.glegal.service.GlegalWorkflowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description 工作流接口
 * @date 2020-09-30
 */
@Api("法务合同工作流")
@RestController
@RequestMapping(value = "glegalworkflow")
public class GlegalWorkflowController {

    @Resource
    private GlegalWorkflowService glegalWorkflowService;

    private final static Logger logger = LoggerFactory.getLogger(GlegalWorkflowController.class);

    @ApiOperation("回调")
    @PostMapping({"callback"})
    public JSONObject callback(@RequestBody JSONObject param) {
        JSONObject json = new JSONObject();
        try {
            logger.debug("callback_param:{}", param.toJSONString());
            glegalWorkflowService.callback(param);

            json.put("success", true);
            json.put("msg", "success");
            return json;
        } catch (Exception e) {
            logger.error("error", e);
            json.put("success", false);
            json.put("msg", e.getMessage());
            return json;
        }
    }
}
