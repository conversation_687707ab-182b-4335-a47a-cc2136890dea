package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.PurchaseContractBudgetMaterialDto;
import com.midea.pam.common.ctc.vo.ProjectProblemCommentVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;

@Api("项目问题评论")
@RestController
@RequestMapping({"projectProblemComment"})
public class ProjectProblemCommentController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;
    /**
     * 列表
     * @param pageNum
     * @param pageSize
     * @return
     */
    @ApiOperation(value = "列表")
    @GetMapping("page")
    public Response list(@RequestParam(required = false) final Long projectProblemId,
                         @RequestParam(required = false) final Integer pageNum,
                         @RequestParam(required = false) final Integer pageSize) {
        final HashMap<String,Object> param = new HashMap<>();
        param.put("pageNum",pageNum);
        param.put("pageSize",pageSize);
        param.put("projectProblemId",projectProblemId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(),"/projectProblemComment/page",param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url,String.class);
        return JSON.parseObject(responseEntity.getBody(),new TypeReference<DataResponse<PageInfo<ProjectProblemCommentVo>>>(){

        });
    }

    @ApiOperation(value = "可以删除自己发布的评论")
    @GetMapping("delComment")
    public Response delComment(@RequestParam @ApiParam("当前操作用户ID") final Long operationId,
                        @RequestParam @ApiParam("评论ID") final Long commentId){
        final HashMap<String,Object> param = new HashMap<>();
        param.put("operationId",operationId);
        param.put("commentId",commentId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(),"/projectProblemComment/delComment",param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url,String.class);
        return JSON.parseObject(responseEntity.getBody(),new TypeReference<DataResponse<PageInfo<ProjectProblemCommentVo>>>(){

        });
    }

    @ApiOperation(value = "发布评论")
    @PostMapping("publishComment")
    public Response submit(@RequestBody ProjectProblemCommentVo projectProblemCommentVo){
        final String url = String.format("%sprojectProblemComment/publishComment",ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url,projectProblemCommentVo,String.class);
        return JSON.parseObject(responseEntity.getBody(),new TypeReference<DataResponse<Integer>>(){});
    }

}
