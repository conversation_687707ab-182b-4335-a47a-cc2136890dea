package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.DiffCompanyLaborCostSetDTO;
import com.midea.pam.common.basedata.dto.OrgLaborCostTypeSetDTO;
import com.midea.pam.common.basedata.entity.DiffCompanyLaborCostSet;
import com.midea.pam.common.basedata.entity.OrgLaborCostTypeSet;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/6/3
 * @description HR部门费率类型设置
 */
@RestController
@Api("HR部门费率类型设置")
@RequestMapping(value = {"orgLaborCostTypeSet", "mobile/app/orgLaborCostTypeSet"})
public class OrgLaborCostTypeSetController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "给组织下（人力费率类型为role）用户，初始化人力费率角色设置ID")
    @GetMapping("initLaborCostTypeSetId")
    public Response initLaborCostTypeSetId(@RequestParam String orgIds) {
        Map<String, Object> param = new HashMap<>(1);
        param.put("orgIds", orgIds);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "orgLaborCostTypeSet/initLaborCostTypeSetId", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation("分页查询")
    @GetMapping("page")
    public Response page(@ApiParam("使用单位ID") @RequestParam Long companyId,
                         @ApiParam("业务分类ID") @RequestParam(required = false) Long unitId,
                         @ApiParam("HR部门") @RequestParam(required = false) String hrOrgName,
                         @ApiParam("人力费率分类") @RequestParam(required = false) String laborCostTypeCode,
                         @ApiParam("人力费率分类，多个用,隔开") @RequestParam(required = false) String laborCostTypeCodes,
                         @ApiParam("业务分类ID，多个用,隔开") @RequestParam(required = false) String unitIds,
                         @ApiParam("业务角色id，多个用,隔开") @RequestParam(required = false) String laborCostIds,
                         @ApiParam("wbs业务角色id，多个用,隔开") @RequestParam(required = false) String laborWbsCostIds,
                         @ApiParam("角色类型，多个用,隔开") String roleTypeStr,
                         @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") Integer pageSize) {

        final Map<String, Object> param = new HashMap<>();
        param.put("companyId", companyId);
        param.put("unitId", unitId);
        param.put("hrOrgName", hrOrgName);
        param.put("laborCostTypeCode", laborCostTypeCode);
        param.put("laborCostTypeCodes", laborCostTypeCodes);
        param.put("unitIds", unitIds);
        param.put("laborCostIds", laborCostIds);
        param.put("laborWbsCostIds", laborWbsCostIds);
        param.put("roleTypeStr", roleTypeStr);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);

        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "orgLaborCostTypeSet/page", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<OrgLaborCostTypeSetDTO>>>() {
        });
    }

    @ApiOperation("分页查询人力费率-跨单位工时设置")
    @GetMapping("diffCompanyLaborCostSet/query")
    public Response pageDiffCompanyLaborCostSet(@ApiParam("项目使用单位ID") @RequestParam(required = false) Long projectCompanyId,
                                                @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                                @RequestParam(required = false, defaultValue = "10") Integer pageSize) {

        final Map<String, Object> param = new HashMap<>();
        param.put("projectCompanyId", projectCompanyId);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);

        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "orgLaborCostTypeSet/diffCompanyLaborCostSet/query", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<DiffCompanyLaborCostSetDTO>>>() {
        });
    }

    @ApiOperation("查询人力费率-跨单位工时设置")
    @GetMapping("getDiffCompanyLaborCostSet")
    public Response getDiffCompanyLaborCostSet(@ApiParam("项目使用单位ID") @RequestParam(required = false) Long projectCompanyId,
                                               @ApiParam("考勤使用单位ID") @RequestParam(required = false) Long userCompanyId,
                                               @ApiParam("ou") @RequestParam(required = false) Long ouId) {

        final Map<String, Object> param = new HashMap<>();
        param.put("projectCompanyId", projectCompanyId);
        param.put("userCompanyId", userCompanyId);
        param.put("ouId", ouId);

        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "orgLaborCostTypeSet/getDiffCompanyLaborCostSet", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<DiffCompanyLaborCostSetDTO>>() {
        });
    }

    @ApiOperation("删除人力费率-跨单位工时设置")
    @GetMapping({"diffCompanyLaborCostSet/delete"})
    public Response deleteDiffCompanyLaborCostSet(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "orgLaborCostTypeSet/diffCompanyLaborCostSet/delete", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(cleanStr(res), new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation("保存人力费率-跨单位工时设置")
    @PostMapping({"diffCompanyLaborCostSet/save"})
    public Response saveDiffCompanyLaborCostSet(@RequestBody List<DiffCompanyLaborCostSet> diffCompanyLaborCostSetList) {
        String url = String.format("%sorgLaborCostTypeSet/diffCompanyLaborCostSet/save", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, diffCompanyLaborCostSetList, String.class);
        DataResponse<Boolean> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    /**
     * 传空值
     *
     * @param baseUrl 上下文
     * @param action  接口参数
     * @param param   参数
     * @return
     */
    @Override
    public String buildGetUrl(final String baseUrl, final String action, Map<String, Object> param) {
        final StringBuffer url = new StringBuffer(baseUrl).append(action).append("?1=1");
        if (null != param) {
            param.forEach((k, v) -> {
                if (v != null) {
                    url.append("&").append(k).append("=").append(v);
                }
            });
        }
        return url.toString();
    }

    @ApiOperation("编辑")
    @PutMapping("update")
    public Response update(@RequestBody OrgLaborCostTypeSetDTO orgLaborCostTypeSet) {
        String url = String.format("%sorgLaborCostTypeSet/update", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, new HttpEntity<OrgLaborCostTypeSet>(orgLaborCostTypeSet), String.class);
        String res = cleanStr(responseEntity.getBody());
        return JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation("同步")
    @GetMapping("sync")
    public Response sync() {
        String url = String.format("%sorgLaborCostTypeSet/sync", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        String res = cleanStr(responseEntity.getBody());
        return JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation("校验用户是否有HR部门")
    @GetMapping("checkExist")
    public Response checkExist() {
        String url = String.format("%sorgLaborCostTypeSet/checkExist", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        String res = cleanStr(responseEntity.getBody());
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }
}
