package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.midea.pam.common.basedata.dto.UserInfoDto;
import com.midea.pam.common.ctc.dto.WorkingHourDistributeDto;
import com.midea.pam.common.ctc.dto.WorkingHourDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.common.utils.PamCurrentUserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 工时分配controller
 * @date 2019-9-18
 */
@Api("工时分配")
@RestController
@RequestMapping(value = {"workingHourDistribute","mobile/app/workingHourDistribute"})
public class WorkingHourDistributeController extends ControllerHelper {

    private static Logger logger = LoggerFactory.getLogger(WorkingHourController.class);

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "保存工时分配")
    @PutMapping("save")
    public Response save(@RequestBody @ApiParam(name = "WorkingHourDto", value = "工时信息") WorkingHourDistributeDto workingHourDistributeDto) {
        String url = String.format("%sworkingHourDistribute/save", ModelsEnum.CTC.getBaseUrl());
        if(workingHourDistributeDto.getUserId() == null){
            workingHourDistributeDto.setUserId(PamCurrentUserUtil.getCurrentUserDto().getId());
        }
        url = url + "?userId=" + workingHourDistributeDto.getUserId();
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, new HttpEntity<WorkingHourDistributeDto>(workingHourDistributeDto), String.class);
        String res = cleanStr(responseEntity.getBody());
        DataResponse<String> response = Response.dataResponse();
        return response.setData(res);
    }

    @ApiOperation(value = "查询工时分配", response = WorkingHourDistributeDto.class)
    @GetMapping("query")
    public Response query(
            @RequestParam(required = false) @ApiParam(value = "用户ID") Long userId) {
        final Map<String, Object> param = new HashMap<>();
        if(userId == null){
            UserInfoDto userInfoDto = PamCurrentUserUtil.getCurrentUserDto();
            param.put("userId", userInfoDto.getId());
        }else {
            param.put("userId", userId);
        }

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/workingHourDistribute/query", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<JSONObject> response = Response.dataResponse();
        return response.setData(JSONArray.parseObject(res));
    }

}
