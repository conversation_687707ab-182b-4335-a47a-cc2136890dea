package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto;
import com.midea.pam.common.ctc.dto.RdmSettlementSheetDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Api("rdm-结算单")
@RestController
@RequestMapping("rdmSettlementSheet")
public class RdmSettlementSheetController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "同步RDM数据")
    @GetMapping("getRdmSettlementSheet")
    public Response getRdmSettlementSheet(@RequestParam(required = true)String startDate,
                                        @RequestParam(required = false)String endDate){
        final Map<String, Object> param = new HashMap<>();
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "rdmSettlementSheet/getRdmSettlementSheet",param);
        restTemplate.getForObject(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        response.setData("0");
        return response;
    }

    @ApiOperation(value = "结算单查询")
    @GetMapping("query")
    public Response query(@RequestParam(defaultValue = "1") Integer pageNum,
                          @RequestParam(defaultValue = "10") Integer pageSize,
                          @RequestParam(required = false)String project,
                          @RequestParam(required = false)String managerName){
        final Map<String,Object> param = new HashMap<>();
        param.put("pageNum",pageNum);
        param.put("pageSize",pageSize);
        param.put("project",project);
        param.put("managerName",managerName);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "rdmSettlementSheet/query",param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<RdmSettlementSheetDto>>>() {});
    }

}
