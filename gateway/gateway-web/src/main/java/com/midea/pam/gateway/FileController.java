package com.midea.pam.gateway;

import com.alibaba.fastjson.JSONArray;
import com.aliyun.oss.common.utils.HttpHeaders;
import com.midea.mframework.sdk.mform.utills.DownLoadUtils;
import com.midea.mip.document.dto.DocMainDto;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.gateway.entity.FileInfo;
import com.midea.pam.common.gateway.entity.GuideFileInfo;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.base.Code;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.config.OssServiceProperties;
import com.midea.pam.gateway.service.FileService;
import com.midea.pam.gateway.service.OssService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.util.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 文件服务controller
 * @date 2019-4-3
 */
@Api("文件上传下载服务")
@RestController
@RequestMapping(value = {"file", "mobile/app"})
public class FileController {

    private static Logger logger = LoggerFactory.getLogger(FileController.class);

    @Resource
    private FileService fileService;

    @Resource
    private OssService ossService;

    @Resource
    private OssServiceProperties ossServiceProperties;

    @Autowired
    public DownLoadUtils downLoadUtils;

    @ApiOperation("上传文件")
    @PostMapping({"upload"})
    public Response upload(@RequestParam("file") MultipartFile file) {
        try {
            DataResponse<JSONArray> response = Response.dataResponse();
            return response.setData(fileService.upload(file));
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR);
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("下载文件")
    @GetMapping({"{id}"})
    public void download(@PathVariable Long id, HttpServletResponse response) {
        response.setCharacterEncoding("UTF-8");
        //response.setContentType("application/octet-stream");
        response.setContentType("application/force-download");// 设置强制下载不打开
        FileInputStream fis = null;
        try {
            FileInfo fileInfo = fileService.download(id);
            File file = new File(fileInfo.getFilePath());

            if (file.exists()) {
                fis = new FileInputStream(file);
                String fileName = new String(fileInfo.getFileName().getBytes("UTF-8"), "iso-8859-1");// 为了解决中文名称乱码问题
                response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
                IOUtils.copy(fis, response.getOutputStream());
                response.flushBuffer();
            } else {
                logger.warn("文件不存在:{}", fileInfo.getFilePath());
            }

        } catch (Exception e) {
            logger.error("error", e);
        } finally {
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                    logger.error("error", e);
                }
            }
        }
    }

    @ApiOperation(value = "查询文件信息", response = FileInfo.class)
    @GetMapping({"query/{ids}"})
    public Response query(@PathVariable @ApiParam(value = "文件id，支持多个，中间用逗号隔开") String ids) {
        try {

            DataResponse<List<FileInfo>> response = Response.dataResponse();
            return response.setData(fileService.query(ids));
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR);
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation(value = "查询系统操作指引", response = FileInfo.class)
    @GetMapping({"queryGuileFile"})
    public Response queryGuileFile() {
        try {
            DataResponse<List<GuideFileInfo>> response = Response.dataResponse();
            return response.setData(fileService.queryGuileFile());
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR);
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation(value = "oss上传附件")
    @PostMapping("oss/upload")
    public Response ossUpload(@RequestParam MultipartFile file) {
        try {
            DataResponse<JSONArray> response = Response.dataResponse();
            return response.setData(ossService.upload(file));
        } catch (Exception e) {
            logger.error("oss upload error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR);
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation(value = "oss上传附件(多个)")
    @PostMapping("oss/uploadMore")
    public Response ossUploadMore(@RequestParam MultipartFile[] file) {
        try {
            DataResponse<JSONArray> response = Response.dataResponse();
            return response.setData(ossService.upload(file));
        } catch (Exception e) {
            logger.error("oss upload error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR);
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation(value = "oss下载附件")
    @GetMapping("oss/download/{id}")
    public void ossDownload(@PathVariable Long id, HttpServletRequest request, HttpServletResponse response) {
        final FileInfo download = ossService.download(id);
        if (download == null || StringUtils.isEmpty(download.getDocId())) {
            throw new BizException(com.midea.pam.common.enums.Code.ERROR, "附件不存在");
        }
        final String docId = download.getDocId();

        String downLoadRestUrl = ossServiceProperties.getUploadRestUrlHeader() + ossServiceProperties.getDownLoadServiceName();
        InputStream inputStream2 = DownLoadUtils.restDownloadFile(downLoadRestUrl, docId);
        DocMainDto docMainDto = DownLoadUtils.getFileInformation(docId, ossServiceProperties.getAppID(), ossServiceProperties.getAppKey());
        ServletOutputStream outputStream = null;
        try {
            String userAgent = request.getHeader("user-agent").toLowerCase();
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            if (userAgent.contains("MSIE") || userAgent.contains("msie") || userAgent.contains("trident")
                    || userAgent.contains("Trident")) {
                response.setHeader("content-disposition",
                        "attachment;filename="
                                + URLEncoder.encode(docMainDto.getDocName() + "." + docMainDto.getDocExt(), "UTF-8")
                                .replaceAll("\\+", "%20"));
            } else if (userAgent.contains("firefox")) {
                response.setHeader("content-disposition", "attachment;filename="
                        + URLEncoder.encode(new String(("\"" + docMainDto.getDocName() + "." + docMainDto.getDocExt() + "\"").getBytes("UTF-8"),
                        "ISO-8859-1"), "UTF-8"));
            } else {
                response.setHeader("content-disposition", "attachment;filename="
                        + URLEncoder.encode((docMainDto.getDocName() + "." + docMainDto.getDocExt()), "UTF-8"));
            }
            response.setHeader(HttpHeaders.TRANSFER_ENCODING, "chunked");
            outputStream = response.getOutputStream();
            downLoadUtils.copy(inputStream2, outputStream);
            outputStream.close();

        } catch (Exception e) {
            logger.error("oss download error", e);
        }
    }

    @ApiOperation(value = "查询文件预览地址")
    @GetMapping({"preview/{id}"})
    public Response view(@PathVariable @ApiParam(value = "文件Id") Long id) {
        DataResponse<String> response = Response.dataResponse();
        return response.setData(ossService.getPreviewUrl(id));
    }

}