package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.MaterialCostDto;
import com.midea.pam.common.basedata.excelVo.MaterialCostExportExcelVo;
import com.midea.pam.common.basedata.excelVo.MaterialCostImportExcelVo;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.common.utils.DateUtil;
import com.midea.pam.gateway.common.utils.ExportExcelUtil;
import com.midea.pam.gateway.common.utils.FileUtil;
import com.midea.pam.gateway.common.utils.ListUtil;
import com.midea.pam.gateway.utils.FileInitUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("物料成本")
@RestController
@RequestMapping("materialCost")
public class MaterialCostController extends ControllerHelper {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "物料成本列表", response = MaterialCostDto.class)
    @GetMapping({"selectList"})
    public Response selectList(@RequestParam(required = false) @ApiParam("成本类型:1=标准;2=报价;3=估价") Integer materialCostType,
                               @RequestParam(required = false) @ApiParam("价格是否为空") Boolean itemCostIsNull,
                               @RequestParam(required = false) @ApiParam("物料类型") String materielType,
                               @RequestParam(required = false) @ApiParam("业务分类") String businessClassification,
                               @RequestParam(required = false) @ApiParam("物料分类") String materialClassification,
                               @RequestParam(required = false) @ApiParam("模糊:pam编码") String fuzzyPamCode,
                               @RequestParam(required = false) @ApiParam("模糊:erp编码") String fuzzyErpCode,
                               @RequestParam(required = false) @ApiParam("pam编码") String pamCode) {
        final Map<String, Object> param = new HashMap<>();
        param.put("materialCostType", materialCostType);
        param.put("itemCostIsNull", itemCostIsNull);
        param.put("materielType", materielType);
        param.put("businessClassification", businessClassification);
        param.put("materialClassification", materialClassification);
        param.put("fuzzyPamCode", fuzzyPamCode);
        param.put("fuzzyErpCode", fuzzyErpCode);
        param.put("pamCode", pamCode);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "materialCost/selectList", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<MaterialCostDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<MaterialCostDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "物料成本分页", response = MaterialCostDto.class)
    @GetMapping({"selectPage"})
    public Response selectPage(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                               @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                               @RequestParam(required = false) @ApiParam("成本类型:1=标准;2=报价;3=估价") Integer materialCostType,
                               @RequestParam(required = false) @ApiParam("价格是否为空") Boolean itemCostIsNull,
                               @RequestParam(required = false) @ApiParam("物料类型") String materielType,
                               @RequestParam(required = false) @ApiParam("业务分类") String businessClassification,
                               @RequestParam(required = false) @ApiParam("物料分类") String materialClassification,
                               @RequestParam(required = false) @ApiParam("模糊:pam编码") String fuzzyPamCode,
                               @RequestParam(required = false) @ApiParam("模糊:erp编码") String fuzzyErpCode,
                               @RequestParam(required = false) @ApiParam("模糊:物料描述") String fuzzyDescr,
                               @RequestParam(required = false) @ApiParam("核价人id") Long nuclearPriceUserBy,
                               @RequestParam(required = false) @ApiParam("库存组织编码") Integer organizationId,
                               @RequestParam(required = false) @ApiParam("是否有批准供应商, true为是，false为否 ") Boolean approvedSupplier,
                               @RequestParam(required = false) @ApiParam("核价开始时间") String nuclearPriceStartDate,
                               @RequestParam(required = false) @ApiParam("核价结束时间") String nuclearPriceEndDate,
                               @RequestParam(required = false) @ApiParam("物料编码") String itemCode) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("materialCostType", materialCostType);
        param.put("itemCostIsNull", itemCostIsNull);
        param.put("materielType", materielType);
        param.put("businessClassification", businessClassification);
        param.put("materialClassification", materialClassification);
        param.put("fuzzyPamCode", fuzzyPamCode);
        param.put("fuzzyErpCode", fuzzyErpCode);
        param.put("fuzzyDescr", fuzzyDescr);
        param.put("nuclearPriceUserBy", nuclearPriceUserBy);
        param.put("organizationId", organizationId);
        param.put("itemCode", itemCode);
        param.put("approvedSupplier", approvedSupplier);
        param.put("nuclearPriceStartDateStr", nuclearPriceStartDate);
        param.put("nuclearPriceEndDateStr", nuclearPriceEndDate);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "materialCost/selectPage", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<MaterialCostDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<MaterialCostDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "保存物料成本", response = MaterialCostDto.class)
    @PostMapping({"save"})
    public Response addProduct(@RequestBody MaterialCostDto dto) {
        String url = String.format("%smaterialCost/save", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<MaterialCostDto> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<MaterialCostDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "批量保存物料成本", response = MaterialCostDto.class)
    @PostMapping({"saveBatch"})
    public Response saveBatch(@RequestBody List<MaterialCostDto> dtos) {
        String url = String.format("%smaterialCost/updateCostBatch", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dtos, String.class);
        DataResponse<Boolean> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "导出物料成本", response = MaterialCostDto.class)
    @GetMapping("exportMaterialCostList")
    public void exportMaterialCostList(HttpServletResponse response,
                                       @RequestParam(required = false) @ApiParam("成本类型:1=标准;2=报价;3=估价") Integer materialCostType,
                                       @RequestParam(required = false) @ApiParam("价格是否为空") Boolean itemCostIsNull,
                                       @RequestParam(required = false) @ApiParam("物料类型") String materielType,
                                       @RequestParam(required = false) @ApiParam("业务分类") String businessClassification,
                                       @RequestParam(required = false) @ApiParam("物料分类") String materialClassification,
                                       @RequestParam(required = false) @ApiParam("模糊:pam编码") String fuzzyPamCode,
                                       @RequestParam(required = false) @ApiParam("模糊:erp编码") String fuzzyErpCode,
                                       @RequestParam(required = false) @ApiParam("库存组织编码") Integer organizationId,
                                       @RequestParam(required = false) @ApiParam("物料编码") String itemCode) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("materialCostType", materialCostType);
        param.put("itemCostIsNull", itemCostIsNull);
        param.put("materielType", materielType);
        param.put("businessClassification", businessClassification);
        param.put("materialClassification", materialClassification);
        param.put("fuzzyPamCode", fuzzyPamCode);
        param.put("fuzzyErpCode", fuzzyErpCode);
        param.put("organizationId", organizationId);
        param.put("itemCode", itemCode);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "materialCost/selectList", param);
        String responseEntity = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<MaterialCostDto>> dataResponse = JSON.parseObject(responseEntity, new TypeReference<DataResponse<List<MaterialCostDto>>>() {
        });

        List<MaterialCostDto> dataList = dataResponse.getData();
        if (CollectionUtils.isEmpty(dataList)) {
            throw new Exception("没有数据");
        }

        List<MaterialCostExportExcelVo> excelVos = ListUtil.map(dataList, new ListUtil.IteratorTask<MaterialCostExportExcelVo, MaterialCostDto>() {
            @Override
            public MaterialCostExportExcelVo getValue(MaterialCostDto item) {
                MaterialCostExportExcelVo wifi = new MaterialCostExportExcelVo();
                BeanUtils.copyProperties(item, wifi);
                if(wifi.getItemCost() != null){
                    //已估价
                    wifi.setItemCostIsNull(false);
                }else {
                    //未估价
                    wifi.setItemCostIsNull(true);
                }
                return wifi;
            }
        });

        //导出操作
        ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", MaterialCostExportExcelVo.class, "估价明细.xls", response);
    }

    @ApiOperation(value = "导入物料成本")
    @PostMapping("importMaterialCost")
    public Response importMaterialCost(@RequestParam(value = "file") MultipartFile file) {
        List<MaterialCostImportExcelVo> importExcelVos = null;
        try {
            importExcelVos = FileUtil.importExcel(file, MaterialCostImportExcelVo.class);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            Asserts.success(ErrorCode.SYSTEM_FILE_ERROR);
        }
        final String url = String.format("%smaterialCost/saveBatchByPamCodeFromExcel", ModelsEnum.BASEDATA.getBaseUrl());
        String res = restTemplate.postForEntity(url, importExcelVos, String.class).getBody();
        DataResponse<List<MaterialCostImportExcelVo>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<MaterialCostImportExcelVo>>>() {
        });
        return response;
    }

    @ApiOperation(value = "ERP物料成本拉取")
    @GetMapping({"getMaterialCostFromErp"})
    public Response getMaterialCostFromErp(@RequestParam(required = true) String lastUpdateDate,
                                           @RequestParam(required = false) String lastUpdateDateEnd) {
        final Map<String, Object> param = new HashMap<>();
        param.put("lastUpdateDate", lastUpdateDate);
        param.put("lastUpdateDateEnd", lastUpdateDateEnd);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "materialCost/getMaterialCostFromErp", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<String> response = JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
        return response;
    }

    /**
     * @param timeDate 日期格式：yyyy-MM-dd hh:mm:ss
     * @return 物料成本信息
     */
    @ApiOperation(value = "获取具体某一天更新到现在的物料成本")
    @GetMapping("findMaterialCost")
    public Response findMaterialCost(@RequestParam(required = true) String timeDate) {
        final Map<String, Object> param = new HashMap<>();
        param.put("timeDate", timeDate);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "materialCost/findMaterialCost", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<MaterialCostDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<MaterialCostDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "同步")
    @GetMapping("sync")
    public Response sync(@RequestParam(required = true) Long applyNo) {
        final Map<String, Object> param = new HashMap<>();
        param.put("applyNo", applyNo);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "materialCost/sync", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        String res = cleanStr(responseEntity.getBody());
        return JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
    }
}
