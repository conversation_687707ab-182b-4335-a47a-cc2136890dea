package com.midea.pam.gateway.ctc.web;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.mcomponent.core.util.BeanUtils;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.ctc.entity.ProjectProblemOperationRecord;
import com.midea.pam.common.ctc.vo.*;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.utils.ExportExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

@Api("项目问题操作记录")
@RestController
@RequestMapping({"projectProblem/operationRecord"})
public class ProjectProblemOperationRecordController extends ControllerHelper {

    private static final Logger logger = LoggerFactory.getLogger(ProjectProblemOperationRecordController.class);

    @Resource
    private RestTemplate restTemplate;

    /**
     * 列表
     * @param pageNum
     * @param pageSize
     * @return
     */
    @ApiOperation(value = "分页列表")
    @GetMapping("page")
    public Response list(@RequestParam(required = false) Long projectProblemId,
                         @RequestParam(required = false) final Integer pageNum,
                         @RequestParam(required = false) final Integer pageSize) {
        final HashMap<String,Object> param = new HashMap<>();
        param.put("projectProblemId",projectProblemId);
        param.put("pageNum",pageNum);
        param.put("pageSize",pageSize);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(),"/projectProblem/operationRecord/page",param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url,String.class);
        return JSON.parseObject(responseEntity.getBody(),new TypeReference<DataResponse<PageInfo<ProjectProblemOperationRecordVo>>>(){

        });
    }

    @ApiOperation(value = "导出操作记录")
    @GetMapping("execl/export")
    public void export(@RequestParam(required = false) Long projectProblemId,
                       @RequestParam(required = false) final Integer pageNum,
                       @RequestParam(required = false) final Integer pageSize,
                       HttpServletResponse response){
        final HashMap<String,Object> param = new HashMap<>();
        param.put("pageNum",pageNum);
        param.put("pageSize",pageSize);
        param.put("projectProblemId",projectProblemId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(),"/projectProblem/operationRecord/list",param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url,String.class);
        final DataResponse<List<ProjectProblemOperationRecordVo>> dataResponse =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<ProjectProblemOperationRecordVo>>>() {
                });
        final List<ProjectProblemOperationRecordExcVo> list = BeanUtils.copyPropertiesByList(dataResponse.getData(), ProjectProblemOperationRecordExcVo.class);
        AtomicInteger atomicInteger = new AtomicInteger();
        list.stream().forEach(m -> {
            UserInfo userInfo = Objects.requireNonNull(CacheDataUtils.findUserById(m.getCreateBy()));
            if(ObjectUtil.isNotEmpty(userInfo)){
                m.setCreateByName(userInfo.getName());
            }
            m.setNumb(Integer.valueOf(atomicInteger.addAndGet(1) + ""));
        });
        ExportExcelUtil.exportExcel(list, null, "Sheet1", ProjectProblemOperationRecordExcVo.class, "项目问题操作记录导出.xls", response);
    }
}
