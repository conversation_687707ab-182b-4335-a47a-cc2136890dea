package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.DictDto;
import com.midea.pam.common.basedata.entity.Dict;
import com.midea.pam.common.basedata.query.LtcDictQuery;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.Utils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@Api("数据字典")
@RequestMapping(value = {"ltcDict", "mobile/app/ltcDict"})
public class LtcDictController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation("新增数据字典")
    @PostMapping({"add"})
    public Response add(@RequestBody DictDto dto) {
        String url = String.format("%sltcDict/add", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<DictDto> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<DictDto>>() {
        });
        return response;
    }

    @ApiOperation("修改数据字典")
    @PostMapping({"update"})
    public Response update(@RequestBody DictDto dto) {
        String url = String.format("%sltcDict/update", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<DictDto> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<DictDto>>() {
        });
        return dataResponse;
    }

    @ApiOperation("删除数据字典")
    @GetMapping({"delete"})
    public Response delete(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/ltcDict/delete", param);
        DataResponse<String> response = Response.dataResponse();
        response.setData(restTemplate.getForObject(url, String.class));
        return response;
    }

    @ApiOperation("数据字典分页")
    @GetMapping({"selectPage"})
    public Response selectPage(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                               @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                               @RequestParam String type) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("type", type);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/ltcDict/selectPage", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<DictDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<DictDto>>>() {
        });
        return response;
    }

    @ApiOperation("某类型下数据字典")
    @GetMapping({"listByType"})
    public Response listByType(@RequestParam String type, @RequestParam(required = false) @ApiParam("使用单位id") Long unitId) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("type", type);
        param.put("unitId", unitId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/ltcDict/listByType", param);
        return Response.dataResponse(restTemplate.getForObject(url, List.class));
    }

    @ApiOperation("查询付款方式")
    @GetMapping({"selectPaymentMethod"})
    public Response selectPaymentMethod(@RequestParam String paymentType) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        StringBuffer sb = new StringBuffer();
        sb.append("third_bill_payment_type").append(",");
        if ("0".equals(paymentType)) {
            sb.append("prepayment_type");
        } else if ("1".equals(paymentType)) {
            sb.append("payment_type");
        } else {
            //其它场景查询付款方式
            sb.append("prepayment_type").append(",").append("payment_type");
        }
        param.put("types", sb.toString());
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/ltcDict/selectByTypeList", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<DictDto>> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<List<DictDto>>>() {
        });
        List<DictDto> dictList = dataResponse.getData();
        //排序,第三方的排在最后
        List<DictDto> sortList = dictList.stream().map(dictDto -> {
                    int sortTags = "prepayment_type".equals(dictDto.getType()) ? 1 : ("payment_type".equals(dictDto.getType()) ? 2 : 3);
                    dictDto.setSortTags(sortTags);
                    return dictDto;
                }).sorted(Comparator.comparing(DictDto::getSortTags).thenComparing(DictDto::getOrderNum, Comparator.nullsLast(Byte::compareTo))) //排序,默认为顺序
                .collect(Collectors.toList());
        //判断list集合中是否有重复的类型
        if (Objects.equals("0", paymentType) || Objects.equals("1", paymentType)) {
            Map<String, Long> map = sortList.stream().collect(Collectors.groupingBy(DictDto::getCode, Collectors.counting()));
            List<String> list = map.entrySet().stream()
                    .filter(entry -> entry.getValue() > 1) //判断数据是否重复
                    .map(entry -> entry.getKey()).collect(Collectors.toList());//获取重复数据的key
            if (CollectionUtils.isNotEmpty(list)) {
                //list 不为空,则存在数据重复
                throw new BizException(Code.ERROR, "付款方式存在重复数据，请联系IT处理");
            }
        } else {
            //对于重复的数据去重,需要二次排序
            sortList = sortList.stream().collect(Collectors.toMap(DictDto::getCode, Function.identity(), (a, b) -> a)).values().stream()
                    .sorted(Comparator.comparing(DictDto::getSortTags).thenComparing(DictDto::getOrderNum, Comparator.nullsLast(Byte::compareTo)))
                    .collect(Collectors.toList());
        }

        //排序
        return Response.dataResponse(sortList);
    }

    @ApiOperation("根据类型+编码获取数据字典")
    @GetMapping({"listByTypeAndCode"})
    public Response listByTypeAndCode(@RequestParam String type, @RequestParam String code) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("type", type);
        param.put("code", code);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "ltcDict/listByTypeAndCode", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<DictDto>>>() {
        });
    }

    @ApiOperation(value = "数据字典树")
    @PostMapping("tree")
    public Response tree(@RequestBody(required = false) LtcDictQuery query) throws Exception {
        String url = String.format("%sltcDict/tree", ModelsEnum.BASEDATA.getBaseUrl());
        String res = cleanStr(restTemplate.postForObject(url, query, String.class));
        List<Map<String, Object>> dataList = Utils.jsonStr2List(res);
        DataResponse<List<Map<String, Object>>> response = Response.dataResponse();
        return response.setData(dataList);
    }


    @ApiOperation(value = "数据字典树")
    @PostMapping("info")
    public com.midea.pam.gateway.common.base.Response info(@RequestBody(required = false) Long id) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "ltcDict/info", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        com.midea.pam.gateway.common.base.DataResponse<Dict> response = JSON.parseObject(res, new TypeReference<com.midea.pam.gateway.common.base.DataResponse<Dict>>() {
        });
        return response;
    }

    @ApiOperation("获取数据字典")
    @GetMapping({"getLtcDict"})
    public Response listByTypeAndCode(@RequestParam String type, @RequestParam(required = false) String name,
                                      @RequestParam(required = false) String code) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("type", type);
        param.put("name", name);
        param.put("code", code);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "ltcDict/getLtcDict", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<DictDto>>>() {
        });
    }
}
