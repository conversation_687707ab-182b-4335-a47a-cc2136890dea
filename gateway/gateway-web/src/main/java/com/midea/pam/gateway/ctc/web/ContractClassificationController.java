package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.ContractClassificationHeadDTO;
import com.midea.pam.common.ctc.entity.ContractClassificationHead;
import com.midea.pam.common.ctc.entity.ContractClassificationRecord;
import com.midea.pam.common.ctc.excelVo.ContractClassificationDetailExcelVo;
import com.midea.pam.common.ctc.excelVo.ContractClassificationHeadExcelVo;
import com.midea.pam.common.ctc.excelVo.ProjectInvoiceDetailExcelVo;
import com.midea.pam.common.ctc.excelVo.RevenueCostOrderSumExcelVo;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ContractClassificationType;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * @description: 重分类入账控制层
 * @author: ex_xuwj4
 * @create: 2021-10-11
 **/
@Api("重分类入账")
@RestController
@RequestMapping("contractClassification")
public class ContractClassificationController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "创建重分类入账单")
    @PostMapping("create")
    public Response createContractClassification(@RequestBody ContractClassificationRecord classificationRecord) {
        String url = String.format("%scontractClassification/create", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, classificationRecord, String.class).getBody();
        DataResponse<List<ContractClassificationRecord>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<ContractClassificationRecord>>>() {
        });
        return response;
    }

    @ApiOperation(value = "重分类入账单分页查询")
    @GetMapping("page")
    public Response page(@RequestParam(required = false) @ApiParam(value = "入账单号")  String accountNum,
                         @RequestParam(required = false) @ApiParam(value = "单据状态")  String statusStr,
                         @RequestParam(required = false) @ApiParam(value = "业务类型")  String typeStr,
                         @RequestParam(required = false) @ApiParam(value = "币种")     String currencyStr,
                         @RequestParam(required = false) @ApiParam(value = "入账期间")  String glPeriod,
                         @RequestParam(required = false) @ApiParam(value = "红冲状态")  String reverseStatusStr,
                         @RequestParam(required = false) @ApiParam(value = "同步状态")  String syncStatusStr,
                         @RequestParam(required = false) @ApiParam(value = "业务实体")  String ouIdsStr,
                         @RequestParam(required = true) @ApiParam(value = "使用单位")   Long unitId,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {

        Map<String, Object> param = new HashMap<>();
        param.put("accountNum", accountNum);
        param.put("statusStr", statusStr);
        param.put("typeStr", typeStr);
        param.put("glPeriod", glPeriod);
        param.put("currencyStr", currencyStr);
        param.put("reverseStatusStr", reverseStatusStr);
        param.put("syncStatusStr", syncStatusStr);
        param.put("ouIdsStr", ouIdsStr);
        param.put("unitId", unitId);
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "contractClassification/page", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ContractClassificationHeadDTO>>>() {
        });
    }

    @ApiOperation(value = "重分类入账单作废")
    @GetMapping("invalid")
    public Response invalidContractClassification(@RequestParam(required = true) final Long id) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "contractClassification/invalid", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "重分类入账单明细查询")
    @GetMapping("detail/{id}")
    public Response queryDetail(@PathVariable final Long id) {
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "contractClassification/detail/" + id, null);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<ContractClassificationHeadDTO>>() {
        });
    }

    @ApiOperation(value = "重分类入账单冲销")
    @PostMapping("reverse")
    public Response reverseContractClassification(@RequestBody ContractClassificationHeadDTO contractClassificationHeadDTO) {
        String url = String.format("%scontractClassification/reverse", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, contractClassificationHeadDTO, String.class).getBody();
        DataResponse<Integer> response = JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
        return response;
    }

    @ApiOperation(value = "重分类列表导出", response = ContractClassificationHeadDTO.class)
    @GetMapping({"exprot"})
    public void exportContractClassification(
            HttpServletResponse response,
            @RequestParam(required = false) @ApiParam(value = "入账单号")  String accountNum,
            @RequestParam(required = false) @ApiParam(value = "单据状态")  String statusStr,
            @RequestParam(required = false) @ApiParam(value = "业务类型")  String typeStr,
            @RequestParam(required = false) @ApiParam(value = "币种")     String currencyStr,
            @RequestParam(required = false) @ApiParam(value = "入账期间")  String glPeriod,
            @RequestParam(required = false) @ApiParam(value = "红冲状态")  String reverseStatusStr,
            @RequestParam(required = false) @ApiParam(value = "同步状态")  String syncStatusStr,
            @RequestParam(required = false) @ApiParam(value = "业务实体")  String ouIdsStr) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("accountNum", accountNum);
        param.put("statusStr", statusStr);
        param.put("typeStr", typeStr);
        param.put("currencyStr", currencyStr);
        param.put("glPeriod",glPeriod);
        param.put("reverseStatusStr",reverseStatusStr);
        param.put("syncStatusStr", syncStatusStr);
        param.put("ouIdsStr", ouIdsStr);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "contractClassification/export", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        final DataResponse<Map<String,Object>> dataResponse =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String,Object>>>() {
                });
        Map<String,Object>resultMap = dataResponse.getData();
        JSONArray contractClassificationHeadArr = (JSONArray) resultMap.get("contractClassificationHeadList");
        JSONArray contractClassificationDetailArr = (JSONArray) resultMap.get("contractClassificationDetailList");

        List<ContractClassificationHeadExcelVo> contractClassificationHeadExcelVoList = new ArrayList<>();
        List<ContractClassificationDetailExcelVo> contractClassificationDetailExcelVoList = new ArrayList<>();

        if(contractClassificationHeadArr!=null) {
            contractClassificationHeadExcelVoList = JSONObject.parseArray(contractClassificationHeadArr.toJSONString(), ContractClassificationHeadExcelVo.class);
        }

        if(contractClassificationDetailArr!=null) {
            contractClassificationDetailExcelVoList = JSONObject.parseArray(contractClassificationDetailArr.toJSONString(), ContractClassificationDetailExcelVo.class);
        }

        Workbook workbook = ExportExcelUtil.buildDefaultSheet(contractClassificationHeadExcelVoList, ContractClassificationHeadExcelVo.class, null, "重分类列表", true);
        ExportExcelUtil.addSheet(workbook,contractClassificationDetailExcelVoList, ContractClassificationDetailExcelVo.class, null, "合同资产重分类入账信息",true);

        ExportExcelUtil.downLoadExcel("重分类列表导出_"+DateUtils.format(new Date(),"yyyyMMddHHmmss")+".xls", response, workbook);

    }

    @ApiOperation(value = "重分类入账单")
    @GetMapping("sync")
    public Response sync(@RequestParam(required = true) final Long id) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "contractClassification/sync", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "入账明细导出")
    @GetMapping("exprotDetail")
    public void exprotDetail(HttpServletResponse response, @RequestParam Long contractClassificationHeadId) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("contractClassificationHeadId", contractClassificationHeadId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "contractClassification/exprotDetail", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        final DataResponse<Map<String, Object>> dataResponse =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
                });
        Map<String, Object> resultMap = dataResponse.getData();
        JSONObject jsonObject = (JSONObject) resultMap.get("contractClassificationHead");
        if (jsonObject == null) {
            throw new BizException(Code.ERROR, "重分类单据不存在");
        }
        Integer classificationHeadType = jsonObject.getInteger("type");
        ContractClassificationType classificationType = ContractClassificationType.getType(classificationHeadType);
        JSONArray arr1 = (JSONArray) resultMap.get("detailList1");
        JSONArray arr2 = (JSONArray) resultMap.get("detailList2");
        JSONArray arr3 = (JSONArray) resultMap.get("detailList3");

        Workbook workbook = null;
        if (ContractClassificationType.CCFT.getCode().equals(classificationHeadType)) {
            List<ProjectInvoiceDetailExcelVo> detailExcelVoList1 = new ArrayList<>();
            List<ProjectInvoiceDetailExcelVo> detailExcelVoList2 = new ArrayList<>();
            if (arr1 != null) {
                detailExcelVoList1 = JSONObject.parseArray(arr1.toJSONString(), ProjectInvoiceDetailExcelVo.class);
            }
            if (arr2 != null) {
                detailExcelVoList2 = JSONObject.parseArray(arr2.toJSONString(), ProjectInvoiceDetailExcelVo.class);
            }
            workbook = ExportExcelUtil.buildDefaultSheet(detailExcelVoList1, ProjectInvoiceDetailExcelVo.class, null, "项目收入大于项目开票金额（不含税）", true);
            ExportExcelUtil.addSheet(workbook, detailExcelVoList2, ProjectInvoiceDetailExcelVo.class, null, "项目收入小于等于项目开票金额（不含税）", true);
        } else if (ContractClassificationType.LYFT.getCode().equals(classificationHeadType)) {
            List<RevenueCostOrderSumExcelVo> detailExcelVoList1 = new ArrayList<>();
            List<RevenueCostOrderSumExcelVo> detailExcelVoList2 = new ArrayList<>();
            if (arr1 != null) {
                detailExcelVoList1 = JSONObject.parseArray(arr1.toJSONString(), RevenueCostOrderSumExcelVo.class);
            }
            if (arr2 != null) {
                detailExcelVoList2 = JSONObject.parseArray(arr2.toJSONString(), RevenueCostOrderSumExcelVo.class);
            }
            workbook = ExportExcelUtil.buildDefaultSheet(detailExcelVoList1, RevenueCostOrderSumExcelVo.class, null, "自动成本补差工单累计成本大于0的项目", true);
            ExportExcelUtil.addSheet(workbook, detailExcelVoList2, RevenueCostOrderSumExcelVo.class, null, "自动成本补差工单累计成本小于等于0的项目", true);
        } else {
            workbook  = new XSSFWorkbook();
        }

        List<ContractClassificationDetailExcelVo> detailExcelVoList3 = new ArrayList<>();
        if (arr3 != null) {
            detailExcelVoList3 = JSONObject.parseArray(arr3.toJSONString(), ContractClassificationDetailExcelVo.class);
        }
        ExportExcelUtil.addSheet(workbook, detailExcelVoList3, ContractClassificationDetailExcelVo.class, null, "入账信息", true);

        ExportExcelUtil.downLoadExcel(classificationType.getName() + "计算明细导出_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls", response, workbook);
    }
}
