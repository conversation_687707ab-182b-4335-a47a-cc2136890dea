package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.ReceiptMethodDto;
import com.midea.pam.common.basedata.entity.ReceiptMethod;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Api("应收收款方法")
@RequestMapping({"receiptMethod"})
public class ReceiptMethodController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "应收收款方法页面分页查询")
    @GetMapping("selectPage")
    public Response selectPage(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                               @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                               @RequestParam(required = false) @ApiParam("业务实体ID") Long orgId,
                               @RequestParam(required = false) @ApiParam("业务实体ID（多选）") String orgIdStr,
                               @RequestParam(required = false) @ApiParam("业务实体名称") String orgName,
                               @RequestParam(required = false) @ApiParam("收款方法") String receiptMethodName,
                               @RequestParam(required = false) @ApiParam("收款分类名称") String receiptClassName,
                               @RequestParam(required = false) @ApiParam("收款分类名称(多选)") String receiptClassNameStr,
                               @RequestParam(required = false) @ApiParam("银行名称") String bankName,
                               @RequestParam(required = false) @ApiParam("账户名") String bankAccountName
    ) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("orgId", orgId);
        param.put("orgIdStr", orgIdStr);
        param.put("orgName", orgName);
        param.put("receiptMethodName", receiptMethodName);
        param.put("receiptClassName", receiptClassName);
        param.put("receiptClassNameStr", receiptClassNameStr);
        param.put("bankName", bankName);
        param.put("bankAccountName", bankAccountName);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "receiptMethod/selectPage", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<ReceiptMethodDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<ReceiptMethodDto>>>() {

        });
        return response;

            /*       String url = String.format("%sreceiptMethod/selectPage",ModelsEnum.BASEDATA.getBaseUrl());
        String res = cleanStr(restTemplate.postForObject(url, query, String.class));
        PageInfo<Map<String, Object>> data = JSON.parseObject(res, new TypeReference<PageInfo<Map<String, Object>> >(){});
        PageResponse<Map<String, Object>> response = Response.pageResponse();
        return response.convert(data);*/
        //}
    }

    @ApiOperation(value = "应收收款方法erp同步")
    @GetMapping("getReceiptMethodFromErp")
    public Response getReceiptMethodFromErp(@RequestParam(required = false) String lastUpdateDate) {
        final Map<String, Object> param = new HashMap<>();
        param.put("lastUpdateDate", lastUpdateDate);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "receiptMethod/getReceiptMethodFromErp", param);
        restTemplate.getForObject(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        response.setData("0");
        return response;
    }

    @ApiOperation(value = "获取所有收款分类名称")
    @GetMapping("getReceiptClassNameList")
    public Response getReceiptClassNameList() {
        final Map<String, Object> param = new HashMap<>();
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "receiptMethod/getReceiptClassNameList", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<ReceiptMethodDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<ReceiptMethodDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "根据ouId和bankAccountId收款方法查询")
    @GetMapping("getReceiptMethodByOuIdAndBankAccountId")
    public Response getReceiptMethodByOuIdAndBankAccountId(@RequestParam(required = false) Long ouId, @RequestParam(required = false) Long bankAccountId, @RequestParam(required = false) String currencyCode) {
        final Map<String, Object> param = new HashMap<>();
        param.put("ouId", ouId);
        param.put("bankAccountId", bankAccountId);
        param.put("currencyCode", currencyCode);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "receiptMethod/getReceiptMethodByOuIdAndBankAccountId", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<ReceiptMethod>>>() {
        });
    }

}
