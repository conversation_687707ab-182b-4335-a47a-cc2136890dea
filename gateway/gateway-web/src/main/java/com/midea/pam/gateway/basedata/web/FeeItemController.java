package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.FeeItemDto;
import com.midea.pam.common.basedata.entity.FeeItem;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Api("费用类型")
@RestController
@RequestMapping("feeItem")
public class FeeItemController extends ControllerHelper {
    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "新增/修改费用类型")
    @PostMapping("persistence")
    public Response persistence(@RequestBody FeeItem feeItem) {
        final String url = String.format("%sfeeItem/persistence", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, feeItem, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Long>>() {
        });
    }

    @ApiOperation(value = "按id查询编码规则")
    @GetMapping("findById/{id}")
    public Response findById(@PathVariable Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/feeItem/findById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<FeeItem>>() {
        });
    }

    @Deprecated
    @ApiOperation(value = "分页查询费用类型")
    @GetMapping("page")
    public Response getpage(@RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize,
                         @RequestParam(required = false) final String name,
                         @RequestParam(required = false) final Boolean authority) {
        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        param.put(Constants.Page.NAME, name);
        param.put("authority", authority);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/feeItem/page", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<FeeItemDto>>>() {
        });
    }

    @ApiOperation(value = "分页查询费用类型")
    @PostMapping("page")
    public Response page(@RequestBody FeeItemDto query) {
        String url = String.format("%sfeeItem/page", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, query, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<FeeItemDto>>>() {
        });
    }

    @ApiOperation(value = "分页查询合同拆分费用类型")
    @GetMapping("pageByCondition")
    public Response pageByCondition(@RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                    @RequestParam(required = false, defaultValue = "10") final Integer pageSize,
                                    @RequestParam(required = false) final String name,
                                    @RequestParam(required = false) final Boolean authority) {
        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        param.put(Constants.Page.NAME, name);
        param.put("authority", authority);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/feeItem/pageByCondition", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<FeeItemDto>>>() {
        });
    }

    @ApiOperation(value = " 分页查询项目费用类型")
    @GetMapping("pageByProject")
    public Response pageOfProject(@RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                  @RequestParam(required = false, defaultValue = "10") final Integer pageSize,
                                  @RequestParam(required = false) final String name,
                                  @RequestParam(required = false) final Boolean authority,
                                  @RequestParam(required = false) final Long unitId) {
        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        param.put(Constants.Page.NAME, name);
        param.put("authority", authority);
        param.put("unitId", unitId);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/feeItem/pageByProject", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<FeeItemDto>>>() {
        });
    }
}
