package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.CostCollectionDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @program: pam
 * @description: CostCollectionController
 * @author: gaojh1
 * @create: 2019-3-12 10:51
 **/
@Api("成本归集")
@RestController
@RequestMapping("ctc/costCollection")
public class CostCollectionController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "成本归集分页查询")
    @GetMapping("page")
    public Response page(@RequestParam(required = false) final Long id,
                         @ApiParam(name="projectCode",value="项目编码",required=false) @RequestParam(required = false) String projectCode,
                         @ApiParam(name="ouId",value="实体ID",required=false) @RequestParam(required = false) Long ouId,
                         @ApiParam(name="costMethodMain",value="收入/成本方法",required=false) @RequestParam(required = false) Integer costMethodMain,
                         @ApiParam(name="projectName",value="项目名称",required=false) @RequestParam(required = false) String projectName,
                         @ApiParam(name="startTime",value="归集开始时间",required=false) @RequestParam(required = false) Date startTime,
                         @ApiParam(name="endTime",value="归集结束时间",required=false) @RequestParam(required = false) Date endTime,
                         @RequestParam(required = true, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = true, defaultValue = "10")final Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        param.put("id", id);
        param.put("projectCode",projectCode);
        param.put("ouId",ouId);
        param.put("projectName",projectName);
        param.put("type",costMethodMain);
        param.put("startTime",startTime);
        param.put("endTime",endTime);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/costCollection/page", param);
        final String res = restTemplate.getForEntity(url , String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<CostCollectionDto>>>(){});
    }


    @ApiOperation(value = "成本归集")
    @GetMapping("collection")
    public Response collection(@RequestParam(required = false) final Long ouId,
                               @RequestParam(required = true) final String collectionDate) {
        final Map<String, Object> param = new HashMap<>();
        param.put("ouId", ouId);
        param.put("collectionDate", collectionDate);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/costCollection/collection", param);
        final String res = restTemplate.getForEntity(url , String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>(){});
    }

    @ApiOperation(value = "成本归集")
    @GetMapping("updateLaborCostHardWorking")
    public Response updateLaborCostHardWorking(@RequestParam(required = true) @ApiParam(value = "ouId") final Long ouId,
                                               @RequestParam(required = false) @ApiParam(value = "项目id") final Long projectId,
                                               @RequestParam(required = false) @ApiParam(value = "考勤用户id") final Long userId,
                                               @RequestParam(required = false) @ApiParam(value = "填报人部门") final String applyOrg,
                                               @RequestParam(required = false) @ApiParam(value = "考勤日期开始") final String applyDateStart,
                                               @RequestParam(required = false) @ApiParam(value = "考勤日期结束") final String applyDateEnd) {
        final Map<String, Object> param = new HashMap<>();
        param.put("ouId", ouId);
        param.put("projectId", projectId);
        param.put("userId", userId);
        param.put("applyOrg", applyOrg);
        param.put("applyDateStart", applyDateStart);
        param.put("applyDateEnd", applyDateEnd);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/costCollection/updateLaborCostHardWorking", param);
        final String res = restTemplate.getForEntity(url , String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>(){});
    }

    @ApiOperation(value = "成本归集检查和更新")
    @GetMapping("updateCollection")
    public Response updateCollection(@RequestParam(required = true) final Long projectId,
                                     @RequestParam(required = true) final String collectionDateStart,
                                     @RequestParam(required = true) final String collectionDateEnd) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        param.put("collectionDateStart", collectionDateStart);
        param.put("collectionDateEnd", collectionDateEnd);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/costCollection/updateCollection", param);
        final String res = restTemplate.getForEntity(url , String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>(){});
    }

    @ApiOperation(value = "成本归集详情")
    @GetMapping("detail")
    public Response detail(@RequestParam(required = true) final Long id){
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/costCollection/detail", param);
        final String res = restTemplate.getForEntity(url , String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<CostCollectionDto>>(){});
    }

    @ApiOperation(value = "成本信息导出")
    @GetMapping("/export")
    public void export(HttpServletResponse response,
                       @ApiParam(name="projectCode",value="项目编码",required=false) @RequestParam(required = false) String projectCode,
                       @ApiParam(name="ouId",value="实体ID",required=false) @RequestParam(required = false) Long ouId,
                       @ApiParam(name="type",value="收入/成本方法",required=false) @RequestParam(required = false) Integer type,
                       @ApiParam(name="projectName",value="项目名称",required=false) @RequestParam(required = false) String projectName,
                       @ApiParam(name="startTime",value="归集开始时间",required=false) @RequestParam(required = false) Date startTime,
                       @ApiParam(name="endTime",value="归集结束时间",required=false) @RequestParam(required = false) Date endTime
                       )  {
    }

    @ApiOperation(value = "计算本次追溯调整金额")
    @GetMapping("/calcAdjustmentAmount")
    public void calcAdjustmentAmount(HttpServletResponse response, @RequestParam(required = false) Long carryoverId)  {
    }

    @ApiOperation(value = "incomeErpPushTest")
    @GetMapping("/incomeErpPushTest")
    public void incomeErpPushTest(HttpServletResponse response, @RequestParam(required = false) Long id)  {
    }

    @ApiOperation(value = "costPushErpTest")
    @GetMapping("/costPushErpTest")
    public void costPushErpTest(HttpServletResponse response, @RequestParam(required = false) Long id)  {
    }

    @ApiOperation(value = "listWithDetail")
    @GetMapping("/listWithDetail")
    public Response listWithDetail(@RequestParam Long id, @RequestParam Date startTime, @RequestParam Date endTime) {
        return null;
    }
}
