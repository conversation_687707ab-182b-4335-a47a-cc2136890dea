package com.midea.pam.gateway;

import com.github.pagehelper.PageInfo;
import com.midea.pam.common.gateway.dto.MbfRetryMsgDTO;
import com.midea.pam.common.gateway.query.MbfRetryMsgQuery;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.service.DeadLetterNewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api("死信治理")
@RestController
@RequestMapping("deadLetterNew")
public class DeadLetterNewController {

    @Resource
    private DeadLetterNewService deadLetterNewService;

    @ApiOperation("获取 pam_system.mbf_retry_msg 表中的数据")
    @PostMapping("page")
    public Response page(@RequestBody MbfRetryMsgQuery query) {
        Guard.notNull(query, "查询参数为空");
        Guard.notNull(query.getPageNum(), "分页当前页参数为空");
        Guard.notNull(query.getPageSize(), "分页每页数量参数为空");

        DataResponse<PageInfo<MbfRetryMsgDTO>> dataResponse = Response.dataResponse();
        PageInfo<MbfRetryMsgDTO> list = deadLetterNewService.page(query);
        return dataResponse.setData(list);
    }

    @ApiOperation("单个重试")
    @GetMapping("retry/dls/{id}")
    public Response retry(@PathVariable("id") Long id) {
        Guard.notNull(id, "id 不能为空");

        DataResponse<Boolean> dataResponse = Response.dataResponse();
        boolean result = deadLetterNewService.retry(id);
        return dataResponse.setData(result);
    }

    @ApiOperation("批量重试")
    @PostMapping("retry/dls")
    public Response retry(@RequestBody List<Long> idList) {
        Guard.notNullOrEmpty(idList, "idList 不能为空");

        DataResponse<Boolean> dataResponse = Response.dataResponse();
        boolean result = deadLetterNewService.retry(idList);
        return dataResponse.setData(result);
    }
}
