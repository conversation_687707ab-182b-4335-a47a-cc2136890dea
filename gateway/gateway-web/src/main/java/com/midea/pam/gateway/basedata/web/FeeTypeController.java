package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.PageResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @program: pam
 * @description: FeeTypeController
 * @author: gaojh1
 * @create: 2019-3-12 10:51
 **/
@RestController
@RequestMapping("feeType")
@Api("经济事项")
public class FeeTypeController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "listPage")
    @GetMapping("listPage")
    public Response listPage(@RequestParam(required = false) String feeTypeCode,
                             @RequestParam(required = false) String feeTypeName,
                             @RequestParam(required = true, defaultValue = "false") Boolean active,
                             @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                             @RequestParam(required = false, defaultValue = "10") Integer pageSize) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("feeTypeCode", feeTypeCode);
        param.put("feeTypeName", feeTypeName);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("active", active);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "feeType/listPage",param);
        String res =  restTemplate.getForObject(url , String.class);
        res = cleanStr(res);
        PageInfo<Map<String, Object>> data = JSON.parseObject(res, new TypeReference<PageInfo<Map<String, Object>> >(){});
        PageResponse<Map<String, Object>> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation(value = "getFeeTypeFromEms")
    @GetMapping("getFeeTypeFromEms")
    public Response getFeeTypeFromEms() {
        final Map<String, Object> param = new HashMap<>();
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "feeType/getFeeTypeFromEms",param);
        restTemplate.getForObject(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        response.setData("0");
        return response;
    }

}
