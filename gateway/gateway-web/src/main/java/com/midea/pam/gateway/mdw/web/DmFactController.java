package com.midea.pam.gateway.mdw.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/17
 * @description 项目看板指标应用层
 */
@Api("项目费用支付明细")
@RestController
@RequestMapping(value = {"/dm/fact", "/mobile/app/dm/fact"})
public class DmFactController extends ControllerHelper {
    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "主动调用定时")
    @GetMapping("/execute")
    public Response active(@RequestParam(required = false) Long projectId) {

        final  Map<String,Object> param = new HashMap<>();
        param.put("projectId",projectId);
        final String url = buildGetUrl(ModelsEnum.MDW.getBaseUrl(),"/dm/fact/execute/",param);
        final String res = restTemplate.getForEntity(url,String.class).getBody();
        return JSON.parseObject(res,new TypeReference<DataResponse<Boolean>>(){});
    }


}