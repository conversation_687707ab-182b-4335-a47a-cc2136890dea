package com.midea.pam.gateway.ctc.web;

import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

@Api("项目预立项转正回调")
@RestController
@RequestMapping("projectPreviewCallBack")
public class ProjectPreviewCallBackController {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "审批中")
    @PutMapping("approvaling/skipSecurityInterceptor")
    public Response approvaling(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                                @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%sprojectPreviewCallBack/approvaling/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "审批通过")
    @PutMapping("approved/skipSecurityInterceptor")
    public Response approved(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                             @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                             @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                             @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%sprojectPreviewCallBack/approved/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "驳回")
    @PutMapping("refused/skipSecurityInterceptor")
    public Response refused(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                            @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                            @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                            @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%sprojectPreviewCallBack/refused/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "撤回")
    @PutMapping("return/skipSecurityInterceptor")
    public Response returned(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                             @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                             @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                             @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%sprojectPreviewCallBack/return/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "作废")
    @PutMapping("updateStatusAbandonForProjectPreview/skipSecurityInterceptor")
    public Response updateStatusAbandonForProjectPreview(@RequestParam(required = false) Long formInstanceId,
                                                         @RequestParam(required = false) String fdInstanceId,
                                                         @RequestParam(required = false) String formUrl,
                                                         @RequestParam(required = false) String eventName,
                                                         @RequestParam(required = false) String handlerId,
                                                         @RequestParam(required = false) Long companyId,
                                                         @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sprojectPreviewCallBack/updateStatusAbandonForProjectPreview/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "删除")
    @PutMapping("updateStatusDeleteForProjectPreview/skipSecurityInterceptor")
    public Response updateStatusDeleteForProjectPreview(@RequestParam(required = false) Long formInstanceId,
                                                        @RequestParam(required = false) String fdInstanceId,
                                                        @RequestParam(required = false) String formUrl,
                                                        @RequestParam(required = false) String eventName,
                                                        @RequestParam(required = false) String handlerId,
                                                        @RequestParam(required = false) Long companyId,
                                                        @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sprojectPreviewCallBack/updateStatusDeleteForProjectPreview/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "处理人通过")
    @PutMapping("agree/skipSecurityInterceptor")
    public Response agree(@RequestParam(required = false) Long formInstanceId,
                          @RequestParam(required = false) String fdInstanceId,
                          @RequestParam(required = false) String formUrl,
                          @RequestParam(required = false) String eventName,
                          @RequestParam(required = false) String handlerId,
                          @RequestParam(required = false) Long companyId,
                          @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sprojectPreviewCallBack/agree/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

}
