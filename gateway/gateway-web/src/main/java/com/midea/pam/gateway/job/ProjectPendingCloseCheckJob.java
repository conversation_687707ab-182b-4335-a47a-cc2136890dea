package com.midea.pam.gateway.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.ctc.web.ProjectCheckController;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
public class ProjectPendingCloseCheckJob {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private ProjectCheckController projectCheckController;


    @XxlJob("projectPendingCloseCheckJob")
    public ReturnT<String> execute(String s) throws Exception {
        logger.info("*************** 自动结项检查开始 ***************");
        //指定单位执行
        try {
            if (StringUtils.isNotBlank(s)) {
                Long companyId = Long.valueOf(s);
                check(companyId);
                return ReturnT.SUCCESS;
            }
        } catch (NumberFormatException e) {
            logger.error("自动结项检查定时执行失败", e);
            return ReturnT.FAIL;
        }

        //查询所有生效的单位
        final String url = String.format("%sunit/queryAllEnableUnit", ModelsEnum.BASEDATA.getBaseUrl());
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<Unit>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<Unit>>>() {
        });
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getData())) {
            logger.error("自动结项检查查询使用单位信息异常");
            return ReturnT.FAIL;
        }
        try {
            for (Unit unit : response.getData()) {
                Long companyId = unit.getId();
                //结项检查
                check(companyId);
            }
        } catch (Exception e) {
            logger.error("自动结项检查定时执行失败", e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }

    public void check(Long companyId) {
        //1.查询待结项列表
        ProjectDto query = new ProjectDto();
        query.setCompanyId(companyId);
        final String url = String.format("%sstatistics/projectPendingClose/getAutoCheckList", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, query, String.class);
        List<ProjectDto> list = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<ProjectDto>>>() {
        }).getData();

        //2.查询流程信息
        Map<String, Object> workflowInfo = projectCheckController.getWorkflowInfo(companyId);

        ProjectDto param = new ProjectDto();
        param.setCompanyId(companyId);
        param.setPendingCloseProjectList(list);
        param.setWorkflowInfo(workflowInfo);

        //3.批量结项检查
        final String checkUrl = String.format("%sproject/checkItemBatch", ModelsEnum.CTC.getBaseUrl());
        restTemplate.postForEntity(checkUrl, param, String.class);
    }
}
