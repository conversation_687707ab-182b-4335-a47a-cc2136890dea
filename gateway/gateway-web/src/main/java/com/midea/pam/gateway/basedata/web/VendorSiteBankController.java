package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.VendorSiteBankDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;


@Api("供应商主数据")
@RestController
@RequestMapping("vendorSiteBank")
public class VendorSiteBankController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "供应商主数据详情", response = VendorSiteBankDto.class)
    @GetMapping({"getById"})
    public Response view(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);

        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/vendorSiteBank/getById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<VendorSiteBankDto> response = JSON.parseObject(res, new TypeReference<DataResponse<VendorSiteBankDto>>() {
        });
        return response;
    }
}
