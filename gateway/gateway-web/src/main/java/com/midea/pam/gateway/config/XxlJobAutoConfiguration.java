package com.midea.pam.gateway.config;

import com.midea.pam.gateway.job.ProjectPendingCloseCheckJob;
import com.midea.pam.gateway.job.WorkFlowDraftSubmitTemporaryRecordJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class XxlJobAutoConfiguration {

    private static final Logger logger = LoggerFactory.getLogger(XxlJobAutoConfiguration.class);

    @Bean
    public WorkFlowDraftSubmitTemporaryRecordJob workFlowDraftSubmitTemporaryRecordJob() {
        return new WorkFlowDraftSubmitTemporaryRecordJob();
    }

    @Bean
    public ProjectPendingCloseCheckJob projectPendingCloseCheckJob() {
        return new ProjectPendingCloseCheckJob();
    }
}

