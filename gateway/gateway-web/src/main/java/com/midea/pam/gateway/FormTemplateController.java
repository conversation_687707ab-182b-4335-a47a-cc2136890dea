package com.midea.pam.gateway;

import com.github.pagehelper.PageInfo;
import com.midea.pam.common.gateway.dto.FormTemplateDTO;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.service.FormTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api("审批配置")
@RestController
@RequestMapping("formTemplate")
public class FormTemplateController {

    @Resource
    private FormTemplateService formTemplateService;


    @ApiOperation("列表")
    @PostMapping("page")
    public Response page(@RequestBody FormTemplateDTO formTemplateDTO) {
        DataResponse<PageInfo<FormTemplateDTO>> response = Response.dataResponse();
        return response.setData(formTemplateService.page(formTemplateDTO));
    }

    @ApiOperation("保存")
    @PostMapping("save")
    public Response save(@RequestBody FormTemplateDTO formTemplateDTO) {
        DataResponse<FormTemplateDTO> response = Response.dataResponse();
        return response.setData(formTemplateService.save(formTemplateDTO));
    }

    @ApiOperation("详情")
    @GetMapping("detail")
    public Response detail(@RequestParam Long id) {
        DataResponse<FormTemplateDTO> response = Response.dataResponse();
        return response.setData(formTemplateService.detail(id));
    }

    @ApiOperation("删除")
    @DeleteMapping("{id}")
    public Response deleteById(@PathVariable Long id) {
        DataResponse<Integer> response = Response.dataResponse();
        return response.setData(formTemplateService.deleteById(id));
    }
}
