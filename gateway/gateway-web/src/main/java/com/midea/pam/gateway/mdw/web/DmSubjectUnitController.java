package com.midea.pam.gateway.mdw.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.mdw.dto.DmSubjectDto;
import com.midea.pam.common.mdw.dto.DmSubjectUnitDto;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/02/07
 * @description 使用单位指标配置
 */
@Api("使用单位指标配置")
@RestController
@RequestMapping({"dm/subjectUnit"})
public class DmSubjectUnitController extends ControllerHelper {
    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "列表查询")
    @GetMapping("page")
    public Response page(@ApiParam("使用单位id") Long unitId,
                         @RequestParam(required = false) @ApiParam("指标code") String subjectCode,
                         @RequestParam(required = false) @ApiParam("指标名称") String subjectName,
                         @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") Integer pageSize) throws Exception {
        final Map<String, Object> params = new HashMap<>();
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        params.put("subjectCode", subjectCode);
        params.put("subjectName", subjectName);
        params.put("unitId", unitId);
        String url = buildGetUrl(ModelsEnum.MDW.getBaseUrl(), "/dm/subjectUnit/page", params);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<DmSubjectUnitDto>>>() {
        });
    }

    @ApiOperation(value = "按使用单位查询看板指标配置")
    @GetMapping("findByUnitId/{unitId}")
    public Response findByUnitId(@PathVariable Long unitId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("unitId", unitId);
        final String url = buildGetUrl(ModelsEnum.MDW.getBaseUrl(), "/dm/subjectUnit/findByUnitId", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<DmSubjectDto>>>() {
        });
    }

    @ApiOperation(value = "新增/修改看板指标配置")
    @PostMapping("save")
    public Response save(@RequestBody DmSubjectUnitDto dmSubjectUnitDto) {
        final String url = String.format("%sdm/subjectUnit/save", ModelsEnum.MDW.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dmSubjectUnitDto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }

}