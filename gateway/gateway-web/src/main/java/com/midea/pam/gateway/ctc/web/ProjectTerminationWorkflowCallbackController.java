package com.midea.pam.gateway.ctc.web;

import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * @program: common-module
 * @description: 项目终止流程回调
 * @author:zhongpeng
 * @create:2020-06-05 08:42
 **/
@Api("项目终止流程回调")
@RestController
@RequestMapping("projectTermination/workflow/callback")
public class ProjectTerminationWorkflowCallbackController {
    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "发起审批")
    @PutMapping("draftSubmit/skipSecurityInterceptor")
    public Response draftSubmitCallback(@RequestParam(required = false) Long formInstanceId,
                                        @RequestParam(required = false) String fdInstanceId,
                                        @RequestParam(required = false) String formUrl,
                                        @RequestParam(required = false) String eventName,
                                        @RequestParam(required = false) String handlerId,
                                        @RequestParam(required = false) Long companyId,
                                        @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sprojectTermination/workflow/callback/draftSubmit/skipSecurityInterceptor" +
                "?formInstanceId=%s" +
                "&fdInstanceId=%s" +
                "&formUrl=%s" +
                "&eventName=%s" +
                "&handlerId=%s" +
                "&companyId=%s" +
                "&createUserId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "驳回")
    @PutMapping("refuse/skipSecurityInterceptor")
    public Response refuseCallback(@RequestParam(required = false) Long formInstanceId,
                                   @RequestParam(required = false) String fdInstanceId,
                                   @RequestParam(required = false) String formUrl,
                                   @RequestParam(required = false) String eventName,
                                   @RequestParam(required = false) String handlerId,
                                   @RequestParam(required = false) Long companyId,
                                   @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sprojectTermination/workflow/callback/refuse/skipSecurityInterceptor" +
                "?formInstanceId=%s" +
                "&fdInstanceId=%s" +
                "&formUrl=%s" +
                "&eventName=%s" +
                "&handlerId=%s" +
                "&companyId=%s" +
                "&createUserId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "撤回")
    @PutMapping("draftReturn/skipSecurityInterceptor")
    public Response draftReturnCallback(@RequestParam(required = false) Long formInstanceId,
                                        @RequestParam(required = false) String fdInstanceId,
                                        @RequestParam(required = false) String formUrl,
                                        @RequestParam(required = false) String eventName,
                                        @RequestParam(required = false) String handlerId,
                                        @RequestParam(required = false) Long companyId,
                                        @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sprojectTermination/workflow/callback/draftReturn/skipSecurityInterceptor" +
                "?formInstanceId=%s" +
                "&fdInstanceId=%s" +
                "&formUrl=%s" +
                "&eventName=%s" +
                "&handlerId=%s" +
                "&companyId=%s" +
                "&createUserId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "通过")
    @PutMapping("pass/skipSecurityInterceptor")
    public Response passCallback(@RequestParam(required = false) Long formInstanceId,
                                 @RequestParam(required = false) String fdInstanceId,
                                 @RequestParam(required = false) String formUrl,
                                 @RequestParam(required = false) String eventName,
                                 @RequestParam(required = false) String handlerId,
                                 @RequestParam(required = false) Long companyId,
                                 @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sprojectTermination/workflow/callback/pass/skipSecurityInterceptor" +
                "?formInstanceId=%s" +
                "&fdInstanceId=%s" +
                "&formUrl=%s" +
                "&eventName=%s" +
                "&handlerId=%s" +
                "&companyId=%s" +
                "&createUserId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "废弃")
    @PutMapping("abandon/skipSecurityInterceptor")
    public Response abandon(@RequestParam(required = false) Long formInstanceId,
                            @RequestParam(required = false) String fdInstanceId,
                            @RequestParam(required = false) String formUrl,
                            @RequestParam(required = false) String eventName,
                            @RequestParam(required = false) String handlerId,
                            @RequestParam(required = false) Long companyId,
                            @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sprojectTermination/workflow/callback/abandon/skipSecurityInterceptor" +
                "?formInstanceId=%s" +
                "&fdInstanceId=%s" +
                "&formUrl=%s" +
                "&eventName=%s" +
                "&handlerId=%s" +
                "&companyId=%s" +
                "&createUserId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "删除")
    @PutMapping("delete/skipSecurityInterceptor")
    public Response delete(@RequestParam(required = false) Long formInstanceId,
                           @RequestParam(required = false) String fdInstanceId,
                           @RequestParam(required = false) String formUrl,
                           @RequestParam(required = false) String eventName,
                           @RequestParam(required = false) String handlerId,
                           @RequestParam(required = false) Long companyId,
                           @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sprojectTermination/workflow/callback/delete/skipSecurityInterceptor" +
                "?formInstanceId=%s" +
                "&fdInstanceId=%s" +
                "&formUrl=%s" +
                "&eventName=%s" +
                "&handlerId=%s" +
                "&companyId=%s" +
                "&createUserId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

}
