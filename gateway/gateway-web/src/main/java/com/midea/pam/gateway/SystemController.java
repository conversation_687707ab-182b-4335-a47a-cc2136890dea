package com.midea.pam.gateway;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.midea.mcomponent.core.util.Assert;
import com.midea.mcomponent.core.util.BeanUtils;
import com.midea.pam.cache.ValueCache;
import com.midea.pam.common.basedata.dto.FistUnitOu;
import com.midea.pam.common.basedata.dto.OperatingUnitDto;
import com.midea.pam.common.basedata.dto.UserInfoDto;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.basedata.entity.OrgUnit;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.gateway.entity.CurrentContext;
import com.midea.pam.common.gateway.entity.CurrentContextExample;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.CacheKey;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.Symbol;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.common.utils.PamCurrentUserUtil;
import com.midea.pam.gateway.service.CurrentContextService;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("系统公共类")
@RestController
@RequestMapping(value = {"system", "mobile/app/system"})
public class SystemController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private CurrentContextService currentContextService;

    @Resource
    private ValueCache valueCache;

    @ApiOperation("查询并选择当前用户的单位")
    @GetMapping("unitList")
    public Response unitList() {
        final Map<String, Object> param = new HashMap<>();
        String loginUserName = PamCurrentUserUtil.getCurrentUserName();
        final UserInfo userinfo = CacheDataUtils.findUserByMip(loginUserName);
        if (userinfo == null) {
            return new DataResponse<List<OrgUnit>>();
        }
        param.put("userId", userinfo.getId());
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/orgUnit/selectUserTopUnit", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<List<OrgUnit>> data = JSON.parseObject(res, new TypeReference<DataResponse<List<OrgUnit>>>() {
        });
//        Assert.isTrue(ListUtils.isNotEmpty(data.getData()), "账号未授权,请联系系统管理员！");
        if (ListUtils.isNotEmpty(data.getData()) && data.getData().size() == 1) {
            setCurrentContext(data.getData().get(0));
        }
        return data;
    }

    /**
     * 设置当前登录部门
     *
     * @param unit 部门
     * @return 记录id
     */
    private int setCurrentContext(final OrgUnit unit) {
        String loginUserName = PamCurrentUserUtil.getCurrentUserName();
        final CurrentContextExample condition = new CurrentContextExample();
        condition.createCriteria().andLoginUserNameEqualTo(loginUserName);
        final List<CurrentContext> contexts = currentContextService.selectByExample(condition);
        final UserInfo userInfo = CacheDataUtils.findUserByMip(loginUserName);

        //用户当前一级单位下所有ou
        String ouStr = "";
        final List<FistUnitOu> fistUnitOu = getFirstUnitOu(userInfo.getId(), unit.getId());
        for (FistUnitOu unitOu : fistUnitOu) {
            ouStr = ouStr + unitOu.getId() + ",";
        }
        if (ouStr.length() > 0) {
            ouStr = ouStr.substring(0, ouStr.length() - 1);
        }
        //用户当前一级单位下所有二级单位(虚拟部门)
        String secondUnitStr = "";
        final List<OrgUnit> OrgUnitList = getSecondUnit(userInfo.getId(), unit.getId());
        for (OrgUnit orgUnit : OrgUnitList) {
            secondUnitStr = secondUnitStr + orgUnit.getId() + ",";
        }
        if (secondUnitStr.length() > 0) {
            secondUnitStr = secondUnitStr.substring(0, secondUnitStr.length() - 1);
        }

        UserInfoDto currentUserInfo = SystemContext.get();
        if (currentUserInfo != null) {
            //ou上下文
            final List<Long> ouList = new ArrayList<>();
            if (com.midea.pam.common.util.StringUtils.isNotEmpty(ouStr)) {
                final List<String> ous = Arrays.asList(ouStr.split(Symbol.COMMA));
                ous.forEach(ou -> {
                    ouList.add(Long.parseLong(ou));
                });
            }

            //虚拟部门上下文
            final List<Long> secondUnitIdList = new ArrayList<>();
            if (com.midea.pam.common.util.StringUtils.isNotEmpty(secondUnitStr)) {
                final List<String> suIds = Arrays.asList(secondUnitStr.split(Symbol.COMMA));
                suIds.forEach(suId -> {
                    secondUnitIdList.add(Long.parseLong(suId));
                });
            }
            currentUserInfo.setOus(ouList);
            currentUserInfo.setSecondUnitIds(secondUnitIdList);
        }

        if (ListUtils.isEmpty(contexts)) {
            final CurrentContext context = new CurrentContext();
            context.setLoginUserName(loginUserName);
            context.setCurrentOrgId(unit.getId());
            context.setCurrentOrgName(unit.getUnitName());
            context.setOuIds(ouStr);
            context.setSecondUnitIds(secondUnitStr);
            valueCache.set(buildKey(Constants.Prefix.SYSTEM_CONTEXT + loginUserName), context);
            return currentContextService.insert(context);
        } else {
            final CurrentContext context = contexts.get(0);
            context.setCurrentOrgId(unit.getId());
            context.setCurrentOrgName(unit.getUnitName());
            context.setOuIds(ouStr);
            context.setSecondUnitIds(secondUnitStr);
            valueCache.set(buildKey(Constants.Prefix.SYSTEM_CONTEXT + loginUserName), context);
            return currentContextService.updateByPrimaryKeySelective(context);
        }
    }


    @ApiOperation("选择单位")
    @PutMapping("selelctUnit")
    public Response selectUnit(@RequestBody OrgUnit unit) {
        final DataResponse<Integer> response = Response.dataResponse();
        return response.setData(setCurrentContext(unit));
    }


    @ApiOperation("查询当前系统用户的二级(虚拟)部门")
    @GetMapping("selectSecondtUnit")
    public Response selectSecondtUnit() {
        String loginUserName = PamCurrentUserUtil.getCurrentUserName();
        final CurrentContextExample condition = new CurrentContextExample();
        condition.createCriteria().andLoginUserNameEqualTo(loginUserName);
        final List<CurrentContext> contexts = currentContextService.selectByExample(condition);
        Assert.notEmpty(contexts, "请先选择单位!");
        final Map<String, Object> param = new HashMap<>();
        param.put("userId", CacheDataUtils.findUserByMip(loginUserName).getId());
        param.put("unitId", contexts.get(0).getCurrentOrgId());
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/orgUnit/selectUserSecondUnit", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<OrgUnit>>>() {
        });
    }

    @ApiOperation("查询当前用户的一级单位")
    @GetMapping("currentUnits")
    public Response currentUnits() {
        final Map<String, Object> param = new HashMap<>();
        String loginUserName = PamCurrentUserUtil.getCurrentUserName();
        final UserInfo userinfo = CacheDataUtils.findUserByMip(loginUserName);
        param.put("userId", userinfo.getId());
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/orgUnit/selectUserTopUnit", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<OrgUnit>>>() {
        });
    }

    @ApiOperation("当前登录用户")
    @GetMapping("currentUser")
    public Response currentUser() {
        String loginUserName = PamCurrentUserUtil.getCurrentUserName();
        final UserInfo userinfo = CacheDataUtils.findUserByMip(loginUserName);
        if (userinfo == null) {
            return new DataResponse<UserInfo>().setData(PamCurrentUserUtil.getCurrentUser());
        }
        return new DataResponse<UserInfo>().setData(userinfo);
    }

    @ApiOperation("当前登录用户")
    @GetMapping("currentUserWithUnitList")
    public Response currentUserWithUnitList() {
        String loginUserName = PamCurrentUserUtil.getCurrentUserName();
        final UserInfo userinfo = CacheDataUtils.findUserByMip(loginUserName);
        UserInfoDto userInfoDto = null;
        if (userinfo == null) {
            userInfoDto = SystemContext.get();
        } else {
            userInfoDto = new UserInfoDto();
            BeanUtils.copyProperties(userinfo, userInfoDto);
        }

        final Map<String, Object> param = new HashMap<>();
        param.put("userId", userInfoDto.getId());
        final String unitUrl = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/orgUnit/selectUserTopUnit", param);
        final String res = restTemplate.getForEntity(unitUrl, String.class).getBody();
        final DataResponse<List<OrgUnit>> data = JSON.parseObject(res, new TypeReference<DataResponse<List<OrgUnit>>>() {
        });
        List<OrgUnit> orgUnits = data.getData();
//        Assert.isTrue(ListUtils.isNotEmpty(orgUnits), "账号未授权,请联系系统管理员！");
        if (orgUnits != null && data.getData().size() == 1) {
            setCurrentContext(data.getData().get(0));
            userInfoDto.setOrgUnits(orgUnits);
        }

        return new DataResponse<UserInfoDto>().setData(userInfoDto);
    }

    @ApiOperation(value = "查询用户的所有单位的ou.")
    @GetMapping("selectUserOuList")
    public Response selectUserOuList(@RequestParam(required = false) Long userId, @RequestParam(required = false) Long unitId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("userId", userId);
        param.put("unitId", unitId);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/orgUnit/selectUserOuList", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<OperatingUnitDto>>>() {
        });
    }


    @ApiOperation(value = "查询用户的当前单位的ou.")
    @GetMapping("selectCurrentFirstUnitOu")
    public Response selectAtPresentFirstUnitO() {
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/orgUnit/selectCurrentFirstUnitOu", null);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<OperatingUnit>>>() {
        });
    }

    @ApiOperation(value = "用户一级单位下的ou")
    @GetMapping("selectFirstUnitOu")
    public Response selectFistUnitOu(@RequestParam(required = false) Long userId, @RequestParam(required = true) Long firstUnitId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("userId", userId);
        param.put("firstUnitId", firstUnitId); //firstUnitId
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/orgUnit/selectFirstUnitOu", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<FistUnitOu>>>() {
        });
    }

    private List<FistUnitOu> getFirstUnitOu(Long userId, Long firstUnitId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("userId", userId);
        param.put("firstUnitId", firstUnitId); //firstUnitId
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/orgUnit/selectFirstUnitOu", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<List<FistUnitOu>> listDataResponse = JSON.parseObject(res, new TypeReference<DataResponse<List<FistUnitOu>>>() {
        });
        if (listDataResponse != null && ListUtils.isNotEmpty(listDataResponse.getData())) {
            return listDataResponse.getData();
        }
        return Lists.newArrayList();
    }

    /**
     * 二级单位
     *
     * @param userId
     * @param firstUnitId
     * @return
     */
    private List<OrgUnit> getSecondUnit(Long userId, Long firstUnitId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("userId", userId);
        param.put("unitId", firstUnitId);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/orgUnit/selectUserSecondUnit", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<List<OrgUnit>> listDataResponse = JSON.parseObject(res, new TypeReference<DataResponse<List<OrgUnit>>>() {
        });
        if (listDataResponse != null && ListUtils.isNotEmpty(listDataResponse.getData())) {
            return listDataResponse.getData();
        }
        return Lists.newArrayList();
    }

    private String buildKey(final String key) {
        return CacheKey.getNamespace() + ":" + key;
    }

    @ApiOperation(value = "获取当前用户")
    @GetMapping("findCurrUser")
    public JSONObject findCurrUser() {
        JSONObject response = new JSONObject();

        String loginUserName = PamCurrentUserUtil.getCurrentUserName();
        if (StringUtils.isBlank(loginUserName)) {
            response.put("success", false);
        } else {
            response.put("success", true);

            JSONObject data = new JSONObject();
            data.put("code", "200");

            response.put("data", data);

            JSONObject user = new JSONObject();
            user.put("id", "200");
            user.put("name", loginUserName);
            user.put("loginName", loginUserName);

            data.put("user", user);
        }

        return response;
    }

    @ApiOperation(value = "查询PAM下生效的OU")
    @GetMapping("findByAllValid")
    public Response findByAllValid() {
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/operatingUnit/findByAllValid", null);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<OperatingUnitDto>>>() {
        });
    }

}
