package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.UnitDto;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: pam
 * @description: 虚拟部门 网关控制层
 * @author: fangyl
 * @create: 2019-4-10
 **/
@RestController
@RequestMapping("unit")
@Api("虚拟部门")
public class UnitController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查询虚拟部门")
    @GetMapping("list")
    public Response list(@RequestParam(required = false) @ApiParam(value = "id") String id,
                         @RequestParam(required = false) @ApiParam(value = "父节点id") String parentId,
                         @RequestParam(required = false) @ApiParam(value = "业务分类") String unitName,
                         @RequestParam(required = false) @ApiParam(value = "是否父节点(0:是 1:否)") String parentFlag) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("parentId", parentId);
        param.put("unitName", unitName);
        param.put("parentFlag", parentFlag);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "unit/list", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<UnitDto>>>() {
        });
    }

    @ApiOperation(value = "分页查询当前使用单位下的虚拟部门")
    @GetMapping("page")
    public Response page(@RequestParam(required = false) String unitName,
                         @RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put("unitName", unitName);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "unit/page", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<PageInfo<UnitDto>>>>() {
        });
    }

    @ApiOperation(value = "查询有效的二级虚拟部门")
    @GetMapping("queryAvailableList")
    public Response queryAvailableList(@RequestParam(required = false) @ApiParam(value = "id") String id,
                                       @RequestParam(required = false) @ApiParam(value = "父节点id") String parentId,
                                       @RequestParam(required = false) @ApiParam(value = "组织分类") String orgType) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("parentId", parentId);
        param.put("orgType", orgType);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "unit/queryAvailableList", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<UnitDto>>>() {
        });
    }

    @ApiOperation(value = "根据用户的虚拟部门id与编码查询二级虚拟部门")
    @GetMapping("getSecondUnit")
    public Response getSecondUnit(@RequestParam(required = false) @ApiParam(value = "areaCode") String areaCode) {
        final Map<String, Object> param = new HashMap<>();
        param.put("areaCode", areaCode);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "unit/getSecondUnit", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<UnitDto>>() {
        });
    }

    @ApiOperation(value = "根据ou获取当前使用单位下所有的销售部门/业务分类")
    @GetMapping("selectUnitByOu")
    public Response selectUnitByOu(@RequestParam Long ouId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("ouId", ouId);
        param.put("parentUnitId", SystemContext.getUnitId());
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "unit/selectUnitByOu", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<Unit>>>() {
        });
    }

    @ApiOperation(value = "查询虚拟部门业务实体和预算部门")
    @GetMapping("getUnitOuBudgetDep")
    public Response getUnitOuBudgetDep(@RequestParam(required = false) @ApiParam(value = "id") String id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "unit/getUnitOuBudgetDep", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<UnitDto>>() {
        });
    }

    @ApiOperation(value = "新增虚拟部门")
    @PostMapping("add")
    public Response add(@RequestBody UnitDto unitDto) {
        final String url = String.format("%sunit/add", ModelsEnum.BASEDATA.getBaseUrl());
        String res = restTemplate.postForEntity(url, unitDto, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Long>>() {
        });
    }

    @ApiOperation(value = "更新虚拟部门")
    @PostMapping("update")
    public Response update(@RequestBody UnitDto unitDto) {
        final String url = String.format("%sunit/update", ModelsEnum.BASEDATA.getBaseUrl());
        String res = restTemplate.postForEntity(url, unitDto, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Long>>() {
        });
    }


    @ApiOperation(value = "根据虚拟单位ID获取单据前缀(美云:M，机器人:R)")
    @GetMapping("getUnitSeqPerfix")
    public Response getUnitSeqPerfix(@RequestParam(required = false) @ApiParam(value = "id") Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "unit/getUnitSeqPerfix", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "查询用户初始化数据权限列表")
    @GetMapping("queryInitUnit")
    public Response queryInitUnit() {
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "unit/queryInitUnit", null);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<UnitDto>>>() {
        });
    }

    @ApiOperation(value = "获取所有使用单位")
    @GetMapping("queryAllUnit")
    public Response queryAllUnit() {
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "unit/queryAllUnit", null);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<Unit>>>() {
        });
    }
}
