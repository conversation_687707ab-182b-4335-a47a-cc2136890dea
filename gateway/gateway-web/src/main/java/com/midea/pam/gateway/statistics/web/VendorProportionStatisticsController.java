package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.VendorProportionCtcDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;


@RestController
@RequestMapping("statistics/vendorProportionCtc")
@Api("供货比例")
public class VendorProportionStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "查询供货比例列表")
    @GetMapping("selectPage")
    public Response selectPage(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                               @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize,
                               @RequestParam(required = false) @ApiParam(value = "库存组织") String org,
                               @RequestParam(required = false) @ApiParam(value = "物料编码") String materialCode) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("org", org);
        param.put("materialCode", materialCode);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/vendorProportionCtc/selectPage", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<VendorProportionCtcDto>>>() {
        });
    }

}
