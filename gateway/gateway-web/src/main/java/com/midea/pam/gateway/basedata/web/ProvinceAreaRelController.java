package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.ProvinceAreaRelDto;
import com.midea.pam.common.basedata.entity.AreaUnitRel;
import com.midea.pam.common.basedata.entity.ProvinceAreaRel;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("省市与区域关系")
@RestController
@RequestMapping("provinceAreaRel")
public class ProvinceAreaRelController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation("根据省编码查询")
    @GetMapping("findByAreaCode")
    public Response findByAreaCode(@RequestParam(required = false) String areaCode) throws Exception{
        final Map<String, Object> param = new HashMap<>();
        param.put("areaCode", areaCode);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/provinceAreaRel/findByAreaCode", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<ProvinceAreaRelDto>>>(){});
    }
}
