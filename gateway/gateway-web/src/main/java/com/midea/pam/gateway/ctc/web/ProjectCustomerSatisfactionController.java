package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.ProjectCustomerSatisfactionDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * @PackageClassName: com.midea.pam.gateway.ctc.web.ProjectCustomerSatisfactionController
 * @Description: 客户满意度
 * @Author: JerryH
 * @Date: 2023-02-02, 0002 下午 02:27:16
 */
@Api("客户满意度")
@RestController
@RequestMapping("ctc/projectCustomerSatisfaction")
public class ProjectCustomerSatisfactionController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "保存客户满意度")
    @PostMapping("save")
    public Response save(@RequestBody ProjectCustomerSatisfactionDto projectCustomerSatisfactionDto){
        final String url = String.format("%sprojectCustomerSatisfaction/save", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, projectCustomerSatisfactionDto, DataResponse.class).getBody();
    }

    @ApiOperation(value = "根据id查找客户满意度详情")
    @GetMapping("findById/{id}")
    public Response findById(@PathVariable("id") Long id) {
        final String url = String.format("%sprojectCustomerSatisfaction/findById/%s", ModelsEnum.CTC.getBaseUrl(), id);
        String forEntity = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(forEntity, new TypeReference<DataResponse<ProjectCustomerSatisfactionDto>>() {
        });
    }
}
