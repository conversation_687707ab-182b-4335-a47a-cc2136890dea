package com.midea.pam.gateway.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "PurchaserMaterialRequest", description = "采购物料需求请求对象")
public class PurchaserMaterialRequest {
    @ApiModelProperty("页码")
    Integer pageNum;
    @ApiModelProperty("每页记录数")
    Integer pageSize;
    @ApiModelProperty("模糊:ERP物料编码")
    String fuzzyErpCode;
    @ApiModelProperty("模糊:物料描述")
    String fuzzyMaterielDescr;
    @ApiModelProperty("模糊:PAM物料编码")
    String fuzzyPamCode;
    @ApiModelProperty("模糊:项目名称")
    String fuzzyProjectName;
    @ApiModelProperty("模糊:项目编号")
    String fuzzyProjectNum;
    @ApiModelProperty("模糊:WBS")
    String wbsSummaryCode;
    @ApiModelProperty("业务实体")
    String projectOuId;
    @ApiModelProperty("状态(0：待采购，1：已关闭)")
    String statusStr;
    @ApiModelProperty("交货开始日期")
    String deliveryStartTime;
    @ApiModelProperty("交货结束日期")
    String deliveryEndTime;
    @ApiModelProperty("发布开始日期")
    String publishStartTime;
    @ApiModelProperty("发布结束日期")
    String publishEndTime;
    @ApiModelProperty("更新开始日期")
    String updateStartDate;
    @ApiModelProperty("更新结束日期")
    String updateEndDate;
    @ApiModelProperty("采购类型(1：默认、2：外购物料、3：wbs)")
    Integer purchaseType;
    @ApiModelProperty("模糊:需求单号")
    String requirementCode;
    @ApiModelProperty("模糊:设计发布批次号")
    String designReleaseLotNumber;
    @ApiModelProperty("模糊:活动事项编码")
    String activityCode;
    @ApiModelProperty("是否急件")
    Boolean dispatchIs;
    @ApiModelProperty("图号/型号")
    String model;
    @ApiModelProperty("品牌")
    String brand;
    @ApiModelProperty("图纸版本号")
    String chartVersion;
    @ApiModelProperty("物料中类")
    String codingMiddleclass;
    @ApiModelProperty("物料小类")
    String materialType;
    @ApiModelProperty("需求分类")
    String requirementTypeStr;
}