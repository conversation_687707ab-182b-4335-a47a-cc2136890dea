package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.VendorPenaltyConfigDto;
import com.midea.pam.common.ctc.excelVo.VendorPenaltyConfigExcelVo;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.utils.ExportExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("vendorPenaltyConfig")
@Api("供应商罚扣成本配置")
public class VendorPenaltyConfigController {
    @Resource
    private RestTemplate restTemplate;

    @ApiOperation("保存或更新")
    @PostMapping("saveOrUpdate")
    public Response saveOrUpdate(@RequestBody List<VendorPenaltyConfigDto> configDtoList){
        String url = ModelsEnum.CTC.getBaseUrl() + "vendorPenaltyConfig/saveOrUpdate";
        return restTemplate.postForObject(url,configDtoList, DataResponse.class);
    }

    @ApiOperation("查询当前单位下的罚扣成本配置")
    @GetMapping("list")
    public Response list(VendorPenaltyConfigDto dto){
        return restTemplate.getForObject(formListUrl(dto), DataResponse.class);
    }

    @ApiOperation("导出当前单位下的罚扣成本配置")
    @GetMapping("export")
    public void export(VendorPenaltyConfigDto dto, HttpServletResponse httpServletResponse){
        String res = restTemplate.getForObject(formListUrl(dto), String.class);
        DataResponse<List<VendorPenaltyConfigExcelVo>> dataResponse =
                JSON.parseObject(res, new TypeReference<DataResponse<List<VendorPenaltyConfigExcelVo>>>() {});
        if(dataResponse==null){
            throw new BizException(Code.ERROR,res);
        }
        if(dataResponse.getCode()!=0){
            throw new BizException(Code.ERROR,dataResponse.getMsg());
        }
        if(ListUtils.isNotEmpty(dataResponse.getData())) {
            int index = 1;
            for (VendorPenaltyConfigExcelVo vo : dataResponse.getData()) {
                vo.setIndex(index++);
            }
        }
        ExportExcelUtil.exportExcel(dataResponse.getData(), null, "Sheet1",
                VendorPenaltyConfigExcelVo.class, "采购订单罚扣配置excel导出.xls", httpServletResponse);
    }

    private String formListUrl(VendorPenaltyConfigDto dto){
        String url = ModelsEnum.CTC.getBaseUrl() + "vendorPenaltyConfig/list";
        Map<String,String> params = new HashMap<>(3);
        params.put("penaltyType",dto.getPenaltyType());
        params.put("projectActivityCode",dto.getProjectActivityCode());
        params.put("wbsCode",dto.getWbsCode());
        return buildUrl(url, params);
    }

    private String buildUrl(String url, Map<String,String> params){
        String queryParams = params.entrySet().stream()
                .filter(e -> StringUtils.isNotEmpty(e.getValue()))
                .map(e -> e.getKey() + "=" + e.getValue())
                .collect(Collectors.joining("&"));
        if(!queryParams.isEmpty()){
            url += "?" + queryParams;
        }
        return url;
    }
}
