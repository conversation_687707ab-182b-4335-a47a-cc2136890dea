package com.midea.pam.gateway.ctc.web;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.ProjectBaselineBatchDto;
import com.midea.pam.common.ctc.entity.ProjectBaselineBatch;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

@Api("wbs预算基线批次")
@RestController
@RequestMapping("projectBaselineBatch")
public class ProjectBaselineBatchController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查询wbs基线批次列表")
    @PostMapping("list")
    public Response list(@RequestBody ProjectBaselineBatchDto param) {
        final String url = String.format("%sprojectBaselineBatch/list", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, param, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<ProjectBaselineBatch>>>() {
        });
    }

    @ApiOperation(value = "删除wbs基线批次")
    @PostMapping("deleteById/{id}")
    public Response deleteById(@PathVariable("id") Long id) {
        final String url = String.format("%sprojectBaselineBatch/deleteById/%s", ModelsEnum.CTC.getBaseUrl(), id);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, new HashMap<>(), String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "保存wbs基线批次")
    @PostMapping("save")
    public Response save(@RequestBody ProjectBaselineBatchDto data) {
        final String url = String.format("%sprojectBaselineBatch/save", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, data, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Long>>() {
        });
    }
}
