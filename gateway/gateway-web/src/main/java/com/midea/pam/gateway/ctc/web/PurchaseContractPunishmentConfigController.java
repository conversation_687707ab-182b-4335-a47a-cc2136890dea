package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.MaterialCostTransferDetailDto;
import com.midea.pam.common.ctc.dto.PurchaseContractPunishmentConfigDto;
import com.midea.pam.common.ctc.dto.PurchaseContractPunishmentDto;
import com.midea.pam.common.ctc.vo.MaterialCostTransferDetailsExcelVO;
import com.midea.pam.common.ctc.vo.PurchaseContractPunishmentConfigExcelVO;
import com.midea.pam.common.ctc.vo.PurchaseContractPunishmentVo;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 采购合同罚扣配置控制层
 */
@Api("采购合同罚扣配置")
@RestController
@RequestMapping("purchaseContractPunishmentConfig")
public class PurchaseContractPunishmentConfigController extends ControllerHelper {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation("合同罚扣配置保存或更新")
    @RequestMapping("saveOrUpdate")
    public Response saveOrUpdate(@RequestBody List<PurchaseContractPunishmentConfigDto> dtoList) {
        final String url = String.format("%spurchaseContractPunishmentConfig/saveOrUpdate", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dtoList, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation("合同罚扣配置列表")
    @PostMapping("list")
    public Response list(@RequestBody(required = false) PurchaseContractPunishmentConfigDto dto) {
        final String url = String.format("%spurchaseContractPunishmentConfig/list", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<PurchaseContractPunishmentConfigDto>>>() {
        });
    }

    @ApiOperation(value = "合同罚扣配置导出")
    @PostMapping("export")
    public void export(HttpServletResponse response, @RequestBody PurchaseContractPunishmentConfigDto dto) {
        final String url = String.format("%spurchaseContractPunishmentConfig/list", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<List<PurchaseContractPunishmentConfigDto>> configListData = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<PurchaseContractPunishmentConfigDto>>>() {
        });
        List<PurchaseContractPunishmentConfigDto> data = configListData.getData();
        logger.info("合同罚扣配置导出-查询的数据量为:{}", data.size());
        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("采购合同罚扣配置excel导出.xls");
        List<PurchaseContractPunishmentConfigExcelVO> excelVOS = new ArrayList<>();
        try {
            for (int num = 0; num < data.size(); num++) {
                PurchaseContractPunishmentConfigDto config = data.get(num);
                PurchaseContractPunishmentConfigExcelVO excelVO = new PurchaseContractPunishmentConfigExcelVO();
                excelVO.setNum(num + 1); // 直接使用 num + 1 作为序号
                excelVO.setPunishmentType(config.getPunishmentType());
                excelVO.setAllowManualCreation(config.getAllowManualCreation() != null && config.getAllowManualCreation() == 1 ? "是" : "否");
                excelVO.setAccountingSubject(config.getAccountingSubject());
                excelVO.setCreator(config.getCreator());
                excelVO.setUpdater(config.getUpdater());
                //日期格式YYYY-MM-DD
                excelVO.setUpdateAt(DateUtils.formatDate(config.getUpdateAt()));
                excelVOS.add(excelVO);
            }
        } catch (Exception e) {
            logger.error("导出合同罚扣配置时发生错误: {}", e.getMessage(), e);
            throw new BizException(Code.ERROR,"导出合同罚扣配置失败，具体错误信息请查看日志。");
        }
        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(excelVOS, PurchaseContractPunishmentConfigExcelVO.class, null, "采购合同罚扣配置", true);
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }


}
