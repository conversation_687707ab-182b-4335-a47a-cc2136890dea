package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.statistics.dto.PortalTargetDto;
import com.midea.pam.common.statistics.dto.PortalTodoBusinessDto;
import com.midea.pam.common.statistics.dto.PortalWarningDTO;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.utils.PamCurrentUserUtil;
import com.midea.pam.gateway.service.MipWorkflowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Api("个人门户")
@RestController
@RequestMapping("personalPortal")
public class PersonalPortalController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private MipWorkflowService mipWorkflowService;
    @ApiOperation(value = "统计待办事项")
    @GetMapping("getTodoBusiness")
    public Response getTodoBusiness() {
        final Map<String, Object> param = new HashMap<>();
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/personalPortal/getTodoBusiness", param);
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(600 * 1000);
        httpRequestFactory.setConnectTimeout(600 * 1000);
        httpRequestFactory.setReadTimeout(600 * 1000);
        restTemplate.setRequestFactory(httpRequestFactory);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<PortalTodoBusinessDto>> response = JSON.parseObject(cleanStr(res), new TypeReference<DataResponse<List<PortalTodoBusinessDto>>>(){});
        List<PortalTodoBusinessDto> dtos = new ArrayList<>();
        if(response != null){
            List<PortalTodoBusinessDto> data = response.getData();
            if(CollectionUtils.isNotEmpty(data)){
                for(PortalTodoBusinessDto portalTodoBusinessDto : data){
                    //如果是待审批工作流
                    if(Objects.equals(portalTodoBusinessDto.getTargetName(), "待处理流程")){
                        portalTodoBusinessDto.setCount(getMyRunningProcess());
                    }
                }
                //过滤
                data.stream().forEach(e -> {
                    String count = String.valueOf(e.getCount());
                    if(!count.equals("0")){
                        dtos.add(e);
                    }
                });
            }
            response.setData(dtos);
        }
        return response;
    }

    private Long getMyRunningProcess() {
        String loginUserName = PamCurrentUserUtil.getCurrentUserName();
        JSONObject params = new JSONObject();
        params.put("page", 1);
        params.put("pageSize", 10);
        JSONObject res = mipWorkflowService.getMyRunningProcess(loginUserName, params);
        if(res != null){
            JSONObject body = res.getJSONObject("body");
            if(body != null && body.getJSONObject("data") != null){
                return body.getJSONObject("data").getLong("rowCount") != null ?
                        body.getJSONObject("data").getLong("rowCount") : 0L;
            }
        }
        return 0L;
    }

    @ApiOperation(value = "统计系统预警")
    @GetMapping("getNoticeBusiness")
    public Response getNoticeBusiness(@RequestParam(required = false) @ApiParam("模糊条件") final String fuzzyLike) {
        final Map<String, Object> param = new HashMap<>();
        param.put("fuzzyLike", fuzzyLike);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/personalPortal/getNoticeBusiness", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<PortalWarningDTO>>>() {
        });
    }

    @ApiOperation(value = "查询用户的门户指标")
    @GetMapping("getPersonalPortalTarget")
    public Response getPersonalPortalTarget() {
        final Map<String, Object> param = new HashMap<>();
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/personalPortal/getPersonalPortalTarget", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<PortalTargetDto>>>() {
        });
    }

    @ApiOperation(value = "工时日历")
    @GetMapping("getWorkingHourCalendar")
    public com.midea.pam.gateway.common.base.Response getWorkingHourCalendar() {
        final Map<String, Object> param = new HashMap<>();
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/personalPortal/getWorkingHourCalendar", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        com.midea.pam.gateway.common.base.DataResponse<JSONArray> response = com.midea.pam.gateway.common.base.Response.dataResponse();
        return response.setData(JSONArray.parseArray(res));
    }

    @ApiOperation(value = "汇总工时")
    @GetMapping("countWorkinghour")
    public com.midea.pam.gateway.common.base.Response countWorkinghour() {
        final Map<String, Object> param = new HashMap<>();
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/personalPortal/countWorkinghour", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        com.midea.pam.gateway.common.base.DataResponse<JSONArray> response = com.midea.pam.gateway.common.base.Response.dataResponse();
        return response.setData(JSONArray.parseArray(res));
    }

}
