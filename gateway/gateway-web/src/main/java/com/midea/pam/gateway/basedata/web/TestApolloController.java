package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;

/**
 * Description pass平台配置测试
 * Created by liuqing
 * Date 2022/4/21 16:27
 */
@RestController
@RequestMapping("testApollo")
@Api("测试Apollo配置")
public class TestApolloController extends ControllerHelper {

    @Autowired
    private RestTemplate restTemplate;

    @GetMapping("keyset")
    public Response keyset() {
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "testApollo/keyset", new HashMap<>());
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
    }

    @GetMapping("employeeFilter")
    public Response employeeFilter() {
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "testApollo/employeeFilter", new HashMap<>());
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
    }

    @GetMapping("companyFilterOrgMap")
    public Response companyFilterOrgMap() {
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "testApollo/companyFilterOrgMap", new HashMap<>());
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
    }
}
