package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.BusiSceneNonSaleDetailDto;
import com.midea.pam.common.ctc.dto.BusiSceneNonSaleDto;
import com.midea.pam.common.ctc.entity.BusiSceneNonSale;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: pam
 * @description: 非销售业务场景配置 网关控制层
 * @author: fangyl
 * @create: 2019-4-18
 **/
@RestController
@RequestMapping("busiSceneNonSale")
@Api("非销售业务场景配置")
public class BusiSceneNonSaleController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "分页查询非销售业务场景配置列表")
    @GetMapping("listPage")
    public Response listPage(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") String pageNum,
                             @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") String pageSize,
                             @RequestParam(required = false) @ApiParam(value = "id") String id,
                             @RequestParam(required = false) @ApiParam(value = "业务场景") String busiSceneName,
                             @RequestParam(required = false) @ApiParam(value = "业务实体") Long ouId) {
        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        param.put("id", id);
        param.put("busiSceneName", busiSceneName);
        param.put("ouId", ouId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "busiSceneNonSale/listPage", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<BusiSceneNonSale>>>() {
        });
    }

    @ApiOperation(value = "查询非销售业务场景配置列表")
    @GetMapping("list")
    public Response list(@RequestParam(required = false) @ApiParam(value = "id") String id,
                         @RequestParam(required = false) @ApiParam(value = "业务场景") String busiSceneName,
                         @RequestParam(required = false) @ApiParam(value = "业务实体") Long ouId,
                         @RequestParam(required = false) @ApiParam(value = "类型") Integer type) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("busiSceneName", busiSceneName);
        param.put("ouId", ouId);
        param.put("type", type);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "busiSceneNonSale/list", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<BusiSceneNonSaleDto>>>() {
        });
    }

    @ApiOperation(value = "分页查询非销售业务场景配置详细")
    @GetMapping("detailPage")
    public Response detailPage(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") String pageNum,
                               @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") String pageSize,
                               @RequestParam(required = false) @ApiParam(value = "id") String id,
                               @RequestParam(required = false) @ApiParam(value = "非销售业务场景配置列表ID") String busiSceneNonSaleId) {
        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        param.put("id", id);
        param.put("busiSceneNonSaleId", busiSceneNonSaleId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "busiSceneNonSale/detailPage", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<BusiSceneNonSaleDetailDto>>>() {
        });
    }

    @ApiOperation(value = "删除非销售业务场景配置")
    @GetMapping("delete")
    public Response delete(@RequestParam @ApiParam(value = "非销售业务场景配置列表ID") Long busiSceneNonSaleId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("busiSceneNonSaleId", busiSceneNonSaleId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "busiSceneNonSale/delete", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation(value = "保存非销售业务场景配置")
    @PostMapping("save")
    public Response save(@RequestBody BusiSceneNonSaleDto busiSceneNonSaleDto) {
        final String url = String.format("%sbusiSceneNonSale/save", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, busiSceneNonSaleDto, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Long>>() {
        });
    }

}
