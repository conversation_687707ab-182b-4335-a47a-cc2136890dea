package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.ReceivableTrxDto;
import com.midea.pam.common.basedata.query.ReceiptMethodQuery;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.PageResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@RestController
@Api("应收款活动")
@RequestMapping({"receivableTrx"})
public class ReceivableTrxController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "应收收款方法页面分页查询")
    @GetMapping("selectPage")
    public Response selectPage(@RequestParam(required = false) String receivableTrxName,
                               @RequestParam(required = false) Long ouId,
                               @RequestParam(required = false) String ouName,
                               @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                               @RequestParam(required = false, defaultValue = "10") Integer pageSize) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        param.put("receivableTrxName", receivableTrxName);
        param.put("ouId", ouId);
        param.put("ouName" ,ouName);

        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/receivableTrx/selectPage", param);
        final String res = restTemplate.getForEntity(url , String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<ReceivableTrxDto>>>(){});
    }

    @ApiOperation(value = "getReceivableTrxErp")
    @GetMapping("getReceivableTrxErp")
    public Response getReceivableTrxErp(@RequestParam(required = false) String lastUpdateDate){
        final Map<String, Object> param = new HashMap<>();
        param.put("lastUpdateDate", lastUpdateDate);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "receivableTrx/getReceivableTrxErp",param);
        restTemplate.getForObject(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        response.setData("0");
        return response;
    }

}
