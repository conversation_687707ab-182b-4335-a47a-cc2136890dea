package com.midea.pam.gateway;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Api("权限平台")
@RestController
@RequestMapping(value = {"authority"})
public class AuthorityController extends ControllerHelper {
    private static Logger logger = LoggerFactory.getLogger(AuthorityController.class);
    @Resource
    private RestTemplate restTemplate;

    @ApiOperation("推送权限平台使用单位信息")
    @PostMapping({"pushUnit"})
    public String pushUnit(@RequestBody String param) {
        logger.info("pushUnit网关接口开始执行...");
        final Map<String, Object> param1 = new HashMap<>();
        param1.put("param", param);
        String url = String.format("%sauthority/pushUnit", ModelsEnum.BASEDATA.getBaseUrl(), param1);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, param, String.class);
        DataResponse<String> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<String>>() {
        });
        System.out.println(responseEntity.getBody());
        return response.getData();
    }

    @ApiOperation("推送权限平台使用单位组织产品信息")
    @PostMapping({"pushUnitOrgInfo"})
    public String pushUnitOrgInfo(@RequestBody String param) {
        logger.info("pushUnitOrgInfo网关接口开始执行...");
        final Map<String, Object> param1 = new HashMap<>();
        param1.put("param", param);
        String url = String.format("%sauthority/pushUnitOrgInfo", ModelsEnum.BASEDATA.getBaseUrl(), param1);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, param, String.class);
        DataResponse<String> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<String>>() {
        });
        System.out.println(responseEntity.getBody());
        return response.getData();
    }

    @ApiOperation("推送权限平台用户-数据权限数据")
    @PostMapping({"pushUserUnitRelInfo"})
    public String pushUserUnitRelInfo(@RequestBody String param) {
        logger.info("pushUserUnitRelInfo网关接口开始执行...");
        final Map<String, Object> grantUserParam = new HashMap<>();
        grantUserParam.put("param", param);
        String url = String.format("%sauthority/pushUserUnitRelInfo", ModelsEnum.BASEDATA.getBaseUrl(), grantUserParam);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, param, String.class);
        DataResponse<String> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<String>>() {
        });
        System.out.println(responseEntity.getBody());
        return response.getData();
    }

    @ApiOperation("写入用户-数据权限数据")
    @PostMapping({"writeUserUnitRelInfo"})
    public String writeUserUnitRelInfo(@RequestBody String param) {
        logger.info("writeUserUnitRelInfo网关接口开始执行...");
        final Map<String, Object> userRoleParam = new HashMap<>();
        userRoleParam.put("param", param);
        String url = String.format("%sauthority/writeUserUnitRelInfo", ModelsEnum.BASEDATA.getBaseUrl(), userRoleParam);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, param, String.class);
        DataResponse<String> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<String>>() {
        });
        System.out.println(responseEntity.getBody());
        return response.getData();
    }

    @ApiOperation("推送权限平台角色数据")
    @PostMapping({"pushRoleData"})
    public String pushRoleData(@RequestBody String param) {
        logger.info("pushRoleData网关接口开始执行...");
        final Map<String, Object> param1 = new HashMap<>();
        param1.put("param", param);
        String url = String.format("%sauthority/pushRoleData", ModelsEnum.BASEDATA.getBaseUrl(), param1);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, param, String.class);
        DataResponse<String> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<String>>() {
        });
        System.out.println(responseEntity.getBody());
        return response.getData();
    }

    @ApiOperation("推送权限平台用户角色数据")
    @PostMapping({"pushUserRoleData"})
    public String pushUserRoleData(@RequestBody String param) {
        logger.info("pushUserRoleData网关接口开始执行...");
        final Map<String, Object> userParam = new HashMap<>();
        userParam.put("param", param);
        String url = String.format("%sauthority/pushUserRoleData", ModelsEnum.BASEDATA.getBaseUrl(), userParam);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, param, String.class);
        DataResponse<String> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<String>>() {
        });
        System.out.println(responseEntity.getBody());
        return response.getData();
    }

    @ApiOperation("写入用户-角色权限数据")
    @PostMapping({"writeRoleInfo"})
    public String writeRoleInfo(@RequestBody String param) {
        logger.info("writeRoleInfo网关接口开始执行...");
        final Map<String, Object> userRoleParam = new HashMap<>();
        userRoleParam.put("param", param);
        String url = String.format("%sauthority/writeRoleInfo", ModelsEnum.BASEDATA.getBaseUrl(), userRoleParam);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, param, String.class);
        DataResponse<String> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<String>>() {
        });
        System.out.println(responseEntity.getBody());
        return response.getData();
    }

    @ApiOperation("推送权限平台报表组信息")
    @PostMapping({"pushReportGroupInfo"})
    public String pushReportGroupInfo(@RequestBody String param) {
        logger.info("pushReportGroupInfo网关接口开始执行...");
        final Map<String, Object> reportParam = new HashMap<>();
        reportParam.put("param", param);
        String url = String.format("%sauthority/pushReportGroupInfo", ModelsEnum.STATISTICS.getBaseUrl(), reportParam);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, param, String.class);
        DataResponse<String> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<String>>() {
        });
        System.out.println(responseEntity.getBody());
        return response.getData();
    }

    @ApiOperation("推送权限平台用户-报表权限数据")
    @PostMapping({"pushReportGroupGrantUserInfo"})
    public String pushReportGroupGrantUserInfo(@RequestBody String param) {
        logger.info("pushReportGroupGrantUserInfo网关接口开始执行...");
        final Map<String, Object> grantUserParam = new HashMap<>();
        grantUserParam.put("param", param);
        String url = String.format("%sauthority/pushReportGroupGrantUserInfo", ModelsEnum.STATISTICS.getBaseUrl(), grantUserParam);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, param, String.class);
        DataResponse<String> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<String>>() {
        });
        System.out.println(responseEntity.getBody());
        return response.getData();
    }

    @ApiOperation("写入用户-报表权限数据")
    @PostMapping({"writeReportGroupGrantUserInfo"})
    public String writeReportGroupGrantUserInfo(@RequestBody String param) {
        logger.info("writeReportGroupGrantUserInfo网关接口开始执行...");
        final Map<String, Object> grantUserParam = new HashMap<>();
        grantUserParam.put("param", param);
        String url = String.format("%sauthority/writeReportGroupGrantUserInfo", ModelsEnum.STATISTICS.getBaseUrl(), grantUserParam);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, param, String.class);
        DataResponse<String> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<String>>() {
        });
        System.out.println(responseEntity.getBody());
        return response.getData();
    }
}
