package com.midea.pam.gateway.ctc.web;

import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2019-4-26
 * @description 合同流程回调
 */
@Api("销售合同新增流程回调")
@RestController
@RequestMapping("contract/workflow/callback")
public class ContractWorkflowCallbackController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "发起审批")
    @PutMapping("draftSubmit/skipSecurityInterceptor")
    public Response draftSubmitCallback(@RequestParam(required = false) Long formInstanceId,
                                        @RequestParam(required = false) String fdInstanceId,
                                        @RequestParam(required = false) String formUrl,
                                        @RequestParam(required = false) String eventName,
                                        @RequestParam(required = false) String handlerId,
                                        @RequestParam(required = false) Long companyId,
                                        @RequestParam(required = false) Long createUserId) {
        String url = String.format("%scontract/workflow/callback/draftSubmit/skipSecurityInterceptor" +
                        "?formInstanceId=%d" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%d" +
                        "&createUserId=%d",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "通过")
    @PutMapping("pass/skipSecurityInterceptor")
    public Response passCallback(@RequestParam(required = false) Long formInstanceId,
                                 @RequestParam(required = false) String fdInstanceId,
                                 @RequestParam(required = false) String formUrl,
                                 @RequestParam(required = false) String eventName,
                                 @RequestParam(required = false) String handlerId,
                                 @RequestParam(required = false) Long companyId,
                                 @RequestParam(required = false) Long createUserId,
                                 @RequestParam(required = false) String timestamp) {
        String url = String.format("%scontract/workflow/callback/pass/skipSecurityInterceptor" +
                        "?formInstanceId=%d" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%d" +
                        "&createUserId=%d" +
                        "&timestamp=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId, timestamp);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "驳回")
    @PutMapping("refuse/skipSecurityInterceptor")
    public Response refuseCallback(@RequestParam(required = false) Long formInstanceId,
                                   @RequestParam(required = false) String fdInstanceId,
                                   @RequestParam(required = false) String formUrl,
                                   @RequestParam(required = false) String eventName,
                                   @RequestParam(required = false) String handlerId,
                                   @RequestParam(required = false) Long companyId,
                                   @RequestParam(required = false) Long createUserId) {
        String url = String.format("%scontract/workflow/callback/refuse/skipSecurityInterceptor" +
                        "?formInstanceId=%d" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%d" +
                        "&createUserId=%d",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "撤回")
    @PutMapping("draftReturn/skipSecurityInterceptor")
    public Response draftReturnCallback(@RequestParam(required = false) Long formInstanceId,
                                        @RequestParam(required = false) String fdInstanceId,
                                        @RequestParam(required = false) String formUrl,
                                        @RequestParam(required = false) String eventName,
                                        @RequestParam(required = false) String handlerId,
                                        @RequestParam(required = false) Long companyId,
                                        @RequestParam(required = false) Long createUserId) {
        String url = String.format("%scontract/workflow/callback/draftReturn/skipSecurityInterceptor" +
                        "?formInstanceId=%d" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%d" +
                        "&createUserId=%d",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "作废")
    @PutMapping("abandon/skipSecurityInterceptor")
    public Response abandon(@RequestParam(required = false) Long formInstanceId,
                            @RequestParam(required = false) String fdInstanceId,
                            @RequestParam(required = false) String formUrl,
                            @RequestParam(required = false) String eventName,
                            @RequestParam(required = false) String handlerId,
                            @RequestParam(required = false) Long companyId,
                            @RequestParam(required = false) Long createUserId) {
        String url = String.format("%scontract/workflow/callback/abandon/skipSecurityInterceptor" +
                        "?formInstanceId=%d" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%d" +
                        "&createUserId=%d",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "删除")
    @PutMapping("delete/skipSecurityInterceptor")
    public Response delete(@RequestParam(required = false) Long formInstanceId,
                           @RequestParam(required = false) String fdInstanceId,
                           @RequestParam(required = false) String formUrl,
                           @RequestParam(required = false) String eventName,
                           @RequestParam(required = false) String handlerId,
                           @RequestParam(required = false) Long companyId,
                           @RequestParam(required = false) Long createUserId) {
        String url = String.format("%scontract/workflow/callback/delete/skipSecurityInterceptor" +
                        "?formInstanceId=%d" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%d" +
                        "&createUserId=%d",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "处理人通过")
    @PutMapping("agree/skipSecurityInterceptor")
    public Response agree(@RequestParam(required = false) Long formInstanceId,
                          @RequestParam(required = false) String fdInstanceId,
                          @RequestParam(required = false) String formUrl,
                          @RequestParam(required = false) String eventName,
                          @RequestParam(required = false) String handlerId,
                          @RequestParam(required = false) Long companyId,
                          @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%scontract/workflow/callback/agree/skipSecurityInterceptor" +
                        "?formInstanceId=%d" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%d" +
                        "&createUserId=%d",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }


    @ApiOperation(value = "销售合同基本信息变更-发起审批")
    @PutMapping("draftChangeSubmit/skipSecurityInterceptor")
    public Response draftChangeSubmit(@RequestParam(required = false) Long formInstanceId,
                                      @RequestParam(required = false) String fdInstanceId,
                                      @RequestParam(required = false) String formUrl,
                                      @RequestParam(required = false) String eventName,
                                      @RequestParam(required = false) String handlerId,
                                      @RequestParam(required = false) Long companyId,
                                      @RequestParam(required = false) Long createUserId) {
        String url = String.format("%scontract/workflow/callback/draftChangeSubmit/skipSecurityInterceptor" +
                        "?formInstanceId=%d" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%d" +
                        "&createUserId=%d",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "销售合同基本信息变更-通过")
    @PutMapping("passChange/skipSecurityInterceptor")
    public Response passChange(@RequestParam(required = false) Long formInstanceId,
                               @RequestParam(required = false) String fdInstanceId,
                               @RequestParam(required = false) String formUrl,
                               @RequestParam(required = false) String eventName,
                               @RequestParam(required = false) String handlerId,
                               @RequestParam(required = false) Long companyId,
                               @RequestParam(required = false) Long createUserId,
                               @RequestParam(required = false) String timestamp) {
        String url = String.format("%scontract/workflow/callback/passChange/skipSecurityInterceptor" +
                        "?formInstanceId=%d" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%d" +
                        "&createUserId=%d" +
                        "&timestamp=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId, timestamp);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "销售合同基本信息变更-驳回")
    @PutMapping("refuseChange/skipSecurityInterceptor")
    public Response refuseChange(@RequestParam(required = false) Long formInstanceId,
                                 @RequestParam(required = false) String fdInstanceId,
                                 @RequestParam(required = false) String formUrl,
                                 @RequestParam(required = false) String eventName,
                                 @RequestParam(required = false) String handlerId,
                                 @RequestParam(required = false) Long companyId,
                                 @RequestParam(required = false) Long createUserId) {
        String url = String.format("%scontract/workflow/callback/refuseChange/skipSecurityInterceptor" +
                        "?formInstanceId=%d" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%d" +
                        "&createUserId=%d",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "销售合同基本信息变更-撤回")
    @PutMapping("draftChangeReturn/skipSecurityInterceptor")
    public Response draftChangeReturn(@RequestParam(required = false) Long formInstanceId,
                                      @RequestParam(required = false) String fdInstanceId,
                                      @RequestParam(required = false) String formUrl,
                                      @RequestParam(required = false) String eventName,
                                      @RequestParam(required = false) String handlerId,
                                      @RequestParam(required = false) Long companyId,
                                      @RequestParam(required = false) Long createUserId) {
        String url = String.format("%scontract/workflow/callback/draftChangeReturn/skipSecurityInterceptor" +
                        "?formInstanceId=%d" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%d" +
                        "&createUserId=%d",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "销售合同基本信息变更-作废")
    @PutMapping("abandonChange/skipSecurityInterceptor")
    public Response abandonChange(@RequestParam(required = false) Long formInstanceId,
                                  @RequestParam(required = false) String fdInstanceId,
                                  @RequestParam(required = false) String formUrl,
                                  @RequestParam(required = false) String eventName,
                                  @RequestParam(required = false) String handlerId,
                                  @RequestParam(required = false) Long companyId,
                                  @RequestParam(required = false) Long createUserId) {
        String url = String.format("%scontract/workflow/callback/abandonChange/skipSecurityInterceptor" +
                        "?formInstanceId=%d" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%d" +
                        "&createUserId=%d",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "销售合同基本信息变更-删除")
    @PutMapping("deleteChange/skipSecurityInterceptor")
    public Response deleteChange(@RequestParam(required = false) Long formInstanceId,
                                 @RequestParam(required = false) String fdInstanceId,
                                 @RequestParam(required = false) String formUrl,
                                 @RequestParam(required = false) String eventName,
                                 @RequestParam(required = false) String handlerId,
                                 @RequestParam(required = false) Long companyId,
                                 @RequestParam(required = false) Long createUserId) {
        String url = String.format("%scontract/workflow/callback/deleteChange/skipSecurityInterceptor" +
                        "?formInstanceId=%d" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%d" +
                        "&createUserId=%d",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "销售合同基本信息变更-处理人通过")
    @PutMapping("agreeChange/skipSecurityInterceptor")
    public Response agreeChange(@RequestParam(required = false) Long formInstanceId,
                                @RequestParam(required = false) String fdInstanceId,
                                @RequestParam(required = false) String formUrl,
                                @RequestParam(required = false) String eventName,
                                @RequestParam(required = false) String handlerId,
                                @RequestParam(required = false) Long companyId,
                                @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%scontract/workflow/callback/agreeChange/skipSecurityInterceptor" +
                        "?formInstanceId=%d" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%d" +
                        "&createUserId=%d",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

}
