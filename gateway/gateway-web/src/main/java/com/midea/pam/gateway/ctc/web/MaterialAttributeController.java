package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.mcomponent.core.util.StringUtil;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.MaterialAdjustHeaderDTO;
import com.midea.pam.common.ctc.dto.MaterialAttributeImportDto;
import com.midea.pam.common.ctc.entity.AsyncRequestResult;
import com.midea.pam.common.ctc.excelVo.DesignPlanDetailChangeImportExcelVO;
import com.midea.pam.common.ctc.vo.DesignPlanDetailChangeExportExcelVO;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.utils.FileUtil;
import com.midea.pam.gateway.service.OssService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @program: common-module
 * @description: 物料属性网关
 * @author:zhongpeng
 * @create:2021-02-23 15:02
 **/
@Api("物料属性")
@RestController
@RequestMapping("materialAttribute")
public class MaterialAttributeController extends ControllerHelper {
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private OssService ossService;


    @ApiOperation(value = "导入物料属性更新(异步处理)", response = AsyncRequestResult.class)
    @PostMapping("importMaterialAttributeAsync")
    public Response importMaterialAttributeAsync(@RequestParam(value = "file") MultipartFile file) {
        List<DesignPlanDetailChangeImportExcelVO> detailExcelVos = null;
        try {
            detailExcelVos = FileUtil.importExcel(file, DesignPlanDetailChangeImportExcelVO.class, 1, 0);
            Iterator<DesignPlanDetailChangeImportExcelVO> iterator = detailExcelVos.iterator();
            while (iterator.hasNext()) {
                DesignPlanDetailChangeImportExcelVO detailExcelVo = iterator.next();
                if (StringUtil.isNull(detailExcelVo.getPamCode())
                        && StringUtil.isNull(detailExcelVo.getChartVersionNew())
                        && Objects.isNull(detailExcelVo.getUnitWeightNew())
                        && StringUtil.isNull(detailExcelVo.getMachiningPartTypeNew())
                        && StringUtil.isNull(detailExcelVo.getSurfaceHandleNew())
                        && Objects.isNull(detailExcelVo.getMaterialNew())
                        && Objects.isNull(detailExcelVo.getOrSparePartsMaskNew())) {
                    iterator.remove();
                }
            }
            Asserts.notEmpty(detailExcelVos, ErrorCode.SYSTEM_FILE_EMPTY);
        } catch (Exception e) {
            logger.info("更新物料属性生成文件，上传失败:", e.getMessage());
            Asserts.success(ErrorCode.SYSTEM_FILE_ERROR);
        }
        final String url = String.format("%smaterialAttribute/matchingMaterialChangingAsync", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, detailExcelVos, String.class).getBody();
        DataResponse<AsyncRequestResult> response = JSON.parseObject(res, new TypeReference<DataResponse<AsyncRequestResult>>() {
        });
        return response;
    }

    @ApiOperation(value = "查找物料属性变更的详情")
    @GetMapping("findMaterialAttributeChangeInfo")
    public Response findMaterialAttributeChangeInfo(@RequestParam Long id) {

        Map<String, Object> param = new HashMap<>();
        param.put("id", id);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "materialAttribute/findMaterialAttributeChangeInfo", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<MaterialAdjustHeaderDTO> response = JSON.parseObject(res, new TypeReference<DataResponse<MaterialAdjustHeaderDTO>>() {
        });
        return response;
    }


    @ApiOperation(value = "物料属性变更保存或编辑")
    @PostMapping("materialAttributeSaveOrUpdate")
    public Response materialAttributeSaveOrUpdate(@RequestBody MaterialAdjustHeaderDTO dto) {
        final String url = String.format("%smaterialAttribute/materialAttributeSaveOrUpdate", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, dto, String.class).getBody();
        DataResponse<MaterialAdjustHeaderDTO> response = JSON.parseObject(res, new TypeReference<DataResponse<MaterialAdjustHeaderDTO>>() {
        });
        return response;
    }


    @ApiOperation(value = "发起审批")
    @PutMapping("updateStatusChecking/skipSecurityInterceptor")
    public com.midea.pam.gateway.common.base.Response updateStatusChecking(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                                                           @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                                                           @RequestParam(required = false) String handlerId,
                                                                           @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialAttribute/updateStatusChecking/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        com.midea.pam.gateway.common.base.DataResponse<String> response = com.midea.pam.gateway.common.base.Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "驳回")
    @PutMapping("updateStatusReject/skipSecurityInterceptor")
    public com.midea.pam.gateway.common.base.Response updateStatusReject(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                                                         @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                                                         @RequestParam(required = false) String handlerId,
                                                                         @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialAttribute/updateStatusReject/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        com.midea.pam.gateway.common.base.DataResponse<String> response = com.midea.pam.gateway.common.base.Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "通过")
    @PutMapping("updateStatusPass/skipSecurityInterceptor")
    public com.midea.pam.gateway.common.base.Response updateStatusPass(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                                                       @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                                                       @RequestParam(required = false) String handlerId,
                                                                       @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialAttribute/updateStatusPass/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        com.midea.pam.gateway.common.base.DataResponse<String> response = com.midea.pam.gateway.common.base.Response.dataResponse();
        return response;
    }


    @ApiOperation(value = "撤回")
    @PutMapping("updateStatusDraft/skipSecurityInterceptor")
    public com.midea.pam.gateway.common.base.Response updateStatusDraftReturn(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                                                              @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                                                              @RequestParam(required = false) String handlerId,
                                                                              @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialAttribute/updateStatusDraftReturn/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return com.midea.pam.gateway.common.base.Response.dataResponse();
    }

    @ApiOperation(value = "废弃")
    @PutMapping("abandon/skipSecurityInterceptor")
    public com.midea.pam.gateway.common.base.Response abandon(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                                              @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                                              @RequestParam(required = false) String handlerId,
                                                              @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialAttribute/abandon/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        com.midea.pam.gateway.common.base.DataResponse<String> response = com.midea.pam.gateway.common.base.Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "删除")
    @PutMapping("delete/skipSecurityInterceptor")
    public com.midea.pam.gateway.common.base.Response delete(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                                             @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                                             @RequestParam(required = false) String handlerId,
                                                             @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialAttribute/delete/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        com.midea.pam.gateway.common.base.DataResponse<String> response = com.midea.pam.gateway.common.base.Response.dataResponse();
        return response;
    }


    @ApiOperation(value = "物料变更检查错误数据导出", response = ResponseMap.class)
    @PostMapping("exportMaterialChangeErrorDetail")
    public void exportMaterialChangeErrorDetail(HttpServletResponse response, @RequestBody List<MaterialAttributeImportDto> materialAttributeImportDtos) {

        final String url = String.format("%smaterialAttribute/exportMaterialChangeErrorDetail", ModelsEnum.CTC.getBaseUrl());

        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, materialAttributeImportDtos, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("物料属性变更错误数据_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");

        JSONArray materialDetailChangelArr = (JSONArray) resultMap.get("designPlanDetailChangeExportExcelVOS");

        List<DesignPlanDetailChangeExportExcelVO> detailChangeExportExcelVOS = new ArrayList<>();
        if (materialDetailChangelArr != null) {
            detailChangeExportExcelVOS = JSONObject.parseArray(materialDetailChangelArr.toJSONString(), DesignPlanDetailChangeExportExcelVO.class);
            for (int i = 0; i < detailChangeExportExcelVOS.size(); i++) {
                DesignPlanDetailChangeExportExcelVO detailChangeExportExcelVO = detailChangeExportExcelVOS.get(i);
                detailChangeExportExcelVO.setNum(i + 1);
            }
        }

        final Workbook workbook = com.midea.pam.common.util.ExportExcelUtil.buildDefaultSheet(detailChangeExportExcelVOS, DesignPlanDetailChangeExportExcelVO.class, null, "物料属性", true);

        com.midea.pam.common.util.ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }


    @ApiOperation(value = "导入物料属性更新")
    @PostMapping("importMaterialAttributeChange")
    public Response importMaterialAttributeChange(@RequestParam(value = "file") MultipartFile file, @RequestParam Long organizationId) {
        DataResponse<List<MaterialAttributeImportDto>> response = Response.dataResponse();

        List<DesignPlanDetailChangeImportExcelVO> importExcelVos = null;
        String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);
        if (!suffix.equalsIgnoreCase("xlsx")) {
            throw new MipException("导入的文件类型不符合要求，请上传Excel！");
        }
        try {
            importExcelVos = FileUtil.importExcel(file, DesignPlanDetailChangeImportExcelVO.class, 1, 0);
            Iterator<DesignPlanDetailChangeImportExcelVO> iterator = importExcelVos.iterator();
            while (iterator.hasNext()) {
                DesignPlanDetailChangeImportExcelVO detailExcelVo = iterator.next();
                if (StringUtil.isNull(detailExcelVo.getPamCode())
                        && StringUtil.isNull(detailExcelVo.getChartVersionNew())
                        && Objects.isNull(detailExcelVo.getUnitWeightNew())
                        && StringUtil.isNull(detailExcelVo.getMachiningPartTypeNew())
                        && StringUtil.isNull(detailExcelVo.getSurfaceHandleNew())
                        && Objects.isNull(detailExcelVo.getMaterialNew())
                        && Objects.isNull(detailExcelVo.getOrSparePartsMaskNew())) {
                    iterator.remove();
                }
            }
            Asserts.notEmpty(importExcelVos, ErrorCode.SYSTEM_FILE_EMPTY);
            importExcelVos.get(0).setOrganizationId(organizationId);
            final String url = String.format("%smaterialAttribute/importMaterialAttributeChange", ModelsEnum.CTC.getBaseUrl());
            String res = restTemplate.postForEntity(url, importExcelVos, String.class).getBody();
            try {
                response = JSON.parseObject(res, new TypeReference<DataResponse<List<MaterialAttributeImportDto>>>() {
                });
            } catch (Exception e) {
                DataResponse<List<MaterialAttributeImportDto>> responseImportDtos = JSON.parseObject(res, new TypeReference<DataResponse<List<MaterialAttributeImportDto>>>() {
                });
                return responseImportDtos;
            }
        } catch (Exception e) {
            logger.info("物料属性更新文件上传失败:", e.getMessage());
            throw new MipException(response.getMsg());
        }

        return response;

    }


}
