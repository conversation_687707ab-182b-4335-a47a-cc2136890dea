package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.StorageDto;
import com.midea.pam.common.basedata.excelVo.StorageExcelVO;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/3/1
 * @description
 */
@Api("统计-库存信息")
@RestController
@RequestMapping("statistics/storage")
public class StorageStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查询库存信息列表")
    @GetMapping("page")
    public Response list(StorageDto storageDto,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(storageDto);
        params.put("list", Boolean.TRUE);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/storage/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<StorageDto>> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<PageInfo<StorageDto>>>() {
                });

        return response;
    }

    private Map buildParam(StorageDto storageDto) {
        final Map<String, Object> params = new HashMap<>();
        //ERP物料编码 物料描述 子库 货位 PAM编码 名称 品牌 型号
        params.put("segment1", storageDto.getSegment1());
        params.put("description", storageDto.getDescription());
        params.put("subinventoryCodeStr", storageDto.getSubinventoryCodeStr());
        params.put("locator", storageDto.getLocator());
        params.put("pamCode", storageDto.getPamCode());
        params.put("name", storageDto.getName());
        params.put("brand", storageDto.getBrand());
        params.put("model", storageDto.getModel());
        params.put("operatingUnitIdsStr", storageDto.getOperatingUnitIdsStr());
        params.put("organizationCodesStr", storageDto.getOrganizationCodesStr());
        params.put("shelves", storageDto.getShelves());
        return params;
    }


    @ApiOperation(value = "库存信息列表导出", response = ResponseMap.class)
    @GetMapping("export")
    public void listExport(HttpServletResponse response, StorageDto storageDto) {
        final Map<String, Object> params = buildParam(storageDto);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/storage/v1/export", params);
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(6000 * 1000);
        httpRequestFactory.setConnectTimeout(6000 * 1000);
        httpRequestFactory.setReadTimeout(6000 * 1000);
        restTemplate.setRequestFactory(httpRequestFactory);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String,
                Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("库存现有量_" + DateUtils.format(new Date(), "yyyyMMddhhmmss"));
        fileName.append(".xls");

        JSONArray storageArr = (JSONArray) resultMap.get("storageList");

        List<StorageExcelVO> storageExcelVOS = new ArrayList<>();
        if (storageArr != null) {
            storageExcelVOS = JSONObject.parseArray(storageArr.toJSONString(), StorageExcelVO.class);
            for (int i = 0; i < storageExcelVOS.size(); i++) {
                StorageExcelVO storageExcelVO = storageExcelVOS.get(i);
                storageExcelVO.setNum(i + 1);
            }
        }

        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(storageExcelVOS, StorageExcelVO.class, null, "库存信息", true);
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

}
