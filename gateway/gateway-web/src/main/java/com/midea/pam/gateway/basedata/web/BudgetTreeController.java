package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.entity.BudgetTree;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.PageResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.utils.DateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: pam
 * @description: BudgetTreeController
 * @author: gaojh1
 * @create: 2019-3-12 10:51
 **/
@RestController
@RequestMapping("budgetTree")
@Api("预算树")
public class BudgetTreeController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "listPage")
    @GetMapping("listPage")
    public Response listPage(@RequestParam(required = false) String budgetName,
                             @RequestParam(required = false) String emsOrgName,
                             @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                             @RequestParam(required = false, defaultValue = "10") Integer pageSize) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("budgetName", budgetName);
        param.put("emsOrgName", emsOrgName);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);

        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "budgetTree/listPage", param);
        String res = restTemplate.getForObject(url, String.class);
        res = cleanStr(res);
        PageInfo<Map<String, Object>> data = JSON.parseObject(res, new TypeReference<PageInfo<Map<String, Object>>>() {
        });
        PageResponse<Map<String, Object>> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation(value = "预算树ems同步")
    @GetMapping("getBudgetTreeFromEms")
    public Response getBudgetTreeFromEms(@RequestParam(required = false) String lastUpdateDate) {
        final Map<String, Object> param = new HashMap<>();
        if (StringUtils.isNotEmpty(lastUpdateDate)){
            param.put("lastUpdateDate", lastUpdateDate);
        }else {
            param.put("lastUpdateDate", DateUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
        }
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "budgetTree/getBudgetTreeFromEms", param);
        restTemplate.getForObject(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        response.setData("0");
        return response;
    }

    @ApiOperation(value = "预算树查询")
    @GetMapping("query")
    public Response query(@RequestParam(required = true) Long ouId,
                          @RequestParam(required = true) Long emsBusiorgId,
                          @RequestParam(required = true) String year) {
        final Map<String, Object> param = new HashMap<>();
        param.put("emsBusiorgId", emsBusiorgId);
        param.put("year", year);
        param.put("ouId", ouId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "budgetTree/query", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<BudgetTree>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<BudgetTree>>>() {
        });
        return response;
    }

}
