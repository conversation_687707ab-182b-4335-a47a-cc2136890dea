package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.ProjectFundsDeployDto;
import com.midea.pam.common.ctc.dto.ProjectFundsDeployVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Description: 项目奖金设置
 * @Date: 2020-12-15 15:16
 * @Version: 1.0
 */
@Api("项目奖金设置")
@RestController
@RequestMapping({"projectFund"})
public class ProjectFundsDeployController extends ControllerHelper {
    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查询项目奖金列表")
    @GetMapping("list")
    public Response getProjectFundList(@RequestParam(required = false) @ApiParam("项目编号") String projectCode,
                                       @RequestParam(required = false) @ApiParam("项目名称") String projectName,
                                       @RequestParam(required = false) @ApiParam("项目类型") String type,
                                       @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                       @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        Map<String, Object> param = new HashMap<>(5);
        param.put("projectCode", projectCode);
        param.put("projectName", projectName);
        param.put("type", type);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "projectFund/list", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ProjectFundsDeployDto>>>() {
        });
    }

    @ApiOperation(value = "新增项目奖金")
    @PostMapping("save")
    public Response insertProductFund(@RequestBody ProjectFundsDeployVo dto) {
        final String url = String.format("%sprojectFund/save", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Long>>() {
        });
    }

    @ApiOperation(value = "删除项目奖金")
    @DeleteMapping("delete")
    public Response deleteProductFund(@ApiParam("项目奖金发放设置id") @RequestParam final Long id) {
      Map<String, Object> param = new HashMap<>(1);
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "projectFund/delete", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Integer> response = JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
        return response;
    }

    @ApiOperation(value = "根据项目奖金id查询项目奖金详情")
    @GetMapping("getById")
    public Response getById(@ApiParam("项目奖id") @RequestParam final Long id) {
        Map<String, Object> param = new HashMap<>(1);
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "projectFund/getById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<ProjectFundsDeployDto> response = JSON.parseObject(res, new TypeReference<DataResponse<ProjectFundsDeployDto>>() {
        });
        return response;
    }

}
