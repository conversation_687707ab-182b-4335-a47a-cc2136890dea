package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.InvoiceApplyDto;
import com.midea.pam.common.ctc.dto.InvoiceReceivableDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.ProcessTemplate;
import com.midea.pam.common.gateway.entity.MifCallbackLog;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.mapper.MifCallbackLogMapper;
import com.midea.pam.gateway.service.MipWorkflowInnerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @date 2019-4-29
 */
@Api("开票申请")
@RestController
@RequestMapping("invoiceReceivable")
public class InvoiceReceivableController extends ControllerHelper {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private MifCallbackLogMapper mifCallbackLogMapper;
    @Resource
    private MipWorkflowInnerService mipWorkflowInnerService;


    @ApiOperation(value = "查询", response = InvoiceReceivableDto.class)
    @GetMapping("query")
    public Response query(@RequestParam(required = false) @ApiParam(value = "应收发票号") String invoiceCode,
                          @RequestParam(required = false) @ApiParam(value = "开票申请号") String applyCode,
                          @RequestParam(required = false) @ApiParam(value = "应收发票类型") String invoiceType,
                          @RequestParam(required = false) @ApiParam(value = "开票日期开始") String invoiceDateStart,
                          @RequestParam(required = false) @ApiParam(value = "开票日期结束") String invoiceDateEnd,
                          @RequestParam(required = false) @ApiParam(value = "开票客户名称") String externalInvoiceUser,
                          @RequestParam(required = false) @ApiParam(value = "客户名称") String customerName,
                          @RequestParam(required = false) @ApiParam(value = "业务实体") String ouId,
                          @RequestParam(required = false) @ApiParam(value = "总账日期开始") String glDateStart,
                          @RequestParam(required = false) @ApiParam(value = "总账日期结束") String glDateEnd,
                          @RequestParam(required = false) @ApiParam(value = "状态") String status,
                          @RequestParam(required = false) @ApiParam(value = "金额") String taxIncludedPrice,
                          @RequestParam(required = false) @ApiParam(value = "erp状态") String erpStatus,
                          @RequestParam(required = false) @ApiParam(value = "erp消息") String erpMsg,
                          @RequestParam(required = false) @ApiParam(value = "创建日期开始") String createAtStart,
                          @RequestParam(required = false) @ApiParam(value = "创建日期结束") String createAtEnd,
                          @RequestParam(required = false) @ApiParam(value = "合同id") Long contractId,
                          @RequestParam(required = false) @ApiParam(value = "开票申请头表ID") Long applyHeaderId,
                          @RequestParam(required = false) @ApiParam(value = "创建人") String createUser,
                          @RequestParam(required = false) @ApiParam(value = "合同ID") String contractIdsStr,
                          @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                          @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put("invoiceCode", invoiceCode);
        param.put("applyCode", applyCode);
        param.put("invoiceType", invoiceType);
        param.put("invoiceDateStart", invoiceDateStart);
        param.put("invoiceDateEnd", invoiceDateEnd);
        param.put("externalInvoiceUser", externalInvoiceUser);
        param.put("customerName", customerName);
        param.put("ouId", ouId);
        param.put("glDateStart", glDateStart);
        param.put("glDateEnd", glDateEnd);
        param.put("status", status);
        param.put("taxIncludedPrice", taxIncludedPrice);
        param.put("erpStatus", erpStatus);
        param.put("erpMsg", erpMsg);
        param.put("createAtStart", createAtStart);
        param.put("createAtEnd", createAtEnd);
        param.put("contractId", contractId);
        param.put("applyHeaderId", applyHeaderId);
        param.put("contractIdsStr", contractIdsStr);
        param.put("createUser", createUser);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/invoiceReceivable/query", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();

        DataResponse<JSONObject> data = Response.dataResponse();
        data.setData(JSON.parseObject(res));
        return data;
    }

    @ApiOperation(value = "根据资金单据行id查询可核销的发票")
    @GetMapping("getListByClaimDetailId")
    public Response getListByClaimDetailId(@RequestParam @ApiParam(value = "资金单据行id") Long claimDetailId,
                                           @RequestParam(required = false) @ApiParam(value = "应收发票号") String invoiceCode) {
        final Map<String, Object> param = new HashMap<>();
        param.put("claimDetailId", claimDetailId);
        param.put("invoiceCode", invoiceCode);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/invoiceReceivable/getListByClaimDetailId", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<InvoiceReceivableDto>>>() {
        });
    }

    @ApiOperation(value = "同步ERP")
    @GetMapping("syncErp")
    public Response syncErp(@RequestParam("ids") String ids) {
        final Map<String, Object> param = new HashMap<>();
        param.put("ids", ids);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/invoiceReceivable/syncErp", param);
        return restTemplate.getForEntity(url, DataResponse.class).getBody();
    }

    @ApiOperation(value = "红冲")
    @PostMapping("reverse")
    public Response reverse(@RequestBody @ApiParam(name = "InvoiceReceivableDto", value = "应收发票") InvoiceReceivableDto invoiceReceivable) {
        final String url = String.format("%sinvoiceReceivable/reverse", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, invoiceReceivable, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Long>>() {
        });

    }

    @ApiOperation(value = "关联交易发票红冲")
    @PostMapping("transactionReverse")
    public Response transactionReverse(@RequestBody @ApiParam(name = "InvoiceReceivable", value = "应收发票") InvoiceReceivableDto invoiceReceivableDto) {
        final String url = String.format("%sinvoiceReceivable/transactionReverse", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, invoiceReceivableDto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Long>>() {
        });

    }

    @ApiOperation(value = "红冲应收发票审批详情查询", response = InvoiceApplyDto.class)
    @GetMapping("reverseInvoiceView/{id}")
    public Response details(@PathVariable Long id) {
        Map<String, Object> param = new HashMap<>();
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "invoiceReceivable/reverseInvoiceView/" + id, param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<InvoiceReceivableDto>>() {
        });
    }

    @ApiOperation(value = "移动审批-应收红冲审批")
    @GetMapping({"getReverseInvoiceApp"})
    public Response getReverseInvoiceApp(@RequestParam Long id) {
        String url = String.format("%sinvoiceReceivable/getReverseInvoiceApp?id=%s", ModelsEnum.CTC.getBaseUrl(), id);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<ResponseMap>>() {
        });
    }

    @ApiOperation(value = "红冲应收发票作废")
    @GetMapping("abandon/{id}")
    public Response abandon(@PathVariable Long id) {
        Map<String, Object> param = new HashMap<>();
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "invoiceReceivable/abandon/" + id, param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        //同时作废工作流
        if (response.getCode() == 0) {
            mipWorkflowInnerService.draftAbandon(ProcessTemplate.REVERSE_INVOICE_APP.getCode(), id);
        }
        return response;
    }

    @ApiOperation(value = "更新Gl日期")
    @GetMapping("updateGlDate")
    public Response updateGlDate(@RequestParam @ApiParam("应收发票id") Long id,
                                 @RequestParam @ApiParam("Gl日期") String glDate) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("glDate", glDate);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/invoiceReceivable/updateGlDate", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation(value = "更新应收发票到期日")
    @GetMapping(value = "modifyDueDate")
    public Response modifyDueDate(@ApiParam("应收发票id") @RequestParam Long id,
                                  @ApiParam("到期日日期") @RequestParam String dueDate) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("dueDate", dueDate);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/invoiceReceivable/modifyDueDate", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return Response.response();
    }

    @ApiOperation("同步美的开票平台应收发票开票状态，由美的开票平台调用")
    @PostMapping(value = "syncInvoiceInfoFromMif")
    public Response syncInvoiceInfoFromMif(@RequestBody InvoiceReceivableDto invoiceReceivableDto) {
        logger.info("美的开票平台同步发票信息：{}", JSON.toJSONString(invoiceReceivableDto));
        MifCallbackLog mifCallbackLog = new MifCallbackLog();
        mifCallbackLog.setCallbackUrl("http://pam:9080/pam/invoiceReceivable/syncInvoiceInfoFromMif");
        mifCallbackLog.setCallbackParams(JSON.toJSONString(invoiceReceivableDto));
        mifCallbackLog.setCallbackTime(new Date());
        DataResponse<?> dataResponse;
        try {
            String url = ModelsEnum.CTC.getBaseUrl() + "invoiceReceivable/syncInvoiceInfoFromMif";
            dataResponse = restTemplate.postForObject(url, invoiceReceivableDto, DataResponse.class);
            mifCallbackLog.setCallbackResult(JSON.toJSONString(dataResponse));
        } catch (Exception e) {
            logger.error("同步美的开票平台应收发票开票状态失败", e);
            throw e;
        } finally {
            try {
                mifCallbackLogMapper.insert(mifCallbackLog);
            } catch (Exception e) {
                logger.info("美的开票平台异步回调日志保存失败：{}", JSON.toJSONString(invoiceReceivableDto));
            }
        }
        return dataResponse;
    }

    @ApiOperation("美的开票平台发票查询")
    @GetMapping(value = "searchInvoiceInfoFromMif")
    public Response searchInvoiceInfoFromMif(@RequestParam Long id) {
        String url = ModelsEnum.CTC.getBaseUrl() + "invoiceReceivable/searchInvoiceInfoFromMif?id=" + id;
        return restTemplate.getForObject(url, DataResponse.class);
    }

    @ApiOperation("发票写入美的开票平台")
    @GetMapping(value = "pushBlueInvoiceToMif")
    public Response pushBlueInvoiceToMif(@RequestParam Long id) {
        String url = ModelsEnum.CTC.getBaseUrl() + "invoiceReceivable/pushBlueInvoiceToMif?id=" + id;
        return restTemplate.getForObject(url, DataResponse.class);
    }

    @ApiOperation("红冲发票写入美的开票平台")
    @GetMapping(value = "pushRedInvoiceToMif")
    public Response pushRedInvoiceToMif(@RequestParam Long id) {
        String url = ModelsEnum.CTC.getBaseUrl() + "invoiceReceivable/pushRedInvoiceToMif?id=" + id;
        return restTemplate.getForObject(url, DataResponse.class);
    }

    @ApiOperation("查询对接开票名称配置")
    @GetMapping(value = "queryMifConfig")
    public Response queryMifConfig(@RequestParam Long ouId) {
        String url = ModelsEnum.CTC.getBaseUrl() + "invoiceReceivable/queryMifConfig?ouId=" + ouId;
        return restTemplate.getForObject(url, DataResponse.class);
    }

    @ApiOperation("对接开票名称配置保存")
    @PostMapping("saveMifConfig")
    public Response saveMifConfig(@RequestBody InvoiceReceivableDto dto) {
        final String url = String.format("%sinvoiceReceivable/saveMifConfig", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
    }
}
