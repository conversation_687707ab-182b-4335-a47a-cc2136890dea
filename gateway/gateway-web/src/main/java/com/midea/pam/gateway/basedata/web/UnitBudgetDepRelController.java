package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.BudgetDepDto;
import com.midea.pam.common.basedata.entity.BudgetDep;
import com.midea.pam.common.basedata.entity.FeeItem;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("UnitBudgetDepRel")
@Api("虚拟部门和预算部门的对应关系")
public class UnitBudgetDepRelController extends ControllerHelper {
    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查询虚拟部门下的预算部门")
    @GetMapping("findBudgetDepByUnitId")
    public Response findBudgetDepByUnitId(@RequestParam(required = false) @ApiParam(value = "unitId") Long unitId,
                                          @RequestParam(required = false) @ApiParam(value = "ouId") Long ouId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("unitId", unitId);
        param.put("ouId", ouId);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/UnitBudgetDepRel/findBudgetDepByUnitId", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<BudgetDepDto>>>() {
        });
    }

}
