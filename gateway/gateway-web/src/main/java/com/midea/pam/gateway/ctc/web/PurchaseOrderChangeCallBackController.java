package com.midea.pam.gateway.ctc.web;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.FileConstants;
import com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderReceiptsDto;
import com.midea.pam.common.ctc.dto.SaveBatchPurchaseOrderRecordDto;
import com.midea.pam.common.ctc.dto.SavePurchaseOrderChangeRecordDto;
import com.midea.pam.common.ctc.entity.FileListInfo;
import com.midea.pam.common.ctc.excelVo.PurchaseOrderDetailExcelVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.OrderStatusEnum;
import com.midea.pam.common.util.BeanConverter;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.service.MipWorkflowInnerService;
import com.midea.pam.gateway.service.OssService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.midea.pam.common.util.StringUtils.buildGetUrl;


@Api("采购订单变更审批回调")
@RestController
@RequestMapping("purchaseOrderChangeCallBack")
@Slf4j
public class PurchaseOrderChangeCallBackController {
    @Resource
    private RestTemplate restTemplate;

    @Resource
    private OssService ossService;

    @Resource
    private MipWorkflowInnerService mipWorkflowInnerService;

    /**
     * 流程审批中回调方法.
     *
     * @param formInstanceId 表单实例id
     */
    @ApiOperation(value = "审批中")
    @PutMapping("draftSubmit/skipSecurityInterceptor")
    public Response draftSubmit(@RequestParam(required = false) Long formInstanceId,
                                @RequestParam(required = false) String fdInstanceId,
                                @RequestParam(required = false) String formUrl,
                                @RequestParam(required = false) String eventName,
                                @RequestParam(required = false) String handlerId,
                                @RequestParam(required = false) Long companyId,
                                @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%spurchaseOrderChangeCallBack/draftSubmit/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        // 生成附件
        generateAttachments(formInstanceId.toString(), handlerId);
        return Response.dataResponse();
    }

    /**
     * 流程审批完成回调方法。
     *
     * @param formInstanceId 表单实例id
     */
    @ApiOperation(value = "审批通过")
    @PutMapping("pass/skipSecurityInterceptor")
    public Response pass(@RequestParam(required = false) Long formInstanceId,
                         @RequestParam(required = false) String fdInstanceId,
                         @RequestParam(required = false) String formUrl,
                         @RequestParam(required = false) String eventName,
                         @RequestParam(required = false) String handlerId,
                         @RequestParam(required = false) Long companyId,
                         @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%spurchaseOrderChangeCallBack/pass/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    /**
     * 流程审批驳回回调方法.
     *
     * @param formInstanceId 表单实例id
     */
    @ApiOperation(value = "驳回")
    @PutMapping("refuse/skipSecurityInterceptor")
    public Response refuse(@RequestParam(required = false) Long formInstanceId,
                           @RequestParam(required = false) String fdInstanceId,
                           @RequestParam(required = false) String formUrl,
                           @RequestParam(required = false) String eventName,
                           @RequestParam(required = false) String handlerId,
                           @RequestParam(required = false) Long companyId,
                           @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%spurchaseOrderChangeCallBack/refuse/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    /**
     * 流程审批撤回回调方法.
     *
     * @param formInstanceId 表单实例id
     */
    @ApiOperation(value = "撤回")
    @PutMapping("draftReturn/skipSecurityInterceptor")
    public Response draftReturn(@RequestParam(required = false) Long formInstanceId,
                                @RequestParam(required = false) String fdInstanceId,
                                @RequestParam(required = false) String formUrl,
                                @RequestParam(required = false) String eventName,
                                @RequestParam(required = false) String handlerId,
                                @RequestParam(required = false) Long companyId,
                                @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%spurchaseOrderChangeCallBack/draftReturn/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    /**
     * 流程审批废弃回调方法.
     *
     * @param formInstanceId 表单实例id
     */
    @ApiOperation(value = "废弃")
    @PutMapping("abandon/skipSecurityInterceptor")
    public Response abandon(@RequestParam(required = false) Long formInstanceId,
                            @RequestParam(required = false) String fdInstanceId,
                            @RequestParam(required = false) String formUrl,
                            @RequestParam(required = false) String eventName,
                            @RequestParam(required = false) String handlerId,
                            @RequestParam(required = false) Long companyId,
                            @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%spurchaseOrderChangeCallBack/abandon/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    /**
     * 流程审批删除回调方法.
     *
     * @param formInstanceId 表单实例id
     */
    @ApiOperation(value = "删除")
    @PutMapping("delete/skipSecurityInterceptor")
    public Response delete(@RequestParam(required = false) Long formInstanceId,
                           @RequestParam(required = false) String fdInstanceId,
                           @RequestParam(required = false) String formUrl,
                           @RequestParam(required = false) String eventName,
                           @RequestParam(required = false) String handlerId,
                           @RequestParam(required = false) Long companyId,
                           @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%spurchaseOrderChangeCallBack/delete/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "处理人通过")
    @PutMapping("agree/skipSecurityInterceptor")
    public Response agree(@RequestParam(required = false) Long formInstanceId,
                          @RequestParam(required = false) String fdInstanceId,
                          @RequestParam(required = false) String formUrl,
                          @RequestParam(required = false) String eventName,
                          @RequestParam(required = false) String handlerId,
                          @RequestParam(required = false) Long companyId,
                          @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%spurchaseOrderChangeCallBack/agree/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "页面按钮作废")
    @PutMapping("button/abandon/skipSecurityInterceptor")
    public Response buttonAbandon(@RequestParam(required = false) Long formInstanceId) {
        String url = String.format("%spurchaseOrderChangeCallBack/button/abandon/skipSecurityInterceptor" +
                        "?formInstanceId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId);
        restTemplate.put(url, String.class);
        //同时作废工作流
        mipWorkflowInnerService.draftAbandon("purchaseOrderChangeApp", formInstanceId);
        return Response.dataResponse();
    }

    @ApiOperation(value = "生成采购变更订单附件")
    @GetMapping("generateAttachments")
    public Response generateAttachments(@RequestParam(required = false) String formInstanceId,
                                        @RequestParam(required = false) String handlerId) {

        Map<String, Object> orderMap = new HashMap<>();
        orderMap.put("id", formInstanceId);
        // 查询变更记录详情
        String orderUrl = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "purchaseOrderChange/changeDetails", orderMap);
        String changeRecord = restTemplate.getForEntity(orderUrl, String.class).getBody();
        DataResponse<SavePurchaseOrderChangeRecordDto> response = JSON.parseObject(changeRecord,
                new TypeReference<DataResponse<SavePurchaseOrderChangeRecordDto>>() {
                });

        if (Objects.isNull(response) || Objects.isNull(response.getData())) {
            return Response.err("单据对应采购变更历史订单为空!");
        }
        SavePurchaseOrderChangeRecordDto orderChangeHistoryResult = response.getData();
        SaveBatchPurchaseOrderRecordDto receiptsDtoListAll = orderChangeHistoryResult.getPurchaseOrderReceiptsDtoListAll();
        if (Objects.isNull(receiptsDtoListAll)) {
            return Response.err("单据对应采购变更历史订单为空!");
        }
        // 存储上传文件
        List<FileListInfo> fileList = new ArrayList<>();
        try {
            List<PurchaseOrderReceiptsDto> orderReceiptsDtoList = receiptsDtoListAll.getPurchaseOrderReceiptsDtoList();
            for (PurchaseOrderReceiptsDto orderReceiptsDto : orderReceiptsDtoList) {
                PurchaseOrderDto orderChangeHistory = orderReceiptsDto.getPurchaseOrderDto();
                // 生成excel
                String fileName = "采购订单变更明细_" + orderChangeHistory.getNum() + ".xls";
                ExportParams exportParams = new ExportParams(fileName, "Sheet1");
                List<PurchaseOrderDetailDto> orderDetailChangeHistoryList = orderReceiptsDto.getPurchaseOrderDetailDtoList();
                if (CollectionUtil.isEmpty(orderDetailChangeHistoryList)) {
                    continue;
                }
                // 构建excel对象
                List<PurchaseOrderDetailExcelVO> excelVOS = orderDetailChangeHistoryList.stream()
                        .map(o -> buildExcel(o, orderChangeHistory.getOrderStatus())).collect(Collectors.toList());
                // 文件流
                Workbook workbook = ExcelExportUtil.exportExcel(exportParams, PurchaseOrderDetailExcelVO.class, excelVOS);
                String filePath = FileConstants.TEMP_DIRECTORY + fileName;
                OutputStream out = Files.newOutputStream(Paths.get(filePath));
                workbook.write(out);
                // 上传附件至oss
                uploadFile(filePath, fileList);

                // 生成doc合同文件
                String contractTerms = orderChangeHistory.getContractTerms();
                if (StringUtils.isNotEmpty(contractTerms)) {
                    XWPFDocument document = new XWPFDocument();
                    XWPFParagraph titleParagraph = document.createParagraph();
                    XWPFRun titleParagraphRun = titleParagraph.createRun();
                    titleParagraphRun.setText(orderChangeHistory.getContractTerms());
                    String docFileName = "采购订单变更合同_" + orderChangeHistory.getNum() + ".doc";
                    String docFilePath = FileConstants.TEMP_DIRECTORY + docFileName;
                    FileOutputStream docOutput = new FileOutputStream(docFilePath);
                    document.write(docOutput);
                    uploadFile(docFilePath, fileList);
                }
            }
        } catch (Exception e) {
            log.error("生成采购历史变更订单附件 exception", e);
        }

        if (CollectionUtil.isEmpty(fileList)) {
            return Response.err("生成采购历史变更订单附件失败,请检查原因!");
        }
        Map<String, Object> attachmentsParam = Maps.newHashMap();
        attachmentsParam.put("formInstanceId", formInstanceId);
        attachmentsParam.put("handlerId", handlerId);
        // 保存生成的附件
        String savaAttachmentsUrl = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "purchaseOrderChange/savaAttachments", attachmentsParam);
        String res = restTemplate.postForEntity(savaAttachmentsUrl, fileList, String.class).getBody();
        return Response.dataResponse(res);
    }

    private void uploadFile(String filePath, List<FileListInfo> fileListList) throws Exception {
        File file = new File(filePath);
        FileInputStream fileInputStream = new FileInputStream(file);
        // 上传附件至oss
        MockMultipartFile mockMultipartFile = new MockMultipartFile(file.getName(), file.getName(),
                ContentType.APPLICATION_OCTET_STREAM.toString(), fileInputStream);
        JSONArray jsonArray = ossService.upload(mockMultipartFile);
        if (Objects.isNull(jsonArray) || jsonArray.isEmpty()) {
            return;
        }

        FileListInfo fileInfo = JSONObject.parseObject(jsonArray.getString(0), FileListInfo.class);
        if (Objects.nonNull(fileInfo)) {
            fileListList.add(fileInfo);
        }
    }

    private PurchaseOrderDetailExcelVO buildExcel(PurchaseOrderDetailDto orderDetailDto, Integer orderStatus) {
        PurchaseOrderDetailExcelVO wbsExcelVO = new PurchaseOrderDetailExcelVO();
        String mergeRow = BooleanUtil.isTrue(orderDetailDto.getMergeRows()) ? "是" : "否";
        wbsExcelVO.setMergeRow(mergeRow);
        wbsExcelVO.setStatusStr(OrderStatusEnum.getValue(orderStatus));
        BigDecimal orderNum = Optional.ofNullable(orderDetailDto.getOrderNum()).orElse(BigDecimal.ZERO);
        BigDecimal cancelNum = Optional.ofNullable(orderDetailDto.getCancelNum()).orElse(BigDecimal.ZERO);
        // 实际下单量 = orderNum - cancelNum
        wbsExcelVO.setActualOrderNum(orderNum.subtract(cancelNum));
        Long originId = orderDetailDto.getOriginId();
        String newLine = Objects.isNull(originId) ? "是" : "否";
        wbsExcelVO.setNewLine(newLine);
        return BeanConverter.copy(orderDetailDto, wbsExcelVO);
    }

}
