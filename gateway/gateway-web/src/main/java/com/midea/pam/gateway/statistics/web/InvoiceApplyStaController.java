package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.basedata.dto.CustomViewConfigDTO;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.InvoiceApplyDto;
import com.midea.pam.common.ctc.dto.InvoiceApplyHeaderDto;
import com.midea.pam.common.ctc.vo.InvoiceApplyDetailExcelVO;
import com.midea.pam.common.ctc.vo.InvoiceApplyHeaderExcelVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.*;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.sun.org.apache.xpath.internal.operations.Bool;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * @program: common-module
 * @description: 开票申请报表
 * @author:zhongpeng
 * @create:2020-03-17 09:54
 **/
@Api("开票申请")
@RestController
@RequestMapping("statistics/invoiceApply")
public class InvoiceApplyStaController extends ControllerHelper {
    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查询", response = InvoiceApplyHeaderDto.class)
    @GetMapping("query")
    public Response query(@RequestParam(required = false) @ApiParam(value = "申请编号") String applyCode,
                          @RequestParam(required = false) @ApiParam(value = "开票来源") String applyType,
                          @RequestParam(required = false) @ApiParam(value = "开票日期开始") String invoiceDateStart,
                          @RequestParam(required = false) @ApiParam(value = "开票日期结束") String invoiceDateEnd,
                          @RequestParam(required = false) @ApiParam(value = "开票申请日期开始") String applyDateStart,
                          @RequestParam(required = false) @ApiParam(value = "开票申请日期结束") String applyDateEnd,
                          @RequestParam(required = false) @ApiParam(value = "客户名称") String customerName,
                          @RequestParam(required = false) @ApiParam(value = "客户CRM编码") String customerCode,
                          @RequestParam(required = false) @ApiParam(value = "业务实体") String ouId,
                          @RequestParam(required = false) @ApiParam(value = "总账日期开始") String glDateStart,
                          @RequestParam(required = false) @ApiParam(value = "总账日期结束") String glDateEnd,
                          @RequestParam(required = false) @ApiParam(value = "开票状态") String status,
                          @RequestParam(required = false) @ApiParam(value = "金额") String taxIncludedPrice,
                          @RequestParam(required = false) @ApiParam(value = "创建日期开始") String createAtStart,
                          @RequestParam(required = false) @ApiParam(value = "创建日期结束") String createAtEnd,
                          @RequestParam(required = false) @ApiParam(value = "创建人") String createUserName,
                          @RequestParam(required = false) @ApiParam(value = "是否已开税票") Integer orNotTax,
                          @RequestParam(required = false) @ApiParam(value = "税额下限") Float taxLower,
                          @RequestParam(required = false) @ApiParam(value = "税额上限") Float taxUpper,
                          @RequestParam(required = false) @ApiParam(value = "应收发票类型") String invoiceType,
                          @RequestParam(required = false) @ApiParam(value = "EAM同步状态") String eamStatus,
                          @RequestParam(required = false) @ApiParam(value = "审批通过开始日期") String approvedAtStart,
                          @RequestParam(required = false) @ApiParam(value = "审批通过结束日期") String approvedAtEnd,
                          @RequestParam(required = false) @ApiParam(value = "eam同步消息") String eamMsg,
                          @RequestParam(required = false) @ApiParam(value = "关联采购合同") String eamPurchaseCode,
                          @RequestParam(required = false) @ApiParam(value = "子合同名称") String subContractName,
                          @RequestParam(required = false) @ApiParam(value = "项目名称") String projectName,
                          @RequestParam(required = false) @ApiParam(value = "项目编号") String projectCode,
                          @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                          @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put("applyCode", applyCode);
        param.put("applyType", applyType);
        param.put("invoiceDateStart", invoiceDateStart);
        param.put("invoiceDateEnd", invoiceDateEnd);
        param.put("applyDateStart",applyDateStart);
        param.put("applyDateEnd",applyDateEnd);
        param.put("customerName", customerName);
        param.put("customerCode",customerCode);
        param.put("orNotTax",orNotTax);
        param.put("ouId", ouId);
        param.put("glDateStart", glDateStart);
        param.put("glDateEnd", glDateEnd);
        param.put("status", status);
        param.put("taxIncludedPrice", taxIncludedPrice);
        param.put("createAtStart", createAtStart);
        param.put("createAtEnd", createAtEnd);
        param.put("createUserName", createUserName);
        param.put("taxLower", taxLower);
        param.put("taxUpper", taxUpper);
        param.put("invoiceType",invoiceType);
        param.put("approvedAtStart",approvedAtStart);
        param.put("approvedAtEnd",approvedAtEnd);

        param.put("eamPurchaseCode",eamPurchaseCode);
        param.put("eamMsg",eamMsg);
        param.put("eamStatus",eamStatus);
        param.put("subContractName",subContractName);
        param.put("projectName",projectName);
        param.put("projectCode",projectCode);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/invoiceApply/query", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();

        DataResponse<JSONObject> data = Response.dataResponse();
        data.setData(JSON.parseObject(res));
        return data;
    }

    @ApiOperation(value = "我的查询", response = InvoiceApplyHeaderDto.class)
    @GetMapping("myquery")
    public Response myquery(@RequestParam(required = false) @ApiParam(value = "申请编号") String applyCode,
                            @RequestParam(required = false) @ApiParam(value = "开票来源") String applyType,
                            @RequestParam(required = false) @ApiParam(value = "开票日期开始") String invoiceDateStart,
                            @RequestParam(required = false) @ApiParam(value = "开票日期结束") String invoiceDateEnd,
                            @RequestParam(required = false) @ApiParam(value = "开票申请日期开始") String applyDateStart,
                            @RequestParam(required = false) @ApiParam(value = "开票申请日期结束") String applyDateEnd,
                            @RequestParam(required = false) @ApiParam(value = "客户名称") String customerName,
                            @RequestParam(required = false) @ApiParam(value = "客户CRM编码") String customerCode,
                            @RequestParam(required = false) @ApiParam(value = "业务实体") String ouId,
                            @RequestParam(required = false) @ApiParam(value = "总账日期开始") String glDateStart,
                            @RequestParam(required = false) @ApiParam(value = "总账日期结束") String glDateEnd,
                            @RequestParam(required = false) @ApiParam(value = "开票状态") String status,
                            @RequestParam(required = false) @ApiParam(value = "金额") String taxIncludedPrice,
                            @RequestParam(required = false) @ApiParam(value = "创建日期开始") String createAtStart,
                            @RequestParam(required = false) @ApiParam(value = "创建日期结束") String createAtEnd,
                            @RequestParam(required = false) @ApiParam(value = "创建人") String createUserName,
                            @RequestParam(required = false) @ApiParam(value = "是否已开税票") Integer orNotTax,
                            @RequestParam(required = false) @ApiParam(value = "税额下限") Float taxLower,
                            @RequestParam(required = false) @ApiParam(value = "税额上限") Float taxUpper,
                            @RequestParam(required = false) @ApiParam(value = "应收发票类型") String invoiceType,
                            @RequestParam(required = false) @ApiParam(value = "审批通过开始日期") String approvedAtStart,
                            @RequestParam(required = false) @ApiParam(value = "审批通过结束日期") String approvedAtEnd,
                            @RequestParam(required = false) @ApiParam(value = "子合同名称") String subContractName,
                            @RequestParam(required = false) @ApiParam(value = "项目名称") String projectName,
                            @RequestParam(required = false) @ApiParam(value = "项目编号") String projectCode,
                            @RequestParam(required = false) @ApiParam(value = "关联采购合同") String eamPurchaseCode,

                            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                            @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put("applyCode", applyCode);
        param.put("applyType", applyType);
        param.put("invoiceDateStart", invoiceDateStart);
        param.put("invoiceDateEnd", invoiceDateEnd);
        param.put("applyDateStart",applyDateStart);
        param.put("applyDateEnd",applyDateEnd);
        param.put("customerName", customerName);
        param.put("customerCode",customerCode);
        param.put("orNotTax",orNotTax);
        param.put("ouId", ouId);
        param.put("glDateStart", glDateStart);
        param.put("glDateEnd", glDateEnd);
        param.put("status", status);
        param.put("taxIncludedPrice", taxIncludedPrice);
        param.put("createAtStart", createAtStart);
        param.put("createAtEnd", createAtEnd);
        param.put("createUserName", createUserName);
        param.put("taxLower", taxLower);
        param.put("taxUpper", taxUpper);
        param.put("invoiceType",invoiceType);
        param.put("approvedAtStart",approvedAtStart);
        param.put("approvedAtEnd",approvedAtEnd);
        param.put("subContractName",subContractName);
        param.put("projectName",projectName);
        param.put("projectCode",projectCode);
        param.put("eamPurchaseCode",eamPurchaseCode);

        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/invoiceApply/myquery", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();

        DataResponse<JSONObject> data = Response.dataResponse();
        data.setData(JSON.parseObject(res));
        return data;
    }

    @ApiOperation(value = "开票申请列表导出", response = ResponseMap.class)
    @GetMapping("/export")
    public void listExport(HttpServletResponse response, @RequestParam(required = false) @ApiParam(value = "申请编号") String applyCode,
                           @RequestParam(required = false) @ApiParam(value = "开票来源") String applyType,
                           @RequestParam(required = false) @ApiParam(value = "开票日期开始") String invoiceDateStart,
                           @RequestParam(required = false) @ApiParam(value = "开票日期结束") String invoiceDateEnd,
                           @RequestParam(required = false) @ApiParam(value = "开票申请日期开始") String applyDateStart,
                           @RequestParam(required = false) @ApiParam(value = "开票申请日期结束") String applyDateEnd,
                           @RequestParam(required = false) @ApiParam(value = "客户名称") String customerName,
                           @RequestParam(required = false) @ApiParam(value = "客户CRM编码") String customerCode,
                           @RequestParam(required = false) @ApiParam(value = "业务实体") String ouId,
                           @RequestParam(required = false) @ApiParam(value = "总账日期开始") String glDateStart,
                           @RequestParam(required = false) @ApiParam(value = "总账日期结束") String glDateEnd,
                           @RequestParam(required = false) @ApiParam(value = "开票状态") String status,
                           @RequestParam(required = false) @ApiParam(value = "金额") String taxIncludedPrice,
                           @RequestParam(required = false) @ApiParam(value = "创建日期开始") String createAtStart,
                           @RequestParam(required = false) @ApiParam(value = "创建日期结束") String createAtEnd,
                           @RequestParam(required = false) @ApiParam(value = "创建人") String createUserName,
                           @RequestParam(required = false) @ApiParam(value = "是否已开税票") Integer orNotTax,
                           @RequestParam(required = false) @ApiParam(value = "税额下限") Float taxLower,
                           @RequestParam(required = false) @ApiParam(value = "税额上限") Float taxUpper,
                           @RequestParam(required = false) @ApiParam(value = "应收发票类型") String invoiceType,
                           @RequestParam(required = false) @ApiParam(value = "审批通过开始日期") String approvedAtStart,
                           @RequestParam(required = false) @ApiParam(value = "审批通过结束日期") String approvedAtEnd,
                           @RequestParam(required = false) @ApiParam(value = "子合同名称") String subContractName,
                           @RequestParam(required = false) @ApiParam(value = "项目名称") String projectName,
                           @RequestParam(required = false) @ApiParam(value = "项目编号") String projectCode,
                           @RequestParam(required = false) @ApiParam(value = "关联采购合同") String eamPurchaseCode,

                           @RequestParam(required = false) @ApiParam(value = "是否为我的开票") Boolean isMe){
        final Map<String, Object> param = new HashMap<>();
        param.put("applyCode", applyCode);
        param.put("applyType", applyType);
        param.put("invoiceDateStart", invoiceDateStart);
        param.put("invoiceDateEnd", invoiceDateEnd);
        param.put("applyDateStart",applyDateStart);
        param.put("applyDateEnd",applyDateEnd);
        param.put("customerName", customerName);
        param.put("customerCode",customerCode);
        param.put("orNotTax",orNotTax);
        param.put("ouId", ouId);
        param.put("glDateStart", glDateStart);
        param.put("glDateEnd", glDateEnd);
        param.put("status", status);
        param.put("taxIncludedPrice", taxIncludedPrice);
        param.put("createAtStart", createAtStart);
        param.put("createAtEnd", createAtEnd);
        param.put("createUserName", createUserName);
        param.put("taxLower", taxLower);
        param.put("taxUpper", taxUpper);
        param.put("invoiceType",invoiceType);
        param.put("approvedAtStart",approvedAtStart);
        param.put("approvedAtEnd",approvedAtEnd);
        param.put("subContractName",subContractName);
        param.put("projectName",projectName);
        param.put("projectCode",projectCode);
        param.put("eamPurchaseCode",eamPurchaseCode);

        param.put("isMe",isMe);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/invoiceApply/v1/export", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });
        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("开票申请_"+ DateUtils.format(new Date(),"yyyyMMddHHmmss"));
        fileName.append(".xls");
        JSONArray invoiceApplyHeaderDtosArr = (JSONArray) resultMap.get("invoiceApplyHeaderDtos");
        JSONArray invoiceApplyDetailsDtosArr = (JSONArray) resultMap.get("invoiceApplyDetailsDtos");
        List<InvoiceApplyHeaderExcelVO> invoiceApplyHeaderExcelVOS = JSONObject.parseArray(invoiceApplyHeaderDtosArr.toJSONString(), InvoiceApplyHeaderExcelVO.class);
        List<InvoiceApplyDetailExcelVO> invoiceApplyDetailExcelVOS = JSONObject.parseArray(invoiceApplyDetailsDtosArr.toJSONString(), InvoiceApplyDetailExcelVO.class);
        if (ListUtils.isNotEmpty(invoiceApplyHeaderExcelVOS)){
            for (int i = 0; i < invoiceApplyHeaderExcelVOS.size(); i++) {
                InvoiceApplyHeaderExcelVO invoiceApplyHeaderExcelVO = invoiceApplyHeaderExcelVOS.get(i);
                BigDecimal totalTaxIncludedPrice = invoiceApplyHeaderExcelVO.getTotalTaxIncludedPrice()==null?BigDecimal.ZERO:invoiceApplyHeaderExcelVO.getTotalTaxIncludedPrice();
                BigDecimal totalExclusiveOfTax = invoiceApplyHeaderExcelVO.getTotalExclusiveOfTax()==null?BigDecimal.ZERO:invoiceApplyHeaderExcelVO.getTotalExclusiveOfTax();
                BigDecimal totalPenalty = invoiceApplyHeaderExcelVO.getTotalPenalty()==null?BigDecimal.ZERO:invoiceApplyHeaderExcelVO.getTotalPenalty();
                //税额
                BigDecimal taxCount = totalTaxIncludedPrice.subtract(totalExclusiveOfTax).subtract(totalPenalty).setScale(2, RoundingMode.HALF_UP);
                invoiceApplyHeaderExcelVO.setTaxCount(taxCount);
                invoiceApplyHeaderExcelVO.setNum(i+1);
            }

            if (ListUtils.isNotEmpty(invoiceApplyDetailExcelVOS)){
                for (int i = 0; i < invoiceApplyDetailExcelVOS.size(); i++) {
                    InvoiceApplyDetailExcelVO invoiceApplyDetailExcelVO = invoiceApplyDetailExcelVOS.get(i);
                    if (invoiceApplyDetailExcelVO.getContractOuName() != null){
                        final Unit unit = CacheDataUtils.findUnitById(Long.valueOf(invoiceApplyDetailExcelVO.getContractOuName()));
                        if (unit != null) {
                            invoiceApplyDetailExcelVO.setContractOuName(unit.getUnitName());
                        }
                    }
                    String taxRaceStr = invoiceApplyDetailExcelVO.getTaxRace()==null?"":invoiceApplyDetailExcelVO.getTaxRace().stripTrailingZeros().toPlainString()+"%";
                    invoiceApplyDetailExcelVO.setTaxRaceStr(taxRaceStr);
                    invoiceApplyDetailExcelVO.setNum(i+1);
                }
            }

            Workbook workbook = ExportExcelUtil.buildDefaultSheet(invoiceApplyHeaderExcelVOS, InvoiceApplyHeaderExcelVO.class, null, "开票申请", Boolean.TRUE);
            ExportExcelUtil.addSheet(workbook,invoiceApplyDetailExcelVOS,InvoiceApplyDetailExcelVO.class,null,"开票明细",Boolean.TRUE);
            // 获取配置
            String res = restTemplate.getForEntity(ModelsEnum.BASEDATA.getBaseUrl() + "/resource/user/queryCustomView", String.class).getBody();
            DataResponse<CustomViewConfigDTO> customViewResponse = JSON.parseObject(res, new TypeReference<DataResponse<CustomViewConfigDTO>>() {
            });
            CustomViewConfigDTO customViewConfigDTO = customViewResponse.getData();
            List<String> fieldList = new ArrayList<>();
            if (customViewConfigDTO != null) {
                // 我的開票审批列表导出
                if(Boolean.TRUE.equals(isMe) && StringUtils.isNotEmpty(customViewConfigDTO.getMyInvoiceApplyTableTemplate())){
                    JSONArray jsonArray = JSON.parseArray(customViewConfigDTO.getMyInvoiceApplyTableTemplate());
                    for (int i = 0; i < jsonArray.size(); i++) {
                        fieldList.add(jsonArray.getJSONObject(i).getString("label"));
                    }
                    com.midea.pam.gateway.common.utils.ExportExcelUtil.dynamicExportFirstSheet(workbook, fieldList);
                }
                // 开票申请列表自定义视图导出
                if (Boolean.FALSE.equals(isMe) && StringUtils.isNotEmpty(customViewConfigDTO.getInvoiceApplyTableTemplate())) {
                    JSONArray jsonArray = JSON.parseArray(customViewConfigDTO.getInvoiceApplyTableTemplate());
                    for (int i = 0; i < jsonArray.size(); i++) {
                        fieldList.add(jsonArray.getJSONObject(i).getString("label"));
                    }
                    com.midea.pam.gateway.common.utils.ExportExcelUtil.dynamicExportFirstSheet(workbook, fieldList);
                }
            }
            ExportExcelUtil.downLoadExcel(fileName.toString(),response,workbook);
        }

    }



}
