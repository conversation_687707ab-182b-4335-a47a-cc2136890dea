package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.pam.common.basedata.dto.MaterialDto;
import com.midea.pam.common.basedata.dto.MaterialReturnDetailImportDto;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.MaterialReturnDetailCheckDto;
import com.midea.pam.common.ctc.dto.MaterialReturnDetailDto;
import com.midea.pam.common.ctc.dto.MaterialReturnDto;
import com.midea.pam.common.ctc.dto.MaterialReturnImportDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.entity.MaterialReturnDetail;
import com.midea.pam.common.ctc.entity.OrganizationCustomDict;
import com.midea.pam.common.ctc.excelVo.MaterialReturnDetailTemplateExcelVo;
import com.midea.pam.common.ctc.excelVo.MaterialReturnDetailVo;
import com.midea.pam.common.ctc.vo.MaterialKeepVo;
import com.midea.pam.common.ctc.vo.MaterialReturnDetailExportExcelVo;
import com.midea.pam.common.ctc.vo.MaterialReturnDetailImportExcelVo;
import com.midea.pam.common.ctc.vo.MaterialReturnDetailsExcelVO;
import com.midea.pam.common.ctc.vo.MaterialReturnDetailsWbsExcelVO;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.common.utils.FileUtil;
import com.midea.pam.gateway.service.MipWorkflowInnerService;
import com.midea.pam.gateway.service.OssService;
import com.midea.pam.support.utils.BeanConverter;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @date 2019-5-17
 */
@Api("退料模块")
@RestController
@RequestMapping("materialReturn")
public class MaterialReturnController extends ControllerHelper {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private OssService ossService;

    @Resource
    private MipWorkflowInnerService mipWorkflowInnerService;


    @ApiOperation(value = "暂存草稿")
    @PostMapping("draft")
    public Object draft(@RequestBody @ApiParam(name = "MaterialReturnDto", value = "退料单信息") MaterialReturnDto dto) {
        final String url = String.format("%smaterialReturn/draft", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, dto, String.class).getBody();
    }

    @ApiOperation(value = "保存")
    @PostMapping("save")
    public Object save(@RequestBody @ApiParam(name = "MaterialReturnDto", value = "退料单信息") MaterialReturnDto dto) {
        final String url = String.format("%smaterialReturn/save", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, dto, String.class).getBody();
    }

    @ApiOperation(value = "打单计数")
    @GetMapping("printCount")
    public Object printCount(@RequestParam @ApiParam(value = "退料id") Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/materialReturn/printCount", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        com.midea.pam.gateway.common.base.DataResponse<Integer> response =
                JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
                });
        return response;
    }

    @ApiOperation(value = "废弃")
    @GetMapping("discard")
    public Object discard(@RequestParam @ApiParam(value = "退料id") Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/materialReturn/discard", param);
        ResponseEntity<String> entity = restTemplate.getForEntity(url, String.class);
        if (entity.getStatusCodeValue() == 200) {
            //废弃工作流
            mipWorkflowInnerService.draftAbandon("materialReturnApp", id);
        }

        return entity.getBody();
    }

    @ApiOperation(value = "退料处理")
    @PostMapping("processed")
    public Object processed(@RequestBody @ApiParam(name = "MaterialReturnDto", value = "退料单信息,只传Details和id") MaterialReturnDto dto) {
        final String url = String.format("%smaterialReturn/processed", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, dto, String.class).getBody();
    }

    @ApiOperation(value = "退料批量处理")
    @GetMapping("batchProcessed")
    public Object batchProcessed(@RequestParam @ApiParam(value = "退料ids,以逗号分隔") String ids) {
        final Map<String, Object> param = new HashMap<>();
        param.put("ids", ids);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/materialReturn/batchProcessed", param);
        return restTemplate.getForEntity(url, String.class).getBody();
    }

    @ApiOperation(value = "查询", response = MaterialReturnDto.class)
    @GetMapping("query")
    public Object query(@RequestParam(required = false) @ApiParam(value = "退料编号") String returnCode,
                        @RequestParam(required = false) @ApiParam(value = "项目编号") String projectCode,
                        @RequestParam(required = false) @ApiParam(value = "退料人") String returnUserName,
                        @RequestParam(required = false) @ApiParam(value = "制单人") String fillUserName,
                        @RequestParam(required = false) @ApiParam(value = "库存组织id") Long organizationId,
                        @RequestParam(required = false) @ApiParam(value = "申请日期范围始") String applyTimeMin,
                        @RequestParam(required = false) @ApiParam(value = "申请日期范围尾") String applyTimeMax,
                        @RequestParam(required = false) @ApiParam(value = "是否有差异") Boolean isDifference,
                        @RequestParam(required = false) @ApiParam(value = "状态") Integer status,
                        @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                        @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put("returnCode", returnCode);
        param.put("projectCode", projectCode);
        param.put("returnUserName", returnUserName);
        param.put("fillUserName", fillUserName);
        param.put("organizationId", organizationId);
        param.put("applyTimeMin", applyTimeMin);
        param.put("applyTimeMax", applyTimeMax);
        param.put("isDifference", isDifference);
        param.put("status", status);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/materialReturn/query", param);
        return restTemplate.getForEntity(url, String.class).getBody();
    }

    @ApiOperation(value = "根据id查询", response = MaterialReturnDto.class)
    @GetMapping("queryById")
    public Object query(@RequestParam @ApiParam(value = "退料id") Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/materialReturn/queryById", param);
        return restTemplate.getForEntity(url, String.class).getBody();
    }

    @ApiOperation(value = "同步")
    @GetMapping("sync")
    public Response sync(@RequestParam(required = true) Long applyNo) {
        final Map<String, Object> param = new HashMap<>();
        param.put("applyNo", applyNo);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "materialReturn/sync", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        String res = cleanStr(responseEntity.getBody());
        return JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
    }

/*    @ApiOperation(value = "批量导入退料明细", response = MaterialReturnDetail.class)
    @PostMapping("importMaTerialReturnDetail")
    public Object importMaTerialReturnDetail(@RequestParam(value = "file") MultipartFile file,
                                             @RequestParam Long projectId) {
        final String url = String.format("%smaterialReturn/importMaTerialReturnDetail", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, materialReturnDetailImportExcelVo, String.class);
        return responseEntity.getBody();
    }*/

    @ApiOperation(value = "批量导入退料明细")
    @PostMapping("importMaTerialReturnDetail")
    public Response importMaTerialReturnDetail(@RequestParam(value = "file") MultipartFile file, @RequestParam Long projectId,
                                               @RequestParam String projectCode, @RequestParam Long organizationId,
                                               @RequestParam Long ticketTasksId) {
        DataResponse<List<MaterialReturnImportDto>> response = Response.dataResponse();
        try {

            List<MaterialReturnDetailImportExcelVo> importExcelVos = FileUtil.importExcel(file, MaterialReturnDetailImportExcelVo.class, 1, 0);
            //ist<MaterialReturnDetailImportExcelVo> importExcelVos = ExcelImportUtils.importExcel(file, 1, 0, MaterialReturnDetailImportExcelVo
            // .class);

            Guard.notNullOrEmpty(importExcelVos, "批量导入退料明细为空");

            //查找组织参数，退料是否严控工单逻辑
            final Map<String, Object> query = new HashMap<>();
            query.put("orgId", SystemContext.getUnitId());
            query.put("orgFrom", "company");
            query.put("name", "退料是否严控工单逻辑");
            String url2 = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/organizationCustomDict/queryByOrdId", query);
            String res2 = restTemplate.getForEntity(url2, String.class).getBody();
            DataResponse<List<OrganizationCustomDict>> response2 = JSON.parseObject(res2,
                    new TypeReference<DataResponse<List<OrganizationCustomDict>>>() {
                    });

            Boolean keep = !(ListUtils.isEmpty(response2.getData()) || response2.getData().get(0).getValue().equals("N"));
            importExcelVos.forEach(excelVo -> {
                excelVo.setKeep(keep);
                excelVo.setProjectId(projectId);
                excelVo.setProjectCode(projectCode);
                excelVo.setOrganizationId(organizationId);
                excelVo.setTicketTasksId(ticketTasksId);
            });
            logger.info("批量导入退料明细的importExcelVos：{}", JSON.toJSONString(importExcelVos));
            final String url = String.format("%smaterialReturn/importMaTerialReturnDetail", ModelsEnum.CTC.getBaseUrl());
            String res = restTemplate.postForEntity(url, importExcelVos, String.class).getBody();
            try {
                return JSON.parseObject(res, new TypeReference<DataResponse<List<MaterialReturnImportDto>>>() {
                });
            } catch (Exception e) {
                return JSON.parseObject(res, new TypeReference<DataResponse<List<MaterialReturnDetailImportDto>>>() {
                });
            }
        } catch (ApplicationBizException e) {
            logger.error(e.getMessage(), e);
            throw new ApplicationBizException(e.getMessage());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new BizException(ErrorCode.SYSTEM_FILE_ERROR);
        }
    }

    @ApiOperation(value = "选择明细弹窗物料检验（是否严控）")
    @PostMapping("/checkMaterialIsKeep")
    public Response checkMaterialIsKeep(@RequestBody MaterialKeepVo materialKeepVo) {
        DataResponse<Object> response = Response.dataResponse();
        final String url = String.format("%smaterialReturn/checkMaterialIsKeep", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, materialKeepVo, String.class).getBody();
        response = JSON.parseObject(res, new TypeReference<DataResponse<Object>>() {
        });
        return response;
    }


    @ApiOperation(value = "批量导入退料明细检查错误导出", response = ResponseMap.class)
    @PostMapping("exportMaTerialReturnErrorDetail")
    public void exportMaTerialReturnErrorDetail(HttpServletResponse response, @RequestBody MaterialReturnDetailImportExcelVo importExcelVos) {

      /*  List<MaterialReturnDetailImportExcelVo> importExcelVos = ExcelImportUtils.importExcel(file, 1, 1, MaterialReturnDetailImportExcelVo.class);
        if(importExcelVos.size()>0){
            importExcelVos.get(0).setProjectId(projectId);
            importExcelVos.get(0).setProjectCode(projectCode);
        }*/
        final String url = String.format("%smaterialReturn/exportMaTerialReturnErrorDetail", ModelsEnum.CTC.getBaseUrl());

        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, importExcelVos, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String,
                Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("退料单_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");

        JSONArray materialReturnDetailArr = (JSONArray) resultMap.get("materialReturnDetailList");

        List<MaterialReturnDetailExportExcelVo> materialReturnDetailExportExcelVos = new ArrayList<>();
        if (materialReturnDetailArr != null) {
            materialReturnDetailExportExcelVos = JSONObject.parseArray(materialReturnDetailArr.toJSONString(),
                    MaterialReturnDetailExportExcelVo.class);
            for (int i = 0; i < materialReturnDetailExportExcelVos.size(); i++) {
                MaterialReturnDetailExportExcelVo materialReturnDetailExportExcelVo = materialReturnDetailExportExcelVos.get(i);
                materialReturnDetailExportExcelVo.setNum(i + 1);
            }
        }

        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(materialReturnDetailExportExcelVos, MaterialReturnDetailExportExcelVo.class,
                null, "项目退料", true);

        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    @ApiOperation(value = "起草回调")
    @PutMapping("updateStatusSubmit/skipSecurityInterceptor")
    public Response updateStatusSubmit(@RequestParam(required = false) Long formInstanceId,
                                       @RequestParam(required = false) String fdInstanceId,
                                       @RequestParam(required = false) String formUrl,
                                       @RequestParam(required = false) String eventName,
                                       @RequestParam(required = false) String handlerId,
                                       @RequestParam(required = false) Long companyId,
                                       @RequestParam(required = false) Long createUserId) {
        DataResponse<String> response = Response.dataResponse();
        //查询领料明细
        final Map<String, Object> param1 = new HashMap<>();
        param1.put("id", formInstanceId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/materialReturn/queryById", param1);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<MaterialReturnDto> materialReturnDtoDataResponse = JSON.parseObject(res, new TypeReference<DataResponse<MaterialReturnDto>>() {
        });
        JSONObject jsonObject = new JSONObject();
        try {
            //生成附件
            MultipartFile multipartFile = createAnnex(materialReturnDtoDataResponse.getData());
            //上传附件
            JSONArray result = ossService.upload(multipartFile);
            if (!ObjectUtils.isEmpty(result)) {
                jsonObject = result.getJSONObject(0);
            }
        } catch (Exception e) {
            logger.error("oss upload error", e);
            response.setCode(com.midea.pam.gateway.common.base.Code.ERROR);
            return response.setData(e.getMessage());
        }

        //提交审批
        url = String.format("%smaterialReturn/updateStatusSubmit/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s" +
                        "&fileId=%s" +
                        "&fileName=%s" +
                        "&fileSize=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId
                , jsonObject.get("fileId"), jsonObject.get("fileName"), jsonObject.get("fileSize"));
        restTemplate.put(url, String.class);
        return response;
    }

    @ApiOperation(value = "通过回调")
    @PutMapping("updateStatusPass/skipSecurityInterceptor")
    public Response updateStatusPass(@RequestParam(required = false) Long formInstanceId,
                                     @RequestParam(required = false) String fdInstanceId,
                                     @RequestParam(required = false) String formUrl,
                                     @RequestParam(required = false) String eventName,
                                     @RequestParam(required = false) String handlerId,
                                     @RequestParam(required = false) Long companyId,
                                     @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialReturn/updateStatusPass/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "驳回回调")
    @PutMapping("updateStatusReject/skipSecurityInterceptor")
    public Response updateStatusReject(@RequestParam(required = false) Long formInstanceId,
                                       @RequestParam(required = false) String fdInstanceId,
                                       @RequestParam(required = false) String formUrl,
                                       @RequestParam(required = false) String eventName,
                                       @RequestParam(required = false) String handlerId,
                                       @RequestParam(required = false) Long companyId,
                                       @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialReturn/updateStatusReject/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "审批人作废回调")
    @PutMapping("updateStatusAbandon/skipSecurityInterceptor")
    public Response updateStatusAbandon(@RequestParam(required = false) Long formInstanceId,
                                        @RequestParam(required = false) String fdInstanceId,
                                        @RequestParam(required = false) String formUrl,
                                        @RequestParam(required = false) String eventName,
                                        @RequestParam(required = false) String handlerId,
                                        @RequestParam(required = false) Long companyId,
                                        @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialReturn/updateStatusAbandon/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "审批人撤回回调")
    @PutMapping("updateStatusDraftReturn/skipSecurityInterceptor")
    public Response updateStatusDraftReturn(@RequestParam(required = false) Long formInstanceId,
                                            @RequestParam(required = false) String fdInstanceId,
                                            @RequestParam(required = false) String formUrl,
                                            @RequestParam(required = false) String eventName,
                                            @RequestParam(required = false) String handlerId,
                                            @RequestParam(required = false) Long companyId,
                                            @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialReturn/updateStatusDraftReturn/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "项目领料流程删除")
    @PutMapping("updateStatusDeleteForMaterialReturn/skipSecurityInterceptor")
    public Response updateStatusDeleteForMaterialReturn(@RequestParam(required = false) Long formInstanceId,
                                                        @RequestParam(required = false) String fdInstanceId,
                                                        @RequestParam(required = false) String formUrl,
                                                        @RequestParam(required = false) String eventName,
                                                        @RequestParam(required = false) String handlerId,
                                                        @RequestParam(required = false) Long companyId,
                                                        @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialReturn/updateStatusDeleteForMaterialReturn/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "处理人通过")
    @PutMapping("agree/skipSecurityInterceptor")
    public Response agree(@RequestParam(required = false) Long formInstanceId,
                          @RequestParam(required = false) String fdInstanceId,
                          @RequestParam(required = false) String formUrl,
                          @RequestParam(required = false) String eventName,
                          @RequestParam(required = false) String handlerId,
                          @RequestParam(required = false) Long companyId,
                          @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialReturn/agree/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "移动审批获取项目物料领料")
    @GetMapping({"getMaterialReturnApp"})
    public com.midea.pam.common.base.Response getMaterialReturnApp(@RequestParam Long id) {
        String url = String.format("%smaterialReturn/getMaterialReturnApp?id=%s", ModelsEnum.CTC.getBaseUrl(), id);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<com.midea.pam.common.base.DataResponse<ResponseMap>>() {
        });
    }


    public MultipartFile createAnnex(MaterialReturnDto materialReturnDto) throws IOException {
        //附件名称为：项目编号+“退料明细”+“_”+退料单编号，
        //举例：IA19108退料明细_RLL191125026.xlsx
        MultipartFile multipartFile = null;
        String filePath = materialReturnDto.getProjectCode() + "退料明细_" + materialReturnDto.getReturnCode() + ".xls";
        OutputStream out = null;
        FileInputStream fileInputStream = null;
        try {
            out = new FileOutputStream("/apps/pam/gateway/file/" + filePath);
            ExportParams exportParams = new ExportParams(filePath, "Sheet1");
            List<MaterialReturnDetailVo> detailVoList = new ArrayList<>();
            List<MaterialReturnDetailDto> getDetailList = materialReturnDto.getDetails();
            if (CollectionUtils.isEmpty(getDetailList)) {
                return null;
            }
            int i = 1;
            for (MaterialReturnDetail getDetail : getDetailList) {
                MaterialReturnDetailVo excelVo = new MaterialReturnDetailVo();
                BeanUtils.copyProperties(getDetail, excelVo);
                excelVo.setApplyAmount(getDetail.getApplyAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
                excelVo.setIndex(i++);
                detailVoList.add(excelVo);
            }
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, MaterialReturnDetailVo.class, detailVoList);
            workbook.write(out);
            File pdfFile = new File("/apps/pam/gateway/file/" + filePath);
            fileInputStream = new FileInputStream(pdfFile);
            multipartFile = new MockMultipartFile(pdfFile.getName(), pdfFile.getName(),
                    ContentType.APPLICATION_OCTET_STREAM.toString(), fileInputStream);
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        } finally {
            if (null != fileInputStream) {
                fileInputStream.close();
            }
            if (null != out) {
                out.close();
            }
        }
        return multipartFile;
    }

    @ApiOperation(value = "退料单ERP同步")
    @GetMapping("getMaterialReturnFromErp")
    public Response getMaterialReturnFromErp(@RequestParam(required = false) String lastUpdateDate,
                                             @RequestParam(required = false) String inventoryCode,
                                             @RequestParam(required = false) Long orgId,
                                             @RequestParam(required = false) String lastUpdateDateEnd) {
        final Map<String, Object> param = new HashMap<>();
        param.put("lastUpdateDate", lastUpdateDate);
        param.put("lastUpdateDateEnd", lastUpdateDateEnd);
        param.put("inventoryCode", inventoryCode);
        param.put("orgId", orgId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "materialReturn/getMaterialReturnFromErp", param);
        restTemplate.getForObject(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        response.setData("0");
        return response;
    }

    @ApiOperation(value = "上传文件检查数据", notes = "场景：项目退料(WBS)批量导入")
    @PostMapping("/detail/checkTemplate")
    public Response checkTemplate(@RequestPart("file") MultipartFile file,
                                  @RequestParam(required = true) final Long organizationId,
                                  @RequestParam(required = true) final String materialGetType,
                                  @RequestParam(required = true) final Long projectId) {
        List<MaterialReturnDetailTemplateExcelVo> excelVos = null;
        try {
            excelVos = FileUtil.importExcel(file, MaterialReturnDetailTemplateExcelVo.class, 1, 0);
        } catch (Exception e) {
            throw new MipException("模板解析异常");
        }
        if (CollectionUtils.isEmpty(excelVos)) {
            throw new MipException("当前导入Excel模板中无法匹配有效的记录，请检查");
        }
        Map<String, Object> resultMap = buildErrMsgInfo(excelVos, organizationId, materialGetType, projectId);
        DataResponse<Map<String, Object>> response = Response.dataResponse();
        return response.setData(resultMap);
    }

    private Map<String, Object> buildErrMsgInfo(List<MaterialReturnDetailTemplateExcelVo> excelVos,
                                                Long organizationId,
                                                String materialGetType,
                                                Long projectId) {

        //根据库存组织查询物料
        final Map<String, Object> param = new HashMap<>();
        param.put("organizationId", organizationId);
        param.put("materialGetType", materialGetType);
        param.put("itemCodes", excelVos.stream().map(MaterialReturnDetailTemplateExcelVo::getErpCode).filter(Objects::nonNull).collect(Collectors.joining(",")));
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/material/selectListByOrg", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<MaterialDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<MaterialDto>>>() {
        });
        List<MaterialDto> materialList = response.getData();
        Map<String, MaterialDto> materialMap = materialList.stream().collect(Collectors.toMap(MaterialDto::getItemCode, Function.identity(), (a, b) -> a));

        //查询项目下所有wbs
        param.put("projectId", projectId);
        final String url1 = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "milepostDesignPlan/getWbsLastLayer", param);
        final String res1 = restTemplate.getForEntity(url1, String.class).getBody();
        DataResponse<List<MilepostDesignPlanDetailDto>> response1 = JSON.parseObject(res1, new TypeReference<DataResponse<List<MilepostDesignPlanDetailDto>>>() {
        });
        List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailList = response1.getData();
        Map<String, MilepostDesignPlanDetailDto> milepostDesignPlanDetailMap = milepostDesignPlanDetailList.stream()
                .filter(e -> StringUtils.isNotEmpty(e.getWbsSummaryCode()))
                .collect(Collectors.toMap(MilepostDesignPlanDetailDto::getWbsSummaryCode, Function.identity(), (a, b) -> a));

        List<String> errMsgList = new ArrayList<>();
        List<MaterialDto> dataList = new ArrayList<>();
        for (int i = 0; i < excelVos.size(); i++) {
            MaterialReturnDetailTemplateExcelVo excelVo = excelVos.get(i);
            //校验wbs
            if (StringUtils.isNotEmpty(excelVo.getWbsSummaryCode())) {
                MilepostDesignPlanDetailDto milepostDesignPlanDetailDto = milepostDesignPlanDetailMap.get(excelVo.getWbsSummaryCode());
                if (milepostDesignPlanDetailDto == null) {
                    errMsgList.add(String.format("行：%s，wbs不存在", i + 3));
                } else {
                    excelVo.setWbsDescription(milepostDesignPlanDetailDto.getWbsDescription());
                }
            }
            //校验申请退回数量
            if (StringUtils.isNotEmpty(excelVo.getApplyNum())) {
                if (BigDecimalUtils.isBigDecimal(excelVo.getApplyNum())) {
                    BigDecimal applyAmount = new BigDecimal(excelVo.getApplyNum());
                    if (applyAmount.compareTo(BigDecimal.ZERO) < 0) {
                        errMsgList.add(String.format("行：%s，申请退回数量填写有误", i + 3));
                    }
                    excelVo.setApplyAmount(applyAmount);
                } else {
                    errMsgList.add(String.format("行：%s，申请退回数量填写有误", i + 3));
                }
            }
            //校验物料编码
            if (StringUtils.isEmpty(excelVo.getErpCode())) {
                errMsgList.add(String.format("行：%s，物料编码缺失", i + 3));
            } else {
                MaterialDto item = materialMap.get(excelVo.getErpCode());
                if (item == null) {
                    errMsgList.add(String.format("行：%s，物料在当前OU下不存在", i + 3));
                } else {
                    MaterialDto materialDto = new MaterialDto();
                    BeanUtils.copyProperties(item, materialDto);
                    materialDto.setApplyAmount(Optional.ofNullable(excelVo.getApplyAmount()).orElse(BigDecimal.ZERO));
                    materialDto.setWbsSummaryCode(excelVo.getWbsSummaryCode());
                    materialDto.setWbsDescription(excelVo.getWbsDescription());
                    materialDto.setRemark(excelVo.getRemark());
                    dataList.add(materialDto);
                }
            }
            excelVo.setNum(i + 3);
        }

        //唯一性条件：erpCode + wbsSummaryCode
        Map<String, List<MaterialReturnDetailTemplateExcelVo>> repeatMap = excelVos.stream().collect(Collectors.groupingBy(s -> s.getErpCode() + "_" + s.getWbsSummaryCode()));
        for (String key : repeatMap.keySet()) {
            List<MaterialReturnDetailTemplateExcelVo> repeatList = repeatMap.get(key);
            if (repeatList != null && repeatList.size() > 1) {
                String nums = repeatList.stream().map(s -> "行：" + s.getNum()).collect(Collectors.joining("、"));
                errMsgList.add(String.format("%s重复，请合并填写", nums));
            }
        }

        Map<String, Object> map = new HashMap();
        map.put("flag", CollectionUtils.isEmpty(errMsgList));
        map.put("errMsg", errMsgList);
        if (CollectionUtils.isEmpty(errMsgList)) {
            map.put("dataList", dataList);
        }
        return map;
    }

    @ApiOperation(value = "下载错误数据", notes = "场景：项目退料(WBS)批量导入")
    @PostMapping("/detail/downloadErrorMsg")
    public void downloadErrorMsg(@RequestPart("file") MultipartFile file, @RequestParam String errMsg, HttpServletResponse response) {
        List<String> errMsgList = null;
        try {
            errMsgList = JSONObject.parseArray(errMsg, String.class);
        } catch (Exception e) {
            throw new MipException("参数异常");
        }
        Workbook workbook = null;
        try {
            workbook = WorkbookFactory.create(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(1);
            for (int i = 0; i < errMsgList.size(); ++i) {
                Row row = sheet.createRow(i);
                row.createCell(0).setCellValue(errMsgList.get(i));
            }
        } catch (Exception e) {
            throw new MipException("模板解析异常");
        }
        //导出
        ExportExcelUtil.downLoadExcel("报错信息_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xlsx", response, workbook);
    }

    @ApiOperation(value = "退料单详情检查信息")
    @PostMapping("checkMaterialReturnDetail")
    public Object checkMaterialReturnDetail(@RequestBody @ApiParam(name = "MaterialReturnDetailCheckDto", value = "退料单详情检查信息") MaterialReturnDetailCheckDto dto) {
        final String url = String.format("%smaterialReturn/checkMaterialReturnDetail", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, dto, String.class).getBody();
    }

//    @ApiOperation(value = "测试-处理ERP回调逻辑")
//    @GetMapping("testCallBack")
//    public Object testCallBack(@RequestParam(required = false)String type,
//                               @RequestParam(required = false)Long id,
//                               @RequestParam(required = false)Boolean isSuccess,
//                               @RequestParam(required = false)String msg,
//                               @RequestParam(required = false)String actualCost){
//        final Map<String, Object> param = new HashMap<>();
//        param.put("id", id);
//        param.put("isSuccess", isSuccess);
//        param.put("msg", msg);
//        param.put("actualCost", actualCost);
//        String url ;
//        if(Objects.equals(type,"0")){ //退料单
//            url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "materialReturn/testCallBack", param);
//        }else if (Objects.equals(type,"1")){//领料单
//            url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "materialGet/testCallBack", param);
//        }else {//转移单
//            url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "materialTransfer/testCallBack", param);
//        }
//
//        String res = restTemplate.getForObject(url, String.class);
//        return res;
//
//    }

    @ApiOperation(value = "退料单明细数据导出")
    @GetMapping("exportDetails")
    public void exportDetails(HttpServletResponse response,
                              @RequestParam(required = true) Long id,
                              @RequestParam(required = true) Integer materialReturnType,
                              @RequestParam(required = true) String returnCode) {
        final Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "materialReturn/exportDetails", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<MaterialReturnDetailDto>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<MaterialReturnDetailDto>>>() {
        });
        List<MaterialReturnDetailDto> data = dataResponse.getData();
        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("退料单" + returnCode + "_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");

        if (Objects.equals(2, materialReturnType)) {//项目领料(wbs)
            List<MaterialReturnDetailsWbsExcelVO> excelVOS = BeanConverter.copy(data, MaterialReturnDetailsWbsExcelVO.class);
            for (int i = 0; i < excelVOS.size(); i++) {
                MaterialReturnDetailsWbsExcelVO excelVO = excelVOS.get(i);
                excelVO.setNum(i + 1);
            }
            final Workbook workbook = ExportExcelUtil.buildDefaultSheet(excelVOS, MaterialReturnDetailsWbsExcelVO.class, null, "项目退料明细", true);
            ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
        } else {
            //工单领料/项目领料
            List<MaterialReturnDetailsExcelVO> excelVOS = BeanConverter.copy(data, MaterialReturnDetailsExcelVO.class);
            for (int i = 0; i < excelVOS.size(); i++) {
                MaterialReturnDetailsExcelVO excelVO = excelVOS.get(i);
                excelVO.setNum(i + 1);
            }
            final Workbook workbook = ExportExcelUtil.buildDefaultSheet(excelVOS, MaterialReturnDetailsExcelVO.class, null, "项目领料明细", true);
            ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
        }


    }

}
