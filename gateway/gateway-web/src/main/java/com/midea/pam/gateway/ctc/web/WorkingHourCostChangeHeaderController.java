package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.LaborCostDetailDto;
import com.midea.pam.common.ctc.dto.WorkingHourAccountingDto;
import com.midea.pam.common.ctc.dto.WorkingHourCostChangeHeaderDTO;
import com.midea.pam.common.ctc.excelVo.WorkingHourCostChangeDetailExportVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.utils.BeanConverter;
import com.midea.pam.gateway.common.utils.DateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

@RestController
@RequestMapping("workingHourCostChangeHeader")
@Api("工时变更单")
public class WorkingHourCostChangeHeaderController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "工时变更单", response = WorkingHourCostChangeHeaderDTO.class)
    @GetMapping("getById")
    public com.midea.pam.common.base.Response getById(@RequestParam @ApiParam("批准供应商id") Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/workingHourCostChangeHeader/getById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        com.midea.pam.common.base.DataResponse<WorkingHourCostChangeHeaderDTO> response = JSON.parseObject(res, new TypeReference<com.midea.pam.common.base.DataResponse<WorkingHourCostChangeHeaderDTO>>() {
        });
        return response;
    }


    @ApiOperation(value = "创建工时变更单")
    @PostMapping("addWorkingHourCostChange")
    public Response addWorkingHourCostChange(@RequestBody WorkingHourCostChangeHeaderDTO dto) {
        final String url = String.format("%sworkingHourCostChangeHeader/addWorkingHourCostChange", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "创建工时变更单")
    @PostMapping("fixLevelAndLaborCostType")
    public Response fixLevelAndLaborCostType() {
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/workingHourCostChangeHeader/fixLevelAndLaborCostType", null);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation(value = "工时成本冲销跳转数据")
    @GetMapping("getWorkingAccountCostChange")
    public Response getWorkingAccountCostChange(@RequestParam(required = true) final Integer changeType,
                                                @RequestParam(required = true) final Long historyProjectId,
                                                @RequestParam(required = true) final Long changeProjectId,
                                                @RequestParam(required = false) final String userStr,
                                                @RequestParam(required = false) final Date applyDateStart,
                                                @RequestParam(required = false) final Date applyDateEnd) {
        final Map<String, Object> param = new HashMap<>();
        param.put("changeType", changeType);
        param.put("historyProjectId", historyProjectId);
        param.put("changeProjectId", changeProjectId);
        param.put("userStr", userStr);
        param.put("applyDateStart", applyDateStart);
        param.put("applyDateEnd", applyDateEnd);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/workingHourCostChangeHeader/getWorkingAccountCostChange", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<WorkingHourAccountingDto>>() {
        });
    }

    @ApiOperation(value = "导出工时成本明细")
    @GetMapping("exportDetail")
    public void exportDetail(HttpServletResponse response,
                             @RequestParam(required = true) Long id,
                             @RequestParam(required = true) String code) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/workingHourCostChangeHeader/getById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        com.midea.pam.common.base.DataResponse<WorkingHourCostChangeHeaderDTO> dataResponse = JSON.parseObject(res, new TypeReference<com.midea.pam.common.base.DataResponse<WorkingHourCostChangeHeaderDTO>>() {
        });
        WorkingHourCostChangeHeaderDTO workingHourCostChangeHeaderDTO = dataResponse.getData();
        List<LaborCostDetailDto> dataList = workingHourCostChangeHeaderDTO.getLaborCostDetailDtoNoneList();
        if (CollectionUtils.isEmpty(dataList)) {
            throw new Exception("没有数据");
        }
        // 工时成本明细数据
        List<LaborCostDetailDto> sonLaborCostDetailDtoList = new ArrayList<>();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(dataList)){
            for (LaborCostDetailDto laborCostDetailDto : dataList) {
                if(ListUtils.isEmpty(laborCostDetailDto.getLaborCostDetailDtoList())){
                    continue;
                }
                sonLaborCostDetailDtoList.addAll(laborCostDetailDto.getLaborCostDetailDtoList());
            }
        }
        List<WorkingHourCostChangeDetailExportVo> excelVos = BeanConverter.copy(sonLaborCostDetailDtoList, WorkingHourCostChangeDetailExportVo.class);
        for (int i=0;i<excelVos.size();i++){
            excelVos.get(i).setSerialNumber(i+1);
            excelVos.get(i).setCode(workingHourCostChangeHeaderDTO.getCode());
            excelVos.get(i).setResourceFlag(workingHourCostChangeHeaderDTO.getResourceFlag());
            excelVos.get(i).setChangeType(workingHourCostChangeHeaderDTO.getChangeType());
            excelVos.get(i).setChangeProjectCode(workingHourCostChangeHeaderDTO.getChangeProjectCode());
            excelVos.get(i).setChangeProjectName(workingHourCostChangeHeaderDTO.getChangeProjectName());
            excelVos.get(i).setHistoryProjectCode(workingHourCostChangeHeaderDTO.getHistoryProjectCode());
            excelVos.get(i).setHistoryProjectName(workingHourCostChangeHeaderDTO.getHistoryProjectName());
        }
        //导出操作
        com.midea.pam.gateway.common.utils.ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", WorkingHourCostChangeDetailExportVo.class, this.buildExcelName(code), response);
    }

    private String buildExcelName(String projectCode) {
        return projectCode + "-工时冲销转移-" + DateUtil.format(DateUtil.getCurrentDate(), DateUtil.DATE_INTEGER_PATTERN) + ".xls";
    }

}
