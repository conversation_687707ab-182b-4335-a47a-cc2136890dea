package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import static com.midea.pam.common.base.Response.dataResponse;
import com.midea.pam.common.ctc.dto.PaymentInvoceCheckCancelDto;
import com.midea.pam.common.ctc.dto.PaymentInvoiceDetailDto;
import com.midea.pam.common.ctc.dto.PaymentInvoiceDto;
import com.midea.pam.common.ctc.dto.PaymentInvoiceFreezeRecordDto;
import com.midea.pam.common.ctc.entity.PaymentInvoice;
import com.midea.pam.common.ctc.entity.PaymentInvoiceFreezeRecord;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.ProcessTemplate;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.service.MipWorkflowInnerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

@Api("应付发票明细")
@RestController
@RequestMapping("paymentInvoice")
public class PaymentInvoiceController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private MipWorkflowInnerService mipWorkflowInnerService;


    @ApiOperation(value = "应付发票明细", response = PaymentInvoiceDetailDto.class)
    @GetMapping({"list"})
    public Response list(@RequestParam(required = false) @ApiParam(value = "采购合同id") Long purchaseContractId,
                         @RequestParam(required = false) @ApiParam(value = "销售合同id") String contractIdsStr,
                         @RequestParam(required = false) @ApiParam(value = "发票号") String apInvoiceCode,
                         @RequestParam(required = false) @ApiParam(value = "状态排除") String statusExclude,
                         @RequestParam(required = true) @ApiParam(value = "发票状态") Integer erpStatus) {
        DataResponse<List<PaymentInvoice>> response = dataResponse();
        final Map<String, Object> param = new HashMap<>();
        param.put("purchaseContractId", purchaseContractId);
        param.put("erpStatus", erpStatus);
        param.put("apInvoiceCode", apInvoiceCode);
        param.put("contractIdsStr", contractIdsStr);
        param.put("statusExclude", statusExclude);

        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/paymentInvoice/list", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<PaymentInvoiceDto>>>() {
        });
    }

    @ApiOperation(value = " 可核销的应付发票")
    @GetMapping("listWriteOffInvoice")
    public Response listWriteOffInvoice(@RequestParam(required = true) @ApiParam(value = "采购合同id") Long purchaseContractId,
                                        @RequestParam(required = true) @ApiParam(value = "供应商ID") Long vendorId,
                                        @RequestParam(required = true) @ApiParam(value = "币种") String currency,
                                        @RequestParam(required = false) @ApiParam(value = "币种") String apInvoiceCode) {
        DataResponse<List<PaymentInvoice>> response = dataResponse();
        final Map<String, Object> param = new HashMap<>();
        param.put("purchaseContractId", purchaseContractId);
        param.put("vendorId", vendorId);
        param.put("currency", currency);
        param.put("apInvoiceCode", apInvoiceCode);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/paymentInvoice/listWriteOffInvoice", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<PaymentInvoiceDto>>>() {
        });
    }

    @ApiOperation(value = "应付发票明细", response = PaymentInvoiceDto.class)
    @GetMapping({"view"})
    public Response view(@RequestParam Long id) {
        DataResponse<PaymentInvoiceDto> response = dataResponse();
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);

        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/paymentInvoice/view", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PaymentInvoiceDto>>() {
        });
    }

    @ApiOperation(value = "应付发票写入", response = PaymentInvoiceDetailDto.class)
    @GetMapping({"invoiceImport"})
    public Response invoiceImport(@RequestParam(required = true) @ApiParam(value = "发票信息行ID") Long invoiceDetailId,
                                  @RequestParam(required = false) @ApiParam(value = "入账日期") Date glDate) {
        final Map<String, Object> param = new HashMap<>();
        param.put("invoiceDetailId", invoiceDetailId);
        if (glDate != null) {
            param.put("glDate", glDate.getTime());
        }
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/paymentInvoice/invoiceImport", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "测试关联交易供需关系表")
    @GetMapping({"testJob"})
    public Response testJob() {
        final Map<String, Object> param = new HashMap<>();
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/paymentInvoice/testJob", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "获取关联交易号")
    @GetMapping({"getSeqpkgPortType"})
    public Response getSeqpkgPortType() {
        final Map<String, Object> param = new HashMap<>();
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/paymentInvoice/getSeqpkgPortType", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "采购合同开票保存")
    @PostMapping("saveTheInvoice")
    public Response save(@RequestBody @ApiParam(name = "PaymentInvoiceDto") PaymentInvoiceDto dto) {
        final String url = String.format("%spaymentInvoice/saveTheInvoice", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PaymentInvoiceDto>>() {
        });
    }

    @ApiOperation(value = "更新应付发票总账日期")
    @PostMapping("updateGlDate")
    public Response updateGlDate(@RequestBody @ApiParam(name = "PaymentInvoiceDto") PaymentInvoiceDto dto) {
        final String url = String.format("%spaymentInvoice/updateGlDate", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PaymentInvoiceDto>>() {
        });
    }

    @ApiOperation(value = "更新应付发票发票到期日")
    @PostMapping("updateDueDate")
    public Response updateDueDate(@RequestBody @ApiParam(name = "PaymentInvoiceDto") PaymentInvoiceDto dto) {
        final String url = String.format("%spaymentInvoice/updateDueDate", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PaymentInvoiceDto>>() {
        });
    }

    @ApiOperation(value = "应付发票同步erp")
    @GetMapping("synchToErp")
    public Response synchToErp(@RequestParam String ids) {
        final Map<String, Object> param = new HashMap<>();
        param.put("ids", ids);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/paymentInvoice/synchToErp", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "提交审批")
    @PutMapping("/updateStatusApproval/skipSecurityInterceptor")
    public Response updateStatusApproval(@RequestParam(required = false) Long formInstanceId,
                                         @RequestParam(required = false) String fdInstanceId,
                                         @RequestParam(required = false) String formUrl,
                                         @RequestParam(required = false) String eventName,
                                         @RequestParam(required = false) String handlerId,
                                         @RequestParam(required = false) Long companyId,
                                         @RequestParam(required = false) Long createUserId) {
        String url = String.format("%spaymentInvoice/updateStatusApproval/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "驳回")
    @PutMapping("/updateStatusRefuse/skipSecurityInterceptor")
    public Response updateStatusRefuse(@RequestParam(required = false) Long formInstanceId,
                                       @RequestParam(required = false) String fdInstanceId,
                                       @RequestParam(required = false) String formUrl,
                                       @RequestParam(required = false) String eventName,
                                       @RequestParam(required = false) String handlerId,
                                       @RequestParam(required = false) Long companyId,
                                       @RequestParam(required = false) Long createUserId) {
        String url = String.format("%spaymentInvoice/updateStatusRefuse/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response.setData(responseEntity.getBody());
    }

    @ApiOperation(value = "审批通过")
    @PutMapping("/updateStatusEnable/skipSecurityInterceptor")
    public Response handlerCommunicate(@RequestParam(required = false) Long formInstanceId,
                                       @RequestParam(required = false) String fdInstanceId,
                                       @RequestParam(required = false) String formUrl,
                                       @RequestParam(required = false) String eventName,
                                       @RequestParam(required = false) String handlerId,
                                       @RequestParam(required = false) Long companyId,
                                       @RequestParam(required = false) Long createUserId) {
        String url = String.format("%spaymentInvoice/updateStatusEnable/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "撤回")
    @PutMapping("/updateStatusReturn/skipSecurityInterceptor")
    public Response updateStatusReturn(@RequestParam(required = false) Long formInstanceId,
                                       @RequestParam(required = false) String fdInstanceId,
                                       @RequestParam(required = false) String formUrl,
                                       @RequestParam(required = false) String eventName,
                                       @RequestParam(required = false) String handlerId,
                                       @RequestParam(required = false) Long companyId,
                                       @RequestParam(required = false) Long createUserId) {
        String url = String.format("%spaymentInvoice/updateStatusReturn/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response.setData(responseEntity.getBody());
    }

    @ApiOperation(value = "作废")
    @PutMapping("/abandonChange/skipSecurityInterceptor")
    public Response abandonChange(@RequestParam(required = false) Long formInstanceId,
                                  @RequestParam(required = false) String fdInstanceId,
                                  @RequestParam(required = false) String formUrl,
                                  @RequestParam(required = false) String eventName,
                                  @RequestParam(required = false) String handlerId,
                                  @RequestParam(required = false) Long companyId,
                                  @RequestParam(required = false) Long createUserId) {
        String url = String.format("%spaymentInvoice/abandonChange/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response.setData(responseEntity.getBody());
    }

    @ApiOperation(value = "删除")
    @PutMapping("/deleteChange/skipSecurityInterceptor")
    public Response deleteChange(@RequestParam(required = false) Long formInstanceId,
                                 @RequestParam(required = false) String fdInstanceId,
                                 @RequestParam(required = false) String formUrl,
                                 @RequestParam(required = false) String eventName,
                                 @RequestParam(required = false) String handlerId,
                                 @RequestParam(required = false) Long companyId,
                                 @RequestParam(required = false) Long createUserId) {
        String url = String.format("%spaymentInvoice/deleteChange/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response.setData(responseEntity.getBody());
    }

    @ApiOperation(value = "处理人通过回调")
    @PutMapping("/agreeChange/skipSecurityInterceptor")
    public Response agreeChange(@RequestParam(required = false) Long formInstanceId,
                                @RequestParam(required = false) String fdInstanceId,
                                @RequestParam(required = false) String formUrl,
                                @RequestParam(required = false) String eventName,
                                @RequestParam(required = false) String handlerId,
                                @RequestParam(required = false) Long companyId,
                                @RequestParam(required = false) Long createUserId) {
        String url = String.format("%spaymentInvoice/agreeChange/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response.setData(responseEntity.getBody());
    }



    @ApiOperation(value = "应付发票作废")
    @PostMapping("invalid")
    public Response invalid(@ApiParam(value = "id：应付发票ID") @RequestBody PaymentInvoiceDto paymentInvoiceDto) {
        if (null == paymentInvoiceDto.getId()) {
            throw new BizException(Code.ERROR, "参数id不能为空");
        }
        String url = String.format("%spaymentInvoice/invalid", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForObject(url, paymentInvoiceDto, String.class);
        DataResponse<Boolean> response = JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        //同时作废工作流
        if (response.getCode() == 0) {
            mipWorkflowInnerService.draftAbandon(ProcessTemplate.PAYMENT_INVOICE_APP.getCode(), paymentInvoiceDto.getId());
        }
        return response;
    }

    @ApiOperation(value = "同步付款申请和发票")
    @GetMapping("/paymentSyncData")
    public Response paymentSyncData(@RequestParam(required = false) String lastUpdateDate,@RequestParam(required = false) String paymentApplyCode) {
        DataResponse<String> response = dataResponse();
        final Map<String, Object> param = new HashMap<>();
        param.put("lastUpdateDate", lastUpdateDate);
        param.put("paymentApplyCode",paymentApplyCode);

        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/paymentInvoice/paymentSyncData", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "应付发票批量取消ERP检查")
    @GetMapping("checkCancelErpArray")
    public Response checkCancelErpArray(@ApiParam(value = "应付发票ID集合") @RequestParam String paymentInvoiceIds) {
        final Map<String, Object> param = new HashMap<>();
        param.put("paymentInvoiceIds", paymentInvoiceIds);

        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/paymentInvoice/checkCancelErpArray", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<PaymentInvoceCheckCancelDto>>>() {
        });
    }

    @ApiOperation(value = "应付发票取消ERP")
    @PostMapping("cancelErp")
    public Response cancelErp(@ApiParam(value = "id：应付发票ID") @RequestBody PaymentInvoiceDto paymentInvoiceDto) {
        if (null == paymentInvoiceDto.getId()) {
            throw new BizException(Code.ERROR, "参数id不能为空");
        }
        String url = String.format("%spaymentInvoice/cancelErp", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForObject(url, paymentInvoiceDto, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "查询冻结记录")
    @GetMapping("freeze/page")
    public Response getFreezePage(@RequestParam Long paymentInvoiceId,
                                  @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                  @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put("paymentInvoiceId", paymentInvoiceId);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/paymentInvoice/freeze/page", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<PaymentInvoiceFreezeRecordDto>>>() {
        });
    }

    @ApiOperation(value = "冻结操作")
    @PostMapping("freeze/operate")
    public Response freeze(@RequestBody PaymentInvoiceFreezeRecord paymentInvoiceFreezeRecord) {
        String url = String.format("%spaymentInvoice/freeze/operate", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForObject(url, paymentInvoiceFreezeRecord, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "查询ERP返回的发票信息")
    @GetMapping("queryPaymentInvoiceInfo")
    public Response queryPaymentInvoiceInfo(@RequestParam(required = true) @ApiParam(value = "业务实体id") Long ouId,
                                            @RequestParam(required = false) @ApiParam(value = "创建日期") String createDate,
                                            @RequestParam(required = false) @ApiParam(value = "ap发票编号") String apInvoiceCode){
        final Map<String, Object> param = new HashMap<>();
        param.put("ouId",ouId);
        param.put("createDate",createDate);
        param.put("apInvoiceCode",apInvoiceCode);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/paymentInvoice/queryPaymentInvoiceInfo", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return null;
    }

    @ApiOperation(value = "重新向ERP查询应付发票信息")
    @GetMapping("paymentInvoiceReQueryFromErp")
    public String paymentInvoiceReQueryFromErp(@RequestParam String apInvoiceCode){
        final Map<String, Object> param = new HashMap<>();
        param.put("apInvoiceCode",apInvoiceCode);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/paymentInvoice/paymentInvoiceReQueryFromErp", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return "0";
    }
}
