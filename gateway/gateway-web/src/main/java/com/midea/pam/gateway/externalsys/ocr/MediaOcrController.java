package com.midea.pam.gateway.externalsys.ocr;

import com.midea.pam.common.ctc.dto.PaymentInvoiceDetailDto;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.service.OcrService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;


@Api("对接MIDEA-OCR系统")
@RestController
@RequestMapping(value = {"mediaOcr"})
public class MediaOcrController {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private OcrService ocrService;


    @ApiOperation(value = "发票识别", notes = "场景：录入常规发票，录入罚扣/返利发票")
    @PostMapping("invoiceRecognition")
    public Response invoiceRecognition(@RequestParam MultipartFile file, @RequestParam(required = false) String attribute) {
        DataResponse<List<PaymentInvoiceDetailDto>> dataResponse = Response.dataResponse();
        return dataResponse.setData(ocrService.invoiceRecognition(file, attribute));
    }

    @ApiOperation(value = "混贴识别")
    @PostMapping("cutAndSortRecognition")
    public Response cutAndSortRecognition(@RequestParam MultipartFile file) {
        DataResponse<Object> dataResponse = Response.dataResponse();
        return dataResponse.setData(ocrService.cutAndSortRecognition(file));
    }

}
