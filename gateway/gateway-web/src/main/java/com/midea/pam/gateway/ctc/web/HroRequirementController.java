package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.ctc.dto.HroRequirementChangeDto;
import com.midea.pam.common.ctc.dto.HroRequirementDto;
import com.midea.pam.common.ctc.excelVo.HroRequirementExcelVo;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.HroRequirementStatusEnum;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.gateway.entity.FileInfo;
import com.midea.pam.common.gateway.entity.FileInfoExample;
import com.midea.pam.common.util.*;
import com.midea.pam.gateway.mapper.FileInfoMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Api("人力点工需求")
@RestController
@RequestMapping("hroRequirement")
public class HroRequirementController {

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private FileInfoMapper fileInfoMapper;

    @ApiOperation("暂存或提交人力点工需求")
    @PostMapping("save")
    public Response save(@RequestBody HroRequirementDto hroRequirement){
        String url = ModelsEnum.CTC.getBaseUrl() + "hroRequirement/save";
        return restTemplate.postForObject(url,hroRequirement, DataResponse.class);
    }

    @ApiOperation("单据废弃")
    @GetMapping("abandon")
    public Response abandon(@RequestParam Long id){
        String url = ModelsEnum.CTC.getBaseUrl() + "hroRequirement/abandon?id="+id;
        return restTemplate.getForObject(url, DataResponse.class);
    }

    @ApiOperation("单据回退")
    @GetMapping("fallback")
    public Response fallback(@RequestParam Long id){
        String url = ModelsEnum.CTC.getBaseUrl() + "hroRequirement/fallback?id="+id;
        return restTemplate.getForObject(url, DataResponse.class);
    }

    @ApiOperation("人力点工需求分页查询")
    @GetMapping("page")
    public Response page(HroRequirementDto hroRequirement){
        return pageHroRequirement(hroRequirement);
    }

    @ApiOperation("导出人力点工需求")
    @GetMapping("exportHroRequirement")
    public void exportHroRequirement(HttpServletResponse response, HroRequirementDto hroRequirement){
        hroRequirement.setPageSize(100000);
        DataResponse<PageInfo<HroRequirementDto>> dataResponse = pageHroRequirement(hroRequirement);
        PageInfo<HroRequirementDto> pageInfo = dataResponse.getData();
        if(pageInfo!=null){
            List<HroRequirementDto> hroRequirementDtos = pageInfo.getList();
            List<HroRequirementExcelVo> hroRequirementExcelVos = BeanConverter.copy(hroRequirementDtos, HroRequirementExcelVo.class);
            for (int i = 0; i < hroRequirementExcelVos.size(); i++) {
                hroRequirementExcelVos.get(i).setIndex(i+1);
                hroRequirementExcelVos.get(i).setStatusDesc(
                        HroRequirementStatusEnum.getValue(hroRequirementExcelVos.get(i).getStatus()));
            }
            ExportExcelUtil.exportExcel(hroRequirementExcelVos, null, "Sheet1", HroRequirementExcelVo.class, "人力点工需求单据.xls", response);
        }else{
            throw new BizException(Code.ERROR,"人力点工需求导出失败");
        }
    }

    private DataResponse<PageInfo<HroRequirementDto>> pageHroRequirement(HroRequirementDto hroRequirement){
        String url = ModelsEnum.CTC.getBaseUrl() + "hroRequirement/page";
        Map<String,String> params = new HashMap<>(10);
        params.put("requirementCode",hroRequirement.getRequirementCode());
        if(hroRequirement.getStatusStr()!=null) {
            params.put("statusStr", hroRequirement.getStatusStr());
        }
        params.put("handler",hroRequirement.getHandler());
        params.put("creator",hroRequirement.getCreator());
        params.put("projectCode",hroRequirement.getProjectCode());
        params.put("projectName",hroRequirement.getProjectName());
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if(hroRequirement.getCreateTimeStart()!=null) {
            params.put("createTimeStart", dateFormat.format(hroRequirement.getCreateTimeStart()));
        }
        if(hroRequirement.getCreateTimeEnd()!=null) {
            params.put("createTimeEnd", dateFormat.format(hroRequirement.getCreateTimeEnd()));
        }
        if(hroRequirement.getPageNum()!=null) {
            params.put("pageNum",hroRequirement.getPageNum().toString());
        }
        if(hroRequirement.getPageSize()!=null) {
            params.put("pageSize",hroRequirement.getPageSize().toString());
        }
        url = buildUrl(url,params);
        String res = restTemplate.getForObject(url,String.class);
        DataResponse<PageInfo<HroRequirementDto>> response = null;
        if(res!=null){
            response = JSON.parseObject(res,new TypeReference<DataResponse<PageInfo<HroRequirementDto>>>(){});
        }
        if(response==null || response.getCode()!=0){
            throw new BizException(Code.ERROR,response!=null?response.getMsg():res);
        }
        return response;
    }

    @ApiOperation("详情")
    @GetMapping("detail")
    public Response detail(@RequestParam Long id){
        String url = ModelsEnum.CTC.getBaseUrl() + "hroRequirement/detail?id="+id;
        String resStr = restTemplate.getForObject(url, String.class);
        DataResponse<HroRequirementDto> response = JSON.parseObject(resStr, new TypeReference<DataResponse<HroRequirementDto>>() {
        });
        if(response!=null && response.getCode()==0){
            HroRequirementDto hroRequirement = response.getData();
            if(hroRequirement!=null) {
                if (StringUtils.isNotEmpty(hroRequirement.getAttachmentIds())) {
                    List<Long> ids = Arrays.stream(hroRequirement.getAttachmentIds().split(","))
                            .map(Long::valueOf).collect(Collectors.toList());
                    hroRequirement.setFiles(getFileByIds(ids));
                } else {
                    hroRequirement.setFiles(new ArrayList<>(0));
                }
            }
        }
        return response;
    }

    @ApiOperation("预算查询")
    @PostMapping("getWbsBudget")
    public Response getWbsBudget(@RequestBody HroRequirementDto hroRequirementDto){
        String url = ModelsEnum.CTC.getBaseUrl() + "hroRequirement/getWbsBudget";
        return restTemplate.postForObject(url,hroRequirementDto,DataResponse.class);
    }

    @ApiOperation(value = "废弃变更记录")
    @GetMapping("abandonChange")
    public Response abandonChange(@RequestParam Long changeId){
        String url = ModelsEnum.CTC.getBaseUrl() + "hroRequirement/abandonChange?changeId="+changeId;
        return restTemplate.getForObject(url, DataResponse.class);
    }

    private List<FileInfo> getFileByIds(List<Long> ids){
        FileInfoExample fileInfoExample = new FileInfoExample();
        fileInfoExample.createCriteria().andIdIn(ids);
        List<FileInfo> fileInfoList = fileInfoMapper.selectByExample(fileInfoExample);
        Map<Long, UserInfo> userMap = new HashMap<>(fileInfoList.size());
        fileInfoList.forEach(f->{
            UserInfo userInfo = userMap.computeIfAbsent(f.getCreateBy(), CacheDataUtils::findUserById);
            if(userInfo!=null) {
                f.setCreateName(userInfo.getName());
            }
        });
        return fileInfoList;
    }

    @ApiOperation(value = "审批提交回调")
    @PutMapping("workflow/callback/draftSubmit/skipSecurityInterceptor")
    public Response draftSubmitCallback(@RequestParam(required = false) String formInstanceId,
                                      @RequestParam(required = false) String handlerId) {
        String url = ModelsEnum.CTC.getBaseUrl() + "hroRequirement/workflow/callback/draftSubmit/skipSecurityInterceptor";
        url += "?formInstanceId="+(formInstanceId!=null?formInstanceId:"");
        url += "&handlerId="+(handlerId!=null?handlerId:"");
        restTemplate.put(url, String.class);
        return Response.response();
    }

    @ApiOperation(value = "审批驳回回调")
    @PutMapping("workflow/callback/refuse/skipSecurityInterceptor")
    public Response refuseCallback(@RequestParam(required = false) String formInstanceId,
                                 @RequestParam(required = false) String handlerId) {
        String url = ModelsEnum.CTC.getBaseUrl() + "hroRequirement/workflow/callback/refuse/skipSecurityInterceptor";
        url += "?formInstanceId="+(formInstanceId!=null?formInstanceId:"");
        url += "&handlerId="+(handlerId!=null?handlerId:"");
        restTemplate.put(url, String.class);
        return Response.response();
    }

    @ApiOperation(value = "审批通过回调")
    @PutMapping("workflow/callback/pass/skipSecurityInterceptor")
    public Response passCallback(@RequestParam(required = false) String formInstanceId,
                               @RequestParam(required = false) String handlerId,
                               @RequestParam(required = false) Long companyId) {
        String url = ModelsEnum.CTC.getBaseUrl() + "hroRequirement/workflow/callback/pass/skipSecurityInterceptor";
        url += "?formInstanceId="+(formInstanceId!=null?formInstanceId:"");
        url += "&handlerId="+(handlerId!=null?handlerId:"");
        url += "&companyId="+(companyId!=null?companyId:"");
        restTemplate.put(url, String.class);
        return Response.response();
    }

    @ApiOperation(value = "撤回审批回调")
    @PutMapping("workflow/callback/draftReturn/skipSecurityInterceptor")
    public Response draftReturnCallback(@RequestParam(required = false) String formInstanceId,
                                      @RequestParam(required = false) String handlerId) {
        String url = ModelsEnum.CTC.getBaseUrl() + "hroRequirement/workflow/callback/draftReturn/skipSecurityInterceptor";
        url += "?formInstanceId="+(formInstanceId!=null?formInstanceId:"");
        url += "&handlerId="+(handlerId!=null?handlerId:"");
        restTemplate.put(url, String.class);
        return Response.response();
    }

    @ApiOperation(value = "作废审批回调")
    @PutMapping("workflow/callback/abandon/skipSecurityInterceptor")
    public Response abandonCallback(@RequestParam(required = false) String formInstanceId,
                                  @RequestParam(required = false) String handlerId) {
        String url = ModelsEnum.CTC.getBaseUrl() + "hroRequirement/workflow/callback/abandon/skipSecurityInterceptor";
        url += "?formInstanceId="+(formInstanceId!=null?formInstanceId:"");
        url += "&handlerId="+(handlerId!=null?handlerId:"");
        restTemplate.put(url, String.class);
        return Response.response();
    }

    @ApiOperation(value = "变更")
    @PostMapping("saveChange")
    public Response saveChange(@RequestBody HroRequirementChangeDto hroRequirementChangeDto){
        String url = ModelsEnum.CTC.getBaseUrl() + "hroRequirement/saveChange";
        return restTemplate.postForObject(url,hroRequirementChangeDto,DataResponse.class);
    }

    @ApiOperation(value = "获取历史变更列表")
    @GetMapping("getHistoryChangeList")
    public Response getHistoryChangeList(@RequestParam Long requirementId){
        String url = ModelsEnum.CTC.getBaseUrl() + "hroRequirement/getHistoryChangeList?requirementId="+requirementId;
        return restTemplate.getForObject(url,DataResponse.class);
    }

    @ApiOperation(value = "获取变更明细")
    @GetMapping("getChangeDetail")
    public Response getChangeDetail(@RequestParam Long changeId){
        String url = ModelsEnum.CTC.getBaseUrl() + "hroRequirement/getChangeDetail?changeId="+changeId;
        String resStr = restTemplate.getForObject(url, String.class);
        DataResponse<HroRequirementChangeDto> response = JSON.parseObject(resStr, new TypeReference<DataResponse<HroRequirementChangeDto>>() {
        });
        if(response!=null && response.getCode()==0){
            HroRequirementChangeDto changeDto = response.getData();
            if(StringUtils.isNotEmpty(changeDto.getAttachmentIds())) {
                List<Long> ids = Arrays.stream(changeDto.getAttachmentIds().split(","))
                        .map(Long::valueOf).collect(Collectors.toList());
                changeDto.setFiles(getFileByIds(ids));
            }else{
                changeDto.setFiles(new ArrayList<>(0));
            }
        }
        return response;
    }



    @ApiOperation(value = "审批提交回调")
    @PutMapping("change/workflow/callback/draftSubmit/skipSecurityInterceptor")
    public Response changeDraftSubmitCallback(@RequestParam(required = false) String formInstanceId,
                                            @RequestParam(required = false) String handlerId) {
        String url = ModelsEnum.CTC.getBaseUrl() + "hroRequirement/change/workflow/callback/draftSubmit/skipSecurityInterceptor";
        url += "?formInstanceId="+(formInstanceId!=null?formInstanceId:"");
        url += "&handlerId="+(handlerId!=null?handlerId:"");
        restTemplate.put(url, String.class);
        return Response.response();
    }

    @ApiOperation(value = "审批驳回回调")
    @PutMapping("change/workflow/callback/refuse/skipSecurityInterceptor")
    public Response changeRefuseCallback(@RequestParam(required = false) String formInstanceId,
                                       @RequestParam(required = false) String handlerId) {
        String url = ModelsEnum.CTC.getBaseUrl() + "hroRequirement/change/workflow/callback/refuse/skipSecurityInterceptor";
        url += "?formInstanceId="+(formInstanceId!=null?formInstanceId:"");
        url += "&handlerId="+(handlerId!=null?handlerId:"");
        restTemplate.put(url, String.class);
        return Response.response();
    }

    @ApiOperation(value = "审批通过回调")
    @PutMapping("change/workflow/callback/pass/skipSecurityInterceptor")
    public Response changePassCallback(@RequestParam(required = false) String formInstanceId,
                                     @RequestParam(required = false) String handlerId,
                                     @RequestParam(required = false) Long companyId) {
        String url = ModelsEnum.CTC.getBaseUrl() + "hroRequirement/change/workflow/callback/pass/skipSecurityInterceptor";
        url += "?formInstanceId="+(formInstanceId!=null?formInstanceId:"");
        url += "&handlerId="+(handlerId!=null?handlerId:"");
        url += "&companyId="+(companyId!=null?companyId:"");
        restTemplate.put(url, String.class);
        return Response.response();
    }

    @ApiOperation(value = "撤回审批回调")
    @PutMapping("change/workflow/callback/draftReturn/skipSecurityInterceptor")
    public Response changeDraftReturnCallback(@RequestParam(required = false) String formInstanceId,
                                            @RequestParam(required = false) String handlerId) {
        String url = ModelsEnum.CTC.getBaseUrl() + "hroRequirement/change/workflow/callback/draftReturn/skipSecurityInterceptor";
        url += "?formInstanceId="+(formInstanceId!=null?formInstanceId:"");
        url += "&handlerId="+(handlerId!=null?handlerId:"");
        restTemplate.put(url, String.class);
        return Response.response();
    }

    @ApiOperation(value = "作废审批回调")
    @PutMapping("change/workflow/callback/abandon/skipSecurityInterceptor")
    public Response changeAbandonCallback(@RequestParam(required = false) String formInstanceId,
                                        @RequestParam(required = false) String handlerId) {
        String url = ModelsEnum.CTC.getBaseUrl() + "hroRequirement/change/workflow/callback/abandon/skipSecurityInterceptor";
        url += "?formInstanceId="+(formInstanceId!=null?formInstanceId:"");
        url += "&handlerId="+(handlerId!=null?handlerId:"");
        restTemplate.put(url, String.class);
        return Response.response();
    }

    private String buildUrl(String url,Map<String,String> params){
        String queryParams = params.entrySet().stream()
                .filter(e -> StringUtils.isNotEmpty(e.getValue()))
                .map(e -> e.getKey() + "=" + e.getValue())
                .collect(Collectors.joining("&"));
        if(!queryParams.isEmpty()){
            url += "?" + queryParams;
        }
        return url;
    }
}
