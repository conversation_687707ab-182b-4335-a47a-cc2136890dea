package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @program: pam
 * @description: ProjectBudgetCostController
 * @author: gaojh1
 * @create: 2019-3-12 10:51
 **/
@RestController
@RequestMapping("projectBudgetCost")
@Api("项目预算执行明细")
public class ProjectBudgetCostController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "项目预算执行明细ems同步")
    @GetMapping("getProjectCostFromEms")
    public Response getProjectCostFromEms(@RequestParam(required = false) Integer maxDelNum) {
        String url = String.format("%sprojectBudgetCost/getProjectCostFromEms?maxDelNum=%s", ModelsEnum.CTC.getBaseUrl(), maxDelNum);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response.setData("0");
    }

    @ApiOperation(value = "修改项目预算变更推送ems记录信息的申请原因")
    @GetMapping("updateProjectCostChangeApplyReason")
    public Response updateProjectCostChangeApplyReason(@RequestParam Long id, @RequestParam String applyReason) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("applyReason", applyReason);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectBudgetCost/updateProjectCostChangeApplyReason", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
    }
}
