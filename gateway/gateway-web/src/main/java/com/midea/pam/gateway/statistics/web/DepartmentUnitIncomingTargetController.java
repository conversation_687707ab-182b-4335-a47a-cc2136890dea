package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.statistics.dto.DepartmentUnitIncomingTargetDto;
import com.midea.pam.common.statistics.entity.DepartmentUnitIncomingTarget;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("事业部或产品的收入目标和利润目标")
@RestController
@RequestMapping("departmentUnitIncomingTarget")
public class DepartmentUnitIncomingTargetController extends ControllerHelper {
    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "按ID删除")
    @GetMapping("deleteById")
    public Response deleteById(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/departmentUnitIncomingTarget/deleteById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {});
    }

    @ApiOperation(value = "新增/修改")
    @PostMapping("save")
    public Response save(@RequestBody DepartmentUnitIncomingTarget departmentUnitIncomingTarget) {
        final String url = String.format("%sdepartmentUnitIncomingTarget/save", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, departmentUnitIncomingTarget, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Long>>() {});
    }

    @ApiOperation("批量保存")
    @PostMapping({"saveBatch"})
    public Response saveBatch(@RequestBody List<DepartmentUnitIncomingTarget> departmentUnitIncomingTargets) {
        String url = String.format("%sdepartmentUnitIncomingTarget/saveBatch",ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, departmentUnitIncomingTargets, String.class);
        DataResponse<Long> response= JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<Long>>(){});
        return response;
    }

    @ApiOperation(value = "按id查询")
    @GetMapping("findById/{id}")
    public Response findById(@PathVariable Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/departmentUnitIncomingTarget/findById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<DepartmentUnitIncomingTargetDto>>() {});
    }

    @ApiOperation(value = " 分页查询")
    @GetMapping("page")
    public Response page(@RequestParam(required = false) @ApiParam("年份名称") String yearName,
                         @RequestParam(required = false) @ApiParam("事业部或产品的ID") Long departmentUnitId,
                         @RequestParam(required = false) @ApiParam("0: 按事业部查询, 1: 按产品查询, 空: 全部") Integer unitType,
                         @RequestParam(required = false) @ApiParam("0: 外部, 1: 内部") Integer innerFlag,
                         @RequestParam(required = false) @ApiParam("0: 利润目标， 1: 收入目标") Integer targetType,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "100") final Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put("yearName", yearName);
        param.put("departmentUnitId", departmentUnitId);
        param.put("unitType", unitType);
        param.put("innerFlag", innerFlag);
        param.put("targetType", targetType);
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/departmentUnitIncomingTarget/page", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<DepartmentUnitIncomingTargetDto>>>() {});
    }

    @ApiOperation(value = "列表查询")
    @GetMapping("list")
    public Response list(@RequestParam(required = false) @ApiParam("年份名称") String yearName,
                         @RequestParam(required = false) @ApiParam("事业部或产品的ID") Long departmentUnitId,
                         @RequestParam(required = false) @ApiParam("0: 按事业部查询, 1: 按产品查询, 空: 全部") Integer unitType,
                         @RequestParam(required = false) @ApiParam("0: 外部, 1: 内部") Integer innerFlag,
                         @RequestParam(required = false) @ApiParam("0: 利润目标， 1: 收入目标") Integer targetType) {
        final Map<String, Object> param = new HashMap<>();
        param.put("yearName", yearName);
        param.put("departmentUnitId", departmentUnitId);
        param.put("unitType", unitType);
        param.put("innerFlag", innerFlag);
        param.put("targetType", targetType);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/departmentUnitIncomingTarget/list", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<DepartmentUnitIncomingTargetDto>>>() {});
    }

}
