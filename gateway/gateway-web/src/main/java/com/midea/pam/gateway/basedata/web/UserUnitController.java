package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.UnitDto;
import com.midea.pam.common.basedata.dto.UserOuRelDto;
import com.midea.pam.common.basedata.dto.UserUnitRelBatchSaveDto;
import com.midea.pam.common.basedata.dto.UserUnitRelDto;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: pam
 * @description: 用户虚拟部门 网关控制层
 * @author: fangyl
 * @create: 2019-4-11
 **/
@RestController
@RequestMapping("userUnit")
@Api("用户虚拟部门")
public class UserUnitController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "分页查询用户与虚拟部门关系")
    @GetMapping("userUnitPage")
    public Response userUnitPage(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                 @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                                 @RequestParam(required = false) Long userId) {
        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        param.put("userId", userId);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "userUnit/userUnitPage", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<UserUnitRelDto>>>() {
        });
    }

    @ApiOperation(value = "获取用户的虚拟部门")
    @GetMapping("userUnitList")
    public Response userUnitList(@RequestParam(required = true) Long userId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("userId", userId);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "userUnit/userUnitList", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<UserUnitRelDto>>>() {
        });
    }

    @ApiOperation(value = "分页查询用户与业务实体关系")
    @GetMapping("userOuPage")
    public Response userOuPage(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                               @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                               @RequestParam(required = false) Long userId,
                               @RequestParam(required = false) Long unitId) {
        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        param.put("userId", userId);
        param.put("unitId", unitId);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "userUnit/userOuPage", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<UserOuRelDto>>>() {
        });
    }

    @ApiOperation(value = "保存用户数据权限（虚拟部门和业务实体）")
    @PostMapping("save")
    public Response save(@RequestBody UserUnitRelBatchSaveDto userUnitRelBatchSaveDto) {
        final String url = String.format("%suserUnit/save", ModelsEnum.BASEDATA.getBaseUrl());
        String res = restTemplate.postForEntity(url, userUnitRelBatchSaveDto, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "分页查询用户未授权的虚拟部门信息")
    @GetMapping("pageUnAuthUnit")
    public Response pageUnAuthUnit(@RequestParam(required = false) Long userId,
                                   @RequestParam(required = false) String unitName,
                                   @RequestParam(required = false) String bizUnitName,
                                   @RequestParam(required = false) String bizUnitIds,
                                   @RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                   @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        param.put("userId", userId);
        param.put("unitName", unitName);
        param.put("bizUnitName", bizUnitName);
        param.put("bizUnitIds", bizUnitIds);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "userUnit/pageUnAuthUnit", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<UnitDto>>>() {
        });
    }

    /**
     * 根据用户id查询用户授权的二级虚拟部门
     * @param userId
     * @param parentId
     * @return
     */
    @ApiOperation(value = "根据用户id查询用户授权的二级虚拟部门")
    @GetMapping("queryUserAvailableList")
    public Response queryUserAvailableList(@RequestParam(required = false) Long userId,
                                           @RequestParam(required = false) String parentId){
        final Map<String, Object> param = new HashMap<>();
        param.put("userId",userId);
        param.put("parentId",parentId);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "userUnit/queryUserAvailableList", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res,new TypeReference<DataResponse<List<UnitDto>>>(){});

    }

}
