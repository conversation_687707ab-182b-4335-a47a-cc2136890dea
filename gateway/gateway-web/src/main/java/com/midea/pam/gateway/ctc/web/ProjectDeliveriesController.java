package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.ctc.dto.ProjectDeliveriesDto;
import com.midea.pam.common.ctc.dto.ProjectMilepostDto;
import com.midea.pam.common.enums.DeleteFlagEnum;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.ProcessTemplate;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.FormInstanceExample;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.service.FormInstanceService;
import com.midea.pam.gateway.service.MipWorkflowInnerService;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@Api("里程碑附件交付")
@RequestMapping({"projectDeliveries"})
public class ProjectDeliveriesController extends ControllerHelper {

    private final String PROJECT_DELIVERS_ID = "projectDeliveriesApp";

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private MipWorkflowInnerService mipWorkflowInnerService;
    @Resource
    private FormInstanceService formInstanceService;


    @ApiOperation(value = "里程碑交付项目列表", response = ProjectMilepostDto.class)
    @GetMapping({"milestoneDeliveryProjectList"})
    public Response milestoneDeliveryProjectList() {
        final Map<String, Object> param = new HashMap<>();
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectDeliveries/milestoneDeliveryProjectList", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<ProjectMilepostDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<ProjectMilepostDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "里程碑列表", response = ProjectMilepostDto.class)
    @GetMapping({"selectListWithDetail"})
    public Response selectListWithDetail(@RequestParam @ApiParam("项目id") Long projectId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectDeliveries/selectListWithDetail", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<ProjectMilepostDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<ProjectMilepostDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "根据项目Id与里程牌序号orderNum来获取开始时间", response = ProjectMilepostDto.class)
    @GetMapping({"selectTempStartEndTime"})
    public Response selectTempStartEndTime(@RequestParam @ApiParam("项目id") Long projectId,
                                           @RequestParam @ApiParam("里程牌序号") Integer orderNum,
                                           @RequestParam @ApiParam("是否副里程碑0:否，1:是") Boolean helpFlag) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        param.put("orderNum", orderNum);
        param.put("helpFlag", helpFlag);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectDeliveries/selectTempStartEndTime", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<ProjectMilepostDto> response = JSON.parseObject(res, new TypeReference<DataResponse<ProjectMilepostDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "暂定开始时间与结束时间", response = Boolean.class)
    @PostMapping({"saveTempStartEndTime"})
    public Response saveTempStartEndTime(@RequestBody ProjectMilepostDto dto) {
        //BUG2023052395008 A君点了交付流程初始化后没点提交，B君无法操作提交   涵盖：附件交付/结项交付
        //解决方法：B君操作时调起工作流之前将A君的流程实例逻辑删除掉
        FormInstanceExample example = new FormInstanceExample();
        example.createCriteria().andFormInstanceIdEqualTo(dto.getId())
                                .andDeletedFlagEqualTo(DeleteFlagEnum.NOT_DELETED.getBolValue())
                                .andFormUrlIn(Arrays.asList(ProcessTemplate.PROJECT_DELIVERIES_APP.getCode(), ProcessTemplate.PROJECT_CLOSE_MILESTONE_APP.getCode()))
                                .andWfStatusIsNull();
        List<FormInstance> instances = formInstanceService.selectByExample(example);
        if (instances.size() > 0) {
            instances.sort(Comparator.comparing(FormInstance::getId).reversed());
            FormInstance formInstance = instances.get(0);
            if (!Objects.equals(formInstance.getCreateBy(), SystemContext.getUserId())) {
                formInstance.setDeletedFlag(Boolean.TRUE);
                formInstanceService.updateByPrimaryKeySelective(formInstance);
            }
        }
        String url = String.format("%sprojectDeliveries/saveTempStartEndTime", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<String> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
        return response;
    }

    @ApiOperation(value = "获取里程碑", response = ProjectMilepostDto.class)
    @GetMapping({"getWithDetailById"})
    public Response getWithDetailById(@RequestParam @ApiParam("里程碑id") Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectDeliveries/getWithDetailById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<ProjectMilepostDto> response = JSON.parseObject(res, new TypeReference<DataResponse<ProjectMilepostDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "获取里程碑", response = ProjectMilepostDto.class)
    @GetMapping({"getWithDetailByIdExt"})
    public Response getWithDetailByIdExt(@RequestParam @ApiParam("里程碑id") Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectDeliveries/getWithDetailByIdExt", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<ProjectMilepostDto> response = JSON.parseObject(res, new TypeReference<DataResponse<ProjectMilepostDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "保存里程碑交付物", response = Boolean.class)
    @PostMapping({"saveWithDetail"})
    public Response saveWithDetail(@RequestBody ProjectMilepostDto dto) {
        String url = String.format("%sprojectDeliveries/saveWithDetail", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<ProjectMilepostDto> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<ProjectMilepostDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "更新里程碑", response = Boolean.class)
    @PostMapping({"update"})
    public Response update(@RequestBody ProjectDeliveriesDto dto) {
        String url = String.format("%sprojectDeliveries/update", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<Boolean> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }


    @ApiOperation(value = "发起审批")
    @PutMapping("updateStatusChecking/skipSecurityInterceptor")
    public Response updateStatusChecking(@RequestParam(required = false) Long formInstanceId,
                                         @RequestParam(required = false) String fdInstanceId,
                                         @RequestParam(required = false) String formUrl,
                                         @RequestParam(required = false) String eventName,
                                         @RequestParam(required = false) String handlerId,
                                         @RequestParam(required = false) Long companyId,
                                         @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sprojectDeliveries/updateStatusChecking/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "驳回")
    @PutMapping("updateStatusReject/skipSecurityInterceptor")
    public Response updateStatusReject(@RequestParam(required = false) Long formInstanceId,
                                       @RequestParam(required = false) String fdInstanceId,
                                       @RequestParam(required = false) String formUrl,
                                       @RequestParam(required = false) String eventName,
                                       @RequestParam(required = false) String handlerId,
                                       @RequestParam(required = false) Long companyId,
                                       @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sprojectDeliveries/updateStatusReject/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "通过")
    @PutMapping("updateStatusPass/skipSecurityInterceptor")
    public Response updateStatusPass(@RequestParam(required = false) Long formInstanceId,
                                     @RequestParam(required = false) String fdInstanceId,
                                     @RequestParam(required = false) String formUrl,
                                     @RequestParam(required = false) String eventName,
                                     @RequestParam(required = false) String handlerId,
                                     @RequestParam(required = false) Long companyId,
                                     @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sprojectDeliveries/updateStatusPass/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "起草/撤回(工作流回调接口)")
    @PutMapping("updateStatusDraft/skipSecurityInterceptor")
    public Response updateStatusDraft(@RequestParam(required = false) Long formInstanceId,
                                      @RequestParam(required = false) String fdInstanceId,
                                      @RequestParam(required = false) String formUrl,
                                      @RequestParam(required = false) String eventName,
                                      @RequestParam(required = false) String handlerId,
                                      @RequestParam(required = false) Long companyId,
                                      @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sprojectDeliveries/updateStatusDraft/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "撤回(详细设计里程碑点击撤回按钮)")
    @PutMapping("updateStatusDraft")
    public Response updateStatusDraftReturn(@RequestParam(required = false) String formInstanceId, @RequestParam(required = false) String handlerId) {
        String url = String.format("%sprojectDeliveries/updateStatusDraftReturn/skipSecurityInterceptor?formInstanceId=%s&handlerId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId, handlerId);
        restTemplate.put(url, String.class);
        //同时撤回工作流
        mipWorkflowInnerService.draftReturn(PROJECT_DELIVERS_ID, Long.parseLong(formInstanceId));
        return Response.dataResponse();
    }

    @ApiOperation(value = "废弃")
    @PutMapping("abandon/skipSecurityInterceptor")
    public Response abandon(@RequestParam(required = false) Long formInstanceId,
                            @RequestParam(required = false) String fdInstanceId,
                            @RequestParam(required = false) String formUrl,
                            @RequestParam(required = false) String eventName,
                            @RequestParam(required = false) String handlerId,
                            @RequestParam(required = false) Long companyId,
                            @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sprojectDeliveries/abandon/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "删除")
    @PutMapping("delete/skipSecurityInterceptor")
    public Response delete(@RequestParam(required = false) Long formInstanceId,
                           @RequestParam(required = false) String fdInstanceId,
                           @RequestParam(required = false) String formUrl,
                           @RequestParam(required = false) String eventName,
                           @RequestParam(required = false) String handlerId,
                           @RequestParam(required = false) Long companyId,
                           @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sprojectDeliveries/delete/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }
    @ApiOperation(value = "处理人通过")
    @PutMapping("agree/skipSecurityInterceptor")
    public Response agree(@RequestParam(required = false) Long formInstanceId,
                           @RequestParam(required = false) String fdInstanceId,
                           @RequestParam(required = false) String formUrl,
                           @RequestParam(required = false) String eventName,
                           @RequestParam(required = false) String handlerId,
                           @RequestParam(required = false) Long companyId,
                           @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sprojectDeliveries/agree/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }
}
