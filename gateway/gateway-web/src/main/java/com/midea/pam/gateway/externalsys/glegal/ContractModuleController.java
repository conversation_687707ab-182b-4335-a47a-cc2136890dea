package com.midea.pam.gateway.externalsys.glegal;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ContractStatus;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.GlegalContractEnums;
import com.midea.pam.common.enums.WorkflowOperationType;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.gateway.dto.GleContractFlowDTO;
import com.midea.pam.common.gateway.dto.PushContractInfoDTO;
import com.midea.pam.common.gateway.entity.GleContractFlow;
import com.midea.pam.common.gateway.entity.GleContractFlowExample;
import com.midea.pam.common.gateway.entity.GlegalFileInfo;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.base.Code;
import com.midea.pam.gateway.common.utils.PamCurrentUserUtil;
import com.midea.pam.gateway.config.GlegalContractProperties;
import com.midea.pam.gateway.glegal.service.ContractModuleService;
import com.midea.pam.gateway.glegal.service.GlegalInterfaceV2Service;
import com.midea.pam.gateway.mapper.GleContractFlowMapper;
import com.midea.pam.gateway.vo.ContractWorkflowDraftForm;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: common-module
 * @description: 合同对接全球法务系统
 * @author:zhongpeng
 * @create:2020-09-15 11:29
 **/
@Api("销售/采购合同对接全球法务系统")
@RestController
@RequestMapping(value = {"contractModule"})
public class ContractModuleController {
    private static Logger logger = LoggerFactory.getLogger(ContractModuleController.class);

    @Resource
    private ContractModuleService contractModuleService;
    @Resource
    private GlegalContractProperties glegalContractProperties;
    @Resource
    private GlegalInterfaceV2Service glegalInterfaceV2Service;
    @Resource
    private GleContractFlowMapper gleContractFlowMapper;


    @ApiOperation("推送新建的销售/采购合同信息给法务系统（草稿）")
    @GetMapping({"pushContractInfo"})
    public Response pushContractInfo(@RequestParam String contractType,
                                     @RequestParam Long formInstanceId,
                                     @RequestParam Long companyId) {
        DataResponse<String> responseError = Response.dataResponse();
        try {
            DataResponse<JSONObject> response = Response.dataResponse();
            if (contractType.equals(GlegalContractEnums.CONTRACT_TYPE.getCode())) { //销售合同
                final JSONObject jsonObject = contractModuleService.getContractInfo(formInstanceId);
                if (jsonObject != null && jsonObject.getInteger("code") != null && ErrorCode.SUCCESS.getCode() != jsonObject.getInteger("code")) {
                    responseError.setCode(ErrorCode.SUCCESS);
                    responseError.setMsg("ERROR");
                    responseError.setData(jsonObject.getString("msg"));
                    return responseError;
                }
                JSONObject data = jsonObject.getJSONObject("data");
                if (data == null) {
                    responseError.setCode(ErrorCode.SUCCESS);
                    responseError.setMsg("ERROR");
                    responseError.setData(jsonObject.getString("msg"));
                    return responseError;
                }
                final GleContractFlowDTO gleContractFlowDTO = JSONObject.parseObject(data.toString(), GleContractFlowDTO.class);
                GleContractFlowExample gleContractFlowExample = new GleContractFlowExample();
                gleContractFlowExample.setOrderByClause("create_at desc");
                gleContractFlowExample.createCriteria().andContractIdEqualTo(formInstanceId).andContractTypeEqualTo(0).andDataTypeEqualTo(1);
                final List<GleContractFlow> gleContractFlowList = gleContractFlowMapper.selectByExample(gleContractFlowExample);
                if (CollectionUtils.isNotEmpty(gleContractFlowList)) {
                    final GleContractFlow gleContractFlow = gleContractFlowList.get(0);
                    gleContractFlow.setFlowId(gleContractFlowDTO.getFlowId());
                    gleContractFlow.setSourceSystemNumber(gleContractFlowDTO.getSourceSystemNumber());
                    gleContractFlow.setBusinessId(gleContractFlowDTO.getBusinessId());
                    gleContractFlow.setContractCode(gleContractFlowDTO.getContractNumber());
                    gleContractFlow.setCompanyId(companyId != null ? companyId : SystemContext.getUnitId());
                    gleContractFlowMapper.updateByPrimaryKey(gleContractFlow);

                    //返回的信息插入合同
                    contractModuleService.callWriteGleInfo2Contract(formInstanceId, gleContractFlowDTO.getFlowId(), gleContractFlowDTO.getBusinessId(), gleContractFlowDTO.getContractNumber(), "0", null, data.getString("url"));

                } else {
                    throw new MipException("数据错误，请联系管理员！");
                }

                return response.setData(jsonObject);
            } else if (contractType.equals(GlegalContractEnums.PURCHASE_CONTRACT_TYPE.getCode())) { //采购合同
                final JSONObject jsonObject = contractModuleService.getPurchaseContractInfo(formInstanceId);
                if (jsonObject != null && jsonObject.getInteger("code") != null && ErrorCode.SUCCESS.getCode() != jsonObject.getInteger("code")) {
                    responseError.setCode(ErrorCode.SUCCESS);
                    responseError.setMsg("ERROR");
                    responseError.setData(jsonObject.getString("msg"));
                    return responseError;
                }
                JSONObject data = jsonObject.getJSONObject("data");
                if (data == null) {
                    responseError.setCode(ErrorCode.SUCCESS);
                    responseError.setMsg("ERROR");
                    responseError.setData(jsonObject.getString("msg"));
                    return responseError;
                }
                final GleContractFlowDTO gleContractFlowDTO = JSONObject.parseObject(jsonObject.get("data").toString(), GleContractFlowDTO.class);
                GleContractFlowExample gleContractFlowExample = new GleContractFlowExample();
                gleContractFlowExample.setOrderByClause("create_at desc");
                gleContractFlowExample.createCriteria().andContractIdEqualTo(formInstanceId).andContractTypeEqualTo(1).andDataTypeEqualTo(1);
                final List<GleContractFlow> gleContractFlowList = gleContractFlowMapper.selectByExample(gleContractFlowExample);
                if (CollectionUtils.isNotEmpty(gleContractFlowList)) {
                    final GleContractFlow gleContractFlow = gleContractFlowList.get(0);
                    gleContractFlow.setFlowId(gleContractFlowDTO.getFlowId());
                    gleContractFlow.setSourceSystemNumber(gleContractFlowDTO.getSourceSystemNumber());
                    gleContractFlow.setBusinessId(gleContractFlowDTO.getBusinessId());
                    gleContractFlow.setContractCode(gleContractFlowDTO.getContractNumber());
                    gleContractFlow.setCompanyId(companyId != null ? companyId : SystemContext.getUnitId());
                    gleContractFlowMapper.updateByPrimaryKey(gleContractFlow);
                    //返回的信息插入合同
                    contractModuleService.callWriteGleInfo2Contract(formInstanceId, gleContractFlowDTO.getFlowId(), gleContractFlowDTO.getBusinessId(), gleContractFlowDTO.getContractNumber(), "1", null, jsonObject.getJSONObject("data").getString("url"));
                } else {
                    throw new MipException("数据错误，请联系管理员！");
                }

                return response.setData(jsonObject);
            }
            return null;
        } catch (Exception e) {
            logger.error("error", e);
            responseError.setCode(Code.ERROR.getCode());
            responseError.setMsg(e.getMessage());
            return responseError.setData(e.getMessage());
        }
    }

    @ApiOperation("推送新建的销售/采购合同信息给法务系统(审批中)")
    @PutMapping({"v2/pushContractInfoSubmit"})
    public Response pushContractInfoSubmitNew(@RequestBody ContractWorkflowDraftForm form) {
        DataResponse<String> responseError = Response.dataResponse();
        try {
            GleContractFlowExample gleContractFlowExample = new GleContractFlowExample();
            gleContractFlowExample.setOrderByClause("create_at desc");
            gleContractFlowExample.createCriteria().andContractIdEqualTo(Long.valueOf(form.getFormInstanceId()))
                    .andContractTypeEqualTo(Integer.parseInt(form.getContractType()))
                    .andDataTypeEqualTo(1);
            final List<GleContractFlow> gleContractFlows = gleContractFlowMapper.selectByExample(gleContractFlowExample);
            if (CollectionUtils.isEmpty(gleContractFlows)) {
                throw new MipException("法务合同流程信息查询失败，请联系管理员！");
            }
            final GleContractFlow gleContractFlow = gleContractFlows.get(0);

            String loginName = PamCurrentUserUtil.getCurrentUserName();
            if (form.getOpt() == null) {
                form.setOpt(getOpt(loginName, form.getFdId(), WorkflowOperationType.DRAFT_SUBMIT.getName()));
            }
            form.setSubject(gleContractFlow.getContralName() + gleContractFlow.getContractCode());
            JSONObject flowInfoJson = contractModuleService.buildFlowInfo(form.getOpt(), loginName, form.getFdId(), form.getAuditNote(),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeHandler())),
                    Arrays.asList(String.format("关于%s的合同申请流程", form.getSubject()), String.format("Application process for %s", form.getSubject())));

            // 更新对应的合同流程信息
            gleContractFlow.setFlowInfo(flowInfoJson.toJSONString());
            gleContractFlowMapper.updateByPrimaryKey(gleContractFlow);

            DataResponse<JSONObject> response = Response.dataResponse();

            JSONObject jsonObject = null;
            if (form.getContractType().equals(GlegalContractEnums.CONTRACT_TYPE.getCode())) { //销售合同
                jsonObject = contractModuleService.submitContractInfo(Long.valueOf(form.getFormInstanceId()), GlegalContractEnums.CONTRACT_NEW.getCode(), flowInfoJson);
                if (jsonObject != null && Objects.equals(ErrorCode.SUCCESS.getCode(), jsonObject.getInteger("code"))) {
                    //更新合同状态为审核中
                    contractModuleService.approvingContract(Long.valueOf(form.getFormInstanceId()), "0", "2");
                }
            } else if (form.getContractType().equals(GlegalContractEnums.PURCHASE_CONTRACT_TYPE.getCode())) { //采购合同
                jsonObject = contractModuleService.submitPurchaseContractInfo(Long.valueOf(form.getFormInstanceId()), GlegalContractEnums.CONTRACT_NEW.getCode(), flowInfoJson);
                if (jsonObject != null && Objects.equals(ErrorCode.SUCCESS.getCode(), jsonObject.getInteger("code"))) {
                    //更新采购合同状态为审核中
                    contractModuleService.approvingContract(Long.valueOf(form.getFormInstanceId()), "1", "2");
                }
            }
            if (jsonObject != null && jsonObject.getInteger("code") != null && ErrorCode.SUCCESS.getCode() != jsonObject.getInteger("code")) {
                responseError.setCode(ErrorCode.SUCCESS);
                responseError.setMsg("ERROR");
                responseError.setData(jsonObject.getString("msg"));
                return responseError;
            }
            return response.setData(jsonObject);

        } catch (Exception e) {
            logger.error("error", e);
            responseError.setCode(Code.ERROR.getCode());
            responseError.setMsg(e.getMessage());
            return responseError.setData(e.getMessage());
        }
    }


    @ApiOperation("推送变更的合同信息给法务系统(草稿)")
    @GetMapping("pushModifyContractInfo")
    public Response pushModifyContractInfo(@RequestParam String contractType,
                                           @RequestParam Long formInstanceId,
                                           @RequestParam Long companyId) {
        DataResponse<String> responseError = Response.dataResponse();
        try {
            DataResponse<JSONObject> response = Response.dataResponse();
            if (contractType.equals("0")) { //销售合同
                final JSONObject jsonObject = contractModuleService.getModifyContractInfo(formInstanceId);
                if (jsonObject.getString("code").equals("1")) {
                    responseError.setCode(ErrorCode.SUCCESS);
                    responseError.setMsg("ERROR");
                    responseError.setData(jsonObject.getString("msg"));
                    return responseError;
                }
                final GleContractFlowDTO gleContractFlowDTO = JSONObject.parseObject(jsonObject.get("data").toString(), GleContractFlowDTO.class);
                GleContractFlowExample gleContractFlowExample = new GleContractFlowExample();
                gleContractFlowExample.setOrderByClause("create_at desc");
                gleContractFlowExample.createCriteria().andContractIdEqualTo(formInstanceId).andContractTypeEqualTo(0).andDataTypeEqualTo(2);
                final List<GleContractFlow> gleContractFlowList = gleContractFlowMapper.selectByExample(gleContractFlowExample);
                if (CollectionUtils.isNotEmpty(gleContractFlowList)) {
                    final GleContractFlow gleContractFlow = gleContractFlowList.get(0);
                    gleContractFlow.setFlowId(gleContractFlowDTO.getFlowId());
                    gleContractFlow.setSourceSystemNumber(gleContractFlowDTO.getSourceSystemNumber());
                    gleContractFlow.setBusinessId(gleContractFlowDTO.getBusinessId());
                    gleContractFlow.setContractCode(gleContractFlowDTO.getContractNumber());
                    gleContractFlow.setCompanyId(companyId != null ? companyId : SystemContext.getUnitId());
                    gleContractFlowMapper.updateByPrimaryKey(gleContractFlow);
                    //返回的信息插入变更头
                    contractModuleService.callWriteGleInfo2ModifyContract(formInstanceId, gleContractFlowDTO.getFlowId(), "0", null, jsonObject.getJSONObject("data").getString("url"));
                } else {
                    throw new MipException("数据错误，请联系管理员！");
                }
                return response.setData(jsonObject);
            } else if (contractType.equals("1")) { //采购合同
                final JSONObject jsonObject = contractModuleService.getModifyPurchaseContractInfo(formInstanceId);
                if (jsonObject.getString("code").equals("1")) {
                    responseError.setCode(ErrorCode.SUCCESS);
                    responseError.setMsg("ERROR");
                    responseError.setData(jsonObject.getString("msg"));
                    return responseError;
                }
                final GleContractFlowDTO gleContractFlowDTO = JSONObject.parseObject(jsonObject.get("data").toString(), GleContractFlowDTO.class);
                GleContractFlowExample gleContractFlowExample = new GleContractFlowExample();
                gleContractFlowExample.setOrderByClause("create_at desc");
                gleContractFlowExample.createCriteria().andContractIdEqualTo(formInstanceId).andContractTypeEqualTo(1).andDataTypeEqualTo(2);
                final List<GleContractFlow> gleContractFlowList = gleContractFlowMapper.selectByExample(gleContractFlowExample);
                if (CollectionUtils.isNotEmpty(gleContractFlowList)) {
                    final GleContractFlow gleContractFlow = gleContractFlowList.get(0);
                    gleContractFlow.setFlowId(gleContractFlowDTO.getFlowId());
                    gleContractFlow.setSourceSystemNumber(gleContractFlowDTO.getSourceSystemNumber());
                    gleContractFlow.setBusinessId(gleContractFlowDTO.getBusinessId());
                    gleContractFlow.setContractCode(gleContractFlowDTO.getContractNumber());
                    gleContractFlow.setCompanyId(companyId != null ? companyId : SystemContext.getUnitId());
                    gleContractFlowMapper.updateByPrimaryKey(gleContractFlow);
                    //返回的信息插入变更头
                    contractModuleService.callWriteGleInfo2ModifyContract(formInstanceId, gleContractFlowDTO.getFlowId(), "1", null, jsonObject.getJSONObject("data").getString("url"));
                } else {
                    throw new MipException("数据错误，请联系管理员！");
                }
                return response.setData(jsonObject);
            }
            return null;
        } catch (Exception e) {
            logger.error("error", e);
            responseError.setCode(Code.ERROR.getCode());
            responseError.setMsg(e.getMessage());
            return responseError.setData(e.getMessage());
        }
    }

    @ApiOperation("推送变更的合同信息给法务系统(审批中)")
    @PutMapping({"v2/pushModifyContractInfoSubmit"})
    public Response pushModifyContractInfoSubmitNew(@RequestBody ContractWorkflowDraftForm form) {
        DataResponse<String> responseError = Response.dataResponse();
        try {
            GleContractFlowExample gleContractFlowExample = new GleContractFlowExample();
            gleContractFlowExample.setOrderByClause("create_at desc");
            gleContractFlowExample.createCriteria().andContractIdEqualTo(Long.valueOf(form.getFormInstanceId()))
                    .andContractTypeEqualTo(Integer.parseInt(form.getContractType()))
                    .andDataTypeEqualTo(2);
            final List<GleContractFlow> gleContractFlows = gleContractFlowMapper.selectByExample(gleContractFlowExample);
            if (CollectionUtils.isEmpty(gleContractFlows)) {
                throw new MipException("法务合同流程信息查询失败，请联系管理员！");
            }
            final GleContractFlow gleContractFlow = gleContractFlows.get(0);

            String loginName = PamCurrentUserUtil.getCurrentUserName();
            if (form.getOpt() == null) {
                form.setOpt(getOpt(loginName, form.getFdId(), WorkflowOperationType.DRAFT_SUBMIT.getName()));
            }
            form.setSubject(gleContractFlow.getContralName() + gleContractFlow.getContractCode());
            JSONObject flowInfoJson = contractModuleService.buildFlowInfo(form.getOpt(), loginName, form.getFdId(), form.getAuditNote(),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeHandler())),
                    Arrays.asList(String.format("关于%s的合同变更流程", form.getSubject()), String.format("Amendment process for %s", form.getSubject())));

            // 更新对应的合同流程信息
            gleContractFlow.setFlowInfo(flowInfoJson.toJSONString());
            gleContractFlowMapper.updateByPrimaryKey(gleContractFlow);

            DataResponse<JSONObject> response = Response.dataResponse();

            JSONObject jsonObject = null;
            if (form.getContractType().equals(GlegalContractEnums.CONTRACT_TYPE.getCode())) { //销售合同
                jsonObject = contractModuleService.submitModifyContractInfo(Long.valueOf(form.getFormInstanceId()), GlegalContractEnums.CONTRACT_MODIFY.getCode(), flowInfoJson);
                if (jsonObject != null && Objects.equals(ErrorCode.SUCCESS.getCode(), jsonObject.getInteger("code"))) {
                    //更新合同状态为变更中
                    contractModuleService.updateContractAndChangeway(Long.valueOf(form.getFormInstanceId()),GlegalContractEnums.CONTRACT_MODIFY.getCode());
                }
            } else if (form.getContractType().equals(GlegalContractEnums.PURCHASE_CONTRACT_TYPE.getCode())) { //采购合同
                jsonObject = contractModuleService.submitModifyPurchaseContractInfo(Long.valueOf(form.getFormInstanceId()), GlegalContractEnums.CONTRACT_MODIFY.getCode(), flowInfoJson);
                if (jsonObject != null && Objects.equals(ErrorCode.SUCCESS.getCode(), jsonObject.getInteger("code"))) {
                    //更新采购合同状态为变更中
                    contractModuleService.updatePurchaseContractAndChangeway(Long.valueOf(form.getFormInstanceId()));
                }
            }
            if (jsonObject != null && jsonObject.getInteger("code") != null && ErrorCode.SUCCESS.getCode() != jsonObject.getInteger("code")) {
                responseError.setCode(ErrorCode.SUCCESS);
                responseError.setMsg("ERROR");
                responseError.setData(jsonObject.getString("msg"));
                return responseError;
            }
            return response.setData(jsonObject);

        } catch (Exception e) {
            logger.error("error", e);
            responseError.setCode(Code.ERROR.getCode());
            responseError.setMsg(e.getMessage());
            return responseError.setData(e.getMessage());
        }
    }

    @ApiOperation("推送废弃合同新增信息给法务系统")
    @PostMapping("v2/pushAbandonContractInfo")
    public Response pushAbandonContractInfoNew(@RequestBody PushContractInfoDTO pushContractInfoDTO) {
        DataResponse<String> responseError = Response.dataResponse();
        try {
            DataResponse<JSONObject> response = Response.dataResponse();
            JSONObject jsonObject = new JSONObject();
            if (pushContractInfoDTO.getOperationType().equals("1")) {  // 前端暂无调用
                // 根据流程id，删除法务的流程
                jsonObject = contractModuleService.getAbandonContractInfo(pushContractInfoDTO.getFlowId());
            } else if (pushContractInfoDTO.getOperationType().equals("2")) { //撤回或实例化的草稿、驳回
                GleContractFlowExample gleContractFlowExample = new GleContractFlowExample();
                gleContractFlowExample.setOrderByClause("create_at desc");
                gleContractFlowExample.createCriteria().andContractIdEqualTo(Long.valueOf(pushContractInfoDTO.getFormInstanceId()))
                        .andFlowIdEqualTo(pushContractInfoDTO.getFlowId())
                        .andContractTypeEqualTo(Integer.parseInt(pushContractInfoDTO.getContractType()))
                        .andDataTypeEqualTo(1);
                final List<GleContractFlow> gleContractFlows = gleContractFlowMapper.selectByExample(gleContractFlowExample);
                if (CollectionUtils.isEmpty(gleContractFlows)) {
                    throw new MipException("法务合同流程信息查询异常，请联系管理员！");
                }
                GleContractFlow gleContractFlow = gleContractFlows.get(0);
                JSONObject flowInfoJson;
                if (StringUtils.isNotEmpty(gleContractFlow.getFlowInfo())) {
                    flowInfoJson = JSONObject.parseObject(gleContractFlow.getFlowInfo());
                } else {
                    String subject = gleContractFlow.getContralName() + gleContractFlow.getContractCode();
                    flowInfoJson = this.buildDraftAbandonFlowInfo(null, gleContractFlow.getCreateUserCode(), gleContractFlow.getFlowId(), "",
                            null,
                            Arrays.asList(String.format("关于%s的合同申请流程", subject), String.format("Application process for %s", subject)));
                    // 更新对应的合同流程信息
                    gleContractFlow.setFlowInfo(flowInfoJson.toJSONString());
                    gleContractFlowMapper.updateByPrimaryKey(gleContractFlow);
                }
                if (flowInfoJson.getJSONObject("processParam") != null) {
                    //获取作废按钮信息
                    JSONObject opt = getOpt(PamCurrentUserUtil.getCurrentUserName(), pushContractInfoDTO.getFlowId(), WorkflowOperationType.DRAFT_ABANDON.getName());
                    if (opt.getJSONArray("operationTaskList") != null && opt.getJSONArray("operationTaskList").size() > 0) {
                        JSONObject task = (JSONObject) opt.getJSONArray("operationTaskList").get(0);

                        //任务ID（在获取可用操作接口中得到）,processParam中的operationType值不是draft【保存草稿】时，本参数必填
                        flowInfoJson.getJSONObject("processParam").put("taskId", task.getString("fdTaskId"));
                        //节点任务类型（在获取可用操作接口中得到）,processParam中的operationType值不是draft【保存草稿】时，本参数必填
                        flowInfoJson.getJSONObject("processParam").put("activityType", task.getString("fdActivityType"));
                    }
                    flowInfoJson.getJSONObject("processParam").put("operationType", opt.getString("operationType"));  //draft_abandon
                }

                if (GlegalContractEnums.CONTRACT_TYPE.getCode().equals(pushContractInfoDTO.getContractType())) {
                    jsonObject = contractModuleService.submitContractInfo(Long.valueOf(pushContractInfoDTO.getFormInstanceId()), GlegalContractEnums.CONTRACT_NEW.getCode(), flowInfoJson);
                } else if (GlegalContractEnums.PURCHASE_CONTRACT_TYPE.getCode().equals(pushContractInfoDTO.getContractType())) {
                    jsonObject = contractModuleService.submitPurchaseContractInfo(Long.valueOf(pushContractInfoDTO.getFormInstanceId()), GlegalContractEnums.CONTRACT_NEW.getCode(), flowInfoJson);
                }

            }
            if (!Objects.equals(jsonObject.getString("code"), "0")) {
                responseError.setCode(ErrorCode.SUCCESS);
                responseError.setMsg("ERROR");
                responseError.setData(jsonObject.getString("msg"));
                return responseError;
            }
            //更新合同状态
            contractModuleService.callWriteGleInfo2Contract(Long.valueOf(pushContractInfoDTO.getFormInstanceId()), pushContractInfoDTO.getFlowId(), null, null, pushContractInfoDTO.getContractType(), ContractStatus.CANCEL.getCode(), null);
            return response.setData(jsonObject);
        } catch (Exception e) {
            logger.error("error", e);
            responseError.setCode(Code.ERROR.getCode());
            responseError.setMsg(e.getMessage());
            return responseError.setData(e.getMessage());
        }
    }

    private JSONObject buildDraftAbandonFlowInfo(JSONObject opt, String loginName, String fdId, String auditNote, JSONArray changeNodeHandler, List<String> subjectList) {
        //================================ 构建formParam ================================
        JSONObject formParam = new JSONObject();
        JSONArray subjectForMultiLanguages = new JSONArray();
        JSONObject jsonSubject = new JSONObject();
        jsonSubject.put("languageType", "zh-CN");
        jsonSubject.put("subject", subjectList.get(0));
        subjectForMultiLanguages.add(jsonSubject);
        jsonSubject = new JSONObject();
        jsonSubject.put("languageType", "en-US");
        jsonSubject.put("subject", subjectList.get(1));
        subjectForMultiLanguages.add(jsonSubject);
        formParam.put("subjectForMultiLanguages", subjectForMultiLanguages);
        formParam.put("mainLanguageType4Subject", "zh-CN");

        //================================ 构建processParam ================================
        JSONObject processParam = new JSONObject();
        //审批人流程处理方式  废弃
        processParam.put("operationType", "draft_abandon");
        //流程处理意见
        processParam.put("auditNote", auditNote);
        processParam.put("jumpToNodeId", "");
        processParam.put("manualBranchToNodes", new ArrayList<>());
        processParam.put("refusePassedToAllApprover", Boolean.TRUE);
        processParam.put("refusePassedToThisNode", Boolean.TRUE);
        processParam.put("toOtherPersons", "");

        JSONObject json = new JSONObject();
        json.put("loginName", loginName);
        json.put("fdId", fdId);
        json.put("fdTemplateCode", "mls_contract_CPAM");
        json.put("formParam", formParam);
        json.put("processParam", processParam);
        json.put("auditFileDocIdList", Arrays.asList(""));

        return json;
    }

    @ApiOperation("推送废弃合同变更信息给法务系统")
    @PostMapping("v2/pushAbandonModifyContractInfo")
    public Response pushAbandonModifyContractInfoNew(@RequestBody PushContractInfoDTO pushContractInfoDTO) {
        DataResponse<String> responseError = Response.dataResponse();
        try {
            DataResponse<JSONObject> response = Response.dataResponse();
            JSONObject jsonObject = new JSONObject();

            if (pushContractInfoDTO.getOperationType().equals("1")) { //前端暂无调用
                //根据流程id，删除法务的流程
                jsonObject = contractModuleService.getAbandonContractInfo(pushContractInfoDTO.getFlowId());
            } else if (pushContractInfoDTO.getOperationType().equals("2")) { //撤回或实例化的草稿、驳回
                GleContractFlowExample gleContractFlowExample = new GleContractFlowExample();
                gleContractFlowExample.setOrderByClause("create_at desc");
                gleContractFlowExample.createCriteria().andContractIdEqualTo(Long.valueOf(pushContractInfoDTO.getFormInstanceId()))
                        .andFlowIdEqualTo(pushContractInfoDTO.getFlowId())
                        .andContractTypeEqualTo(Integer.parseInt(pushContractInfoDTO.getContractType()))
                        .andDataTypeEqualTo(2);
                final List<GleContractFlow> gleContractFlows = gleContractFlowMapper.selectByExample(gleContractFlowExample);
                if (CollectionUtils.isEmpty(gleContractFlows)) {
                    throw new MipException("法务合同流程信息查询异常，请联系管理员！");
                }
                GleContractFlow gleContractFlow = gleContractFlows.get(0);
                JSONObject flowInfoJson;
                if (StringUtils.isNotEmpty(gleContractFlow.getFlowInfo())) {
                    flowInfoJson = JSONObject.parseObject(gleContractFlow.getFlowInfo());
                } else {
                    String subject = gleContractFlow.getContralName() + gleContractFlow.getContractCode();
                    flowInfoJson = this.buildDraftAbandonFlowInfo(null, gleContractFlow.getCreateUserCode(), gleContractFlow.getFlowId(), "",
                            null,
                            Arrays.asList(String.format("关于%s的合同变更流程", subject), String.format("Amendment process for %s", subject)));
                    // 更新对应的合同流程信息
                    gleContractFlow.setFlowInfo(flowInfoJson.toJSONString());
                    gleContractFlowMapper.updateByPrimaryKey(gleContractFlow);
                }

                if (flowInfoJson.getJSONObject("processParam") != null) {
                    //获取作废按钮信息
                    JSONObject opt = getOpt(PamCurrentUserUtil.getCurrentUserName(), pushContractInfoDTO.getFlowId(), WorkflowOperationType.DRAFT_ABANDON.getName());
                    if (opt.getJSONArray("operationTaskList") != null && opt.getJSONArray("operationTaskList").size() > 0) {
                        JSONObject task = (JSONObject) opt.getJSONArray("operationTaskList").get(0);

                        //任务ID（在获取可用操作接口中得到）,processParam中的operationType值不是draft【保存草稿】时，本参数必填
                        flowInfoJson.getJSONObject("processParam").put("taskId", task.getString("fdTaskId"));
                        //节点任务类型（在获取可用操作接口中得到）,processParam中的operationType值不是draft【保存草稿】时，本参数必填
                        flowInfoJson.getJSONObject("processParam").put("activityType", task.getString("fdActivityType"));
                    }
                    flowInfoJson.getJSONObject("processParam").put("operationType", opt.getString("operationType"));  //draft_abandon
                }

                if (GlegalContractEnums.CONTRACT_TYPE.getCode().equals(pushContractInfoDTO.getContractType())) {
                    jsonObject = contractModuleService.submitModifyContractInfo(Long.valueOf(pushContractInfoDTO.getFormInstanceId()), GlegalContractEnums.CONTRACT_MODIFY.getCode(), flowInfoJson);
                } else if (GlegalContractEnums.PURCHASE_CONTRACT_TYPE.getCode().equals(pushContractInfoDTO.getContractType())) {
                    jsonObject = contractModuleService.submitModifyPurchaseContractInfo((Long.valueOf(pushContractInfoDTO.getFormInstanceId())), GlegalContractEnums.CONTRACT_MODIFY.getCode(), flowInfoJson);
                }
            }else if (pushContractInfoDTO.getOperationType().equals("0")) { //合同终止废弃
                GleContractFlowExample gleContractFlowExample = new GleContractFlowExample();
                gleContractFlowExample.setOrderByClause("create_at desc");
                gleContractFlowExample.createCriteria().andContractIdEqualTo(Long.valueOf(pushContractInfoDTO.getFormInstanceId()))
                        .andFlowIdEqualTo(pushContractInfoDTO.getFlowId())
                        .andContractTypeEqualTo(Integer.parseInt(pushContractInfoDTO.getContractType()))
                        .andDataTypeEqualTo(0);
                final List<GleContractFlow> gleContractFlows = gleContractFlowMapper.selectByExample(gleContractFlowExample);
                if (CollectionUtils.isEmpty(gleContractFlows)) {
                    throw new MipException("法务合同流程信息查询异常，请联系管理员！");
                }
                GleContractFlow gleContractFlow = gleContractFlows.get(0);
                JSONObject flowInfoJson = JSONObject.parseObject(gleContractFlow.getFlowInfo());
                if (flowInfoJson.getJSONObject("processParam") != null) {
                    //获取作废按钮信息
                    JSONObject opt = getOpt(PamCurrentUserUtil.getCurrentUserName(), pushContractInfoDTO.getFlowId(), WorkflowOperationType.DRAFT_ABANDON.getName());
                    if (opt.getJSONArray("operationTaskList") != null && opt.getJSONArray("operationTaskList").size() > 0) {
                        JSONObject task = (JSONObject) opt.getJSONArray("operationTaskList").get(0);

                        //任务ID（在获取可用操作接口中得到）,processParam中的operationType值不是draft【保存草稿】时，本参数必填
                        flowInfoJson.getJSONObject("processParam").put("taskId", task.getString("fdTaskId"));
                        //节点任务类型（在获取可用操作接口中得到）,processParam中的operationType值不是draft【保存草稿】时，本参数必填
                        flowInfoJson.getJSONObject("processParam").put("activityType", task.getString("fdActivityType"));
                    }
                    flowInfoJson.getJSONObject("processParam").put("operationType", opt.getString("operationType"));  //draft_abandon
                }
                jsonObject = contractModuleService.submitModifyContractInfo(Long.valueOf(pushContractInfoDTO.getFormInstanceId()), GlegalContractEnums.CONTRACT_TERMINATION.getCode(), flowInfoJson);
            }
            if (!Objects.equals(jsonObject.getString("code"), "0")) {
                responseError.setCode(ErrorCode.SUCCESS);
                responseError.setMsg("ERROR");
                responseError.setData(jsonObject.getString("msg"));
                return responseError;
            }
            //更新变更头状态
            contractModuleService.callWriteGleInfo2ModifyContract(Long.valueOf(pushContractInfoDTO.getFormInstanceId()), null, pushContractInfoDTO.getContractType(), null, null);
            return response.setData(jsonObject);
        } catch (Exception e) {
            logger.error("error", e);
            responseError.setCode(Code.ERROR.getCode());
            responseError.setMsg(e.getMessage());
            return responseError.setData(e.getMessage());
        }
    }

    @ApiOperation("合同终止草稿提交")
    @GetMapping("pushTerminationSaveDraft")
    public Response pushTerminationSaveDraft(@RequestParam String contractType,
                                           @RequestParam Long formInstanceId,
                                           @RequestParam Long companyId) {
        DataResponse<String> responseError = Response.dataResponse();
        try {
            DataResponse<JSONObject> response = Response.dataResponse();
            if (contractType.equals("0")) { //销售合同
                final JSONObject jsonObject = contractModuleService.getTerminationContractInfo(formInstanceId);
                if (jsonObject.getString("code").equals("1")) {
                    responseError.setCode(ErrorCode.SUCCESS);
                    responseError.setMsg("ERROR");
                    responseError.setData(jsonObject.getString("msg"));
                    return responseError;
                }
                /**
                 * {
                 *   "code" : "0",
                 *   "msg" : "Success",
                 *   "timestamp" : "2024-09-09 15:45:25.331",
                 *   "data" : {
                 *     "businessId" : "1282728569169088512",
                 *     "contractNumber" : "GL0124090200001",
                 *     "flowId" : "344115895774822418",
                 *     "url" : "https://glegalsit.midea.com/index.html#/contract/cpam/terminated/1833049046953611265"
                 *   }
                 * }
                 */
                GleContractFlowDTO gleContractFlowDTO = JSONObject.parseObject(jsonObject.get("data").toString(), GleContractFlowDTO.class);
                GleContractFlowExample gleContractFlowExample = new GleContractFlowExample();
                gleContractFlowExample.setOrderByClause("create_at desc");
                gleContractFlowExample.createCriteria().andContractIdEqualTo(formInstanceId).andContractTypeEqualTo(0).andDataTypeEqualTo(0);
                final List<GleContractFlow> gleContractFlowList = gleContractFlowMapper.selectByExample(gleContractFlowExample);
                if (CollectionUtils.isNotEmpty(gleContractFlowList)) {
                    GleContractFlow gleContractFlow = gleContractFlowList.get(0);
                    gleContractFlow.setFlowId(gleContractFlowDTO.getFlowId());
                    gleContractFlow.setSourceSystemNumber(gleContractFlowDTO.getSourceSystemNumber());
                    gleContractFlow.setBusinessId(gleContractFlowDTO.getBusinessId());
                    gleContractFlow.setContractCode(gleContractFlowDTO.getContractNumber());
                    gleContractFlow.setCompanyId(companyId != null ? companyId : SystemContext.getUnitId());
                    gleContractFlowMapper.updateByPrimaryKey(gleContractFlow);
                    //返回的信息插入变更头
                    contractModuleService.callWriteGleInfo2ModifyContract(formInstanceId, gleContractFlowDTO.getFlowId(), "0", null, jsonObject.getJSONObject("data").getString("url"));
                } else {
                    throw new MipException("数据错误，请联系管理员！");
                }
                return response.setData(jsonObject);
            }
            return null;
        } catch (Exception e) {
            logger.error("error", e);
            responseError.setCode(Code.ERROR.getCode());
            responseError.setMsg(e.getMessage());
            return responseError.setData(e.getMessage());
        }
    }

    @ApiOperation(value = "上传附件到全球法务系统")
    @PostMapping("glegal/upload")
    public Response glegalUpload(@RequestParam("file") MultipartFile file, String fileSign, String fileAttachmentId) {
        try {
            return Response.dataResponse(contractModuleService.upload(file, fileSign, fileAttachmentId));
        } catch (Exception e) {
            logger.error("glegal upload error", e);
            return Response.err(e.getMessage());
        }
    }

    @ApiOperation(value = "从全球法务系统获取预览附件地址")
    @GetMapping("glegal/review")
    public Response glegalReview(@RequestParam String attachmentId,
                                 @RequestParam String fileId,
                                 @RequestParam String mipAccount) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("attachmentId", attachmentId);
            jsonObject.put("fileId", fileId);
            jsonObject.put("mipAccount", mipAccount);
            return Response.dataResponse(contractModuleService.glegalReview(jsonObject));
        } catch (Exception e) {
            logger.error("glegal review error", e);
            return Response.err(e.getMessage());
        }
    }

    @ApiOperation(value = "下载附件到全球法务系统")
    @GetMapping("glegal/download")
    public void glegalDownload(@RequestParam String attachmentId,
                               @RequestParam String fileId,
                               @RequestParam String mipAccount,
                               HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        if (StringUtils.isEmpty(fileId)){
            throw new BizException(ErrorCode.ERROR.getCode(), "获取文件ID失败，请刷新页面重新操作!");
        }
        try {
            Map<String, String> params = new HashMap<>();
            params.put("attachmentId", attachmentId);
            params.put("fileId", fileId);
            params.put("mipAccount", mipAccount);
            contractModuleService.download(params, httpServletRequest, httpServletResponse);
        } catch (Exception e) {
            logger.error("glegal download error -> {}", e.getMessage());
            throw new BizException(ErrorCode.ERROR.getCode(), "法务文件信息获取失败，请确认文件是否有效!");
        }
    }


    @ApiOperation(value = "全球法务系统删除附件")
    @GetMapping("glegal/delFile")
    public Response glegalDelFile(@RequestParam String attachmentId,
                                  @RequestParam String fileId,
                                  @RequestParam String mipAccount) {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("attachmentId", attachmentId);
            params.put("fileId", fileId);
            params.put("mipAccount", mipAccount);
            return Response.dataResponse(contractModuleService.delFile(params));
        } catch (Exception e) {
            logger.error("glegal delFile error -> {}", e.getMessage());
            return Response.err(e.getMessage());
        }
    }

    @ApiOperation(value = "查询附件")
    @GetMapping("glegal/glegalQueryFile")
    public Response glegalQueryFile(@RequestParam(required = false) Long id,
                                    @RequestParam(required = false) String idStr,
                                    @RequestParam(required = false) String docId,
                                    @RequestParam(required = false) String fileAttachmentId,
                                    @RequestParam(required = false) String fileName) {
        try {
            Map<String, Object> params = new HashMap<>();
            if (id != null) {
                params.put("id", id);
            }
            if (StringUtils.isNotEmpty(idStr)) {
                final List<String> stringList = Arrays.stream(idStr.split(",")).map(String::valueOf)
                        .collect(Collectors.toList());
                params.put("idList", stringList);
            }
            if (StringUtils.isNotEmpty(docId)) {
                params.put("docId", docId);
            }
            if (StringUtils.isNotEmpty(fileAttachmentId)) {
                params.put("fileAttachmentId", fileAttachmentId);
            }
            if (StringUtils.isNotEmpty(fileName)) {
                params.put("fileName", fileName);
            }
            final List<GlegalFileInfo> glegalFileInfos = contractModuleService.glegalQueryFile(params);
            Map<String, GlegalFileInfo> collect = glegalFileInfos.stream()
                    .collect(Collectors.toMap(GlegalFileInfo::getDocId, s -> s, (o1, o2) -> o1));

            return Response.dataResponse(collect.values().stream().sorted(Comparator.comparing(GlegalFileInfo::getCreateAt)).collect(Collectors.toList()));
        } catch (Exception e) {
            logger.error("glegal queryfile error -> {}", e.getMessage());
            return Response.err(e.getMessage());
        }
    }

    @ApiOperation(value = "获取档案信息分页列表")
    @PostMapping("glegal/selectContractInfoAuth")
    public Response glegalContractInfoAuth(@RequestParam(required = false) String contractCode,
                                           @RequestParam(required = false) String contractName) {
        try {
            JSONObject params = new JSONObject();
            params.put("contractCode", contractCode);
            params.put("contractName", contractName);
            params.put("contractSource", glegalContractProperties.getAppId());
            return Response.dataResponse(contractModuleService.selectContractInfoAuth(params));
        } catch (Exception e) {
            logger.error("glegal selectContractInfoAuth error -> {}", e.getMessage());
            return Response.err(e.getMessage());
        }
    }

    @ApiOperation(value = "获取法务合同档案授权token")
    @PostMapping("glegal/getContactToken")
    public Response getContactToken(@RequestParam(required = false) String contractCode,
                                    @RequestParam(required = false) String contractName) {
        try {
            JSONObject params = new JSONObject();
            return Response.dataResponse(contractModuleService.getContactToken(params));
        } catch (Exception e) {
            logger.error("glegal getContactToken error -> {}", e.getMessage());
            return Response.err(e.getMessage());
        }
    }

    @ApiOperation("新建销售/采购合同判断前端穿入的attachmentId是否已经存在")
    @GetMapping("checkAttachmentId")
    public Response checkAttachmentId(@RequestParam(required = true) String attachmentId) {
        DataResponse<Boolean> response = Response.dataResponse();
        try {
            Boolean result = contractModuleService.checkAttachmentId(attachmentId);
            //1：已经存在  0:未存在
            response.setData(result);
            response.setCode(Code.SUCCESS.getCode());
            response.setMsg("SUCCESS");
        } catch (Exception e) {
            logger.error("error", e);
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
        }
        return response;
    }

    @ApiOperation(value = "获取流程表格信息")
    @GetMapping("glegal/getProcessTableInfoAndManualNodes")
    public Response getProcessTableInfoAndManualNodes(@RequestParam String fdId) {
        try {
            JSONObject params = new JSONObject();
            params.put("fdId", fdId);
            params.put("loginName", PamCurrentUserUtil.getCurrentUserName());
            return Response.dataResponse(contractModuleService.getProcessTableInfoAndManualNodes(params));
        } catch (Exception e) {
            logger.error("glegal getProcessTableInfoAndManualNodes error -> {}", e.getMessage());
            return Response.err(e.getMessage());
        }
    }

    @ApiOperation(value = "获取操作按钮信息列表")
    @GetMapping("glegal/getOperationList")
    public Response getOperationList(@RequestParam String fdId) {
        try {
            JSONObject params = new JSONObject();
            params.put("fdId", fdId);
            params.put("loginName", PamCurrentUserUtil.getCurrentUserName());
            return Response.dataResponse(contractModuleService.getOperationList(params));
        } catch (Exception e) {
            logger.error("glegal getOperationList error -> {}", e.getMessage());
            return Response.err(e.getMessage());
        }
    }

    @ApiOperation(value = "获取流程审批意见")
    @GetMapping("glegal/getAuditeNote")
    public Response getAuditeNote(@RequestParam String fdId) {
        try {
            JSONObject params = new JSONObject();
            params.put("fdId", fdId);
            params.put("loginName", PamCurrentUserUtil.getCurrentUserName());
            return Response.dataResponse(contractModuleService.getAuditeNote(params));
        } catch (Exception e) {
            logger.error("glegal getAuditeNote error -> {}", e.getMessage());
            return Response.err(e.getMessage());
        }
    }

    @ApiOperation(value = "获取流转日志")
    @GetMapping("glegal/getFlowLog")
    public Response getFlowLog(@RequestParam String fdId) {
        try {
            JSONObject params = new JSONObject();
            params.put("fdId", fdId);
            params.put("loginName", PamCurrentUserUtil.getCurrentUserName());
            return Response.dataResponse(contractModuleService.getFlowLog(params));
        } catch (Exception e) {
            logger.error("glegal getFlowLog error -> {}", e.getMessage());
            return Response.err(e.getMessage());
        }
    }

    private JSONObject getOpt(String loginName, String fdId, String optName) {
        if (optName == null) return null;

        JSONObject params = new JSONObject();
        params.put("fdId", fdId);
        params.put("loginName", loginName);
        JSONObject res = contractModuleService.getOperationList(params);
        if (res == null) return null;

        logger.info("getOpt res:{}", res.toJSONString());
        JSONObject body = res.getJSONObject("body");
        JSONArray datas = body.getJSONArray("data");

        for (int i = 0; i < datas.size(); i++) {
            JSONObject data = datas.getJSONObject(i);
            JSONArray operationList = data.getJSONArray("operationList");
            for (int j = 0; j < operationList.size(); j++) {
                JSONObject opt = operationList.getJSONObject(j);
                if (optName.equals(opt.getString("name"))) {
                    return opt;
                }
            }
        }

        // 处理流程重复操作，导致操作不成功场景
        // 无对应操作权限，
        throw new BizException(ErrorCode.ERROR.getCode(), "操作失败，流程状态已变更，请刷新页面确认你的操作权限");
    }

    /**
     * 合同终止草稿推送 统一新建、变更、终止（后续优化）
     * @param form
     * @return
     */
    @ApiOperation("提交流程操作")
    @PutMapping({"saveSubmit"})
    public Response saveSubmit(@RequestBody ContractWorkflowDraftForm form) {
        DataResponse<String> responseError = Response.dataResponse();
        try {
            GleContractFlowExample gleContractFlowExample = new GleContractFlowExample();
            gleContractFlowExample.setOrderByClause("create_at desc");
            gleContractFlowExample.createCriteria().andContractIdEqualTo(Long.valueOf(form.getFormInstanceId()))
                    .andContractTypeEqualTo(Integer.parseInt(form.getContractType()))
                    .andDataTypeEqualTo(0);//contract_id所对应的应该记录，data_type值应该是唯一的。若统一，可以去掉这个条件 todo
             List<GleContractFlow> gleContractFlows = gleContractFlowMapper.selectByExample(gleContractFlowExample);
            if (CollectionUtils.isEmpty(gleContractFlows)) {
                throw new MipException("法务合同流程信息查询失败，请联系管理员！");
            }
             GleContractFlow gleContractFlow = gleContractFlows.get(0);

            String loginName = PamCurrentUserUtil.getCurrentUserName();
            if (form.getOpt() == null) {
                form.setOpt(getOpt(loginName, form.getFdId(), WorkflowOperationType.DRAFT_SUBMIT.getName()));
            }
            form.setSubject(gleContractFlow.getContralName() + gleContractFlow.getContractCode());
            //封装统一的入参
            JSONObject flowInfoJson = contractModuleService.buildFlowInfo(form.getOpt(), loginName, form.getFdId(), form.getAuditNote(),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeHandler())),
                    Arrays.asList(String.format("关于%s的合同终止流程", form.getSubject()), String.format("Termination process for %s", form.getSubject())));

            // 更新对应的合同流程信息
            gleContractFlow.setFlowInfo(flowInfoJson.toJSONString());
            gleContractFlowMapper.updateByPrimaryKey(gleContractFlow);

            DataResponse<JSONObject> response = Response.dataResponse();

            JSONObject jsonObject = null;
            if (form.getContractType().equals(GlegalContractEnums.CONTRACT_TYPE.getCode())) { //销售合同
                jsonObject = contractModuleService.submitModifyContractInfo(Long.valueOf(form.getFormInstanceId()), GlegalContractEnums.CONTRACT_TERMINATION.getCode(), flowInfoJson);
                if (jsonObject != null && Objects.equals(ErrorCode.SUCCESS.getCode(), jsonObject.getInteger("code"))) {
                    //更新合同状态为终止审批中
                    contractModuleService.updateContractAndChangeway(Long.valueOf(form.getFormInstanceId()),GlegalContractEnums.CONTRACT_TERMINATION.getCode());
                }
            }
            if (jsonObject != null && jsonObject.getInteger("code") != null && ErrorCode.SUCCESS.getCode() != jsonObject.getInteger("code")) {
                responseError.setCode(ErrorCode.SUCCESS);
                responseError.setMsg("ERROR");
                responseError.setData(jsonObject.getString("msg"));
                return responseError;
            }
            return response.setData(jsonObject);

        } catch (Exception e) {
            logger.error("error", e);
            responseError.setCode(Code.ERROR.getCode());
            responseError.setMsg(e.getMessage());
            return responseError.setData(e.getMessage());
        }
    }

}
