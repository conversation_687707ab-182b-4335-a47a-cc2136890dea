package com.midea.pam.gateway.statistics.web;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.GscPaymentInvoiceDTO;
import com.midea.pam.common.ctc.vo.GscPaymentInvoiceExcelVO;
import com.midea.pam.common.ctc.vo.PurchaseContractPunishmentDetailExcelVO;
import com.midea.pam.common.ctc.vo.PurchaseContractPunishmentExcelVO;
import com.midea.pam.common.ctc.vo.PurchaseContractPunishmentVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Api("gsc开票申请")
@RestController
@RequestMapping("gsc/invoice")
public class GscPaymentInvoiceStatisticsController {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation("gsc开票申请列表")
    @PostMapping("page")
    public Response page(@RequestBody GscPaymentInvoiceDTO dto) {
        final String url = String.format("%sgsc/invoice/page", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<GscPaymentInvoiceDTO>>>() {
        });
    }

    @ApiOperation("gsc开票申请详情")
    @GetMapping("detail")
    public Response detail(@RequestParam("id") Long id) {
        final String url = String.format("%sgsc/invoice/detail?id=%s", ModelsEnum.STATISTICS.getBaseUrl(), id);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<GscPaymentInvoiceDTO>>() {
        });
    }

    @ApiOperation("gsc开票申请列表导出")
    @PostMapping("export")
    public void export(HttpServletResponse response, @RequestBody GscPaymentInvoiceDTO dto) {
        final String url = String.format("%sgsc/invoice/export", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<List<GscPaymentInvoiceDTO>> responseData = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<GscPaymentInvoiceDTO>>>() {
        });
        List<GscPaymentInvoiceDTO> gscPaymentInvoiceDTOList = responseData.getData();
        //导出操作
        StringBuffer fileName = new StringBuffer();
        // 获取当前时间
        Date now = new Date();
        // 定义日期格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        // 格式化当前时间
        String formattedDate = sdf.format(now);
        fileName.append("ISP开票申请_");
        fileName.append(formattedDate);
        fileName.append(".xls");
        int num = 0;
        Integer detailNum = 0;
        List<GscPaymentInvoiceExcelVO> excelVOS = new ArrayList<>();
        for (GscPaymentInvoiceDTO gscPaymentInvoiceDTO : gscPaymentInvoiceDTOList) {
            num ++ ;
            GscPaymentInvoiceExcelVO gscPaymentInvoiceExcelVO = BeanUtil.copyProperties(gscPaymentInvoiceDTO, GscPaymentInvoiceExcelVO.class);
            gscPaymentInvoiceExcelVO.setNum(num);
            gscPaymentInvoiceExcelVO.setStatusName(GscPaymentInvoiceExcelVO.convertStatusName(gscPaymentInvoiceDTO.getStatus()));
            gscPaymentInvoiceExcelVO.setTaxInvoiceStatusName(GscPaymentInvoiceExcelVO.convertTaxInvoiceStatusName(gscPaymentInvoiceDTO.getTaxInvoiceStatus()));
            gscPaymentInvoiceExcelVO.setCreateAt(DateUtils.format(gscPaymentInvoiceDTO.getCreateAt(), "yyyy-MM-dd HH:mm:ss"));
            gscPaymentInvoiceExcelVO.setTaxRate(gscPaymentInvoiceDTO.getTaxRate().stripTrailingZeros().toPlainString() + "%");
            gscPaymentInvoiceExcelVO.setInvoiceAttribute(GscPaymentInvoiceExcelVO.convertInvoiceAttribute(gscPaymentInvoiceDTO.getInvoiceAttribute()));
            gscPaymentInvoiceExcelVO.setInvoiceType(GscPaymentInvoiceExcelVO.convertInvoiceType(gscPaymentInvoiceDTO.getInvoiceType()));
            excelVOS.add(gscPaymentInvoiceExcelVO);
        }
        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(excelVOS, GscPaymentInvoiceExcelVO.class, null, "开票申请", true);
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }
}
