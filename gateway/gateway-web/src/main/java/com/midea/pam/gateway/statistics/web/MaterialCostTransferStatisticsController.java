package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.MaterialCostTransferDto;
import com.midea.pam.common.ctc.vo.MaterialCostTransferExcelVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

@Api("统计-成本转移单")
@RestController
@RequestMapping("statistics/materialCostTransfer")
public class MaterialCostTransferStatisticsController extends ControllerHelper {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查询领料单列表")
    @GetMapping("page")
    public Response list(MaterialCostTransferDto materialCostTransferDto,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false) final String applyDateStart,
                         @RequestParam(required = false) final String applyDateEnd,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize){

        final Map<String, Object> params = buildParam(materialCostTransferDto);
        params.put("list", Boolean.TRUE);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        params.put("applyDateStart", applyDateStart);
        params.put("applyDateEnd", applyDateEnd);
        logger.info("请求参数为:{}",JSON.toJSONString(params));

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/materialCostTransfer/v1/list", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<MaterialCostTransferDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<MaterialCostTransferDto>>>() {
        });

        return response;

    }


    @ApiOperation(value = "成本转移单列表导出", response = ResponseMap.class)
    @GetMapping("export")
    public void listExport(HttpServletResponse response,
                           MaterialCostTransferDto materialCostTransferDto,
                           @RequestParam(required = false) final String applyDateStart,
                           @RequestParam(required = false) final String applyDateEnd
                           ){
        final Map<String, Object> params = buildParam(materialCostTransferDto);
        params.put("applyDateStart", applyDateStart);
        params.put("applyDateEnd", applyDateEnd);
        logger.info("导出时请求参数为:{}",JSON.toJSONString(params));
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/materialCostTransfer/v1/export", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("成本转移单_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");

        JSONArray materialCostTransferArr = (JSONArray) resultMap.get("materialCostTransferList");

        List<MaterialCostTransferExcelVo> excelVos = new ArrayList<>();
        if (materialCostTransferArr != null) {
            excelVos = JSONObject.parseArray(materialCostTransferArr.toJSONString(), MaterialCostTransferExcelVo.class);
            for (int i = 0; i < excelVos.size(); i++) {
                MaterialCostTransferExcelVo vo = excelVos.get(i);
                vo.setNum(i + 1);
            }
        }

        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(excelVos, MaterialCostTransferExcelVo.class, null, "成本转移单", true);

        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);


    }

    private Map buildParam(MaterialCostTransferDto dto){
        final Map<String, Object> params = new HashMap<>();
        //转移单编号 单据状态 申请人 制单人 转出项目编号 转入项目编号 仓库编号 库存组织 业务实体
        params.put("costTransferCode",dto.getCostTransferCode());
        params.put("statusStr",dto.getStatusStr());
        params.put("applyUserId",dto.getApplyUserId());
        params.put("applyUserName",dto.getApplyUserName());
        params.put("fillUserId",dto.getFillUserId());
        params.put("fillUserName",dto.getFillUserName());
        params.put("transferOutProjectCode",dto.getTransferOutProjectCode());
        params.put("transferInProjectCode",dto.getTransferInProjectCode());
        params.put("inventoryCode",dto.getInventoryCode());
        params.put("inventoryName",dto.getInventoryName());
        params.put("operatingUnitIdsStr", dto.getOperatingUnitIdsStr());
        params.put("organizationCodesStr", dto.getOrganizationCodesStr());
        params.put("erpCodeStr",dto.getErpCodeStr());
        return params;
    }
}
