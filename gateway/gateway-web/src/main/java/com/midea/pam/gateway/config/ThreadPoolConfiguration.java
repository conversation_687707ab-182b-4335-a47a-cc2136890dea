package com.midea.pam.gateway.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * Description 线程池配置类
 * Created by <PERSON><PERSON><PERSON>
 * Date 2024/4/23 16:23
 */
@Configuration
public class ThreadPoolConfiguration {

    @Bean(name = "exportMipCallbackLogVoExcelExecutor")
    public ThreadPoolTaskExecutor exportMipCallbackLogVoExcelExecutor() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(10);
        threadPoolTaskExecutor.setMaxPoolSize(10);
        threadPoolTaskExecutor.setQueueCapacity(10);
        threadPoolTaskExecutor.setKeepAliveSeconds(3000);
        threadPoolTaskExecutor.setThreadNamePrefix("exportMipCallbackLogVoExcelExecutor");
        return threadPoolTaskExecutor;
    }
}
