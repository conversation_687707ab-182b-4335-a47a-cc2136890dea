package com.midea.pam.gateway.config;

import com.midea.pam.annotation.CheckSubmit;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.common.utils.PamCurrentUserUtil;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2020/4/17
 * @description 接口防重复校验
 */
@Component
@Aspect
public class ReSubmitAspect {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Around("@annotation(com.midea.pam.annotation.CheckSubmit)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        logger.debug("**** 接口防重复校验 ****");
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        CheckSubmit checkSubmit = methodSignature.getMethod().getAnnotation(CheckSubmit.class);
        if (checkSubmit == null) {
            return joinPoint.proceed();
        } else {
            String username = PamCurrentUserUtil.getCurrentUserName();
            String methodName = ((MethodSignature) joinPoint.getSignature()).getMethod().getName();
            String className = joinPoint.getSignature().getDeclaringTypeName();
            methodName = className + ":" + methodName;

            if (!StringUtils.isEmpty(username)) {
                String redisKey = Constants.REDIS_CHECK_SUBMIT_PREFIX + methodName.concat(":").concat(username);
                logger.debug("resubmit {} - {}", redisKey, checkSubmit.delaySeconds());
                boolean submitAble = stringRedisTemplate.opsForValue().setIfAbsent(redisKey, String.valueOf(checkSubmit.delaySeconds()));
                if (!submitAble) {
                    long ttl = stringRedisTemplate.getExpire(redisKey);
                    if (ttl > 0) {
                        Response dataResponse = Response.dataResponse();

                        dataResponse.setCode(checkSubmit.warningCode());
                        dataResponse.setMsg(checkSubmit.warningMessage());

                        stringRedisTemplate.expire(redisKey, checkSubmit.delaySeconds(), TimeUnit.SECONDS);
                        return dataResponse;
                    }
                }
                stringRedisTemplate.expire(redisKey, checkSubmit.delaySeconds(), TimeUnit.SECONDS);
                return joinPoint.proceed();
            } else {
                logger.error("重复表单提交检验 失效: 参数错误:methodName-{},uicId-{}", methodName, username);
            }

            return joinPoint.proceed();
        }
    }
}