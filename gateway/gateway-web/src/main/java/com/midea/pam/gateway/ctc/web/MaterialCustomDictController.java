package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.MaterialCustomDictDto;
import com.midea.pam.common.ctc.dto.MaterialCustomDictHeaderDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@Api("物料分类参数配置模块")
@RestController
@RequestMapping(value = {"materialCustomDict"})
public class MaterialCustomDictController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "保存")
    @PostMapping("save")
    public Object save(@RequestBody @ApiParam(name = "MaterialCustomDictDto", value = "自定义实体") MaterialCustomDictHeaderDto dto) {
        final String url = String.format("%smaterialCustomDict/save", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, dto, String.class).getBody();
    }

    @ApiOperation(value = "删除")
    @GetMapping({"delete"})
    public Object delete(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/materialCustomDict/delete", param);
        return restTemplate.getForEntity(url, String.class).getBody();
    }

    @ApiOperation(value = "复制", response = MaterialCustomDictDto.class)
    @GetMapping("copy")
    public Object copy(@RequestParam @ApiParam(value = "配置对象名id") Long orgId,
                       @RequestParam @ApiParam(value = "配置对象名") String orgName,
                       @RequestParam @ApiParam(value = "要复制的参数id") Long pzId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("orgId", orgId);
        param.put("orgName", orgName);
        param.put("pzId", pzId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/materialCustomDict/copy", param);
        return restTemplate.getForEntity(url, String.class).getBody();
    }

    @ApiOperation(value = "查询", response = MaterialCustomDictDto.class)
    @GetMapping("query")
    public Object query(@RequestParam(required = false) @ApiParam(value = "编码大类") String codingClass,
                        @RequestParam(required = false) @ApiParam(value = "编码大类代码") String codingClassCode,
                        @RequestParam(required = false) @ApiParam(value = "编码中类") String codingMiddleclass,
                        @RequestParam(required = false) @ApiParam(value = "编码中类代码") String codingMiddleclassCode,
                        @RequestParam(required = false) @ApiParam(value = "编码小类") String codingSubclass,
                        @RequestParam(required = false) @ApiParam(value = "编码小类代码") String codingSubclassCode,
                        @RequestParam(required = false) @ApiParam(value = "库存组织ID") Long organizationId,
                        @RequestParam(required = false) @ApiParam(value = "创建人") String createName,
                        @RequestParam(required = false) @ApiParam(value = "更新人") String updateName,
                        @RequestParam(required = false) @ApiParam(value = "创建时间") String createAt,
                        @RequestParam(required = false) @ApiParam(value = "更新时间") String updateAt,
                        @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                        @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put("codingClass", codingClass);
        param.put("codingClassCode", codingClassCode);
        param.put("codingMiddleclass", codingMiddleclass);
        param.put("codingMiddleclassCode", codingMiddleclassCode);
        param.put("codingSubclass", codingSubclass);
        param.put("codingSubclassCode", codingSubclassCode);
        param.put("organizationId", organizationId);
        param.put("createName", createName);
        param.put("updateName", updateName);
        param.put("createAt", createAt);
        param.put("updateAt", updateAt);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/materialCustomDict/query", param);
        return restTemplate.getForEntity(url, String.class).getBody();
    }

    @ApiOperation(value = "查询特定组织参数")
    @GetMapping("getDetailById")
    public Response getDetailById(@RequestParam @ApiParam(value = "配置对象名id") Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/materialCustomDict/getDetailById", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<MaterialCustomDictHeaderDto> response = JSON.parseObject(res, new TypeReference<DataResponse<MaterialCustomDictHeaderDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "查询参数配置属性值集合")
    @PostMapping("queryDetails")
    public Object queryDetails(@RequestBody List<MaterialCustomDictHeaderDto> headerDtoList) {
        final String url = String.format("%smaterialCustomDict/queryDetails", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, headerDtoList, String.class).getBody();
        com.midea.pam.gateway.common.base.DataResponse<JSONArray> response = com.midea.pam.gateway.common.base.Response.dataResponse();
        return response.setData(JSONArray.parseArray(res));
    }

    /**
     * 根据库存组织id、大中小类查询核价员信息
     * 参数说明：organizationId、codingClass、codingMiddleclass、codingSubclass（非必填）
     * @param materialCustomDictHeaderDto
     * @return
     */
    @ApiOperation(value = "根据库存组织id、大中小类查询核价员")
    @PostMapping("queryValuer")
    public Response queryValuer(@RequestBody MaterialCustomDictHeaderDto materialCustomDictHeaderDto) {
        final String url = String.format("%smaterialCustomDict/queryValuer", ModelsEnum.CTC.getBaseUrl());
        final String res = restTemplate.postForEntity(url, materialCustomDictHeaderDto, String.class).getBody();
        DataResponse<MaterialCustomDictHeaderDto> response = JSON.parseObject(res, new TypeReference<DataResponse<MaterialCustomDictHeaderDto>>() {
        });
        return response;
    }

    /**
     * 根据库存组织id、大中小类批量查询核价员信息
     * 参数说明：organizationId、codingClass、codingMiddleclass、codingSubclass（非必填）
     * @param materialCustomDictHeaderDtoList
     * @return
     */
    @ApiOperation(value = "根据库存组织id、大中小类查询核价员")
    @PostMapping("queryValuerList")
    public Object queryValuerList(@RequestBody List<MaterialCustomDictHeaderDto> materialCustomDictHeaderDtoList) {
        final String url = String.format("%smaterialCustomDict/queryValuerList", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, materialCustomDictHeaderDtoList, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<MaterialCustomDictHeaderDto>>>() {
        });
    }
}
