package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.mcomponent.core.util.Assert;
import com.midea.mcomponent.core.util.BeanUtils;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.PurchaseContractNameDuplicateVo;
import com.midea.pam.common.basedata.dto.PurchaseContractTrackLeadDataDto;
import com.midea.pam.common.basedata.dto.PurchaseContractTrackLeadDto;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.PrintPDFParam;
import com.midea.pam.common.ctc.dto.PurchaseContractChangeHeaderDto;
import com.midea.pam.common.ctc.dto.PurchaseContractChangeHistoryVO;
import com.midea.pam.common.ctc.dto.PurchaseContractContentChangeHistoryVO;
import com.midea.pam.common.ctc.dto.PurchaseContractDTO;
import com.midea.pam.common.ctc.dto.PurchaseContractDetailDTO;
import com.midea.pam.common.ctc.dto.PurchaseContractProgressDto;
import com.midea.pam.common.ctc.dto.PurchaseContractQualityReportDto;
import com.midea.pam.common.ctc.dto.PurchaseContractStampDto;
import com.midea.pam.common.ctc.dto.PurchaseContractTemplateDto;
import com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto;
import com.midea.pam.common.ctc.entity.AsyncRequestResult;
import com.midea.pam.common.ctc.excelVo.CheckLogicExcelVo;
import com.midea.pam.common.ctc.excelVo.ErrMsgExcelVo;
import com.midea.pam.common.ctc.excelVo.PurchaseContractBudgetTemplateExcelVo;
import com.midea.pam.common.ctc.excelVo.PurchaseContractDetailImportExcelVO;
import com.midea.pam.common.ctc.excelVo.PurchaseContractTrackLeadExcelVO;
import com.midea.pam.common.ctc.vo.PurchaseContractExcelVO;
import com.midea.pam.common.ctc.vo.PurchaseContractPdfVo;
import com.midea.pam.common.ctc.vo.PurchaseContractProgressVo;
import com.midea.pam.common.ctc.vo.PurchaseContractVO;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.enums.ProcessTemplate;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.TransformFileUtil;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.utils.BeanConverter;
import com.midea.pam.gateway.common.utils.DateUtil;
import com.midea.pam.gateway.common.utils.ExportExcelUtil;
import com.midea.pam.gateway.common.utils.FileUtil;
import com.midea.pam.gateway.glegal.service.ContractModuleService;
import com.midea.pam.gateway.service.CommonPrintPdfService;
import com.midea.pam.gateway.service.MipWorkflowInnerService;
import com.midea.pam.gateway.service.OssService;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2019-6-4
 * @description 采购合同控制层
 */
@Api("采购合同")
@RestController
@RequestMapping("purchaseContract")
public class PurchaseContractController extends ControllerHelper {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    private final static String PURCHASE_CONTRACT_TEMPLATE_ID = "purchaseContractApp";

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private MipWorkflowInnerService mipWorkflowInnerService;
    @Resource
    private OssService ossService;
    @Resource
    private ContractModuleService contractModuleService;
    @Resource
    private CommonPrintPdfService cloudPrintExtService;


    @ApiOperation(value = "基本信息保存")
    @PostMapping("baseInfo")
    public Response saveBaseInfo(@RequestBody PurchaseContractDTO purchaseContractDTO) {
        final String url = String.format("%spurchaseContract/baseInfo", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, purchaseContractDTO, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PurchaseContractVO>>() {
        });
    }

    @ApiOperation(value = "基本信息暂存")
    @PostMapping("baseInfo/temporary")
    public Response saveBaseInfoTemporary(@RequestBody PurchaseContractDTO purchaseContractDTO) {
        final String url = String.format("%spurchaseContract/baseInfo/temporary", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, purchaseContractDTO, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PurchaseContractVO>>() {
        });
    }

    @ApiOperation(value = "采购内容导入", response = AsyncRequestResult.class)
    @PostMapping("purchaseDetail/upload")
    public Response uploadPurchaseDetail(@RequestParam(value = "file") MultipartFile file) {
        DataResponse<Map<String, Object>> response = Response.dataResponse();
        try {
            List<PurchaseContractDetailImportExcelVO> excelVOList;
            try {
                excelVOList = FileUtil.importExcel(file, PurchaseContractDetailImportExcelVO.class, 1, 0);
            } catch (Exception e) {
                excelVOList = FileUtil.importExcel(file, PurchaseContractDetailImportExcelVO.class, 0, 0);
            }
            Iterator<PurchaseContractDetailImportExcelVO> iterator = excelVOList.iterator();
            while (iterator.hasNext()) {
                PurchaseContractDetailImportExcelVO excelVO = iterator.next();
                if (StringUtils.isEmpty(excelVO.getSerialNumber()) && StringUtils.isEmpty(excelVO.getName())
                        && StringUtils.isEmpty(excelVO.getModel()) && StringUtils.isEmpty(excelVO.getBrand())
                        && StringUtils.isEmpty(excelVO.getTaxRate())
                        //删除单价,修改为金额含税和金额不含税
                        && StringUtils.isEmpty(excelVO.getTotalPriceStr()) && StringUtils.isEmpty(excelVO.getNoTaxTotalPriceStr())
                        && StringUtils.isEmpty(excelVO.getUnitName())
                        && StringUtils.isEmpty(excelVO.getNumberStr()) && StringUtils.isEmpty(excelVO.getRemark())) {
                    iterator.remove();
                }
            }
            Asserts.notEmpty(excelVOList, ErrorCode.SYSTEM_FILE_EMPTY);
            String url = String.format("%s%s", ModelsEnum.CTC.getBaseUrl(), "purchaseContract/purchaseDetail/upload");
            String res = restTemplate.postForEntity(url, excelVOList, String.class).getBody();
            response = JSON.parseObject(res, new TypeReference<DataResponse<Map<String, Object>>>() {
            });
        } catch (Exception e) {
            logger.info("采购合同详细内容Excel文件上传失败:", e.getMessage());
            Asserts.success(ErrorCode.SYSTEM_FILE_ERROR);
        }
        return response;
    }

    /**
     * 采购合同详细内容导入-校验结果表导出
     *
     * @param dtoList 导入的Excel包含检查结果的数据
     * @return 校验结果
     */
    @ApiOperation(value = "采购合同详细内容导入校验结果下载")
    @PostMapping("purchaseDetail/exportValidResult")
    public void exportValidResult(HttpServletResponse response, @RequestBody List<PurchaseContractDetailDTO> dtoList) {
        String fileName = "采购合同详细内容导入校验结果_" + DateUtils.format(new Date(), "yyyyMMddHH") + ".xls";
        List<PurchaseContractDetailImportExcelVO> excelVoList = BeanConverter.copy(dtoList, PurchaseContractDetailImportExcelVO.class);
        ExportExcelUtil.exportExcel(excelVoList, null, "采购合同详细内容导入校验结果", PurchaseContractDetailImportExcelVO.class, fileName, response);
    }

    @ApiOperation(value = "采购内容保存")
    @PostMapping("purchaseDetail")
    public Response makePurchaseDetail(@RequestBody PurchaseContractDTO purchaseContractDTO) {
        final String url = String.format("%spurchaseContract/purchaseDetail", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, purchaseContractDTO, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PurchaseContractVO>>() {
        });
    }

    @ApiOperation(value = "采购内容暂存")
    @PostMapping("purchaseDetail/temporary")
    public Response makePurchaseDetailTemporary(@RequestBody PurchaseContractDTO purchaseContractDTO) {
        final String url = String.format("%spurchaseContract/purchaseDetail/temporary", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, purchaseContractDTO, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PurchaseContractVO>>() {
        });
    }

    @ApiOperation(value = "付款计划保存")
    @PostMapping("paymentPlan")
    public Response makePaymentPlan(@RequestBody PurchaseContractDTO purchaseContractDTO) {
        final String url = String.format("%spurchaseContract/paymentPlan", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, purchaseContractDTO, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PurchaseContractVO>>() {
        });
    }

    @ApiOperation(value = "付款计划暂存")
    @PostMapping("paymentPlan/temporary")
    public Response makePaymentPlanTemporary(@RequestBody PurchaseContractDTO purchaseContractDTO) {
        final String url = String.format("%spurchaseContract/paymentPlan/temporary", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, purchaseContractDTO, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PurchaseContractVO>>() {
        });
    }

    @ApiOperation(value = "分页查询")
    @GetMapping("page")
    public Response pagePurchaseContract(@RequestParam(required = false) @ApiParam("合同编号") final String code,
                                         @RequestParam(required = false) @ApiParam("合同名称") final String name,
                                         @RequestParam(required = false) @ApiParam("供应商") final String vendorName,
                                         @RequestParam(required = false) @ApiParam("关联项目") final String projectName,
                                         @RequestParam(required = false) @ApiParam("采购跟进人") final String purchasingFollowerName,
                                         @RequestParam(required = false) @ApiParam("归档日期开始日期") final String filingStartDate,
                                         @RequestParam(required = false) @ApiParam("归档日期结束日期") final String filingEndDate,
                                         @RequestParam(required = false) @ApiParam("合同状态") final Integer status,
                                         @RequestParam(required = false) @ApiParam("排序字段") final String orderParam,
                                         @RequestParam(required = false) @ApiParam("排序类型(1:升序；2：降序)") final Integer orderType,
                                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        Map<String, Object> param = new HashMap<>();
        param.put("code", code);
        param.put("name", name);
        param.put("vendorName", vendorName);
        param.put("projectName", projectName);
        param.put("purchasingFollowerName", purchasingFollowerName);
        param.put("filingStartDate", filingStartDate);
        param.put("filingEndDate", filingEndDate);
        param.put("status", status);
        param.put("orderParam", orderParam);
        param.put("orderType", orderType);
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "purchaseContract/page", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<PurchaseContractVO>>>() {
        });
    }

    @ApiOperation(value = "采购合同导出")
    @GetMapping("excel/export")
    public void export(@RequestParam(required = false) @ApiParam("合同编号") final String code,
                       @RequestParam(required = false) @ApiParam("合同名称") final String name,
                       @RequestParam(required = false) @ApiParam("供应商") final String vendorName,
                       @RequestParam(required = false) @ApiParam("关联项目") final String projectName,
                       @RequestParam(required = false) @ApiParam("采购跟进人") final String purchasingFollowerName,
                       @RequestParam(required = false) @ApiParam("开始时间") final String startTime,
                       @RequestParam(required = false) @ApiParam("结束时间") final String endTime,
                       @RequestParam(required = false) @ApiParam("合同状态") final Integer status,
                       @RequestParam(required = false) @ApiParam("排序字段") final String orderParam,
                       @RequestParam(required = false) @ApiParam("排序类型(1:升序；2：降序)") final Integer orderType,
                       HttpServletResponse response) {
        Map<String, Object> param = new HashMap<>();
        param.put("code", code);
        param.put("name", name);
        param.put("vendorName", vendorName);
        param.put("projectName", projectName);
        param.put("purchasingFollowerName", purchasingFollowerName);
        param.put("startTime", startTime);
        param.put("endTime", endTime);
        param.put("status", status);
        param.put("orderParam", orderParam);
        param.put("orderType", orderType);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "purchaseContract/listDetail", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        final DataResponse<List<PurchaseContractVO>> dataResponse =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<PurchaseContractVO>>>() {
                });
        final List<PurchaseContractVO> data = dataResponse.getData();
        final List<PurchaseContractExcelVO> list = BeanUtils.copyPropertiesByList(data, PurchaseContractExcelVO.class);
        if (ListUtils.isNotEmpty(list)) {
            BigDecimal TEN_THOUSAND = new BigDecimal("10000");
            list.forEach(purchaseContractExcelVO -> {
                final BigDecimal amount = purchaseContractExcelVO.getAmount();
                if (amount != null) {
                    final BigDecimal newAmount = amount.divide(TEN_THOUSAND).setScale(2, RoundingMode.HALF_UP);
                    purchaseContractExcelVO.setAmount(newAmount);
                }
            });
        }

        ExportExcelUtil.exportExcel(list, null, "Sheet1", PurchaseContractExcelVO.class, "采购合同信息.xls", response);
    }

    @ApiOperation(value = "合同详情")
    @GetMapping("detail")
    public Response findDetailById(@RequestParam @ApiParam("合同ID") Long id,
                                   @RequestParam(required = false) @ApiParam("类型") String type,
                                   @RequestParam(required = false) @ApiParam("是否罚扣处理查询") boolean punishmentHandleQuery) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("type", type);
        param.put("punishmentHandleQuery", punishmentHandleQuery);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "purchaseContract/detail", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PurchaseContractVO>>() {
        });
    }

    @ApiOperation(value = "采购合同删除")
    @DeleteMapping("{id}")
    public Response deleteById(@PathVariable @ApiParam("合同ID") Long id) {
        String url = String.format("%spurchaseContract/" + id, ModelsEnum.CTC.getBaseUrl());
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.DELETE, null, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation(value = "采购合同作废")
    @PutMapping("cancel/{id}")
    public Response cancelById(@PathVariable @ApiParam("合同ID") Long id) {
        String url = String.format("%spurchaseContract/cancel/" + id, ModelsEnum.CTC.getBaseUrl());
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        DataResponse<Integer> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
        //同时作废工作流
        if (response.getCode() == 0) {
            mipWorkflowInnerService.draftAbandon(PURCHASE_CONTRACT_TEMPLATE_ID, id);
        }
        return response;
    }

    @ApiOperation(value = "采购合同变更废弃")
    @GetMapping("abandonChange")
    public Response abandonChange(@RequestParam @ApiParam("变更头ID") Long headerId,
                                  @RequestParam @ApiParam("变更类型") Integer changeType) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", headerId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "purchaseContract/abandonChange", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<String> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
        //同时作废工作流
        if (response.getCode() == 0) {
            if (Objects.equals(changeType, 1)) {
                mipWorkflowInnerService.draftAbandon(ProcessTemplate.PURCHASE_CONTRACT_INFO_CHANGE_APP.getCode(), headerId);
            } else if (Objects.equals(changeType, 2)) {
                mipWorkflowInnerService.draftAbandon(ProcessTemplate.PURCHASE_CONTRACT_DETAIL_CHANGE_APP.getCode(), headerId);
            } else if (Objects.equals(changeType, 3)) {
                mipWorkflowInnerService.draftAbandon(ProcessTemplate.PURCHASE_CONTRACT_PLAN_CHANGE_APP.getCode(), headerId);
            }
        }
        return response;
    }


    @ApiOperation(value = "根据合同id获取历史更改信息列表", response = PurchaseContractChangeHeaderDto.class)
    @GetMapping("getChangeHistoryList")
    public Object getChangeHistoryList(@RequestParam Long purchaseContractId, @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                       @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = new HashMap<>();
        params.put(Constants.Page.PAGE_NUM, pageNum);
        params.put(Constants.Page.PAGE_SIZE, pageSize);
        params.put("purchaseContractId", purchaseContractId);

        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseContract/getChangeHistoryList", params);
        return restTemplate.getForEntity(url, String.class).getBody();
    }

    @ApiOperation(value = "变更项目基础信息")
    @PostMapping("baseInfoChange")
    public Object baseInfoChange(@RequestBody PurchaseContractDTO contractDto) {
        final String url = String.format("%spurchaseContract/baseInfoChange", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, contractDto, String.class).getBody();
    }

    @ApiOperation(value = "获取基础信息变更历史信息详情", response = PurchaseContractChangeHistoryVO.class)
    @GetMapping("getBaseChangeHistory")
    public Object getBaseChangeHistory(@RequestParam Long headerId) {
        final Map<String, Object> params = new HashMap<>();
        params.put("headerId", headerId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseContract/getBaseChangeHistory", params);
        return restTemplate.getForEntity(url, String.class).getBody();
    }

    @ApiOperation(value = "变更采购合同内容")
    @PostMapping("contractDetailChange")
    public Object contractDetailChange(@RequestBody PurchaseContractDTO contractDto) {
        final String url = String.format("%spurchaseContract/contractDetailChange", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, contractDto, String.class).getBody();
    }

    @ApiOperation(value = "获取采购合同内容变更历史信息详情", response = PurchaseContractContentChangeHistoryVO.class)
    @GetMapping("getContractDetailChangeHistory")
    public Object getContractDetailChangeHistory(@RequestParam Long headerId) {
        final Map<String, Object> params = new HashMap<>();
        params.put("headerId", headerId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseContract/getContractDetailChangeHistory", params);
        return restTemplate.getForEntity(url, String.class).getBody();
    }

    @ApiOperation(value = "变更采购合同付款计划")
    @PostMapping("paymentPlanChange")
    public Object paymentPlanChange(@RequestBody PurchaseContractDTO contractDto) {
        final String url = String.format("%spurchaseContract/paymentPlanChange", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, contractDto, String.class).getBody();
    }

    @ApiOperation(value = "获取付款计划变更历史信息详情", response = PurchaseContractContentChangeHistoryVO.class)
    @GetMapping("getPaymentPlanChangeHistory")
    public Object getPaymentPlanChangeHistory(@RequestParam Long headerId) {
        final Map<String, Object> params = new HashMap<>();
        params.put("headerId", headerId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseContract/getPaymentPlanChangeHistory", params);
        return restTemplate.getForEntity(url, String.class).getBody();
    }

    @ApiOperation(value = "查询移动审批采购合同新增")
    @GetMapping("getPurchaseContractApp")
    public Response getPurchaseContractApp(@RequestParam Long id) {
        String url = String.format("%s/purchaseContract/getPurchaseContractApp?id=%s", ModelsEnum.CTC.getBaseUrl(), id);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<ResponseMap> response = JSON.parseObject(res, new TypeReference<DataResponse<ResponseMap>>() {
        });
        return response;
    }

    @ApiOperation(value = "查询移动审批采购合同基本信息变更")
    @GetMapping("getPurchaseContractInfoChangeApp")
    public Response getPurchaseContractInfoChangeApp(@RequestParam Long id) {
        String url = String.format("%s/purchaseContract/getPurchaseContractInfoChangeApp?id=%s", ModelsEnum.CTC.getBaseUrl(), id);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<ResponseMap> response = JSON.parseObject(res, new TypeReference<DataResponse<ResponseMap>>() {
        });
        return response;
    }

    @ApiOperation(value = "查询移动审批采购合同内容变更")
    @GetMapping("getPurchaseContractDetailChangeApp")
    public Response getPurchaseContractDetailChangeApp(@RequestParam Long id) {
        String url = String.format("%s/purchaseContract/getPurchaseContractDetailChangeApp?id=%s", ModelsEnum.CTC.getBaseUrl(), id);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<ResponseMap> response = JSON.parseObject(res, new TypeReference<DataResponse<ResponseMap>>() {
        });
        return response;
    }

    @ApiOperation(value = "查询移动审批采购合同付款计划变更")
    @GetMapping("getPurchaseContractPlanChangeApp")
    public Response getPurchaseContractPlanChangeApp(@RequestParam Long id) {
        String url = String.format("%s/purchaseContract/getPurchaseContractPlanChangeApp?id=%s", ModelsEnum.CTC.getBaseUrl(), id);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<ResponseMap> response = JSON.parseObject(res, new TypeReference<DataResponse<ResponseMap>>() {
        });
        return response;
    }

    @ApiOperation(value = "校验采购合同是否可做合同内容变更")
    @GetMapping("/checkBaseInfoChange")
    public Response checkBaseInfoChange(@RequestParam Long id) {
        String url = String.format("%s/purchaseContract/checkBaseInfoChange?id=%s", ModelsEnum.CTC.getBaseUrl(), id);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<Boolean> response = JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation("根据合同ID判断当前登录用户是否有权限查看,true是 false否")
    @GetMapping("/isContractIsView")
    public com.midea.pam.gateway.common.base.Response isContractIsView(@RequestParam(required = true) Long contractId) {
        String url = String.format("%spurchaseContract/isContractIsView?contractId=%s", ModelsEnum.CTC.getBaseUrl(), contractId);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<com.midea.pam.gateway.common.base.DataResponse<Boolean>>() {
        });
    }

    @ApiOperation("根据合同ID判断当前登录用户是否项目经理,true是 false否")
    @GetMapping("/isManager")
    public com.midea.pam.gateway.common.base.Response isManager(@RequestParam(required = true) Long contractId) {
        String url = String.format("%spurchaseContract/isManager?contractId=%s", ModelsEnum.CTC.getBaseUrl(), contractId);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<com.midea.pam.gateway.common.base.DataResponse<Boolean>>() {
        });
    }

    @ApiOperation("根据合同ID判断当前登录用户是否采购跟进人,true是 false否")
    @GetMapping("/isPurchasingFollower")
    public com.midea.pam.gateway.common.base.Response isPurchasingFollower(@RequestParam(required = true) Long contractId) {
        String url = String.format("%spurchaseContract/isPurchasingFollower?contractId=%s", ModelsEnum.CTC.getBaseUrl(), contractId);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<com.midea.pam.gateway.common.base.DataResponse<Boolean>>() {
        });
    }

    @ApiOperation("根据合同ID判断当前登录用户是否有数据权限,true是 false否")
    @GetMapping("/isCreater")
    public com.midea.pam.gateway.common.base.Response isCreater(@RequestParam(required = true) Long contractId) {
        String url = String.format("%spurchaseContract/isCreater?contractId=%s", ModelsEnum.CTC.getBaseUrl(), contractId);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<com.midea.pam.gateway.common.base.DataResponse<Boolean>>() {
        });
    }

    @ApiOperation("根据合同ID判断当前登录用户是否有数据权限,true是 false否")
    @GetMapping("/isDataAuthor")
    public com.midea.pam.gateway.common.base.Response isDataAuthor(@RequestParam(required = true) Long contractId) {
        String url = String.format("%spurchaseContract/isDataAuthor?contractId=%s", ModelsEnum.CTC.getBaseUrl(), contractId);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<com.midea.pam.gateway.common.base.DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "合同进度列表")
    @GetMapping("/progress/list")
    public Response getProgressList(PurchaseContractProgressDto dto) {
        final Map<String, Object> params = buildParam(dto);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "purchaseContract/progress/list", params);

        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<PurchaseContractProgressVo>>() {
        });
    }

    private Map buildParam(PurchaseContractProgressDto dto) {
        final Map<String, Object> params = new HashMap<>();
        params.put("purchaseContractId", dto.getPurchaseContractId());
        params.put("progressCode", dto.getProgressCode());
        params.put("progressStatusStr", dto.getProgressStatusStr());
        params.put("frontStartTimeStr", dto.getFrontStartTimeStr());
        params.put("frontEndTimeStr", dto.getFrontEndTimeStr());
        params.put("backStartTimeStr", dto.getBackStartTimeStr());
        params.put("backEndTimeStr", dto.getBackEndTimeStr());
        return params;
    }

    @ApiOperation(value = "合同进度详情")
    @GetMapping("/progress/detail")
    public Response findProgressDetail(@RequestParam Long purchaseContractProgressId) {
        String url = String.format("%spurchaseContract/progress/detail?purchaseContractProgressId=%s", ModelsEnum.CTC.getBaseUrl(), purchaseContractProgressId);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<PurchaseContractProgressDto>>() {
        });
    }

    @ApiOperation(value = "合同进度执行")
    @PostMapping("/progress/execute")
    public Object progressExecute(@RequestBody PurchaseContractProgressDto dto) {
        final String url = String.format("%spurchaseContract/progress/execute", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, dto, String.class).getBody();
    }

    @ApiOperation(value = "合同进度作废")
    @PutMapping("/progress/cancel/{id}")
    public DataResponse cancel(@PathVariable Long id) {
        String url = String.format("%spurchaseContract/progress/cancel/" + id, ModelsEnum.CTC.getBaseUrl());
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation(value = "合同进度入账信息推送到ERP")
    @GetMapping("/progress/pushToERP")
    public DataResponse pushToERP(@RequestParam Long purchaseContractProgressId) {
        String url = String.format("%spurchaseContract/progress/pushToERP?purchaseContractProgressId=%s", ModelsEnum.CTC.getBaseUrl(), purchaseContractProgressId);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation(value = "质检报告列表")
    @GetMapping("/qualityReport/list")
    public Response getQualityReportList(@RequestParam Long purchaseContractId) {
        String url = String.format("%spurchaseContract/qualityReport/list?purchaseContractId=%s", ModelsEnum.CTC.getBaseUrl(), purchaseContractId);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<List<PurchaseContractQualityReportDto>>>() {
        });
    }

    @ApiOperation(value = "质检报告提交")
    @PostMapping("/qualityReport/save")
    public Object saveQualityReport(@RequestBody PurchaseContractQualityReportDto dto) {
        final String url = String.format("%spurchaseContract/qualityReport/save", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, dto, String.class).getBody();
    }

    @ApiOperation(value = "质检报告删除")
    @DeleteMapping("/qualityReport/{qualityReportId}")
    public Response deleteQualityReport(@PathVariable Long qualityReportId) {
        String url = String.format("%spurchaseContract/qualityReport/" + qualityReportId, ModelsEnum.CTC.getBaseUrl());
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.DELETE, null, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation(value = "合同版本历史列表")
    @GetMapping("/versionHistory/list")
    public Response getVersionHistoryList(@RequestParam Long purchaseContractId) {
        String url = String.format("%spurchaseContract/versionHistory/list?purchaseContractId=%s", ModelsEnum.CTC.getBaseUrl(), purchaseContractId);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<List<PurchaseContractChangeHeaderDto>>>() {
        });
    }

    @ApiOperation(value = "合同版本下载PDF数据封装")
    @GetMapping("/versionHistory/detail")
    public Response getVersionHistoryDetail(@RequestParam Long changeHeaderId, Integer tag) {
        Map<String, Object> param = new HashMap<>();
        param.put("changeHeaderId", changeHeaderId);
        param.put("tag", tag);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseContract/versionHistory/detail", param);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<PurchaseContractPdfVo>>() {
        });
    }

    @ApiOperation(value = "盖章签名新增")
    @PostMapping("/stamp/save")
    public Object saveStamp(@RequestBody PurchaseContractStampDto dto) {
        final String url = String.format("%spurchaseContract/stamp/save", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, dto, String.class).getBody();
    }

    @ApiOperation(value = "盖章签名列表")
    @GetMapping("/stamp/list")
    public Response getStampList(@RequestParam Integer stampType) {
        String url = String.format("%spurchaseContract/stamp/list?stampType=%s", ModelsEnum.CTC.getBaseUrl(), stampType);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<List<PurchaseContractStampDto>>>() {
        });
    }

    @ApiOperation(value = "盖章签名删除")
    @DeleteMapping("/stamp/{stampId}")
    public Response deleteStamp(@PathVariable Long stampId) {
        String url = String.format("%spurchaseContract/stamp/" + stampId, ModelsEnum.CTC.getBaseUrl());
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.DELETE, null, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation(value = "查询采购合同内容")
    @GetMapping("listPurchaseContractDetail")
    public Response listPurchaseContractDetail(@RequestParam Long contractId) {
        String url = ModelsEnum.CTC.getBaseUrl() + "purchaseContract/listPurchaseContractDetail?contractId=" + contractId;
        return restTemplate.getForObject(url, DataResponse.class);
    }

    @ApiOperation(value = "合同模板查询")
    @GetMapping("/template/detail")
    public Response getTemplateDetail(Long purchaseContractId, String templateCode) {
        Map<String, Object> param = new HashMap<>();
        param.put("purchaseContractId", purchaseContractId);
        param.put("templateCode", templateCode);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "purchaseContract/template/detail", param);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<PurchaseContractTemplateDto>>() {
        });
    }

    @ApiOperation(value = "合同模板暂存")
    @PostMapping("/template/save")
    public Object saveTemplate(@RequestBody PurchaseContractTemplateDto dto) {
        final String url = String.format("%spurchaseContract/template/save", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, dto, String.class).getBody();
    }

    @ApiOperation(value = "生成合同模板")
    @PostMapping("/template/generate")
    public Response generateTemplate(@RequestBody PurchaseContractTemplateDto dto) {
        //查询组织参数：PAM采购合同写入法务系统审批
        final Map<String, Object> query = new HashMap<>();
        query.put("orgId", SystemContext.getUnitId());
        query.put("orgFrom", OrgCustomDictOrgFrom.COMPANY.code());
        query.put("name", Constants.PURCHASE_CONTRACT_IF_LEGAL);
        String url0 = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/organizationCustomDict/queryByName", query);
        String res0 = restTemplate.getForEntity(url0, String.class).getBody();
        Set<String> dictSet = JSON.parseObject(res0, new TypeReference<DataResponse<Set<String>>>() {
        }).getData();

        //1.生成云打印预览的url和body
        final String url = String.format("%scloudPrint/purchaseContractTemplatePrint", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<PrintPDFParam> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PrintPDFParam>>() {
        });

        if (response != null && response.getCode() == 0 && response.getData() != null) {
            try {
                //2.将云打印预览的图片转化成byte数组
                PrintPDFParam printPDFParam = response.getData();
                byte[] bytes = cloudPrintExtService.doPostIgnoreVerifySSL(printPDFParam);

                //3.将byte数组转化为上传的file
                MultipartFile file = TransformFileUtil.bytesToMultipartFile(bytes, String.format("%s_%s.pdf", dto.getName(), DateUtils.format(new Date(), "yyyyMMddHHmmss")));

                //4.上传文件
                String fileId = null;
                if (CollectionUtils.isNotEmpty(dictSet) && (dictSet.contains("1"))) { //走法务
                    JSONObject legalResult = contractModuleService.upload(file, "ContractNeedStamp", dto.getFileAttachmentId());
                    if (Objects.equals(legalResult.getString("code"), "0") && legalResult.getJSONObject("data") != null) {
                        fileId = legalResult.getJSONObject("data").getString("fileId");
                    }
                } else { //不走法务
                    JSONArray result = ossService.upload(file);
                    if (result.getJSONObject(0) != null) {
                        fileId = result.getJSONObject(0).getString("fileId");
                    }
                }
                if (StringUtils.isEmpty(fileId)) {
                    throw new MipException("文件上传异常");
                }

                //5.附件id回写采购合同表
                dto.setFileId(fileId);
                final String url1 = String.format("%spurchaseContract/template/save", ModelsEnum.CTC.getBaseUrl());
                ResponseEntity<String> responseEntity1 = restTemplate.postForEntity(url1, dto, String.class);
                DataResponse<Long> response1 = JSON.parseObject(responseEntity1.getBody(), new TypeReference<DataResponse<Long>>() {
                });
                if (response1 != null && response1.getCode() == 0 && response1.getData() != null) {
                    return Response.dataResponse(fileId);
                } else {
                    throw new MipException(Optional.ofNullable(response1).map(DataResponse::getMsg).orElse("回写数据库出现异常"));
                }
            } catch (Exception e) {
                throw new MipException(e.getMessage());
            }
        } else {
            if (response != null) {
                throw new MipException(Optional.ofNullable(response).map(DataResponse::getMsg).orElse("调用云打印系统出现异常"));
            }
        }
        return Response.dataResponse(null);
    }

    @ApiOperation(value = "下载模板", notes = "场景：采购合同关联需求批量导入")
    @PostMapping("/budget/exportTemplate")
    public void importBudget(@RequestBody PurchaseContractDTO dto, HttpServletResponse response) {
        List<PurchaseContractBudgetTemplateExcelVo> excelVos =
                BeanConverter.copy(dto.getDataList(), PurchaseContractBudgetTemplateExcelVo.class);
        excelVos.forEach(s -> s.setUnreleasedAmount_dt(String.valueOf(s.getUnreleasedAmount())));

        Workbook workbook = com.midea.pam.common.util.ExportExcelUtil.buildDefaultSheet(excelVos,
                PurchaseContractBudgetTemplateExcelVo.class, null, "Sheet1", true);
        com.midea.pam.common.util.ExportExcelUtil.addSheet(workbook, Lists.newArrayList(),
                ErrMsgExcelVo.class, null, "错误信息", false);
        com.midea.pam.common.util.ExportExcelUtil.addSheet(workbook, buildCheckLogicInfo(),
                CheckLogicExcelVo.class, "校验逻辑说明：", "校验逻辑", true);

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("采购合同关联需求批量导入模板_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");
        com.midea.pam.common.util.ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    @ApiOperation(value = "上传文件检查数据", notes = "场景：采购合同关联需求批量导入")
    @PostMapping("/budget/checkTemplate")
    public Response checkTemplate(@RequestPart("file") MultipartFile file, @RequestParam String dataList) {
        List<PurchaseContractBudgetTemplateExcelVo> excelVos = null;
        try {
            excelVos = FileUtil.importExcel(file, PurchaseContractBudgetTemplateExcelVo.class);
        } catch (Exception e) {
            throw new MipException("模板解析异常");
        }
        if (CollectionUtils.isEmpty(excelVos)) {
            throw new MipException("当前导入Excel模板中无法匹配有效的记录，请检查");
        }
        List<PurchaseMaterialRequirementDto> datas = null;
        try {
            datas = JSONObject.parseArray(dataList, PurchaseMaterialRequirementDto.class);
        } catch (Exception e) {
            throw new MipException("参数异常");
        }
        List<ErrMsgExcelVo> errMsgExcelVos = buildErrMsgInfo(excelVos, datas);
        Map map = new HashMap();
        map.put("flag", CollectionUtils.isEmpty(errMsgExcelVos));
        map.put("errMsgList", errMsgExcelVos);
        return Response.dataResponse(map);
    }

    @ApiOperation(value = "下载错误数据", notes = "场景：采购合同关联需求批量导入")
    @PostMapping("/budget/downloadErrorMsg")
    public void downloadErrorMsg(@RequestPart("file") MultipartFile file, @RequestParam String dataList, HttpServletResponse response) {
        List<PurchaseContractBudgetTemplateExcelVo> excelVos = null;
        try {
            excelVos = FileUtil.importExcel(file, PurchaseContractBudgetTemplateExcelVo.class);
        } catch (Exception e) {
            throw new MipException("模板解析异常");
        }
        List<PurchaseMaterialRequirementDto> datas = null;
        try {
            datas = JSONObject.parseArray(dataList, PurchaseMaterialRequirementDto.class);
        } catch (Exception e) {
            throw new MipException("参数异常");
        }
        List<ErrMsgExcelVo> errMsgExcelVos = buildErrMsgInfo(excelVos, datas);

        Workbook workbook = com.midea.pam.common.util.ExportExcelUtil.buildDefaultSheet(excelVos,
                PurchaseContractBudgetTemplateExcelVo.class, null, "Sheet1", true);
        com.midea.pam.common.util.ExportExcelUtil.addSheet(workbook, errMsgExcelVos,
                ErrMsgExcelVo.class, null, "错误信息", false);
        com.midea.pam.common.util.ExportExcelUtil.addSheet(workbook, buildCheckLogicInfo(),
                CheckLogicExcelVo.class, "校验逻辑说明：", "校验逻辑", true);

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("采购合同关联需求批量导入错误数据详情_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");
        com.midea.pam.common.util.ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    @ApiOperation(value = "批量导入关联需求", notes = "场景：采购合同关联需求批量导入")
    @PostMapping("/budget/importTemplate")
    public Response importTemplate(@RequestPart("file") MultipartFile file, @RequestParam String dataList) {
        List<PurchaseContractBudgetTemplateExcelVo> excelVos = null;
        try {
            excelVos = FileUtil.importExcel(file, PurchaseContractBudgetTemplateExcelVo.class);
        } catch (Exception e) {
            throw new MipException("模板解析异常");
        }
        List<PurchaseMaterialRequirementDto> datas = null;
        try {
            datas = JSONObject.parseArray(dataList, PurchaseMaterialRequirementDto.class);
        } catch (Exception e) {
            throw new MipException("参数异常");
        }
        List<ErrMsgExcelVo> errMsgExcelVos = buildErrMsgInfo(excelVos, datas);

        Map<String, PurchaseContractBudgetTemplateExcelVo> excelVoMap = excelVos.stream().collect(Collectors.toMap(s -> buildRequirementKey(s), Function.identity(), (a, b) -> a));

        for (PurchaseMaterialRequirementDto data : datas) {
            PurchaseContractBudgetTemplateExcelVo excelVo = excelVoMap.get(buildRequirementKey(data));
            if (excelVo != null) {
                data.setNumber(excelVo.getUnreleasedAmount());
                data.setPrice(excelVo.getPrice());
            }
        }
        Map map = new HashMap();
        map.put("flag", CollectionUtils.isEmpty(errMsgExcelVos));
        map.put("errMsgList", errMsgExcelVos);
        map.put("resultList", datas);
        return Response.dataResponse(map);
    }

    private List<ErrMsgExcelVo> buildErrMsgInfo(List<PurchaseContractBudgetTemplateExcelVo> excelVos, List<PurchaseMaterialRequirementDto> dataList) {
        //需求发布单据编号+WBS号+PAM物料编码
        Map<String, PurchaseMaterialRequirementDto> dataMap = dataList.stream().collect(Collectors.toMap(s -> buildRequirementKey(s), Function.identity(), (a, b) -> a));

        List<ErrMsgExcelVo> errMsgList = new ArrayList<>();
        for (int i = 0; i < excelVos.size(); i++) {
            PurchaseContractBudgetTemplateExcelVo excelVo = excelVos.get(i);
            if (excelVo.getRequirementCode() == null && excelVo.getWbsSummaryCode() == null && excelVo.getPamCode() == null) {
                continue;
            }
            PurchaseMaterialRequirementDto data = dataMap.get(buildRequirementKey(excelVo));
            if (data == null) {
                errMsgList.add(new ErrMsgExcelVo(String.format("行：%s，在列表不存在", i+2)));
            }
            if (BigDecimalUtils.isBigDecimal(excelVo.getUnreleasedAmount_dt())) {
                excelVo.setUnreleasedAmount(new BigDecimal(excelVo.getUnreleasedAmount_dt()));
                if (excelVo.getUnreleasedAmount().compareTo(BigDecimal.ZERO) < 0) {
                    errMsgList.add(new ErrMsgExcelVo(String.format("行：%s，填写的采购合同签订数量不为正数", i+2)));
                }
            } else {
                if (StringUtils.isNotEmpty(excelVo.getUnreleasedAmount_dt())) {
                    errMsgList.add(new ErrMsgExcelVo(String.format("行：%s，填写的采购合同签订数量有误", i+2)));
                }
            }
            if (BigDecimalUtils.isBigDecimal(excelVo.getPrice_dt())) {
                excelVo.setPrice(new BigDecimal(excelVo.getPrice_dt()));
                if (excelVo.getPrice().compareTo(BigDecimal.ZERO) < 0) {
                    errMsgList.add(new ErrMsgExcelVo(String.format("行：%s，填写的单价（不含税）-原币不为正数", i+2)));
                }
            } else {
                if (StringUtils.isNotEmpty(excelVo.getPrice_dt())) {
                    errMsgList.add(new ErrMsgExcelVo(String.format("行：%s，填写的单价（不含税）-原币有误", i+2)));
                }
            }
        }
        return errMsgList;
    }

    private List<CheckLogicExcelVo> buildCheckLogicInfo() {
        List<CheckLogicExcelVo> checkLogicExcelVos = new ArrayList<>();
        checkLogicExcelVos.add(new CheckLogicExcelVo(1, "需求发布单据编号+WBS号+PAM物料编码", "校验在列表是否存在", "行：XX，在列表不存在"));
        checkLogicExcelVos.add(new CheckLogicExcelVo(2, "采购合同签订数量", "校验为正数", "行：XX，填写的采购合同签订数量不为正数"));
        checkLogicExcelVos.add(new CheckLogicExcelVo(3, "单价（不含税）-原币", "校验为正数", "行：XX，填写的单价（不含税）-原币不为正数"));
        return checkLogicExcelVos;
    }

    private String buildRequirementKey(PurchaseMaterialRequirementDto dto) {
        return String.format("requirementCode_%s_wbsSummaryCode_%s_pamCode_%s", dto.getRequirementCode(), dto.getWbsSummaryCode(), dto.getPamCode());
    }

    private String buildRequirementKey(PurchaseContractBudgetTemplateExcelVo excelVo) {
        return String.format("requirementCode_%s_wbsSummaryCode_%s_pamCode_%s", excelVo.getRequirementCode(), excelVo.getWbsSummaryCode(), excelVo.getPamCode());
    }


    @ApiOperation(value = "采购合同新建-检查合同状态")
    @GetMapping("statusCheck")
    public Response addStatusCheck(@RequestParam Long id, @RequestParam(required = false) @ApiParam("操作类型") String operationType) {
        String url = String.format("%spurchaseContract/addStatusCheck?id=%d&operationType=%s", ModelsEnum.CTC.getBaseUrl(), id, operationType);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "采购合同变更-检查合同状态")
    @GetMapping("changeStatusCheck")
    public Response changeStatusCheck(@RequestParam Long id, @RequestParam(required = false) @ApiParam("变更头id") Long changeHeaderId,
                                      @RequestParam(required = false) @ApiParam("操作类型") String operationType) {
        final Map<String, Object> map=new HashMap<>();
        map.put("id",id);
        map.put("changeHeaderId",changeHeaderId);
        map.put("operationType",operationType);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(),"/purchaseContract/changeStatusCheck",map);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "采购合同-更新合同状态")
    @GetMapping("updateContractStatus")
    public Response updateContractStatus(@RequestParam Long id, @RequestParam Integer status) {
        String userMip = SystemContext.getUserMip();
        if (!Objects.equals(userMip, "hongfang") && !Objects.equals(userMip, "yanyw7") && !Objects.equals(userMip, "liangqk4")) {
            throw new MipException("权限错误！");
        }
        String url = String.format("%spurchaseContract/updateContractStatus?id=%d&status=%d", ModelsEnum.CTC.getBaseUrl(), id, status);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }
    @ApiOperation(value = "采购合同-更新追踪货期/货期跟进备注")
    @GetMapping("updateContractById")
    public Response updateContractById(@RequestParam Long id, @RequestParam(required = false) @ApiParam("追踪货期")  String trackLeadTime,
                                       @RequestParam(required = false) @ApiParam("货期跟进备注")  String trackLeadTimeRemark) {
        String url = String.format("%spurchaseContract/updateContractById?id=%d&trackLeadTime=%s&trackLeadTimeRemark=%s", ModelsEnum.CTC.getBaseUrl(), id, trackLeadTime,trackLeadTimeRemark);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }
    @ApiOperation(value = "上传文件检查数据", notes = "场景：采购合同采购合同货期跟进批量维护")
    @PostMapping("/trackLeadTime/checkTemplate")
    public Response newCheckTemplate(@RequestPart("file") MultipartFile file) {
        List<PurchaseContractTrackLeadExcelVO> excelVos;
        try {
            excelVos = FileUtil.importExcel(file, PurchaseContractTrackLeadExcelVO.class, 1, 0);
        } catch (Exception e) {
            throw new MipException("模板解析异常");
        }
        if (org.springframework.util.CollectionUtils.isEmpty(excelVos)) {
            throw new MipException("当前导入Excel模板中无法匹配有效的记录，请检查");
        }
        Map<String, Object> resultMap = newBuildErrMsgInfo(excelVos);
        DataResponse<Map<String, Object>> response = Response.dataResponse();
        return response.setData(resultMap);
    }

    private Map<String, Object> newBuildErrMsgInfo(List<PurchaseContractTrackLeadExcelVO> excelVos) {
        final Map<String, Object> param = new HashMap<>();
        //获取当下单位下的采购合
        //param.put("codes", excelVos.stream().map(PurchaseContractTrackLeadExcelVO::getCode).filter(Objects::nonNull).collect(Collectors.joining(",")));
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseContract/selectListByCodes", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        com.midea.pam.gateway.common.base.DataResponse<List<PurchaseContractTrackLeadDto>> response = JSON.parseObject(res, new TypeReference<com.midea.pam.gateway.common.base.DataResponse<List<PurchaseContractTrackLeadDto>>>() {
        });
        List<PurchaseContractTrackLeadDto> trackLeadDtoList = response.getData();
        Assert.notEmpty(trackLeadDtoList, ErrorCode.CTC_PURCHASE_CONTRACT_NOT_EXISTS.getMsg());
        Map<String, PurchaseContractTrackLeadDto> trackLeadMap = trackLeadDtoList.stream().collect(Collectors.toMap(PurchaseContractTrackLeadDto::getCode, Function.identity(), (a, b) -> a));
        List<PurchaseContractTrackLeadDto> dataList = new ArrayList<>();
        boolean flag=true;
        for (int i = 0; i < excelVos.size(); i++) {
            List<String> strList = new ArrayList<>();
            PurchaseContractTrackLeadExcelVO vo = excelVos.get(i);
            PurchaseContractTrackLeadDto dto=new PurchaseContractTrackLeadDto();
            BeanUtils.copyProperties(vo,dto);
            if(StringUtils.isEmpty(dto.getCode())){
                strList.add("合同编号不能为空!");
                flag=false;
            }else{
                if(!trackLeadMap.containsKey(dto.getCode())){
                    strList.add("合同编号不存在!");
                    flag=false;
                }
            }
            String trackLeadTime=dto.getTrackLeadTime();
            if(StringUtils.isNotEmpty(trackLeadTime)){
                try {
                    DateUtil.parseDate(trackLeadTime, DateUtil.DATE_PATTERN);
                }catch (Exception e){
                    strList.add("追踪货期格式有误!");
                    flag=false;
                }
            }
            if(!flag){
                dto.setTrackLeadMsg(String.join(", ", strList));
            }
            dataList.add(dto);
        }

        Map<String, Object> map = new HashMap<>();
        map.put("flag", flag);
        map.put("dataList", dataList);
        return map;
    }

    @ApiOperation(value = "下载错误数据", notes = "场景：采购合同货期跟进批量维护")
    @PostMapping("/trackLeadTime/downloadErrorMsg")
    public void newDownloadErrorMsg(@RequestBody PurchaseContractTrackLeadDataDto dto, HttpServletResponse response) {
        List<PurchaseContractTrackLeadDto> dataList=dto.getDataList();
        List<PurchaseContractTrackLeadExcelVO> excelVos = BeanUtils.copyPropertiesByList(dataList, PurchaseContractTrackLeadExcelVO.class);
        for (int i = 0; i < excelVos.size(); i++) {
            PurchaseContractTrackLeadExcelVO excel = excelVos.get(i);
            excel.setNumber(i + 1);
        }
        Workbook workbook;
        try {
            workbook = com.midea.pam.common.util.ExportExcelUtil.buildDefaultSheet(excelVos, PurchaseContractTrackLeadExcelVO.class, null, "采购合同货期跟进批量维护信息", true);
        } catch (Exception e) {
            throw new MipException("模板解析异常");
        }
        //导出
        com.midea.pam.common.util.ExportExcelUtil.downLoadExcel("报错信息_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls", response, workbook);
    }

    @ApiOperation(value = "批量导入货期跟进批量维护")
    @PostMapping("importPurchaseContractTrackLead")
    public com.midea.pam.gateway.common.base.Response importPurchaseContractTrackLead(@RequestBody PurchaseContractTrackLeadDataDto dto) {
        List<PurchaseContractTrackLeadDto> dataList=dto.getDataList();
        Guard.notNullOrEmpty(dataList, "批量导入货期跟进批量维护为空");
        try {
            logger.info("批量导入货期跟进批量维护的importExcelVos：{}", JSON.toJSONString(dataList));
            final String url = String.format("%spurchaseContract/importPurchaseContractTrackLead", ModelsEnum.CTC.getBaseUrl());
            String res = restTemplate.postForEntity(url, dataList, String.class).getBody();
            try {
                return JSON.parseObject(res, new TypeReference<com.midea.pam.gateway.common.base.DataResponse<List<PurchaseContractTrackLeadDto>>>() {
                });
            } catch (Exception e) {
                return JSON.parseObject(res, new TypeReference<com.midea.pam.gateway.common.base.DataResponse<List<PurchaseContractTrackLeadDto>>>() {
                });
            }
        } catch (ApplicationBizException e) {
            logger.error(e.getMessage(), e);
            throw new ApplicationBizException(e.getMessage());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new BizException(ErrorCode.SYSTEM_FILE_ERROR);
        }
    }


    @ApiOperation(value = "查询重复名称采购合同列表")
    @GetMapping("getDuplicateNameContractList")
    public Response getDuplicateNameContractList(@RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                                 @RequestParam(required = false, defaultValue = "10") final Integer pageSize,
                                                 @RequestParam(required = false) @ApiParam("id") Long id,
                                                 @RequestParam @ApiParam("需要查询的合同名称") String name,
                                                 @RequestParam(required = false) @ApiParam("合同编号") String code,
                                                 @RequestParam(required = false) @ApiParam("合同状态(多选)") String manyStatus,
                                                 @RequestParam(required = false) @ApiParam("项目编号") String projectCode,
                                                 @RequestParam(required = false) @ApiParam("供应商名称") String vendorName,
                                                 @RequestParam(required = false) @ApiParam("项目经理") String managerName,
                                                 @RequestParam(required = false) @ApiParam("采购跟进人") String purchasingFollowerName
                                                 ) {

        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("name", name);
        param.put("code", code);
        param.put("manyStatus", manyStatus);
        param.put("projectCode", projectCode);
        param.put("vendorName", vendorName);
        param.put("managerName", managerName);
        param.put("purchasingFollowerName", purchasingFollowerName);
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);

        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "purchaseContract/getDuplicateNameContractList", param);
        ResponseEntity<String> resp = restTemplate.postForEntity(url, param,String.class);
        return JSON.parseObject(resp.getBody(), new TypeReference<DataResponse<PageInfo<PurchaseContractVO>>>() {
        });
    }


    @ApiOperation(value = "采购合同罚扣处理")
    @PostMapping("punishmentHandle")
    public Object punishmentHandle(@RequestBody PurchaseContractDTO purchaseContractDTO){
        final String url = String.format("%spurchaseContract/punishmentHandle", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, purchaseContractDTO, String.class).getBody();
    }
}
