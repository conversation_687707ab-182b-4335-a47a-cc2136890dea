package com.midea.pam.gateway.mdw.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/25
 * @description 内部工时明细
 */
@Api("内部工时明细")
@RestController
@RequestMapping(value = {"/dwd/internalHours", "/mobile/app/mdw/internalHours"})
public class DwdInternalHoursController extends ControllerHelper {
    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "主动调用定时")
    @GetMapping("/active/{id}")
    public Response active(@PathVariable Long id) {
        final String url = String.format("%sdwd/internalHours/active/" + id, ModelsEnum.MDW.getBaseUrl());
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "定时测试")
    @GetMapping("/testJob")
    public Response testJob() {
        final Map<String, Object> params = new HashMap<>();
        final String url = buildGetUrl(ModelsEnum.MDW.getBaseUrl(), "/dwd/internalHours/testJob", params);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

}