package com.midea.pam.gateway.ctc.web;

import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

@Api("项目预立项回调")
@RestController
@RequestMapping("preProjectCallBack")
public class PreProjectCallBackController {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "审批中")
    @PutMapping("approvaling/skipSecurityInterceptor")
    public Response approvaling(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                                @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%spreProjectCallBack/approvaling/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "审批通过")
    @PutMapping("approved/skipSecurityInterceptor")
    public Response approved(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                             @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                             @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                             @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%spreProjectCallBack/approved/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "驳回")
    @PutMapping("refused/skipSecurityInterceptor")
    public Response refused(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                            @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                            @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                            @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%spreProjectCallBack/refused/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "撤回")
    @PutMapping("return/skipSecurityInterceptor")
    public Response returned(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                             @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                             @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                             @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%spreProjectCallBack/return/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "作废")
    @PutMapping("abandon/skipSecurityInterceptor")
    public Response abandoned(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                              @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                              @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                              @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%spreProjectCallBack/abandon/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "删除")
    @PutMapping("delete/skipSecurityInterceptor")
    public Response deleted(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                            @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                            @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                            @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%spreProjectCallBack/delete/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "处理人通过")
    @PutMapping("agree/skipSecurityInterceptor")
    public Response agree(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                          @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                          @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId,
                          @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%spreProjectCallBack/agree/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

}
