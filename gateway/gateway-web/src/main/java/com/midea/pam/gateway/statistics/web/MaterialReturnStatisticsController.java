package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.MaterialReturnDto;
import com.midea.pam.common.ctc.vo.MaterialReturnExcelVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/3/1
 * @description
 */
@Api("统计-退料单")
@RestController
@RequestMapping("statistics/materialReturn")
public class MaterialReturnStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "查询退料单列表")
    @GetMapping("page")
    public Response list(MaterialReturnDto materialReturnDto,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false) final String applyTimeMin,
                         @RequestParam(required = false) final String applyTimeMax,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(materialReturnDto);
        params.put("list", Boolean.TRUE);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        params.put("applyTimeMax", applyTimeMax);
        params.put("applyTimeMin", applyTimeMin);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/materialReturn/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<MaterialReturnDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<MaterialReturnDto>>>() {
        });

        return response;
    }

    private Map buildParam(MaterialReturnDto materialReturnDto) {
        final Map<String, Object> params = new HashMap<>();
        //退料单编号 单据状态 制单人 退料人 申请日期 退料子库 退料货位 项目号 同步状态
        params.put("returnCode", materialReturnDto.getReturnCode());
        params.put("statusStr", materialReturnDto.getStatusStr());
        params.put("fillUserId", materialReturnDto.getFillUserId());
        params.put("fillUserName", materialReturnDto.getFillUserName());
        params.put("returnUserId", materialReturnDto.getReturnUserId());
        params.put("returnUserName", materialReturnDto.getReturnUserName());
        params.put("inventoryCode", materialReturnDto.getInventoryCode());
        params.put("locationCode", materialReturnDto.getLocationCode());
        params.put("projectCode", materialReturnDto.getProjectCode());
        params.put("managerName", materialReturnDto.getManagerName());
        params.put("erpCodeStr", materialReturnDto.getErpCodeStr());
        params.put("resouce", materialReturnDto.getResouce());
        params.put("operatingUnitIdsStr", materialReturnDto.getOperatingUnitIdsStr());
        params.put("organizationCodesStr", materialReturnDto.getOrganizationCodesStr());
        params.put("reasonsStr", materialReturnDto.getReasonsStr());
        params.put("materialReturnTypeStr", materialReturnDto.getMaterialReturnTypeStr());
        return params;
    }


    @ApiOperation(value = "退料单列表导出", response = ResponseMap.class)
    @GetMapping("export")
    public void listExport(HttpServletResponse response,
                           MaterialReturnDto materialReturnDto,
                           @RequestParam(required = false) final String applyTimeMin,
                           @RequestParam(required = false) final String applyTimeMax) {
        final Map<String, Object> params = buildParam(materialReturnDto);
        params.put("applyTimeMax", applyTimeMax);
        params.put("applyTimeMin", applyTimeMin);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/materialReturn/v1/export", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("退料单_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");

        JSONArray materialReturnArr = (JSONArray) resultMap.get("materialReturnList");

        List<MaterialReturnExcelVO> materialReturnExcelVOS = new ArrayList<>();
        if (materialReturnArr != null) {
            materialReturnExcelVOS = JSONObject.parseArray(materialReturnArr.toJSONString(), MaterialReturnExcelVO.class);
            for (int i = 0; i < materialReturnExcelVOS.size(); i++) {
                MaterialReturnExcelVO materialReturnExcelVO = materialReturnExcelVOS.get(i);
                materialReturnExcelVO.setNum(i + 1);
            }
        }

        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(materialReturnExcelVOS, MaterialReturnExcelVO.class, null, "退料单", true);

        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

}
