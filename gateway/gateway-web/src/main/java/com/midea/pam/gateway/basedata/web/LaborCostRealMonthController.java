package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.LaborCostRankRealMonthDetailDto;
import com.midea.pam.common.basedata.dto.LaborCostRankRealMonthDetailImportDto;
import com.midea.pam.common.basedata.dto.LaborCostRankRealMonthDto;
import com.midea.pam.common.basedata.dto.LaborCostRankRealMonthRecordDto;
import com.midea.pam.common.basedata.excelVo.LaborCostRealMonthImportExcelVo;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.ExcelImportUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Api("月份实际人力费用")
@RequestMapping({"laborCostRealMonth"})
public class LaborCostRealMonthController extends ControllerHelper {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private RestTemplate restTemplate;

    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // 工具API

    @ApiOperation("读取可用的月份实际人力费用职级")
    @GetMapping({"levelNameList"})
    public Response levelNameList(@RequestParam Long monthId, @RequestParam(required = false) Long bizUnitId) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("monthId", monthId);
        param.put("bizUnitId", bizUnitId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCostRealMonth/levelNameList", param);
        String res = restTemplate.getForEntity(url , String.class).getBody();
        DataResponse<List<String>> response= JSON.parseObject(cleanStr(res), new TypeReference<DataResponse<List<String>>>(){});
        return response;
    }

    @ApiOperation("读取可用的月份实际人力费用角色")
    @GetMapping({"roleNameList"})
    public Response roleNameList(@RequestParam Long monthId, @RequestParam(required = false) Long bizUnitId) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("monthId", monthId);
        param.put("bizUnitId", bizUnitId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCostRealMonth/roleNameList", param);
        String res = restTemplate.getForEntity(url , String.class).getBody();
        DataResponse<List<String>> response= JSON.parseObject(cleanStr(res), new TypeReference<DataResponse<List<String>>>(){});
        return response;
    }

    @ApiOperation("查看月份实际人力费用是否已存在")
    @GetMapping({"isMonthExisted"})
    public Response isMonthExisted(@RequestParam String monthName, @RequestParam Long bizUnitId) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("monthName", monthName);
        param.put("bizUnitId", bizUnitId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCostRealMonth/isMonthExisted", param);
        String res = restTemplate.getForEntity(url , String.class).getBody();
        DataResponse<Boolean> response= JSON.parseObject(cleanStr(res), new TypeReference<DataResponse<Boolean>>(){});
        return response;
    }

    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // 增删改查API

    @ApiOperation("新增职级月份实际人力费用")
    @PostMapping({"add"})
    public Response add(@RequestBody LaborCostRankRealMonthDto dto) throws Exception {
        String url = String.format("%slaborCostRealMonth/add",ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<LaborCostRankRealMonthDto> response= JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<LaborCostRankRealMonthDto>>(){});
        return response;
    }

    @ApiOperation("修改职级月份实际人力费用")
    @PostMapping({"update"})
    public Response update(@RequestBody LaborCostRankRealMonthDto dto) throws Exception {
        String url = String.format("%slaborCostRealMonth/update",ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<LaborCostRankRealMonthDto> response= JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<LaborCostRankRealMonthDto>>(){});
        return response;
    }

    @ApiOperation("删除职级月份实际人力费用")
    @GetMapping({"delete"})
    public Response delete(@RequestParam Long monthId) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", monthId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCostRealMonth/delete", param);
        String res = restTemplate.getForEntity(url , String.class).getBody();
        DataResponse<Boolean> response= JSON.parseObject(cleanStr(res), new TypeReference<DataResponse<Boolean>>(){});
        return response;
    }

    @ApiOperation(value = "职级月份实际人力费用分页查询，根据单位ID查月份表")
    @GetMapping("selectPage")
    public Response selectPage(@RequestParam Long bizUnitId,
                               @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                               @RequestParam(required = false, defaultValue = "10") Integer pageSize) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("bizUnitId", bizUnitId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCostRealMonth/selectPage", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<LaborCostRankRealMonthDto>> response= JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<PageInfo<LaborCostRankRealMonthDto>>>(){});
        return response;
    }

    @ApiOperation(value = "职级月份实际人力费用列表查询，根据单位ID查月份表")
    @GetMapping("selectList")
    public Response selectList(@RequestParam Long bizUnitId) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("bizUnitId", bizUnitId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCostRealMonth/selectList", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<LaborCostRankRealMonthDto>> response= JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<List<LaborCostRankRealMonthDto>>>(){});
        return response;
    }

    @ApiOperation("批量保存职级月份实际人力费用详情")
    @PostMapping({"saveBatchDetail"})
    public Response saveBatchDetail(@RequestBody LaborCostRankRealMonthRecordDto realMonthRecordDto) throws Exception {
        String url = String.format("%slaborCostRealMonth/saveBatchDetail",ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, realMonthRecordDto, String.class);
        DataResponse<Long> response= JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<Long>>(){});
        return response;
    }

    @ApiOperation(value = "职级月份实际人力费用信息，根据月份ID查月份信息")
    @GetMapping("selectMonthInfobyMonthId")
    public Response selectMonthInfobyMonthId(@RequestParam Long monthId) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", monthId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCostRealMonth/selectMonthInfobyMonthId", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<LaborCostRankRealMonthDto> response= JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<LaborCostRankRealMonthDto>>(){});
        return response;
    }

    @ApiOperation(value = "职级月份实际人力费用详情分页查询，根据月份ID查详情")
    @GetMapping("selectDetailPage")
    public Response selectDetailPage(@RequestParam Long monthId,
                                     @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                     @RequestParam(required = false, defaultValue = "10") Integer pageSize) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("laborCostRankRealMonthId", monthId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCostRealMonth/selectDetailPage", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<LaborCostRankRealMonthDetailDto>> response= JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<PageInfo<LaborCostRankRealMonthDetailDto>>>(){});
        return response;
    }

    @ApiOperation(value = "职级月份实际人力费用详情列表查询，根据月份ID查详情")
    @GetMapping("selectDetailList")
    public Response selectDetailList(@RequestParam Long monthId) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("laborCostRankRealMonthId", monthId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCostRealMonth/selectDetailList", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<LaborCostRankRealMonthDetailDto>> response= JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<List<LaborCostRankRealMonthDetailDto>>>(){});
        return response;
    }

    @ApiOperation(value = "职级实际人力费用变更历史列表查询，根据主表月份ID查历史月份表")
    @GetMapping("selectHistoryList")
    public Response selectHistoryList(@RequestParam Long monthId) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("laborCostRankRealMonthId", monthId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCostRealMonth/selectHistoryList", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<LaborCostRankRealMonthRecordDto>> response= JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<List<LaborCostRankRealMonthRecordDto>>>(){});
        return response;
    }

    @ApiOperation(value = "职级实际人力费用变更历史详情列表查询，根据历史月份表ID查历史详情表")
    @GetMapping("selectHistoryDetailList")
    public Response selectHistoryDetailList(@RequestParam Long monthId) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("laborCostRankRealMonthRecordId", monthId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCostRealMonth/selectHistoryDetailList", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<LaborCostRankRealMonthRecordDto> response= JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<LaborCostRankRealMonthRecordDto>>(){});
        return response;
    }

    @ApiOperation(value = "查询移动审批职级实际人力费用变更", response = ResponseMap.class)
    @GetMapping("/getLaborCostRealMonthApp")
    public Response getLaborCostRealMonthApp(@RequestParam Long laborCostRankRealMonthRecordId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("laborCostRankRealMonthRecordId", laborCostRankRealMonthRecordId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCostRealMonth/getLaborCostRealMonthApp", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<ResponseMap> response= JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<ResponseMap>>(){});
        return response;
    }

    @ApiOperation(value = "查询有效的人力费率明细")
    @GetMapping("getValidDetails")
    public Response getValidDetails(@RequestParam(required = false) Long bizUnitId,
                                    @RequestParam(required = false) String month,
                                    @RequestParam(required = false) String levelName) {
        final Map<String, Object> param = new HashMap<>();
        param.put("bizUnitId", bizUnitId);
        param.put("month", month);
        param.put("levelName", levelName);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCostRealMonth/getValidDetails", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<LaborCostRankRealMonthDetailDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<LaborCostRankRealMonthDetailDto>>>(){});
        return response;
    }

    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

    @ApiOperation(value = "导入月职级实际人力费用")
    @PostMapping("importLaborCostRealMonth")
    public Response importLaborCostRealMonth(@RequestParam(value = "file") MultipartFile file,
                                             @RequestParam Long monthId) throws Exception {
        DataResponse<Long> response = Response.dataResponse();
        try {
            List<LaborCostRealMonthImportExcelVo> importExcelVos = ExcelImportUtils.importExcel(file, 1, 1, LaborCostRealMonthImportExcelVo.class);
            //List<LaborCostRealMonthImportExcelVo> importExcelVos = FileUtil.importExcel(file, LaborCostRealMonthImportExcelVo.class);

            if (importExcelVos.size()>0) {
                importExcelVos.get(0).setLaborCostRankRealMonthId(monthId);
                final String url = String.format("%slaborCostRealMonth/saveBatchDetailFromExcel", ModelsEnum.BASEDATA.getBaseUrl());

                String res = restTemplate.postForEntity(url, importExcelVos, String.class).getBody();
                try {
                    response = JSON.parseObject(res, new TypeReference<DataResponse<Long>>() {});
                    return response;
                } catch (Exception e){
                    DataResponse<List<LaborCostRankRealMonthDetailImportDto>> responseImportDtos = JSON.parseObject(res, new TypeReference<DataResponse<List<LaborCostRankRealMonthDetailImportDto>>>() {});
                    return responseImportDtos;
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            Asserts.success(ErrorCode.SYSTEM_FILE_ERROR);

            response = Response.dataResponse();
            response.setCode((com.midea.pam.gateway.common.base.Code) Code.ERROR);
            response.setMsg(e.getMessage());
        }

        return response;
    }

    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // 流程回调处理

    @ApiOperation(value = "发起审批")
    @PutMapping("approvaling/skipSecurityInterceptor")
    public Response updateStatusChecking(@RequestParam(required = false) String formInstanceId, @RequestParam(required = false) String handlerId) {
        String url = String.format("%slaborCostRealMonth/approvaling/skipSecurityInterceptor?formInstanceId=%s&handlerId=%s", ModelsEnum.BASEDATA.getBaseUrl(), formInstanceId, handlerId);
        // todo，使用restTemplate.exchange去代替restTemplate.put
        // restTemplate.exchange
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "驳回")
    @PutMapping("refused/skipSecurityInterceptor")
    public Response updateStatusReject(@RequestParam(required = false) String formInstanceId, @RequestParam(required = false) String handlerId) {
        String url = String.format("%slaborCostRealMonth/refused/skipSecurityInterceptor?formInstanceId=%s&handlerId=%s", ModelsEnum.BASEDATA.getBaseUrl(), formInstanceId, handlerId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "通过")
    @PutMapping("approved/skipSecurityInterceptor")
    public Response updateStatusPass(@RequestParam(required = false) String formInstanceId, @RequestParam(required = false) String handlerId) {
        String url = String.format("%slaborCostRealMonth/approved/skipSecurityInterceptor?formInstanceId=%s&handlerId=%s", ModelsEnum.BASEDATA.getBaseUrl(), formInstanceId, handlerId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "撤回")
    @PutMapping("return/skipSecurityInterceptor")
    public Response updateStatusReturn(@RequestParam(required = false) String formInstanceId, @RequestParam(required = false) String handlerId) {
        String url = String.format("%slaborCostRealMonth/return/skipSecurityInterceptor?formInstanceId=%s&handlerId=%s", ModelsEnum.BASEDATA.getBaseUrl(), formInstanceId, handlerId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "作废")
    @PutMapping("abandon/skipSecurityInterceptor")
    public Response updateStatusAbandon(@RequestParam(required = false) String formInstanceId, @RequestParam(required = false) String handlerId) {
        String url = String.format("%slaborCostRealMonth/abandon/skipSecurityInterceptor?formInstanceId=%s&handlerId=%s", ModelsEnum.BASEDATA.getBaseUrl(), formInstanceId, handlerId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "删除")
    @PutMapping("delete/skipSecurityInterceptor")
    public Response updateStatusDelete(@RequestParam(required = false) String formInstanceId, @RequestParam(required = false) String handlerId) {
        String url = String.format("%slaborCostRealMonth/delete/skipSecurityInterceptor?formInstanceId=%s&handlerId=%s", ModelsEnum.BASEDATA.getBaseUrl(), formInstanceId, handlerId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }
}
