package com.midea.pam.gateway.mdw.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @PackageClassName: com.midea.pam.gateway.mdw.web.DwsWorkingHourIhrAttendController
 * @Description: 项目工时与考勤工时汇总
 * @Author: JerryH
 * @Date: 2023-03-21, 0021 下午 03:47
 */
@Api("项目工时与考勤工时汇总")
@RestController
@RequestMapping("/mdw/workingHourIhrAttend")
public class DwsWorkingHourIhrAttendController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "项目工时与考勤工时汇总")
    @GetMapping("statisticsWorkingHourIhrAttend")
    public Response statisticsWorkingHourIhrAttend(@RequestParam(required = false) Boolean isPullAll,
                                                   @RequestParam(required = false) Date pullDate,
                                                   @RequestParam(required = false) Integer pullDay,
                                                   @RequestParam(required = false) Long parentUnitId) {
        Map<String, Object> param = new HashMap<>();
        param.put("parentUnitId", parentUnitId);
        param.put("pullDay", pullDay);
        param.put("pullDate", pullDate);
        param.put("isPullAll", isPullAll);
        final String url = buildGetUrl(ModelsEnum.MDW.getBaseUrl(), "dws/workingHourIhrAttend/statisticsWorkingHourIhrAttend", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
    }
}
