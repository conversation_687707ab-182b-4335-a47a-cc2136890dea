package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.basedata.entity.TargetDescription;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: common-module
 * @description: 个人主页指标说明
 * @author:zhongpeng
 * @create:2020-12-03 13:51
 **/
@RestController
@Api("主页指标说明")
@RequestMapping({"targetDescription"})
public class TargetDescriptionController extends ControllerHelper {
    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "获取个人主页指标说明")
    @GetMapping("getTargetDescriptionList")
    public Response getTargetDescriptionList(@RequestParam(required = false) @ApiParam("id") Long id,
                                             @RequestParam(required = false) @ApiParam("指标说明项") String target,
                                             @RequestParam(required = false) @ApiParam("指标说明类id") Integer typeId){
        final Map<String, Object> param = new HashMap<>();
        param.put("id",id);
        param.put("target",target);
        param.put("typeId",typeId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "targetDescription/getTargetDescriptionList",param);
        String res = restTemplate.getForEntity(url,String.class).getBody();
        DataResponse<List<TargetDescription>> response = JSON.parseObject(res,new TypeReference<DataResponse<List<TargetDescription>>>(){
        });
        return response;
    }
}
