package com.midea.pam.gateway.mdw.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @PackageClassName: com.midea.pam.gateway.mdw.web.DwdProjectCustomerSatisfactionController
 * @Description: 满意度宽表数据
 * @Author: JerryH
 * @Date: 2023-02-10, 0010 上午 10:02:08
 */
@Api("满意度宽表数据")
@RestController
@RequestMapping("mdw/dwdProjectCustomerSatisfaction")
public class DwdProjectCustomerSatisfactionController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "手动拉取并统计客户满意度宽表数据")
    @GetMapping("saveOrUpdateProjectCustomerSatisfaction")
    public Response saveOrUpdateProjectCustomerSatisfaction(@RequestParam(required = false) Long projectId) {
        Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        final String url = buildGetUrl(ModelsEnum.MDW.getBaseUrl(), "mdw/dwdProjectCustomerSatisfaction/saveOrUpdateProjectCustomerSatisfaction", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "手动统计客户满意度宽表数据")
    @GetMapping("statisticsProjectCustomerSatisfaction")
    public Response statisticsProjectCustomerSatisfaction(@RequestParam(required = false) Long projectId) {
        Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        final String url = buildGetUrl(ModelsEnum.MDW.getBaseUrl(), "mdw/dwdProjectCustomerSatisfaction/statisticsProjectCustomerSatisfaction", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
    }
}
