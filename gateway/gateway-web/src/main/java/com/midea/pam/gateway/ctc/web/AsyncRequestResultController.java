package com.midea.pam.gateway.ctc.web;

import com.midea.pam.common.ctc.entity.AsyncRequestResult;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Api("异步请求结果")
@RestController
@RequestMapping("ctc/asyncRequestResult")
public class AsyncRequestResultController extends ControllerHelper {

    @ApiOperation(value = "异步请求结果详情", response = AsyncRequestResult.class)
    @GetMapping({"view"})
    public void view(@RequestParam  Long id) {
    }

}
