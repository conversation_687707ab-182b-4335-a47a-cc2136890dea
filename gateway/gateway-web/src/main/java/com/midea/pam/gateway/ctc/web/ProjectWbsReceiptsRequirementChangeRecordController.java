package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsRequirementChangeRecordDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

@Api("详细设计需求预算变更记录")
@RestController
@RequestMapping("projectWbsReceiptsRequirementChangeRecord")
public class ProjectWbsReceiptsRequirementChangeRecordController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "需求预算变更按钮查询")
    @GetMapping("findRequirementDetailByProjectWbsReceiptsId")
    public Response findRequirementDetailByProjectWbsReceiptsId(@ApiParam("需求发布单据id") @RequestParam Long projectWbsReceiptsId) {
        final String url = String.format("%sprojectWbsReceiptsRequirementChangeRecord/findRequirementDetailByProjectWbsReceiptsId" +
                "?projectWbsReceiptsId=%s", ModelsEnum.CTC.getBaseUrl(), projectWbsReceiptsId);
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(600 * 1000);
        httpRequestFactory.setConnectTimeout(600 * 1000);
        httpRequestFactory.setReadTimeout(600 * 1000);
        restTemplate.setRequestFactory(httpRequestFactory);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectWbsReceiptsRequirementChangeRecordDto>>() {
        });
    }

    @ApiOperation(value = "提交变更")
    @PostMapping("submitRequirementChangeRecord")
    public Response submitRequirementChangeRecord(@RequestBody ProjectWbsReceiptsRequirementChangeRecordDto dto) {
        final String url = String.format("%sprojectWbsReceiptsRequirementChangeRecord/submitRequirementChangeRecord", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<ProjectWbsReceiptsRequirementChangeRecordDto>>() {
        });
    }

    @ApiOperation(value = "查看详细设计需求预算变更详情")
    @GetMapping("findRequirementChangeRecord")
    public Response findRequirementChangeRecord(@ApiParam("详细设计需求预算变更id") @RequestParam Long id) {
        final String url = String.format("%sprojectWbsReceiptsRequirementChangeRecord/findRequirementChangeRecord?id=%s",
                ModelsEnum.CTC.getBaseUrl(), id);
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(600 * 1000);
        httpRequestFactory.setConnectTimeout(600 * 1000);
        httpRequestFactory.setReadTimeout(600 * 1000);
        restTemplate.setRequestFactory(httpRequestFactory);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectWbsReceiptsRequirementChangeRecordDto>>() {
        });
    }

    @ApiOperation(value = "修改")
    @PostMapping("updateRequirementChangeRecord")
    public Response updateRequirementChangeRecord(@RequestBody ProjectWbsReceiptsRequirementChangeRecordDto dto) {
        final String url = String.format("%sprojectWbsReceiptsRequirementChangeRecord/updateRequirementChangeRecord", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }

}
