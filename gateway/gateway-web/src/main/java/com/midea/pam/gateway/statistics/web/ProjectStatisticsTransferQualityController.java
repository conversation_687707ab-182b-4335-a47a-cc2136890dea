package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.vo.ProjectExcelVO;
import com.midea.pam.common.ctc.vo.ProjectToTransferExcelVO;
import com.midea.pam.common.ctc.vo.ProjectTransferQualityExcelVO;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.BeanConverter;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Description
 * Created by liuqing
 * Date 2021/12/9 15:01
 */
@Api("统计-转质保项目")
@RestController
@RequestMapping("statistics/projectTransferQuality")
public class ProjectStatisticsTransferQualityController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation("转质保项目列表")
    @GetMapping("page")
    public Response page(ProjectDto projectDto,
                         @RequestParam(required = false) @ApiParam("事业部名称") String departmentName,
                         @RequestParam(required = false) @ApiParam("是否项目展示列表") String isView,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize,
                         @RequestParam(required = false) Integer transferProjectState) {
        final Map<String, Object> params = buildParam(projectDto);
        // 授权的（数据权限）
        params.put("list", true);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        params.put("transferProjectState", transferProjectState);
        params.put("departmentName", departmentName);
        params.put("isView", isView);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectTransferQuality/page", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<ProjectDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ProjectDto>>>() {
        });

        return response;
    }

    @ApiOperation("转质保项目列表")
    @GetMapping("selectToTransfer")
    public Response selectToTransfer(@RequestParam(required = false) @ApiParam("项目编号") String code,
                                     @RequestParam(required = false) @ApiParam("项目名称") String name,
                                     @RequestParam(required = false) @ApiParam("项目经理") String managerName,
                                     @RequestParam(required = false) @ApiParam("项目类型多选") String typesStr,
                                     @RequestParam(required = false) @ApiParam("客户名称") String customerName,
                                     @RequestParam(required = false) @ApiParam("质保开始时间起始") String qualityAssuranceStartTimeStart,
                                     @RequestParam(required = false) @ApiParam("质保开始时间结束") String qualityAssuranceStartTimeEnd,
                                     @RequestParam(required = false) @ApiParam("质保结束时间起始") String qualityAssuranceEndTimeStart,
                                     @RequestParam(required = false) @ApiParam("质保结束时间结束") String qualityAssuranceEndTimeEnd,
                                     @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                     @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = new HashMap<>();
        params.put("code", code);
        params.put("name", name);
        params.put("managerName", managerName);
        params.put("typesStr", typesStr);
        params.put("customerName", customerName);
        params.put("qualityAssuranceStartTimeStart", qualityAssuranceStartTimeStart);
        params.put("qualityAssuranceStartTimeEnd", qualityAssuranceStartTimeEnd);
        params.put("qualityAssuranceEndTimeStart", qualityAssuranceEndTimeStart);
        params.put("qualityAssuranceEndTimeEnd", qualityAssuranceEndTimeEnd);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectTransferQuality/selectToTransfer", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<ProjectDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ProjectDto>>>() {
        });

        return response;
    }

    @ApiOperation("项目列表导出")
    @GetMapping("export")
    public void listExport(HttpServletResponse response, ProjectDto projectDto) {
        final Map<String, Object> params = buildParam(projectDto);
        // 项目编号、项目名称、业务实体、客户名称、客户CRM编码、销售子合同编号、商机编号、“项目经理”模糊查询
        // "项目类型”、“项目属性”、项目状态、是否预立项使用下拉搜索(多选);
        params.put("list", projectDto.getList());
        params.put("me", projectDto.getMe());

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectTransferQuality/export", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<ProjectDto>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<ProjectDto>>>() {
        });

        //导出操作
        String fileName = String.format("%s%s%s", "待转质保项目列表_", DateUtils.format(new Date(), "yyyyMMddHHmmss"), ".xls");

        List<ProjectTransferQualityExcelVO> projectExcelVOS = BeanConverter.copy(dataResponse.getData(), ProjectTransferQualityExcelVO.class);
        for (int i = 0; i < projectExcelVOS.size(); i++) {
            ProjectTransferQualityExcelVO projectExcelVO = projectExcelVOS.get(i);
            projectExcelVO.setNum(i + 1);
        }
        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(projectExcelVOS, ProjectExcelVO.class, null, "基础信息", true);
        ExportExcelUtil.downLoadExcel(fileName, response, workbook);
    }

    @ApiOperation("待转质保列表导出")
    @GetMapping("exportToTransfer")
    public void exportToTransfer(HttpServletResponse response,
                                 @RequestParam(required = false) @ApiParam("项目编号") String code,
                                 @RequestParam(required = false) @ApiParam("项目名称") String name,
                                 @RequestParam(required = false) @ApiParam("项目经理") String managerName,
                                 @RequestParam(required = false) @ApiParam("项目类型多选") String typesStr,
                                 @RequestParam(required = false) @ApiParam("客户名称") String customerName,
                                 @RequestParam(required = false) @ApiParam("质保开始时间起始") String qualityAssuranceStartTimeStart,
                                 @RequestParam(required = false) @ApiParam("质保开始时间结束") String qualityAssuranceStartTimeEnd,
                                 @RequestParam(required = false) @ApiParam("质保结束时间起始") String qualityAssuranceEndTimeStart,
                                 @RequestParam(required = false) @ApiParam("质保结束时间结束") String qualityAssuranceEndTimeEnd) {
        final Map<String, Object> params = new HashMap<>();
        params.put("code", code);
        params.put("name", name);
        params.put("managerName", managerName);
        params.put("typesStr", typesStr);
        params.put("customerName", customerName);
        params.put("qualityAssuranceStartTimeStart", qualityAssuranceStartTimeStart);
        params.put("qualityAssuranceStartTimeEnd", qualityAssuranceStartTimeEnd);
        params.put("qualityAssuranceEndTimeStart", qualityAssuranceEndTimeStart);
        params.put("qualityAssuranceEndTimeEnd", qualityAssuranceEndTimeEnd);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectTransferQuality/exportToTransfer", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<ProjectDto>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<ProjectDto>>>() {
        });
        if (dataResponse == null || dataResponse.getData() == null) {
            throw new BizException(Code.ERROR,"导出数据为空");
        }
        //导出操作
        String fileName = String.format("%s%s%s", "待转质保列表_", DateUtils.format(new Date(), "yyyyMMddHHmmss"), ".xls");

        List<ProjectToTransferExcelVO> projectExcelVOS = BeanConverter.copy(dataResponse.getData(), ProjectToTransferExcelVO.class);
        for (int i = 0; i < projectExcelVOS.size(); i++) {
            ProjectToTransferExcelVO projectExcelVO = projectExcelVOS.get(i);
            projectExcelVO.setNum(i + 1);
        }
        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(projectExcelVOS, ProjectToTransferExcelVO.class, null, "项目信息", true);
        ExportExcelUtil.downLoadExcel(fileName, response, workbook);
    }

    @ApiOperation("标记无需转质保")
    @GetMapping("markNotNeedTransferQuality")
    public Response markNotNeedTransferQuality(@RequestParam Long projectId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectTransferQuality/markNotNeedTransferQuality", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation("修改质保时间")
    @PostMapping("updateQualityAssuranceTime")
    public Response updateQualityAssuranceTime(@RequestBody ProjectDto projectDto) {
        final String url = String.format("%sstatistics/projectTransferQuality/updateQualityAssuranceTime", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, projectDto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    private Map buildParam(ProjectDto projectDto) {
        final Map<String, Object> params = new HashMap<>();
        // 项目编号、项目名称、业务实体、客户名称、客户CRM编码、销售子合同编号、商机编号、“项目经理”模糊查询
        // "项目类型”、“项目属性”、项目状态、是否预立项使用下拉搜索(多选);
        params.put("code", projectDto.getCode());
        params.put("name", projectDto.getName());
        params.put("ouIds", projectDto.getOuIds());
        params.put("customerName", projectDto.getCustomerName());
        params.put("customerCode", projectDto.getCustomerCode());
        params.put("contractCode", projectDto.getContractCode());
        params.put("businessCode", projectDto.getBusinessCode());
        params.put("businessId", projectDto.getBusinessId());
        params.put("managerName", projectDto.getManagerName());
        params.put("types", projectDto.getTypes());
        params.put("priceTypes", projectDto.getPriceTypes());
        params.put("statuses", projectDto.getStatuses());
        params.put("previewFlags", projectDto.getPreviewFlags());

        params.put("status", projectDto.getStatus());
        params.put("priceType", projectDto.getPriceType());
        params.put("type", projectDto.getType());
        params.put("ouId", projectDto.getOuId());
        params.put("previewFlag", projectDto.getPreviewFlag());

        params.put("typesStr", projectDto.getTypesStr());
        params.put("priceTypesStr", projectDto.getPriceTypesStr());
        params.put("statusesStr", projectDto.getStatusesStr());
        params.put("previewFlagsStr", projectDto.getPreviewFlagsStr());
        params.put("ouIdsStr", projectDto.getOuIdsStr());
        params.put("resourceStatusStr", projectDto.getResourceStatusStr());
        params.put("isObjectiveProject", projectDto.getIsObjectiveProject());
        params.put("projectLevel", projectDto.getProjectLevel());
        params.put("fuzzyLike", projectDto.getFuzzyLike());
        params.put("unitName", projectDto.getUnitName());
        params.put("departmentName", projectDto.getDepartmentName());
        params.put("departmentNameStr", projectDto.getDepartmentNameStr());

        return params;
    }
}
