package com.midea.pam.gateway.ctc.web;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.WbsDynamicFieldsDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("wbs模板规则信息")
@RestController
@RequestMapping("wbsTemplateRule")
public class WbsTemplateRuleController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "获取wbs动态列")
    @GetMapping("getWbsDynamicFields")
    public Response getWbsDynamicFields(@ApiParam("wbs模板信息id") @RequestParam Long wbsTemplateInfoId) {
        Map<String, Object> param = new HashMap<>(5);
        param.put("wbsTemplateInfoId", wbsTemplateInfoId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "wbsTemplateRule/getWbsDynamicFields", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<WbsDynamicFieldsDto>>>() {
        });
    }


}
