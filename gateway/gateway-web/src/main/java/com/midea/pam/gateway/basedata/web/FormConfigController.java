package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.FormConfigDTO;
import com.midea.pam.common.basedata.entity.FormConfig;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("动态表单配置")
@RestController
@RequestMapping("formConfig")
public class FormConfigController extends ControllerHelper {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private RestTemplate restTemplate;

    /**
     * 根据使用单位id和模块名查询
     *
     * @param unitId    使用单位   必填字段
     * @param module    模块 必填字段
     * @param subModule 子模块 非必填
     * @param subModule 场景 非必填
     * @return
     */
    @ApiOperation(value = "根据使用单位id和模块名查询", response = FormConfig.class)
    @GetMapping("listByUnitIdAndModule")
    public Response listByUnitIdAndModule(@RequestParam(required = false) Long unitId,
                                          @RequestParam String module,
                                          @RequestParam(required = false) String subModule,
                                          @RequestParam(required = false) String scene) {
        final Map<String, Object> param = new HashMap<>(2);
        param.put("unitId", unitId);
        param.put("module", module);
        param.put("subModule", subModule);
        param.put("scene", scene);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/formConfig/listByUnitIdAndModule", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<FormConfigDTO>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<FormConfigDTO>>>() {
        });
        return response;
    }

    @ApiOperation(value = "保存动态表单配置")
    @PostMapping("save")
    public Response save(@RequestBody List<FormConfig> formConfigList, @RequestParam(required = false) String scene) {
        String url = String.format("%s/formConfig/save?scene=%s", ModelsEnum.BASEDATA.getBaseUrl(), scene);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, formConfigList, String.class);
        DataResponse<Boolean> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    /**
     * 根据使用单位查询商机复盘配置
     *
     * @param unitId 使用单位ID，不填查当前用户使用单位
     * @return
     */
    @ApiOperation(value = "商机复盘配置", response = FormConfig.class)
    @GetMapping("businessReplayConfig")
    public Response businessReplayConfig(Long unitId) {
        final Map<String, Object> param = new HashMap<>(1);
        param.put("unitId", unitId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/formConfig/businessReplayConfig", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<FormConfig>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<FormConfig>>>() {
        });
        return response;
    }

    /**
     * 从其它使用单位复制配置数据到当前使用单位，需要前端提示用户会覆盖之前的数据
     *
     * @param srcUnitId  配置数据源使用单位
     * @param module     配置数据目标使用单位，
     * @param destUnitId 配置数据目标使用单位，
     * @return
     */
    @ApiOperation(value = "复制到当前使用单位")
    @GetMapping("copyToCurrentUnit")
    public Response copyToCurrentUnit(@RequestParam Long srcUnitId, @RequestParam String module, @RequestParam(required = false) Long destUnitId) {
        final Map<String, Object> param = new HashMap<>(1);
        param.put("srcUnitId", srcUnitId);
        param.put("module", module);
        param.put("destUnitId", destUnitId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/formConfig/copyToCurrentUnit", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<String> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<String>>() {
        });
        return response;
    }

    /**
     * 根据使用单位id和模块名，子模块，codeName查询
     *
     * @param unitId    使用单位   必填字段
     * @param module    模块 必填字段
     * @param subModule 子模块 必填
     * @return
     */
    @ApiOperation(value = "根据使用单位id和模块名，子模块，codeName查询", response = FormConfig.class)
    @GetMapping("listByUnitIdAndCodeName")
    public Response listByUnitIdAndCodeName(@RequestParam(required = false) Long unitId,
                                            @RequestParam(required = false) String module,
                                            @RequestParam(required = false) String subModule
    ) {
        final Map<String, Object> param = new HashMap<>(2);
        param.put("unitId", unitId);
        param.put("module", module);
        param.put("subModule", subModule);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/formConfig/listByUnitIdAndCodeName", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<FormConfigDTO>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<FormConfigDTO>>>() {
        });
        return response;
    }

    @ApiOperation(value = "保存/删除商机报价单动态表单配置")
    @PostMapping("saveAndDel")
    public Response saveAndDel(@RequestBody FormConfig formConfigList, @RequestParam(required = false) Integer type) {
        String url = String.format("%s/formConfig/saveAndDel?scene=%s", ModelsEnum.BASEDATA.getBaseUrl(), type);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, formConfigList, String.class);
        DataResponse<Boolean> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "根据使用单位id、模块名、编码、使用场景查询配置值")
    @GetMapping("getConfigValue")
    public Response getConfigValue(@RequestParam Long unitId,
                                   @RequestParam String module,
                                   @RequestParam String code,
                                   @RequestParam String scene) {
        final Map<String, Object> param = new HashMap<>(8);
        param.put("unitId", unitId);
        param.put("module", module);
        param.put("code", code);
        param.put("scene", scene);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/formConfig/getConfigValue", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Map<String, String>> response = JSON.parseObject(res, new TypeReference<DataResponse<Map<String, String>>>() {
        });
        return response;
    }
}
