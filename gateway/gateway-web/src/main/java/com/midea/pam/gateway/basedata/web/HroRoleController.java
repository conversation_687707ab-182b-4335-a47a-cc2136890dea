package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.HroRoleDto;
import com.midea.pam.common.basedata.excelVo.HroRoleExcelVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.BeanConverter;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.utils.ExportExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Api("人力点工角色")
@RestController
@RequestMapping("hroRole")
public class HroRoleController {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation("保存或更新人力点工角色")
    @PostMapping("saveOrUpdate")
    public Response saveOrUpdate(@RequestBody List<HroRoleDto> hroRoleDtos){
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "hroRole/saveOrUpdate";
        return restTemplate.postForObject(url,hroRoleDtos,DataResponse.class);
    }

    @ApiOperation("查询当前单位的人力点工角色")
    @GetMapping("findHroRole")
    public Response findHroRole(HroRoleDto hroRoleDto){
        return getHroRole(hroRoleDto);
    }

    @ApiOperation("导出人力点工角色")
    @GetMapping("exportHroRole")
    public void exportHroRole(HttpServletResponse response,HroRoleDto hroRoleDto){
        DataResponse<List<HroRoleDto>> dataResponse = getHroRole(hroRoleDto);
        if(dataResponse.getCode()==0){
            List<HroRoleDto> hroRoleDtos = dataResponse.getData();
            if(ListUtils.isNotEmpty(hroRoleDtos)){
                List<HroRoleExcelVo> hroRoleExcelVos = BeanConverter.copy(hroRoleDtos, HroRoleExcelVo.class);
                for (int i = 0; i < hroRoleExcelVos.size(); i++) {
                    hroRoleExcelVos.get(i).setIndex(i+1);
                }
                ExportExcelUtil.exportExcel(hroRoleExcelVos, null, "Sheet1", HroRoleExcelVo.class, "人力点工角色.xls", response);
            }
        }
    }

    private DataResponse<List<HroRoleDto>> getHroRole(HroRoleDto hroRoleDto){
        Map<String,String> params = new HashMap<>(4);
        params.put("materialCode",hroRoleDto.getMaterialCode());
        params.put("roleName",hroRoleDto.getRoleName());
        params.put("wbsCode",hroRoleDto.getWbsCode());
        params.put("projectActivityCode",hroRoleDto.getProjectActivityCode());
        params.put("roleDescribe",hroRoleDto.getRoleDescribe());
        String url = buildUrl(ModelsEnum.BASEDATA.getBaseUrl() + "hroRole/findHroRole",params);
        String res = restTemplate.getForObject(url,String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<List<HroRoleDto>>>() {});
    }

    @ApiOperation("查询当前单位的下有效的人力点工角色")
    @GetMapping("findHroRoleByName")
    public Response findHroRoleByName(@RequestParam(required = false) String roleName){
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "hroRole/findHroRoleByName";
        if(roleName!=null){
            url += "?roleName=" + roleName;
        }
        return restTemplate.getForObject(url,DataResponse.class);
    }

    private String buildUrl(String url,Map<String,String> params){
        String queryParams = params.entrySet().stream()
                .filter(e -> StringUtils.isNotEmpty(e.getValue()))
                .map(e -> e.getKey() + "=" + e.getValue())
                .collect(Collectors.joining("&"));
        if(!queryParams.isEmpty()){
            url += "?" + queryParams;
        }
        return url;
    }
}
