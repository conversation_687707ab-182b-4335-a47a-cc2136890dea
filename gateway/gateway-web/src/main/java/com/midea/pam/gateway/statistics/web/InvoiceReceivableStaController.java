package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.InvoiceApplyDto;
import com.midea.pam.common.ctc.dto.InvoiceReceivableDto;
import com.midea.pam.common.ctc.entity.InvoiceReceivableExternalLogs;
import com.midea.pam.common.ctc.excelVo.KpyImportVo;
import com.midea.pam.common.ctc.vo.InvoiceReceivableExcelVO;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.common.utils.FileUtil;
import com.midea.pam.gateway.remote.RemoteImportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: common-module
 * @description: 应收发票
 * @author:zhongpeng
 * @create:2020-03-19 09:10
 **/
@Api("开票申请")
@RestController
@RequestMapping("statistics/invoiceReceivable")
public class InvoiceReceivableStaController extends ControllerHelper {

    private static final int maxtextStyle = 10000;

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private RemoteImportService remoteImportService;


    @ApiOperation(value = "查询", response = InvoiceReceivableDto.class)
    @GetMapping("query")
    public Response query(@RequestParam(required = false) @ApiParam(value = "应收发票号") String invoiceCode,
                          @RequestParam(required = false) @ApiParam(value = "开票申请号") String applyCode,
                          @RequestParam(required = false) @ApiParam(value = "应收发票类型") String invoiceType,
                          @RequestParam(required = false) @ApiParam(value = "开票日期开始") String invoiceDateStart,
                          @RequestParam(required = false) @ApiParam(value = "开票日期结束") String invoiceDateEnd,
                          @RequestParam(required = false) @ApiParam(value = "开票客户名称") String externalInvoiceUser,
                          @RequestParam(required = false) @ApiParam(value = "客户名称") String customerName,
                          @RequestParam(required = false) @ApiParam(value = "客户CRM编码") String customerCode,
                          @RequestParam(required = false) @ApiParam(value = "业务实体") String ouId,
                          @RequestParam(required = false) @ApiParam(value = "总账日期开始") String glDateStart,
                          @RequestParam(required = false) @ApiParam(value = "总账日期结束") String glDateEnd,
                          @RequestParam(required = false) @ApiParam(value = "状态") String status,
                          @RequestParam(required = false) @ApiParam(value = "金额") String taxIncludedPrice,
                          @RequestParam(required = false) @ApiParam(value = "erp状态") String erpStatus,
                          @RequestParam(required = false) @ApiParam(value = "erp消息") String erpMsg,
                          @RequestParam(required = false) @ApiParam(value = "创建日期开始") String createAtStart,
                          @RequestParam(required = false) @ApiParam(value = "创建日期结束") String createAtEnd,
                          @RequestParam(required = false) @ApiParam(value = "合同id") Long contractId,
                          @RequestParam(required = false) @ApiParam(value = "开票申请头表ID") Long applyHeaderId,
                          @RequestParam(required = false) @ApiParam(value = "创建人") String createUserName,
                          @RequestParam(required = false) @ApiParam(value = "合同ID") String contractIdsStr,
                          @RequestParam(required = false) @ApiParam(value = "合同编号") String contractCode,
                          @RequestParam(required = false) @ApiParam(value = "合同名称") String contractName,
                          @RequestParam(required = false) @ApiParam(value = "项目名称") String projectName,
                          @RequestParam(required = false) @ApiParam(value = "项目编号") String projectCode,
                          @RequestParam(required = false) @ApiParam(value = "来源") String resouce,
                          @RequestParam(required = false) @ApiParam(value = "冲销状态") String manyReverseStatus,
                          @RequestParam(required = false) @ApiParam(value = "是否过滤") String filter,
                          @RequestParam(required = false) @ApiParam(value = "发票来源") String sourceStr,

                          @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                          @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put("invoiceCode", invoiceCode);
        param.put("applyCode", applyCode);
        param.put("invoiceType", invoiceType);
        param.put("invoiceDateStart", invoiceDateStart);
        param.put("invoiceDateEnd", invoiceDateEnd);
        param.put("externalInvoiceUser", externalInvoiceUser);
        param.put("customerName", customerName);
        param.put("customerCode", customerCode);
        param.put("ouId", ouId);
        param.put("glDateStart", glDateStart);
        param.put("glDateEnd", glDateEnd);
        param.put("status", status);
        param.put("taxIncludedPrice", taxIncludedPrice);
        param.put("erpStatus", erpStatus);
        param.put("resouce", resouce);
        param.put("erpMsg", erpMsg);
        param.put("createAtStart", createAtStart);
        param.put("createAtEnd", createAtEnd);
        param.put("contractId", contractId);
        param.put("applyHeaderId", applyHeaderId);
        param.put("contractIdsStr", contractIdsStr);
        param.put("createUserName", createUserName);
        param.put("manyReverseStatus", manyReverseStatus);

        param.put("contractCode", contractCode);
        param.put("contractName", contractName);
        param.put("projectName", projectName);
        param.put("projectCode", projectCode);
        param.put("filter", filter);
        param.put("sourceStr", sourceStr);

        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/invoiceReceivable/query", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();

        DataResponse<JSONObject> data = Response.dataResponse();
        data.setData(JSON.parseObject(res));
        return data;
    }

    //PengBo
    @ApiOperation(value = "查询--(优化扩展)", response = InvoiceReceivableDto.class)
    @GetMapping("queryExt")
    public Response queryExt(@RequestParam(required = false) @ApiParam(value = "应收发票号") String invoiceCode,
                             @RequestParam(required = false) @ApiParam(value = "开票申请号") String applyCode,
                             @RequestParam(required = false) @ApiParam(value = "应收发票类型") String invoiceType,
                             @RequestParam(required = false) @ApiParam(value = "开票日期开始") String invoiceDateStart,
                             @RequestParam(required = false) @ApiParam(value = "开票日期结束") String invoiceDateEnd,
                             @RequestParam(required = false) @ApiParam(value = "开票客户名称") String externalInvoiceUser,
                             @RequestParam(required = false) @ApiParam(value = "客户名称") String customerName,
                             @RequestParam(required = false) @ApiParam(value = "客户CRM编码") String customerCode,
                             @RequestParam(required = false) @ApiParam(value = "业务实体") String ouId,
                             @RequestParam(required = false) @ApiParam(value = "总账日期开始") String glDateStart,
                             @RequestParam(required = false) @ApiParam(value = "总账日期结束") String glDateEnd,
                             @RequestParam(required = false) @ApiParam(value = "状态") String status,
                             @RequestParam(required = false) @ApiParam(value = "金额") String taxIncludedPrice,
                             @RequestParam(required = false) @ApiParam(value = "erp状态") String erpStatus,
                             @RequestParam(required = false) @ApiParam(value = "erp消息") String erpMsg,
                             @RequestParam(required = false) @ApiParam(value = "创建日期开始") String createAtStart,
                             @RequestParam(required = false) @ApiParam(value = "创建日期结束") String createAtEnd,
                             @RequestParam(required = false) @ApiParam(value = "合同id") Long contractId,
                             @RequestParam(required = false) @ApiParam(value = "开票申请头表ID") Long applyHeaderId,
                             @RequestParam(required = false) @ApiParam(value = "创建人") String createUserName,
                             @RequestParam(required = false) @ApiParam(value = "合同ID") String contractIdsStr,
                             @RequestParam(required = false) @ApiParam(value = "合同编号") String contractCode,
                             @RequestParam(required = false) @ApiParam(value = "合同名称") String contractName,
                             @RequestParam(required = false) @ApiParam(value = "项目名称") String projectName,
                             @RequestParam(required = false) @ApiParam(value = "项目编号") String projectCode,
                             @RequestParam(required = false) @ApiParam(value = "来源") String resouce,
                             @RequestParam(required = false) @ApiParam(value = "冲销状态") String manyReverseStatus,
                             @RequestParam(required = false) @ApiParam(value = "来源") String filter,
                             @RequestParam(required = false) @ApiParam(value = "发票来源") String sourceStr,
                             @RequestParam(required = false) @ApiParam(value = "应收发票类型，多个逗号分隔") String invoiceTypeStr,
                             @RequestParam(required = false) @ApiParam(value = "应收发票到期日同步状态，多个逗号分隔") String dueDateStatuses,
                             @RequestParam(required = false) @ApiParam(value = "美的开票平台推送状态") Integer sendMifStatus,
                             @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                             @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put("invoiceCode", invoiceCode);
        param.put("applyCode", applyCode);
        param.put("invoiceType", invoiceType);
        param.put("invoiceDateStart", invoiceDateStart);
        param.put("invoiceDateEnd", invoiceDateEnd);
        param.put("externalInvoiceUser", externalInvoiceUser);
        param.put("customerName", customerName);
        param.put("customerCode", customerCode);
        param.put("ouId", ouId);
        param.put("glDateStart", glDateStart);
        param.put("glDateEnd", glDateEnd);
        param.put("status", status);
        param.put("taxIncludedPrice", taxIncludedPrice);
        param.put("erpStatus", erpStatus);
        param.put("resouce", resouce);
        param.put("erpMsg", erpMsg);
        param.put("createAtStart", createAtStart);
        param.put("createAtEnd", createAtEnd);
        param.put("contractId", contractId);
        param.put("applyHeaderId", applyHeaderId);
        param.put("contractIdsStr", contractIdsStr);
        param.put("createUserName", createUserName);
        param.put("manyReverseStatus", manyReverseStatus);
        param.put("dueDateStatuses", dueDateStatuses);

        param.put("contractCode", contractCode);
        param.put("contractName", contractName);
        param.put("projectName", projectName);
        param.put("projectCode", projectCode);
        param.put("filter", filter);
        param.put("sourceStr", sourceStr);
        param.put("invoiceTypeStr", invoiceTypeStr);
        param.put("sendMifStatus", sendMifStatus);

        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/invoiceReceivable/queryExt", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();

        DataResponse<JSONObject> data = Response.dataResponse();
        data.setData(JSON.parseObject(res));
        return data;
    }

    //PengBo
    @ApiOperation(value = "详情查询--（优化扩展）", response = InvoiceApplyDto.class)
    @GetMapping("queryExtDetail")
    public Response queryExtDetail(@RequestParam(required = true) @ApiParam(value = "应收发票Id") Long id) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/invoiceReceivable/queryExtDetail/", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<InvoiceReceivableDto> response = JSON.parseObject(res, new TypeReference<DataResponse<InvoiceReceivableDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "红冲发票同步ERP-重新推送")
    @GetMapping("/resend/{id}")
    public com.midea.pam.common.base.Response active(@PathVariable Long id) {
        final String url = String.format("%sstatistics/invoiceReceivable/resend/" + id, ModelsEnum.STATISTICS.getBaseUrl());
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<com.midea.pam.common.base.DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "详情查询", response = InvoiceApplyDto.class)
    @GetMapping("details/{id}")
    public Response details(@PathVariable Long id) {

        final Map<String, Object> param = new HashMap<>();
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/invoiceReceivable/details/" + id, param);
        String res = restTemplate.getForEntity(url, String.class).getBody();

        DataResponse<InvoiceReceivableDto> response = JSON.parseObject(res, new TypeReference<DataResponse<InvoiceReceivableDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "应收发票列表导出", response = ResponseMap.class)
    @GetMapping("/export")
    public void listExport(HttpServletResponse response, @RequestParam(required = false) @ApiParam(value = "应收发票号") String invoiceCode,
                           @RequestParam(required = false) @ApiParam(value = "开票申请号") String applyCode,
                           @RequestParam(required = false) @ApiParam(value = "应收发票类型") String invoiceType,
                           @RequestParam(required = false) @ApiParam(value = "开票日期开始") String invoiceDateStart,
                           @RequestParam(required = false) @ApiParam(value = "开票日期结束") String invoiceDateEnd,
                           @RequestParam(required = false) @ApiParam(value = "开票客户名称") String externalInvoiceUser,
                           @RequestParam(required = false) @ApiParam(value = "客户名称") String customerName,
                           @RequestParam(required = false) @ApiParam(value = "客户CRM编码") String customerCode,
                           @RequestParam(required = false) @ApiParam(value = "业务实体") String ouId,
                           @RequestParam(required = false) @ApiParam(value = "总账日期开始") String glDateStart,
                           @RequestParam(required = false) @ApiParam(value = "总账日期结束") String glDateEnd,
                           @RequestParam(required = false) @ApiParam(value = "状态") String status,
                           @RequestParam(required = false) @ApiParam(value = "金额") String taxIncludedPrice,
                           @RequestParam(required = false) @ApiParam(value = "erp状态") String erpStatus,
                           @RequestParam(required = false) @ApiParam(value = "erp消息") String erpMsg,
                           @RequestParam(required = false) @ApiParam(value = "创建日期开始") String createAtStart,
                           @RequestParam(required = false) @ApiParam(value = "创建日期结束") String createAtEnd,
                           @RequestParam(required = false) @ApiParam(value = "合同id") Long contractId,
                           @RequestParam(required = false) @ApiParam(value = "开票申请头表ID") Long applyHeaderId,
                           @RequestParam(required = false) @ApiParam(value = "创建人") String createUserName,
                           @RequestParam(required = false) @ApiParam(value = "合同ID") String contractIdsStr,
                           @RequestParam(required = false) @ApiParam(value = "合同编号") String contractCode,
                           @RequestParam(required = false) @ApiParam(value = "合同名称") String contractName,
                           @RequestParam(required = false) @ApiParam(value = "项目名称") String projectName,
                           @RequestParam(required = false) @ApiParam(value = "项目编号") String projectCode,
                           @RequestParam(required = false) @ApiParam(value = "冲销状态") String manyReverseStatus,
                           @RequestParam(required = false) @ApiParam(value = "发票来源") String sourceStr,
                           @RequestParam(required = false) @ApiParam(value = "应收发票类型，多个逗号分隔") String invoiceTypeStr,
                           @RequestParam(required = false) @ApiParam(value = "应收发票到期日同步状态，多个逗号分隔") String dueDateStatuses) {
        final Map<String, Object> param = new HashMap<>();
        param.put("invoiceCode", invoiceCode);
        param.put("applyCode", applyCode);
        param.put("invoiceType", invoiceType);
        param.put("invoiceDateStart", invoiceDateStart);
        param.put("invoiceDateEnd", invoiceDateEnd);
        param.put("externalInvoiceUser", externalInvoiceUser);
        param.put("customerName", customerName);
        param.put("customerCode", customerCode);
        param.put("ouId", ouId);
        param.put("glDateStart", glDateStart);
        param.put("glDateEnd", glDateEnd);
        param.put("status", status);
        param.put("taxIncludedPrice", taxIncludedPrice);
        param.put("erpStatus", erpStatus);
        param.put("erpMsg", erpMsg);
        param.put("createAtStart", createAtStart);
        param.put("createAtEnd", createAtEnd);
        param.put("contractId", contractId);
        param.put("applyHeaderId", applyHeaderId);
        param.put("contractIdsStr", contractIdsStr);
        param.put("createUserName", createUserName);

        param.put("contractCode", contractCode);
        param.put("contractName", contractName);
        param.put("projectName", projectName);
        param.put("projectCode", projectCode);
        param.put("manyReverseStatus", manyReverseStatus);
        param.put("sourceStr", sourceStr);
        param.put("invoiceTypeStr", invoiceTypeStr);
        param.put("dueDateStatuses", dueDateStatuses);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/invoiceReceivable/v1/export", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });
        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("应收发票_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");
        JSONArray invoiceReceivableDtosArr = (JSONArray) resultMap.get("invoiceReceivableDtos");
        List<InvoiceReceivableExcelVO> invoiceReceivableExcelVOS = JSONObject.parseArray(invoiceReceivableDtosArr.toJSONString(), InvoiceReceivableExcelVO.class);

        if (ListUtils.isNotEmpty(invoiceReceivableExcelVOS)) {
            for (int i = 0; i < invoiceReceivableExcelVOS.size(); i++) {
                InvoiceReceivableExcelVO invoiceReceivableExcelVO = invoiceReceivableExcelVOS.get(i);
                String taxRace = invoiceReceivableExcelVO.getTaxRace() == null ? "" : invoiceReceivableExcelVO.getTaxRace().stripTrailingZeros().toPlainString() + "%";
                invoiceReceivableExcelVO.setTaxRaceStr(taxRace);
                if (invoiceReceivableExcelVO.getQuantity() == null) {
                    invoiceReceivableExcelVO.setQuantityStr("");
                } else {
                    BigDecimal bigDecimal = new BigDecimal(invoiceReceivableExcelVO.getQuantity() + "");
                    invoiceReceivableExcelVO.setQuantityStr(bigDecimal.stripTrailingZeros().toPlainString());
                }
                invoiceReceivableExcelVO.setNum(i + 1);
            }
        }

        Workbook workbook = ExportExcelUtil.buildDefaultSheet(invoiceReceivableExcelVOS, InvoiceReceivableExcelVO.class, null, "应收发票", Boolean.TRUE);
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);

    }

    @ApiOperation(value = "获取应收发票类型列表", notes = "应用场景：下拉框筛选")
    @GetMapping("getInvoiceTypeList")
    public Response getInvoiceTypeList() {
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/invoiceReceivable/getInvoiceTypeList", null);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<String>>>() {
        });
    }

    @ApiOperation(value = "金税信息导入模板下载")
    @GetMapping("/export/external")
    public void listExport(HttpServletResponse response) {
        // 创建新的Excel 工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();
        createSheet(workbook);
        ExportExcelUtil.downLoadExcel("应收发票_金税开票导入模板.xls", response, workbook);
    }

    @ApiOperation(value = "金税信息导入校验返回信息")
    @PostMapping("/import/kpyCheck")
    public com.midea.pam.common.base.Response kpyCheck(@RequestParam(value = "file") MultipartFile file) {

        List<KpyImportVo> kpyImportVos = FileUtil.importExcel2(file, KpyImportVo.class, 1, 0);
        // 移除空行的数据
        Iterator<KpyImportVo> iterator = kpyImportVos.iterator();
        while (iterator.hasNext()) {
            KpyImportVo next = iterator.next();
            if (StringUtils.isBlank(next.getSerialNumber())
                    && StringUtils.isBlank(next.getInvoiceCode())
                    && StringUtils.isBlank(next.getExternalInvoiceNum())
                    && StringUtils.isBlank(next.getExternalTaxIncludedPrice_dt())
                    && StringUtils.isBlank(next.getExternalInvoiceAmount_dt())
                    && StringUtils.isBlank(next.getExternalInvoiceTax_dt())
                    && next.getExternalInvoiceDate() == null
                    && StringUtils.isBlank(next.getExternalInvoiceUser())
                    && StringUtils.isBlank(next.getExternalInvoiceCode())
            ) {
                iterator.remove();
            }
        }
        if (ListUtils.isEmpty(kpyImportVos)) throw new BizException(Code.ERROR,"存在字段填写有误，请检查填写的字段");
        boolean b = kpyImportVos
                .stream()
//                .filter(s -> StringUtils.isNoneBlank(s.getSerialNumber()))
                .peek(KpyImportVo::checkNull)
                .anyMatch(s -> StringUtils.isNoneBlank(s.getErrMsg()));
        if (b) {
            return com.midea.pam.common.base.Response.err(kpyImportVos.stream()
                    .map(KpyImportVo::getErrMsg)
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining()));
        } else {
            return remoteImportService.kpyCheck(kpyImportVos);
        }

    }


    @ApiOperation(value = "金税信息")
    @PostMapping("/import/kpyImport")
    public com.midea.pam.common.base.Response kpyImport(@RequestBody List<KpyImportVo> kpyImportVos) {
        return remoteImportService.kpyImport(kpyImportVos);
    }

    @ApiOperation(value = "金税信息作废")
    @PostMapping("/kpyCancel")
    public com.midea.pam.common.base.Response kpyCancel(@RequestBody InvoiceReceivableExternalLogs logs) {
        return remoteImportService.kpyCancel(logs);
    }

    private void createSheet(HSSFWorkbook workbook) {
        HSSFSheet sheet = workbook.createSheet("应收发票_金税开票导入模板");
        sheet.setColumnWidth(0, 14 * 256);
        sheet.setColumnWidth(1, 20 * 256);
        sheet.setColumnWidth(2, 20 * 256);
        sheet.setColumnWidth(3, 26 * 256);
        sheet.setColumnWidth(4, 26 * 256);
        sheet.setColumnWidth(5, 20 * 256);
        sheet.setColumnWidth(6, 25 * 256);
        sheet.setColumnWidth(7, 25 * 256);
        sheet.setColumnWidth(8, 15 * 256);
        HSSFCellStyle remarkStyle = this.getRemarkStyle(workbook);
        HSSFCellStyle titleStyle = this.getTitleStyle(workbook);
        // 设置需要合并的单元格
        CellRangeAddress mergedRegion = new CellRangeAddress(0, 0, 0, 8);
        sheet.addMergedRegion(mergedRegion);

        ArrayList<String> arrayList = new ArrayList<String>();
        arrayList.add("导入说明:");
        arrayList.add("1、一张应收发票，对应一个金税-税票号");
        arrayList.add("2、外币应收发票，本页面的金额请填写本位币金额");
        arrayList.add("3、电票导入，发票代码按照税票号填写");
        arrayList.add("4、开票日期格式：年/月/日 例如 2024/01/01");
        String content = String.join("\n", arrayList);

        // 第一行 标题
        HSSFRow row0 = ExportExcelUtil.createCell(sheet, content, 0, 0, remarkStyle);
        row0.setHeightInPoints(75);

        HSSFRow row1 = ExportExcelUtil.createCell(sheet, "序号", 1, 0, titleStyle);
        ExportExcelUtil.createCell(row1, "应收发票号", 1, titleStyle);
        ExportExcelUtil.createCell(row1, "金税-税票号", 2, titleStyle);
        ExportExcelUtil.createCell(row1, "金税-开票金额（含税）", 3, titleStyle);
        ExportExcelUtil.createCell(row1, "金税-开票金额（不含税）", 4, titleStyle);
        ExportExcelUtil.createCell(row1, "金税-税额 ", 5, titleStyle);
        ExportExcelUtil.createCell(row1, "金税-开票日期", 6, titleStyle);
        ExportExcelUtil.createCell(row1, "金税-开票人", 7, titleStyle);
        ExportExcelUtil.createCell(row1, "发票代码", 8, titleStyle);

        //单元格设为文本格式，默认10000行
        HSSFCellStyle textStyle = workbook.createCellStyle();
        textStyle.setDataFormat(workbook.createDataFormat().getFormat("@")); //文本格式
        for (int i = 2; i < maxtextStyle; i++) {
            HSSFRow row = sheet.createRow(i);
            for (int j = 0; j < 8; j++) {
                HSSFCell cell = row.createCell(j);
                cell.setCellStyle(textStyle);
            }
        }
    }

    private HSSFCellStyle getRemarkStyle(HSSFWorkbook workbook) {
        // 设置字体
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 10);
        // 设置样式
        HSSFCellStyle style = workbook.createCellStyle();
        style.setVerticalAlignment(CellStyle.VERTICAL_CENTER);// 上下居中
        style.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        style.setWrapText(Boolean.TRUE);
        style.setFont(font);
        return style;
    }

    /**
     * 标题--样式
     *
     * @param workbook
     * @return
     */
    private HSSFCellStyle getTitleStyle(HSSFWorkbook workbook) {
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 12);
        // 设置head样式
        HSSFCellStyle style = workbook.createCellStyle();
        style.setVerticalAlignment(CellStyle.VERTICAL_CENTER);// 上下居中
        style.setAlignment(XSSFCellStyle.ALIGN_CENTER);
        style.setFont(font);
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        return style;
    }

}
