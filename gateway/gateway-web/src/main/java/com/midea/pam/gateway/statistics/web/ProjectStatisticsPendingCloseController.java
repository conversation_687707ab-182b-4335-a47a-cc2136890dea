package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.vo.ProjectStatisticsPendingCloseExcelVO;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.BeanConverter;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;


@Api("待结项列表")
@RestController
@RequestMapping("statistics/projectPendingClose")
public class ProjectStatisticsPendingCloseController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation("待结项列表")
    @PostMapping("page")
    public Response page(@RequestBody ProjectDto dto) {
        String url = String.format("%sstatistics/projectPendingClose/page", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<PageInfo<ProjectDto>> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<PageInfo<ProjectDto>>>() {
                });
        return response;
    }

    @ApiOperation("我的待结项列表")
    @PostMapping("selectMyRecordPage")
    public Response selectMyRecordPage(@RequestBody ProjectDto dto) {
        dto.setMe(Boolean.TRUE); //是否查询我的
        String url = String.format("%sstatistics/projectPendingClose/page", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<PageInfo<ProjectDto>> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<PageInfo<ProjectDto>>>() {
                });
        return response;
    }

    @ApiOperation("待结项列表导出")
    @PostMapping("export")
    public void listExport(HttpServletResponse response, @RequestBody ProjectDto dto) {
        final String url = String.format("%sstatistics/projectPendingClose/list", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<List<ProjectDto>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<ProjectDto>>>() {
        });
        if (dataResponse == null || dataResponse.getData() == null) {
            throw new BizException(Code.ERROR, "导出数据为空");
        }

        //导出操作
        String fileName = String.format("%s%s%s", "待结项列表_", DateUtils.format(new Date(), "yyyyMMddHHmmss"), ".xls");

        List<ProjectStatisticsPendingCloseExcelVO> projectExcelVOS = BeanConverter.copy(dataResponse.getData(), ProjectStatisticsPendingCloseExcelVO.class);
        for (int i = 0; i < projectExcelVOS.size(); i++) {
            ProjectStatisticsPendingCloseExcelVO projectExcelVO = projectExcelVOS.get(i);
            projectExcelVO.setNum(i + 1);
        }
        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(projectExcelVOS, ProjectStatisticsPendingCloseExcelVO.class, null, "基础信息", true);
        ExportExcelUtil.downLoadExcel(fileName, response, workbook);
    }

    @ApiOperation("我的待结项列表导出")
    @PostMapping("exportMyOrderRecord")
    public void exportMyOrderRecord(HttpServletResponse response, @RequestBody ProjectDto dto) {
        dto.setMe(Boolean.TRUE); //是否查询我的
        final String url = String.format("%sstatistics/projectPendingClose/list", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<List<ProjectDto>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<ProjectDto>>>() {
        });
        if (dataResponse == null || dataResponse.getData() == null) {
            throw new BizException(Code.ERROR, "导出数据为空");
        }

        //导出操作
        String fileName = String.format("%s%s%s", "我的待结项列表_", DateUtils.format(new Date(), "yyyyMMddHHmmss"), ".xls");

        List<ProjectStatisticsPendingCloseExcelVO> projectExcelVOS = BeanConverter.copy(dataResponse.getData(), ProjectStatisticsPendingCloseExcelVO.class);
        for (int i = 0; i < projectExcelVOS.size(); i++) {
            ProjectStatisticsPendingCloseExcelVO projectExcelVO = projectExcelVOS.get(i);
            projectExcelVO.setNum(i + 1);
        }
        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(projectExcelVOS, ProjectStatisticsPendingCloseExcelVO.class, null, "基础信息", true);
        ExportExcelUtil.downLoadExcel(fileName, response, workbook);
    }
}
