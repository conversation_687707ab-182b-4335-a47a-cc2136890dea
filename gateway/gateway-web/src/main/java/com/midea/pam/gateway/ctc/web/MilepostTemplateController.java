package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.MilepostTemplateDto;
import com.midea.pam.common.ctc.dto.MilepostTemplateStageDto;
import com.midea.pam.common.ctc.entity.MilepostTemplate;
import com.midea.pam.common.ctc.entity.MilepostTemplateDeliveryStandards;
import com.midea.pam.common.ctc.entity.MilepostTemplateStage;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("里程碑模板")
@RestController
@RequestMapping("milepostTemplate")
public class MilepostTemplateController extends ControllerHelper{
    @Resource
    private RestTemplate restTemplate;
    @ApiOperation(value = "新增/修改里程碑模板-阶段")
    @PostMapping("persistence")
    public Response persistence(@RequestBody MilepostTemplateDto dto) {
        final String url = String.format("%smilepostTemplate/persistence", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Long>>(){});
    }

    @ApiOperation(value = "查询并行交付线里程碑")
    @GetMapping("getParallelDeliveryLineMilePost")
    public Response getParallelDeliveryLineMilePost(@RequestParam Long parentId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("parentId", parentId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostTemplate/getParallelDeliveryLineMilePost", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<MilepostTemplateStageDto>>>() {
        });
    }

    @ApiOperation(value = " 分页查询里程碑模板")
    @GetMapping("page")
    public Response page(@RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10")final Integer pageSize,
                         @RequestParam(required = false) final String name,
                         @RequestParam(required = false) final Boolean onlyUserUnit,
                         @RequestParam(required = false) final String type) {
        DataResponse<PageInfo<MilepostTemplate>> response = Response.dataResponse();
        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        param.put(Constants.Page.NAME, name);
        param.put("onlyUserUnit", onlyUserUnit);
        param.put("type", type);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostTemplate/page", param);
        String res = restTemplate.getForEntity(url , String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<MilepostTemplateDto>>>(){});
    }

    @ApiOperation(value = "新增/修改里程碑阶段")
    @PostMapping("persistenceStage")
    public Response persistence(@RequestBody MilepostTemplateStage milepostTemplateStage) {
        final String url = String.format("%smilepostTemplate/persistenceStage", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, milepostTemplateStage, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Long>>(){});
    }

    @ApiOperation(value = " 查询里程碑模板下所有阶段")
    @GetMapping("findByTemplateId")
    public Response page(@RequestParam Long milepostTemplateId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("milepostTemplateId", milepostTemplateId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostTemplate/findByTemplateId", param);
        String res = restTemplate.getForEntity(url , String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<MilepostTemplateStageDto>>>(){});
    }

    @ApiOperation(value = " 查询项目类型下所有里程碑阶段")
    @GetMapping("findByTypeId")
    public Response findByTypeId(@RequestParam Long projectTypeId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectTypeId", projectTypeId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostTemplate/findByTypeId", param);
        String res = restTemplate.getForEntity(url , String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<MilepostTemplateStageDto>>>(){});
    }

    @ApiOperation(value = " 根据使用单位查询所有收入节点里程碑")
    @GetMapping("findIncomeMilepost")
    public Response findIncomeMilepost(@RequestParam(required = false) Long unitId) {
        DataResponse<List<MilepostTemplateStage>> response = Response.dataResponse();
        final Map<String, Object> param = new HashMap<>();
        param.put("unitId", unitId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostTemplate/findIncomeMilepost", param);
        String res = restTemplate.getForEntity(url , String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<MilepostTemplateStageDto>>>(){});
    }

    @ApiOperation(value = " 查询所有项目类型下所有里程碑阶段的交集")
    @PostMapping("findCommonMilepostStageNameByTypeIds")
    public Response findCommonMilepostStageNameByTypeIds(@RequestBody List<Long> projectTypeIds) {
        DataResponse<List<MilepostTemplateStage>> response = Response.dataResponse();
        final String url = String.format("%smilepostTemplate/findCommonMilepostStageNameByTypeIds", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, projectTypeIds, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<String>>>(){});
    }
}
