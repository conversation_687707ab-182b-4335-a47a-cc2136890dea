package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.WriteOffDto;
import com.midea.pam.common.ctc.vo.InvoiceExcVo;
import com.midea.pam.common.ctc.vo.ReceiptContractExcVo;
import com.midea.pam.common.ctc.vo.WriteOffExcVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Api("核销模块")
@RestController
@RequestMapping("statistics/writeOff")
public class WriteOffStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "核销分页查询")
    @GetMapping("page")
    public Response list(WriteOffDto writeOffDto,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(writeOffDto);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/writeOff/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<WriteOffDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<WriteOffDto>>>() {
        });

        return response;
    }

    @ApiOperation(value = "核销列表导出", response = ResponseMap.class)
    @GetMapping("/export")
    public void listExport(HttpServletResponse response, WriteOffDto writeOffDto) {
        final Map<String, Object> param = buildParam(writeOffDto);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/writeOff/v1/export", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("应收核销列表_"+ DateUtils.format(new Date(),"yyyyMMddHHmmss"));
        fileName.append(".xls");

        JSONArray writeOffArr = (JSONArray) resultMap.get("writeOff");
        JSONArray invoiceExcVosArr = (JSONArray) resultMap.get("invoiceExcVos");
        JSONArray receiptContractExcVosArr = (JSONArray) resultMap.get("receiptContractExcVos");

        List<WriteOffExcVo> writeOffExcelVOS = JSONObject.parseArray(writeOffArr.toJSONString(), WriteOffExcVo.class);
        for (int i = 0; i < writeOffExcelVOS.size(); i++) {
            WriteOffExcVo writeOffExcelVo = writeOffExcelVOS.get(i);
            writeOffExcelVo.setNum(i + 1);
        }

        List<InvoiceExcVo> invoiceExcVos = JSONObject.parseArray(invoiceExcVosArr.toJSONString(), InvoiceExcVo.class);
        for (int i = 0; i < invoiceExcVos.size(); i++) {
            InvoiceExcVo invoiceExcelVO = invoiceExcVos.get(i);
            invoiceExcelVO.setNum(i + 1);
        }

        List<ReceiptContractExcVo> receiptContract = JSONObject.parseArray(receiptContractExcVosArr.toJSONString(), ReceiptContractExcVo.class);
        for (int i = 0; i < receiptContract.size(); i++) {
            ReceiptContractExcVo receiptContractExcelVO = receiptContract.get(i);
            receiptContractExcelVO.setNum(i + 1);
        }

        final Workbook workbook = com.midea.pam.common.util.ExportExcelUtil.buildDefaultSheet(writeOffExcelVOS, WriteOffExcVo.class, null, "核销列表", true);
        com.midea.pam.common.util.ExportExcelUtil.addSheet(workbook, invoiceExcVos, InvoiceExcVo.class, null, "收款核销发票明细", true);
        com.midea.pam.common.util.ExportExcelUtil.addSheet(workbook, receiptContract, ReceiptContractExcVo.class, null, "收款合同分配明细", true);

        com.midea.pam.common.util.ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    private Map buildParam(WriteOffDto writeOffDto) {

        final Map<String, Object> params = new HashMap<>();
        params.put("crmCode", writeOffDto.getCrmCode());
        params.put("customerName", writeOffDto.getCustomerName());
        params.put("receiptCode", writeOffDto.getReceiptCode());
        params.put("claimByName", writeOffDto.getClaimByName());
        params.put("ouId", writeOffDto.getOuId());

        params.put("contractStatusStr", writeOffDto.getContractStatusStr());
        params.put("writeOffStatusStr", writeOffDto.getWriteOffStatusStr());
        params.put("invoiceSyncStatusStr", writeOffDto.getInvoiceSyncStatusStr());
        params.put("ouIdStr", writeOffDto.getOuIdStr());

        params.put("payDateStart", writeOffDto.getPayDateStart());
        params.put("payDateEnd", writeOffDto.getPayDateEnd());
        params.put("id",writeOffDto.getId());
        params.put("receiptClaimDetailIds",writeOffDto.getReceiptClaimDetailIds());
        params.put("resource",writeOffDto.getResource());
        params.put("bussinTypeStr",writeOffDto.getBussinTypeStr());
        return params;
    }
}
