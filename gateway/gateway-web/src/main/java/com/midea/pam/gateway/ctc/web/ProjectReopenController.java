package com.midea.pam.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.entity.ProjectReopenHeader;
import com.midea.pam.common.ctc.vo.ProjectVO;
import com.midea.pam.common.enums.ModelsEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static com.midea.pam.common.util.StringUtils.buildGetUrl;

/**
 * Description
 * Created by lintx
 * Date 2021/12/13 17:43
 */
@Api("重新打开项目业务")
@RestController
@RequestMapping("projectReopen")
public class ProjectReopenController {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation("按id查询项目基本信息")
    @GetMapping("getBaseInfoByProjectId")
    public Response getBaseInfoByProjectId(@RequestParam Long projectId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "projectReopen/getBaseInfoByProjectId", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectDto>>() {
        });
    }

    @ApiOperation("保存")
    @PostMapping("save")
    public Response save(@RequestBody ProjectDto projectDto) {
        final String url = String.format("%s%s", ModelsEnum.CTC.getBaseUrl(), "projectReopen/save");
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, projectDto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<ProjectVO>>() {
        });
    }

    @ApiOperation("根据重新打开变更ID查询项目ID")
    @GetMapping("getProjectIdByReopenHeader")
    public Response getProjectIdByReopenHeader(@RequestParam Long projectReopenHeaderId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectReopenHeaderId", projectReopenHeaderId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "projectReopen/getProjectIdByReopenHeader", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Long>>() {
        });
    }

    @ApiOperation("根据重新打开变更ID查询项目ID")
    @PostMapping("initReopenHeader")
    public Response initReopenHeader(@RequestBody ProjectReopenHeader dto) {
        String url = String.format("%sprojectReopen/initReopenHeader", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, dto, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
    }
}
