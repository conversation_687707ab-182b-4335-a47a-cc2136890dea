package com.midea.pam.gateway;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.SdpReceivePaymentApplyHandleResultDTO;
import com.midea.pam.common.ctc.dto.SdpReceivePaymentInvoiceDetailRequestResultDTO;
import com.midea.pam.common.ctc.dto.SdpReceivePaymentInvoiceRequestISPResultDTO;
import com.midea.pam.common.enums.IFlowExpcetionReBuildFormTypeEnum;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.paymentapply.service.GSCInvoiceDetailAutoBuildMipWorkFlowService;
import com.midea.pam.gateway.paymentapply.service.GSCInvoiceIspAutoBuildMipWorkFlowService;
import com.midea.pam.gateway.paymentapply.service.PaymentApplyAutoBuildMipWorkFlowService;
import com.midea.pam.gateway.service.FormInstanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Api("异常流程重新发起")
@RestController
@RequestMapping("IFlowExceptionReBuild")
public class IFlowExceptionReBuildController {

    private static final Logger logger = LoggerFactory.getLogger(IFlowExceptionReBuildController.class);

    @Resource
    private  RestTemplate restTemplate;
    @Resource
    private PaymentApplyAutoBuildMipWorkFlowService paymentApplyAutoBuildMipWorkFlowService;

    @Resource
    private GSCInvoiceIspAutoBuildMipWorkFlowService gscInvoiceIspAutoBuildMipWorkFlowService;

    @Resource
    private GSCInvoiceDetailAutoBuildMipWorkFlowService gscInvoiceDetailAutoBuildMipWorkFlowService;

    @Resource
    private FormInstanceService formInstanceService;


    private String buildGetUrl(String baseUrl, String path, Map<String, Object> params) {
        StringBuilder urlBuilder = new StringBuilder(baseUrl).append(path).append("?");
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            urlBuilder.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        return urlBuilder.toString().replaceAll("&$", "");
    }

    @ApiOperation("异常流程重新发起")
    @RequestMapping("rebuild")
    public Response rebuild(@RequestParam Integer typeCode, @RequestParam String formId) {
        logger.info("异常流程重新发起-开始处理, typeCode: {}, formId: {}", typeCode, formId);

        IFlowExpcetionReBuildFormTypeEnum formTypeEnum = IFlowExpcetionReBuildFormTypeEnum.getEnumByCode(typeCode);
        if (Objects.isNull(formTypeEnum)) {
            logger.error("异常流程重新发起-流程类型不存在, typeCode: {}", typeCode);
            return Response.err("流程类型不存在");
        }

        try {
            String responseBody = requestCtcService(typeCode, formId);
            
            switch (formTypeEnum) {
                case PAYMENT_APPLY:
                    return handlePaymentApply(formId, responseBody);
                case GSC_PAYMENT_INVOICE_ISP:
                    return handleInvoiceIsp(formId, responseBody);
                case GSC_PAYMENT_INVOICE_DETAIL:
                    return handleInvoiceDetail(formId, responseBody);
                default:
                    logger.error("异常流程重新发起-未知的流程类型, typeCode: {}", typeCode);
                    return Response.err("未知的流程类型");
            }
        } catch (Exception e) {
            logger.error("异常流程重新发起-处理异常", e);
            return Response.err("处理异常：" + e.getMessage());
        }
    }

    private String requestCtcService(Integer typeCode, String formId) {
        Map<String, Object> params = new HashMap<>();
        params.put("typeCode", typeCode);
        params.put("formId", formId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "IFlowExceptionReBuild/rebuild", params);
        logger.info("异常流程重新发起-请求CTC服务, url: {}", url);
        
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        return response.getBody();
    }

    private Response handlePaymentApply(String formId, String responseBody) {
        DataResponse<SdpReceivePaymentApplyHandleResultDTO> response = JSON.parseObject(
            responseBody, 
            new TypeReference<DataResponse<SdpReceivePaymentApplyHandleResultDTO>>() {}
        );
        
        if (response == null || response.getData() == null) {
            logger.error("异常流程重新发起-付款申请处理失败, formId: {}", formId);
            return Response.err("付款申请处理失败");
        }

        SdpReceivePaymentApplyHandleResultDTO resultDTO = response.getData();
        boolean buildResult = paymentApplyAutoBuildMipWorkFlowService.autoBuildMipWorkFlow(resultDTO);
        logger.info("异常流程重新发起-付款申请重新提交结果: {}, formId: {}", buildResult, formId);
        
        if (!buildResult) {
            updateAuditStatus(
                String.format("%spaymentApply/updateAuditStatusForException?id=%s", 
                ModelsEnum.CTC.getBaseUrl(), 
                resultDTO.getPaymentApplyId()),
                "付款申请",
                formId
            );
            return Response.err("付款申请流程提交失败");
        }
        
        return Response.response();
    }

    private Response handleInvoiceIsp(String formId, String responseBody) {
        DataResponse<SdpReceivePaymentInvoiceRequestISPResultDTO> response = JSON.parseObject(
            responseBody, 
            new TypeReference<DataResponse<SdpReceivePaymentInvoiceRequestISPResultDTO>>() {}
        );
        
        if (response == null || response.getData() == null) {
            logger.error("异常流程重新发起-ISP开票申请处理失败, formId: {}", formId);
            return Response.err("ISP开票申请处理失败");
        }

        SdpReceivePaymentInvoiceRequestISPResultDTO resultDTO = response.getData();
        if (!resultDTO.getIsSuccess()) {
            logger.error("异常流程重新发起-ISP开票申请重新提交失败, 原因: {}, formId: {}", resultDTO.getErrorMsg(), formId);
            return Response.err(resultDTO.getErrorMsg());
        }

        if (resultDTO.getAbandonFlow()) {
            boolean result = formInstanceService.logicDeleteAndMipWorkflowDraftAbandon(
                resultDTO.getIspInvoiceApplyId(),
                resultDTO.getFormUrl(),
                "pam",
                resultDTO.getUnitId()
            );
            logger.info("异常流程重新发起-ISP开票申请作废结果: {}, formId: {}", result, formId);
            return Response.response();
        }

        boolean buildResult = gscInvoiceIspAutoBuildMipWorkFlowService.autoBuildMipWorkFlow(resultDTO);
        logger.info("异常流程重新发起-ISP开票申请重新提交结果: {}, formId: {}", buildResult, formId);
        
        if (!buildResult) {
            updateAuditStatus(
                String.format("%sgscPaymentInvoice/updateAuditStatusForException?id=%s", 
                ModelsEnum.CTC.getBaseUrl(), 
                resultDTO.getIspInvoiceApplyId()),
                "ISP开票申请",
                formId
            );
            return Response.err("ISP开票申请流程提交失败");
        }
        
        return Response.response();
    }

    private Response handleInvoiceDetail(String formId, String responseBody) {
        DataResponse<SdpReceivePaymentInvoiceDetailRequestResultDTO> response = JSON.parseObject(
            responseBody, 
            new TypeReference<DataResponse<SdpReceivePaymentInvoiceDetailRequestResultDTO>>() {}
        );
        
        if (response == null || response.getData() == null) {
            logger.error("异常流程重新发起-税票申请处理失败, formId: {}", formId);
            return Response.err("税票申请处理失败");
        }

        SdpReceivePaymentInvoiceDetailRequestResultDTO resultDTO = response.getData();
        if (!resultDTO.getIsSuccess()) {
            logger.error("异常流程重新发起-税票申请重新提交失败, 原因: {}, formId: {}", resultDTO.getErrorMsg(), formId);
            return Response.err(resultDTO.getErrorMsg());
        }

        if (resultDTO.getAbandonFlow()) {
            boolean result = formInstanceService.logicDeleteAndMipWorkflowDraftAbandon(
                resultDTO.getInvoiceApplyId(),
                resultDTO.getFormUrl(),
                "pam",
                resultDTO.getUnitId()
            );
            logger.info("异常流程重新发起-税票申请作废结果: {}, formId: {}", result, formId);
            return Response.response();
        }

        boolean buildResult = gscInvoiceDetailAutoBuildMipWorkFlowService.autoBuildMipWorkFlow(resultDTO);
        logger.info("异常流程重新发起-税票申请重新提交结果: {}, formId: {}", buildResult, formId);
        
        if (!buildResult) {
            updateAuditStatus(
                String.format("%sgscPaymentInvoice/updateTaxInvoiceStatusForException?id=%s", 
                ModelsEnum.CTC.getBaseUrl(), 
                resultDTO.getInvoiceApplyId()),
                "税票申请",
                formId
            );
            return Response.err("税票申请流程提交失败");
        }
        
        return Response.response();
    }

    private void updateAuditStatus(String url, String businessType, String formId) {
        String result = restTemplate.getForObject(url, String.class);
        DataResponse<Boolean> response = JSON.parseObject(result, new TypeReference<DataResponse<Boolean>>() {});
        if (response != null && response.getData()) {
            logger.info("异常流程重新发起-{}更新审核状态为异常成功, formId: {}", businessType, formId);
        }
    }
}
