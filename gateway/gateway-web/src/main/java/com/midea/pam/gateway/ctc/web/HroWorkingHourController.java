package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.ctc.dto.HroWorkingHourChangeDto;
import com.midea.pam.common.ctc.dto.HroWorkingHourDto;
import com.midea.pam.common.ctc.dto.HroWorkingHourItemDto;
import com.midea.pam.common.ctc.excelVo.HroWorkingHourImportExcelVo;
import com.midea.pam.common.ctc.excelVo.HroWorkingHourItemExcelVo;
import com.midea.pam.common.ctc.excelVo.HroWorkingHourItemExcelVo1;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.HroWorkingHourStatus;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.gateway.entity.FileInfo;
import com.midea.pam.common.gateway.entity.FileInfoExample;
import com.midea.pam.common.util.*;
import com.midea.pam.gateway.common.utils.FileUtil;
import com.midea.pam.gateway.mapper.FileInfoMapper;
import com.midea.pam.gateway.service.OssService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Api("点工工时")
@RestController
@RequestMapping("hroWorkingHour")
public class HroWorkingHourController {

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private FileInfoMapper fileInfoMapper;

    @Resource
    private OssService ossService;

    @ApiOperation(value = "点工工时数据导入")
    @PostMapping("importWorkingHour")
    public Response importWorkingHour(@RequestPart MultipartFile file,
                                      @RequestParam(required = false) Long importId){
        List<HroWorkingHourItemExcelVo> excelVoList;
        try {
            excelVoList = FileUtil.importExcel(file, HroWorkingHourItemExcelVo.class, 1, 0);
        }catch(BizException e){
            if(e.getMessage()==null){
                throw new BizException(Code.ERROR,"模板解析失败,请检测是否存在空行");
            }
            throw e;
        }
        if(excelVoList.isEmpty()){
            throw new BizException(Code.ERROR,"excel数据解析为空");
        }
        String url = ModelsEnum.CTC.getBaseUrl() + "hroWorkingHour/importWorkingHour?importId="
                + (importId!=null?importId:"");
        String res = restTemplate.postForObject(url, excelVoList, String.class);
        DataResponse<HroWorkingHourDto> response = JSON.parseObject(res, new TypeReference<DataResponse<HroWorkingHourDto>>() {
        });
        if(response!=null && response.getCode()==0){
            HroWorkingHourDto hroWorkingHourDto = response.getData();
            try {
                hroWorkingHourDto.setUploadFiles(ossService.upload(file));
            } catch (IOException e) {
                throw new BizException(Code.ERROR,"文件上传失败");
            }
        }
        return response;
    }

    @ApiOperation(value = "点工工时生成校验excel文件")
    @PostMapping("generateCheckReport")
    public void generateCheckReport(@RequestBody HroWorkingHourDto hroWorkingHourDto,
                                    HttpServletResponse response){
        // 创建Excel工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFFont baseFont = workbook.createFont();
        baseFont.setFontName("宋体");
        baseFont.setFontHeightInPoints((short) 11);

        // 换行样式
        HSSFCellStyle wrapCellStyle = workbook.createCellStyle();
        wrapCellStyle.setWrapText(Boolean.TRUE);
        wrapCellStyle.setFont(baseFont);

        // 居中样式
        HSSFCellStyle centerCellStyle = workbook.createCellStyle();
        centerCellStyle.setVerticalAlignment(XSSFCellStyle.VERTICAL_CENTER);
        centerCellStyle.setAlignment(XSSFCellStyle.ALIGN_CENTER);
        centerCellStyle.setFont(baseFont);

        // 靠左样式
        HSSFCellStyle leftCellStyle = workbook.createCellStyle();
        leftCellStyle.setVerticalAlignment(XSSFCellStyle.VERTICAL_CENTER);
        leftCellStyle.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        leftCellStyle.setFont(baseFont);

        generateSheet1(workbook,wrapCellStyle,centerCellStyle,hroWorkingHourDto);
        generateSimpleSheet(workbook,leftCellStyle,hroWorkingHourDto);

        ExcelUtil.downLoadExcel("点工工时校验信息.xls", response, workbook);
    }

    private void generateSimpleSheet(HSSFWorkbook workbook,
                                     HSSFCellStyle centerCellStyle,
                                     HroWorkingHourDto hroWorkingHourDto) {
        HSSFSheet sheet1 = workbook.createSheet("角色清单");
        sheet1.setColumnWidth(0,100*256);
        int row = 0;
        for (String role : hroWorkingHourDto.getRoles()) {
            generateCell(sheet1,row++,0,role,centerCellStyle);
        }

        HSSFSheet sheet2 = workbook.createSheet("错误信息");
        sheet2.setColumnWidth(0,100*256);
        row = 0;
        for (String errMsg : hroWorkingHourDto.getImportErrorMsgs()) {
            generateCell(sheet2,row++,0,errMsg,centerCellStyle);
        }
    }

    private void generateSheet1(HSSFWorkbook workbook,
                                HSSFCellStyle wrapCellStyle,
                                HSSFCellStyle centerCellStyle,
                                HroWorkingHourDto hroWorkingHourDto){
        HSSFSheet sheet = workbook.createSheet("工单任务工时导入");
        sheet.setColumnWidth(0,14*256);
        sheet.setColumnWidth(1,18*256);
        sheet.setColumnWidth(2,25*256);
        sheet.setColumnWidth(3,16*256);
        sheet.setColumnWidth(4,16*256);
        sheet.setColumnWidth(5,16*256);
        sheet.setColumnWidth(6,25*256);
        sheet.setColumnWidth(7,30*256);
        sheet.setColumnWidth(8,8*256);
        sheet.setColumnWidth(9,25*256);
        // 创建字体
        HSSFFont redFont = workbook.createFont();
        redFont.setFontName("宋体");
        redFont.setFontHeightInPoints((short) 10);
        redFont.setColor(HSSFColor.RED.index);

        HSSFCellStyle centerRedCellStyle = workbook.createCellStyle();
        centerRedCellStyle.cloneStyleFrom(centerCellStyle);
        centerRedCellStyle.setFont(redFont);

        // 创建行
        HSSFRow row0 = sheet.createRow(0);
        HSSFRow row1 = sheet.createRow(1);
        // 设置行的高度
        row0.setHeightInPoints(45);
        row1.setHeightInPoints(16);
        String tip = "模板填写说明：\n" +
                "1、红色字段为必填项\n" +
                "2、导入时注意删除示例数据";
        generateRegionCell(sheet,0,0,0,9,tip,wrapCellStyle);
        String[] titles = "出勤日期,项目号,项目名称,合同号,MIP账号,姓名,供应商编码,角色,工时,备注".split(",");
        HSSFCellStyle cellStyle;
        int row = 1;
        int col = 0;
        for (String title : titles) {
            if("项目名称".equals(title) || "备注".equals(title) ){
                cellStyle = centerCellStyle;
            }else{
                cellStyle = centerRedCellStyle;
            }
            generateCell(sheet,row,col++,title,cellStyle);
        }
        for (HroWorkingHourItemExcelVo vo : hroWorkingHourDto.getExcelVoList()) {
            col = 0;
            row++;
            generateCell(sheet,row,col++,vo.getWorkDateStr(),centerCellStyle);
            generateCell(sheet,row,col++,vo.getProjectCode(),centerCellStyle);
            generateCell(sheet,row,col++,vo.getProjectName(),centerCellStyle);
            generateCell(sheet,row,col++,vo.getContractCode(),centerCellStyle);
            generateCell(sheet,row,col++,vo.getUsername(),centerCellStyle);
            generateCell(sheet,row,col++,vo.getName(),centerCellStyle);
            generateCell(sheet,row,col++,vo.getVendorCode(),centerCellStyle);
            generateCell(sheet,row,col++,vo.getRoleName(),centerCellStyle);
            generateCell(sheet,row,col++,vo.getWorkingHour()!=null?vo.getWorkingHour().stripTrailingZeros().toPlainString():"",centerCellStyle);
            generateCell(sheet,row,col,vo.getRemark(),centerCellStyle);
        }
    }

    private HSSFRow getOrCreateRow(HSSFSheet sheet, int index){
        HSSFRow row = sheet.getRow(index);
        if(row==null){
            row = sheet.createRow(index);
        }
        return row;
    }

    /**
     * 创建跨多行多列的单元格
     * @param sheet：sheet页
     * @param firstRow：开始行
     * @param lastRow：结束行
     * @param firstCol：开始列
     * @param lastCol：结束列
     * @param value：值
     * @param cellStyle：样式
     */
    private void generateRegionCell(HSSFSheet sheet, int firstRow, int lastRow,int firstCol, int lastCol, String value, HSSFCellStyle cellStyle){
        for (int i = firstRow; i <= lastRow; i++) {
            HSSFRow row = getOrCreateRow(sheet, firstRow);
            for (int j = firstCol; j <= lastCol; j++) {
                row.createCell(firstCol);
            }
        }
        HSSFCell cell = sheet.getRow(firstRow).getCell(firstCol);
        if(cellStyle!=null) {
            cell.setCellStyle(cellStyle);
        }
        cell.setCellValue(value);
        sheet.addMergedRegion(new CellRangeAddress(firstRow, lastRow, firstCol, lastCol));
    }

    /**
     * 创建不跨多行多列的单元格
     * @param sheet：sheet页
     * @param row：行
     * @param col：列
     * @param value：值
     * @param cellStyle：样式
     */
    private void generateCell(HSSFSheet sheet, int row,int col, String value, HSSFCellStyle cellStyle){
        HSSFCell cell = getOrCreateRow(sheet, row).createCell(col);
        if(cellStyle!=null) {
            cell.setCellStyle(cellStyle);
        }
        cell.setCellValue(value);
    }

    @ApiOperation(value = "点工工时保存")
    @PostMapping("saveHroWorkingHour")
    public Response saveHroWorkingHour(@RequestBody HroWorkingHourDto hroWorkingHourDto){
        String url = ModelsEnum.CTC.getBaseUrl() + "hroWorkingHour/saveHroWorkingHour";
        return restTemplate.postForObject(url,hroWorkingHourDto, DataResponse.class);
    }

    @ApiOperation(value = "查询点工工时导入单据")
    @GetMapping("pageHroWorkingHourImport")
    public Response pageHroWorkingHourImport(HroWorkingHourDto hroWorkingHourDto){
        String url = ModelsEnum.CTC.getBaseUrl() + "hroWorkingHour/pageHroWorkingHourImport";
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Map<String,Object> params = new HashMap<>(10);
        params.put("pageNum",hroWorkingHourDto.getPageNum());
        params.put("pageSize",hroWorkingHourDto.getPageSize());
        params.put("code",hroWorkingHourDto.getCode());
        params.put("statusStr",hroWorkingHourDto.getStatusStr());
        params.put("creator",hroWorkingHourDto.getCreator());
        if(hroWorkingHourDto.getStartTime()!=null) {
            params.put("startTime", dateFormat.format(hroWorkingHourDto.getStartTime()));
        }
        if(hroWorkingHourDto.getEndTime()!=null) {
            params.put("endTime", dateFormat.format(hroWorkingHourDto.getEndTime()));
        }
        url = buildUrl(url,params);
        return restTemplate.getForObject(url,DataResponse.class);
    }

    @ApiOperation(value = "导出点工工时导入单据")
    @GetMapping("exportHroWorkingHourImport")
    public void exportHroWorkingHourImport(HroWorkingHourDto hroWorkingHourDto,
                                           HttpServletResponse servletResponse) throws IOException {
        hroWorkingHourDto.setPageNum(1);
        hroWorkingHourDto.setPageSize(1000000);
        Response importResponse = pageHroWorkingHourImport(hroWorkingHourDto);
        String res = JSON.toJSONString(importResponse);
        DataResponse<PageInfo<HroWorkingHourImportExcelVo>> importDataResponse = JSON.parseObject(res,
                new TypeReference<DataResponse<PageInfo<HroWorkingHourImportExcelVo>>>() {});
        boolean isSuccess = false;
        if(importDataResponse!=null && importDataResponse.getCode()==0){
            List<HroWorkingHourImportExcelVo> importExcelVos = importDataResponse.getData().getList();
            importExcelVos.forEach(e->e.setStatusDesc(HroWorkingHourStatus.getValue(e.getStatus())));
            String importIdStr = importExcelVos.stream().map(e -> e.getId().toString()).collect(Collectors.joining(","));
            Workbook workbook = ExportExcelUtil.buildDefaultSheet(importExcelVos, HroWorkingHourImportExcelVo.class, null,  "人力点工工时导入单据", true);

            HroWorkingHourItemDto hroWorkingHourItemDto = new HroWorkingHourItemDto();
            hroWorkingHourItemDto.setImportIdStr(importIdStr);
            Response itemResponse = listHroWorkingHourItem(hroWorkingHourItemDto);
            res = JSON.toJSONString(itemResponse);
            DataResponse<List<HroWorkingHourItemExcelVo>> itemDataResponse = JSON.parseObject(res,
                    new TypeReference<DataResponse<List<HroWorkingHourItemExcelVo>>>() {});
            if (itemDataResponse != null && itemDataResponse.getCode() == 0) {
                List<HroWorkingHourItemExcelVo> itemExcelVos = itemDataResponse.getData();
                ExportExcelUtil.addSheet(workbook, itemExcelVos, HroWorkingHourItemExcelVo.class, null, "工时明细", true);
                ExportExcelUtil.downLoadExcel("人力点工工时导入单据列表导出.xls", servletResponse, workbook);
                isSuccess = true;
            }
        }
        if(!isSuccess){
            servletResponse.setContentType(MediaType.APPLICATION_JSON_UTF8_VALUE);
            servletResponse.getWriter().print(res);
        }
    }

    @ApiOperation(value = "点工工时导入单据详情")
    @GetMapping("detail")
    public Response detail(@RequestParam Long id){
        String url = ModelsEnum.CTC.getBaseUrl() + "hroWorkingHour/detail?id="+id;
        String resStr = restTemplate.getForObject(url, String.class);
        DataResponse<HroWorkingHourDto> response = JSON.parseObject(resStr, new TypeReference<DataResponse<HroWorkingHourDto>>() {
        });
        if(response!=null && response.getCode()==0){
            HroWorkingHourDto hroWorkingHourDto = response.getData();
            if(hroWorkingHourDto!=null) {
                if (StringUtils.isNotEmpty(hroWorkingHourDto.getAttachmentIds())) {
                    List<Long> ids = Arrays.stream(hroWorkingHourDto.getAttachmentIds().split(","))
                            .map(Long::valueOf).collect(Collectors.toList());
                    hroWorkingHourDto.setAttachments(getFileByIds(ids));
                } else {
                    hroWorkingHourDto.setAttachments(new ArrayList<>(0));
                }
                if(hroWorkingHourDto.getImportFileId()!=null){
                    List<FileInfo> files = getFileByIds(Collections.singletonList(hroWorkingHourDto.getImportFileId()));
                    if(ListUtils.isNotEmpty(files)){
                        hroWorkingHourDto.setImportFile(files.get(0));
                    }
                }
            }
        }
        return response;
    }

    private List<FileInfo> getFileByIds(List<Long> ids){
        FileInfoExample fileInfoExample = new FileInfoExample();
        fileInfoExample.createCriteria().andIdIn(ids);
        List<FileInfo> fileInfoList = fileInfoMapper.selectByExample(fileInfoExample);
        Map<Long, UserInfo> userMap = new HashMap<>(fileInfoList.size());
        fileInfoList.forEach(f->{
            UserInfo userInfo = userMap.computeIfAbsent(f.getCreateBy(), CacheDataUtils::findUserById);
            if(userInfo!=null) {
                f.setCreateName(userInfo.getName());
            }
        });
        return fileInfoList;
    }

    @ApiOperation(value = "查询点工工时导入行")
    @GetMapping("listHroWorkingHourItem")
    public Response listHroWorkingHourItem(HroWorkingHourItemDto hroWorkingHourItemDto){
        String url = ModelsEnum.CTC.getBaseUrl() + "hroWorkingHour/listHroWorkingHourItem";
        Map<String,Object> params = new HashMap<>(2);
        params.put("importIdStr",hroWorkingHourItemDto.getImportIdStr());
        params.put("contractCode",hroWorkingHourItemDto.getContractCode());
        params.put("isExportContractWorkingHour",hroWorkingHourItemDto.getIsExportContractWorkingHour());
        url = buildUrl(url,params);
        return restTemplate.getForObject(url,DataResponse.class);
    }

    @ApiOperation(value = "导出点工工时行")
    @GetMapping("exportHroWorkingHourItem")
    public void exportHroWorkingHourItem(HroWorkingHourItemDto hroWorkingHourItemDto,
                                             HttpServletResponse servletResponse){
        Response response = listHroWorkingHourItem(hroWorkingHourItemDto);
        DataResponse<List<HroWorkingHourItemExcelVo>> dataResponse = JSON.parseObject(JSON.toJSONString(response),
                new TypeReference<DataResponse<List<HroWorkingHourItemExcelVo>>>() {});
        List<HroWorkingHourItemExcelVo> excelVos = Collections.emptyList();
        if(dataResponse!=null && dataResponse.getCode()==0){
            excelVos = dataResponse.getData();
        }
        ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", HroWorkingHourItemExcelVo.class, "人力点工工时导入单据工时明细.xls", servletResponse);
    }

    @ApiOperation(value = "生成点工工时excel")
    @PostMapping("generateHroWorkingHourItemExcel")
    public void generateHroWorkingHourItemExcel(@RequestBody List<HroWorkingHourItemExcelVo1> excelVos,
                                         HttpServletResponse servletResponse){
        excelVos.forEach(e-> e.setProjectStatusDesc(ProjectStatus.getValue(e.getProjectStatus())));
        ExportExcelUtil.exportExcel(excelVos, null, "Sheet1",
                HroWorkingHourItemExcelVo1.class, "人力点工工时导入单工时明细导出.xls", servletResponse);
    }

    @ApiOperation(value = "查询点工工时导入变更列表")
    @GetMapping("getHistoryChangeList")
    public Response getHistoryChangeList(@RequestParam Long importId){
        String url = ModelsEnum.CTC.getBaseUrl() + "hroWorkingHour/getHistoryChangeList?importId="+importId;
        return restTemplate.getForObject(url,DataResponse.class);
    }

    @ApiOperation(value = "采购合同工时明细")
    @GetMapping("hroWorkingHourDetail")
    public Response hroWorkingHourDetail(@RequestParam Long contractId){
        String url = ModelsEnum.CTC.getBaseUrl() + "hroWorkingHour/hroWorkingHourDetail?contractId="+contractId;
        return restTemplate.getForObject(url,DataResponse.class);
    }

    @ApiOperation(value = "废弃单据")
    @GetMapping("abandon")
    public Response abandon(@RequestParam Long id){
        String url = ModelsEnum.CTC.getBaseUrl() + "hroWorkingHour/abandon?id="+id;
        return restTemplate.getForObject(url,DataResponse.class);
    }

    @ApiOperation(value = "审批提交回调")
    @PutMapping("workflow/callback/draftSubmit/skipSecurityInterceptor")
    public Response draftSubmitCallback(@RequestParam(required = false) String formInstanceId) {
        String url = ModelsEnum.CTC.getBaseUrl() +
                "hroWorkingHour/workflow/callback/draftSubmit/skipSecurityInterceptor";
        url += "?formInstanceId=" + (formInstanceId!=null?formInstanceId:"");
        return restTemplate.getForObject(url,DataResponse.class);
    }

    @ApiOperation(value = "审批驳回回调")
    @PutMapping("workflow/callback/refuse/skipSecurityInterceptor")
    public Response refuseCallback(@RequestParam(required = false) String formInstanceId) {
        String url = ModelsEnum.CTC.getBaseUrl() +
                "hroWorkingHour/workflow/callback/refuse/skipSecurityInterceptor";
        url += "?formInstanceId=" + (formInstanceId!=null?formInstanceId:"");
        return restTemplate.getForObject(url,DataResponse.class);
    }

    @ApiOperation(value = "审批通过回调")
    @PutMapping("workflow/callback/pass/skipSecurityInterceptor")
    public Response passCallback(@RequestParam(required = false) String formInstanceId) {
        String url = ModelsEnum.CTC.getBaseUrl() +
                "hroWorkingHour/workflow/callback/pass/skipSecurityInterceptor";
        url += "?formInstanceId=" + (formInstanceId!=null?formInstanceId:"");
        return restTemplate.getForObject(url,DataResponse.class);
    }

    @ApiOperation(value = "撤回审批回调")
    @PutMapping("workflow/callback/draftReturn/skipSecurityInterceptor")
    public Response draftReturnCallback(@RequestParam(required = false) String formInstanceId) {
        String url = ModelsEnum.CTC.getBaseUrl() +
                "hroWorkingHour/workflow/callback/draftReturn/skipSecurityInterceptor";
        url += "?formInstanceId=" + (formInstanceId!=null?formInstanceId:"");
        return restTemplate.getForObject(url,DataResponse.class);
    }

    @ApiOperation(value = "作废审批回调")
    @PutMapping("workflow/callback/abandon/skipSecurityInterceptor")
    public Response abandonCallback(@RequestParam(required = false) String formInstanceId) {
        String url = ModelsEnum.CTC.getBaseUrl() +
                "hroWorkingHour/workflow/callback/abandon/skipSecurityInterceptor";
        url += "?formInstanceId=" + (formInstanceId!=null?formInstanceId:"");
        return restTemplate.getForObject(url,DataResponse.class);
    }

    @ApiOperation(value = "工时变更保存")
    @PostMapping("saveChange")
    public Response saveChange(@RequestBody HroWorkingHourChangeDto hroWorkingHourChangeDto) {
        String url = ModelsEnum.CTC.getBaseUrl() + "hroWorkingHour/saveChange";
        return restTemplate.postForObject(url,hroWorkingHourChangeDto,DataResponse.class);
    }

    @ApiOperation(value = "工时变更详情")
    @GetMapping("changeDetail")
    public Response changeDetail(@RequestParam Long id){
        String url = ModelsEnum.CTC.getBaseUrl() + "hroWorkingHour/changeDetail?id="+id;
        String resStr = restTemplate.getForObject(url, String.class);
        DataResponse<HroWorkingHourChangeDto> response = JSON.parseObject(resStr, new TypeReference<DataResponse<HroWorkingHourChangeDto>>() {
        });
        if(response!=null && response.getCode()==0){
            HroWorkingHourChangeDto changeDto = response.getData();
            if(StringUtils.isNotEmpty(changeDto.getAttachmentIds())) {
                List<Long> ids = Arrays.stream(changeDto.getAttachmentIds().split(","))
                        .map(Long::valueOf).collect(Collectors.toList());
                changeDto.setFiles(getFileByIds(ids));
            }else{
                changeDto.setFiles(new ArrayList<>(0));
            }
        }
        return response;
    }

    @ApiOperation(value = "变更审批提交回调")
    @PutMapping("change/workflow/callback/draftSubmit/skipSecurityInterceptor")
    public Response changeDraftSubmitCallback(@RequestParam(required = false) String formInstanceId) {
        String url = ModelsEnum.CTC.getBaseUrl() +
                "hroWorkingHour/change/workflow/callback/draftSubmit/skipSecurityInterceptor";
        url += "?formInstanceId=" + (formInstanceId!=null?formInstanceId:"");
        return restTemplate.getForObject(url,DataResponse.class);
    }

    @ApiOperation(value = "变更审批驳回回调")
    @PutMapping("change/workflow/callback/refuse/skipSecurityInterceptor")
    public Response changeRefuseCallback(@RequestParam(required = false) String formInstanceId) {
        String url = ModelsEnum.CTC.getBaseUrl() +
                "hroWorkingHour/change/workflow/callback/refuse/skipSecurityInterceptor";
        url += "?formInstanceId=" + (formInstanceId!=null?formInstanceId:"");
        return restTemplate.getForObject(url,DataResponse.class);
    }

    @ApiOperation(value = "变更审批通过回调")
    @PutMapping("change/workflow/callback/pass/skipSecurityInterceptor")
    public Response changePassCallback(@RequestParam(required = false) String formInstanceId) {
        String url = ModelsEnum.CTC.getBaseUrl() +
                "hroWorkingHour/change/workflow/callback/pass/skipSecurityInterceptor";
        url += "?formInstanceId=" + (formInstanceId!=null?formInstanceId:"");
        return restTemplate.getForObject(url,DataResponse.class);
    }

    @ApiOperation(value = "变更撤回审批回调")
    @PutMapping("change/workflow/callback/draftReturn/skipSecurityInterceptor")
    public Response changeDraftReturnCallback(@RequestParam(required = false) String formInstanceId) {
        String url = ModelsEnum.CTC.getBaseUrl() +
                "hroWorkingHour/change/workflow/callback/draftReturn/skipSecurityInterceptor";
        url += "?formInstanceId=" + (formInstanceId!=null?formInstanceId:"");
        return restTemplate.getForObject(url,DataResponse.class);
    }

    @ApiOperation(value = "变更作废审批回调")
    @PutMapping("change/workflow/callback/abandon/skipSecurityInterceptor")
    public Response changeAbandonCallback(@RequestParam(required = false) String formInstanceId) {
        String url = ModelsEnum.CTC.getBaseUrl() +
                "hroWorkingHour/change/workflow/callback/abandon/skipSecurityInterceptor";
        url += "?formInstanceId=" + (formInstanceId!=null?formInstanceId:"");
        return restTemplate.getForObject(url,DataResponse.class);
    }


    private String buildUrl(String url,Map<String,Object> params){
        String queryParams = params.entrySet().stream()
                .filter(e -> e.getValue()!=null)
                .map(e -> e.getKey() + "=" + e.getValue())
                .collect(Collectors.joining("&"));
        if(!queryParams.isEmpty()){
            url += "?" + queryParams;
        }
        return url;
    }
}
