package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.ctc.vo.IncomeCalculateProductTaskExcelVO;
import com.midea.pam.common.ctc.vo.IncomeCalculateProjectTaskExcelVO;
import com.midea.pam.common.ctc.vo.SubContractExcelVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.statistics.dto.IncomeCalculateDTO;
import com.midea.pam.common.statistics.dto.IncomeCalculateProjectTaskDTO;
import com.midea.pam.common.statistics.entity.IncomeCalculateProjectTask;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.common.utils.BeanConverter;
import com.midea.pam.gateway.common.utils.ListUtil;
import com.midea.pam.gateway.common.utils.PamCurrentUserUtil;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/9/17
 * @description 收入预测
 */
@Api("收入预测")
@RestController
@RequestMapping("statistics/incomeCalculateProjectTask")
public class IncomeCalculateProjectTaskController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "未提交分页查询")
    @GetMapping("pageNotCommitted")
    public Response page(@RequestParam(required = false) String projectCode,
                         @RequestParam(required = false) String projectName,
                         @RequestParam(required = false) String statusesStr,
                         @RequestParam(required = false) Long calculateId,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = new HashMap<>();
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        params.put("projectCode", projectCode);
        params.put("projectName", projectName);
        params.put("statusesStr", statusesStr);
        params.put("calculateId", calculateId);

        String loginUserName = PamCurrentUserUtil.getCurrentUserName();
        final UserInfo userinfo = CacheDataUtils.findUserByMip(loginUserName);
        params.put("projectManager", userinfo.getId());

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/incomeCalculateProjectTask/pageNotCommitted", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<IncomeCalculateProjectTask>> response =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<IncomeCalculateProjectTask>>>() {
                });
        return response;
    }

    @ApiOperation(value = "暂存", response = Integer.class)
    @PostMapping("save")
    public Response save(@RequestBody List<IncomeCalculateProjectTask> incomeCalculateProjectTasks) {
        final String url = String.format("%sstatistics/incomeCalculateProjectTask/save", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, incomeCalculateProjectTasks, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation(value = "提交", response = Integer.class)
    @PostMapping("commit")
    public Response commit(@RequestBody List<IncomeCalculateProjectTask> incomeCalculateProjectTasks) {
        final String url = String.format("%sstatistics/incomeCalculateProjectTask/commit", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, incomeCalculateProjectTasks, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation(value = "导出有权限的任务清单")
    @GetMapping("exportAuth")
    public void exportAuth(@RequestParam Long calculateId,
                           HttpServletResponse response) {
        final Map<String, Object> params = new HashMap<>();
        params.put("calculateId", calculateId);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/incomeCalculateProjectTask/listAuthTasks", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<IncomeCalculateProjectTask>> dataResponse =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<IncomeCalculateProjectTask>>>() {
                });

        List<IncomeCalculateProjectTaskExcelVO> excelVOS = new ArrayList<>();
        List<IncomeCalculateProjectTask> data = dataResponse.getData();
        if (ListUtils.isNotEmpty(data)) {
            excelVOS = BeanConverter.copy(data, IncomeCalculateProjectTaskExcelVO.class);
            int i = 1;
            for (IncomeCalculateProjectTaskExcelVO task : excelVOS) {
                task.setNumber(i++);
            }
        }
        Workbook workbook = ExportExcelUtil.buildDefaultSheet(excelVOS, IncomeCalculateProjectTaskExcelVO.class, null, "项目经理任务明细", true);
        ExportExcelUtil.downLoadExcel("项目经理任务明细_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls", response, workbook);
    }

}
