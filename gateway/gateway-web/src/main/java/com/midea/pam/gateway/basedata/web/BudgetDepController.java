package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.entity.BudgetDep;
import com.midea.pam.common.ctc.dto.MaterialGetInventoryDto;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.PageResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("budgetDep")
@Api("预算部门")
public class BudgetDepController extends ControllerHelper {
    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "预算部门列表")
    @GetMapping("list")
    public Response list(@RequestParam(required = false) String budgetDepName){
        final Map<String, Object> param = new HashMap<>();
        param.put("budgetDepName", budgetDepName);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/budgetDep/list", param);
        String res = restTemplate.getForEntity(url , String.class).getBody();
        DataResponse<List<BudgetDep>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<BudgetDep>>>() {
        });
        return response;
    }




    @ApiOperation(value = "分页预算部门查询")
    @GetMapping("listPage")
    public Response list(@RequestParam(required = false) String budgetDepCode,
                         @RequestParam(required = false) String emsOrgName,
                         @RequestParam(required = false) String budgetDepName,
                         @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") Integer pageSize){
        final Map<String, Object> param = new HashMap<>();
        param.put("emsOrgName", emsOrgName);
        param.put("budgetDepCode", budgetDepCode);
        param.put("budgetDepName", budgetDepName);
        param.put("pageNum", pageNum);
        param.put("pageSize",pageSize);

        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "budgetDep/listPage",param);
        String res = cleanStr(restTemplate.getForObject(url, String.class));
        PageInfo<BudgetDep> data = JSON.parseObject(res, new TypeReference<PageInfo<BudgetDep>>(){});
        PageResponse<BudgetDep> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation(value = "EMS预算部门接口拉取")
    @GetMapping("getBudgetDepFromEms")
    public Response getBudgetDepFromEms(@RequestParam(required = false) String lastUpdateDate){
        final Map<String, Object> param = new HashMap<>();
        param.put("lastUpdateDate", lastUpdateDate);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "budgetDep/getBudgetDepFromEms",param);
        restTemplate.getForObject(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        response.setData("0");
        return response;
    }

    @ApiOperation(value = "cacheBudgetDep")
    @GetMapping("cacheBudgetDep")
    public Response getBudgetDepFromEms(){
        final Map<String, Object> param = new HashMap<>();
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "budgetDep/cacheBudgetDep",param);
        restTemplate.getForObject(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        response.setData("0");
        return response;
    }
}
