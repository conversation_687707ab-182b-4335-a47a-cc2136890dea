package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.PaymentInvoiceDetailDto;
import com.midea.pam.common.ctc.vo.PaymentInvoiceDetailExcelVO;
import com.midea.pam.common.ctc.vo.PaymentInvoiceExcelVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/3/1
 * @description
 */
@Api("统计-采购发票")
@RestController
@RequestMapping("statistics/paymentInvoiceDetail")
public class PaymentInvoiceDetailStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查询采购发票行列表")
    @GetMapping("page")
    public Response list(PaymentInvoiceDetailDto paymentInvoiceDetailDto,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false) final String invoiceDateStart,
                         @RequestParam(required = false) final String invoiceDateEnd,
                         @RequestParam(required = false) final String createAtStart,
                         @RequestParam(required = false) final String createAtEnd,
                         @RequestParam(required = false) final String dueDateStart,
                         @RequestParam(required = false) final String dueDateEnd,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(paymentInvoiceDetailDto);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        params.put("invoiceDateStart", invoiceDateStart);//发票日期
        params.put("invoiceDateEnd", invoiceDateEnd);//发票日期
        params.put("createAtStart", createAtStart);//录入日期
        params.put("createAtEnd", createAtEnd);//录入日期
        params.put("dueDateStart", dueDateStart);//发票到期日
        params.put("dueDateEnd", dueDateEnd);//发票到期日

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/paymentInvoiceDetail/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<PaymentInvoiceDetailDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<PaymentInvoiceDetailDto>>>() {
        });

        return response;
    }

    private Map buildParam(PaymentInvoiceDetailDto paymentInvoiceDetailDto) {
        final Map<String, Object> params = new HashMap<>();
        params.put("invoiceDetailCode", paymentInvoiceDetailDto.getInvoiceDetailCode());//税票编号
        params.put("attributeStr", paymentInvoiceDetailDto.getAttributeStr());//采购发票属性
        params.put("taxRateStr", paymentInvoiceDetailDto.getTaxRateStr());//采购发票属性
        params.put("purchaseContractName", paymentInvoiceDetailDto.getPurchaseContractName());//采购合同名称
        params.put("purchaseContractCode", paymentInvoiceDetailDto.getPurchaseContractCode());//采购合同编号
        params.put("vendorName", paymentInvoiceDetailDto.getVendorName());//供应商名称
        params.put("vendorCode", paymentInvoiceDetailDto.getVendorCode());//供应商编码
        params.put("invoiceTypeStr", paymentInvoiceDetailDto.getInvoiceTypeStr());//发票类型
        params.put("apInvoiceCode", paymentInvoiceDetailDto.getApInvoiceCode());//应付发票号
        params.put("invoiceStatus", paymentInvoiceDetailDto.getInvoiceStatus());//采购发票状态
        params.put("ouId", paymentInvoiceDetailDto.getOuId());//业务实体
        params.put("currency", paymentInvoiceDetailDto.getCurrency());//业务实体
        params.put("billCode", paymentInvoiceDetailDto.getBillCode());//对账单单号
        params.put("relatePunishment",paymentInvoiceDetailDto.getRelatePunishment());//是否关联罚扣
        return params;
    }


    @ApiOperation(value = "采购发票列表导出", response = ResponseMap.class)
    @GetMapping("export")
    public void listExport(HttpServletResponse response, PaymentInvoiceDetailDto paymentInvoiceDetailDto,
                           @RequestParam(required = false) final String invoiceDateStart,
                           @RequestParam(required = false) final String invoiceDateEnd,
                           @RequestParam(required = false) final String createAtStart,
                           @RequestParam(required = false) final String createAtEnd) {
        final Map<String, Object> params = buildParam(paymentInvoiceDetailDto);
        params.put("invoiceDateStart", invoiceDateStart);//发票日期
        params.put("invoiceDateEnd", invoiceDateEnd);//发票日期
        params.put("createAtStart", createAtStart);//录入日期
        params.put("createAtEnd", createAtEnd);//录入日期
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/paymentInvoiceDetail/v1/export", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("采购发票_"+ DateUtils.format(new Date(),"yyyyMMddHHmmss"));
        fileName.append(".xls");

        JSONArray paymentInvoiceDetailArr = (JSONArray) resultMap.get("paymentInvoiceDetailList");
        JSONArray paymentInvoiceArr = (JSONArray) resultMap.get("paymentInvoiceList");

        List<PaymentInvoiceDetailExcelVO> paymentInvoiceDetailExcelVOS = new ArrayList<>();
        if(paymentInvoiceDetailArr != null){
            paymentInvoiceDetailExcelVOS = JSONObject.parseArray(paymentInvoiceDetailArr.toJSONString(), PaymentInvoiceDetailExcelVO.class);
            for (int i = 0; i < paymentInvoiceDetailExcelVOS.size(); i++) {
                PaymentInvoiceDetailExcelVO paymentInvoiceDetailExcelVO = paymentInvoiceDetailExcelVOS.get(i);
                paymentInvoiceDetailExcelVO.setNumb(i + 1);
            }
        }

        List<PaymentInvoiceExcelVO> paymentInvoiceExcelVOS = new ArrayList<>();
        if(paymentInvoiceArr != null){
            paymentInvoiceExcelVOS = JSONObject.parseArray(paymentInvoiceArr.toJSONString(), PaymentInvoiceExcelVO.class);
            for (int i = 0; i < paymentInvoiceExcelVOS.size(); i++) {
                PaymentInvoiceExcelVO paymentInvoiceExcelVO = paymentInvoiceExcelVOS.get(i);
                paymentInvoiceExcelVO.setNumb(i + 1);
            }
        }

        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(paymentInvoiceDetailExcelVOS, PaymentInvoiceDetailExcelVO.class, null, "采购发票-税票", true);
        ExportExcelUtil.addSheet(workbook, paymentInvoiceExcelVOS, PaymentInvoiceExcelVO.class, null, "采购发票-应付发票", true);
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

}
