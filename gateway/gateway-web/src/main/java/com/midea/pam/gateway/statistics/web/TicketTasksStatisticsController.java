package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.TicketTasksDetailDTO;
import com.midea.pam.common.ctc.vo.TicketTasksDetailExcelVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/7/26
 * @description
 */
@Api("工单任务明细查询")
@RestController
@RequestMapping("statistics/ticketTasks")
public class TicketTasksStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查询工单任务明细列表")
    @GetMapping("page")
    public Response list(TicketTasksDetailDTO ticketTasksDetailDTO,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false) final String ticketPlanStartTimeStart,
                         @RequestParam(required = false) final String ticketPlanStartTimeEnd,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(ticketTasksDetailDTO);
        params.put("list", Boolean.TRUE);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        params.put("ticketPlanStartTimeStart", ticketPlanStartTimeStart); // 工单计划开工日期开始
        params.put("ticketPlanStartTimeEnd", ticketPlanStartTimeEnd); // 工单计划开工日期结束

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/ticketTasks/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<TicketTasksDetailDTO>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<TicketTasksDetailDTO>>>() {
        });

        return response;
    }

    private Map buildParam(TicketTasksDetailDTO ticketTasksDetailDTO) {
        final Map<String, Object> params = new HashMap<>();
        params.put("pamCode", ticketTasksDetailDTO.getPamCode()); // PAM编号
        params.put("erpCode", ticketTasksDetailDTO.getErpCode()); // ERP编号
        params.put("materielDescr", ticketTasksDetailDTO.getMaterielDescr()); // 描述
        params.put("projectCode", ticketTasksDetailDTO.getProjectCode()); // 项目编号
        params.put("projectName", ticketTasksDetailDTO.getProjectName()); // 项目名称
        params.put("ticketTaskCode", ticketTasksDetailDTO.getTicketTaskCode()); // 工单任务号
        params.put("statusStr", ticketTasksDetailDTO.getStatusStr()); // 工单任务状态集合,逗号隔开,工单任务状态：未发布（0），已经布（1），取消（2），终止（3），已完成（4）
        params.put("materialCategoryStr", ticketTasksDetailDTO.getMaterialCategoryStr()); // 物料类型,逗号隔开
        params.put("restPickingNumberTag", ticketTasksDetailDTO.getRestPickingNumberTag()); // 剩余领料数量 (单选,大于0传1, 等于0传0)
        params.put("deletedFlag", ticketTasksDetailDTO.getDeletedFlag()); // 状态, 下拉, 1-删除,0-正常
        params.put("transactionQuantityTag", ticketTasksDetailDTO.getTransactionQuantityTag()); // 库存现有量 (单选,大于0传1, 等于0传0)
        params.put("shelves", ticketTasksDetailDTO.getShelves()); // 货架
        params.put("ouIdStr", ticketTasksDetailDTO.getOuIdStr()); // 业务实体,逗号隔开
        return params;
    }


    @ApiOperation(value = "工单任务明细导出", response = ResponseMap.class)
    @GetMapping("export")
    public void listExport(HttpServletResponse response, TicketTasksDetailDTO ticketTasksDetailDTO,
                           @RequestParam(required = false) final String ticketPlanStartTimeStart,
                           @RequestParam(required = false) final String ticketPlanStartTimeEnd) {
        final Map<String, Object> params = buildParam(ticketTasksDetailDTO);
        params.put("ticketPlanStartTimeStart", ticketPlanStartTimeStart); // 工单计划开工日期开始
        params.put("ticketPlanStartTimeEnd", ticketPlanStartTimeEnd); // 工单计划开工日期结束
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/ticketTasks/v1/export", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("工单任务明细_"+ DateUtils.format(new Date(),"yyyyMMddHHmmss"));
        fileName.append(".xls");

        JSONArray ticketTasksDetailsArr = (JSONArray) resultMap.get("ticketTasksDetailList");

        List<TicketTasksDetailExcelVO> ticketTasksDetailExcelVOS = new ArrayList<>();
        if(ticketTasksDetailsArr != null){
            ticketTasksDetailExcelVOS = JSONObject.parseArray(ticketTasksDetailsArr.toJSONString(), TicketTasksDetailExcelVO.class);
            for (int i = 0; i < ticketTasksDetailExcelVOS.size(); i++) {
                TicketTasksDetailExcelVO ticketTasksExcelVO = ticketTasksDetailExcelVOS.get(i);
                ticketTasksExcelVO.setNum(i + 1);
            }
        }

        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(ticketTasksDetailExcelVOS, TicketTasksDetailExcelVO.class, null, "工单任务明细", true);

        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

}
