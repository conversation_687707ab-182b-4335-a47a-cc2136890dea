package com.midea.pam.gateway.ctc.web;

import cn.hutool.extra.validation.BeanValidationResult;
import cn.hutool.extra.validation.ValidationUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.BatchEditRequirementDeliveryAddressDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto;
import com.midea.pam.common.ctc.dto.RequirementDeliverAddressRepeatCheckDTO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Api("物料采购需求")
@RestController
@RequestMapping("purchaseMaterialRequirement")
public class PurchaseMaterialRequirementController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @Deprecated
    @ApiOperation(value = "采购物料需求分页", response = PurchaseMaterialRequirementDto.class)
    @GetMapping({"selectPageWithDetail"})
    public Response selectPageWithDetail(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                         @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize,
                                         @RequestParam(required = false) @ApiParam(value = "模糊物料编码") String fuzzyErpCode,
                                         @RequestParam(required = false) @ApiParam(value = "模糊物料描述") String fuzzyMaterielDescr,
                                         @RequestParam(required = false) @ApiParam(value = "模糊PAM编码") String fuzzyPamCode,
                                         @RequestParam(required = false) @ApiParam(value = "模糊项目名称") String fuzzyProjectName,
                                         @RequestParam(required = false) @ApiParam(value = "模糊项目编号") String fuzzyProjectNum,
                                         @RequestParam(required = false) @ApiParam(value = "业务实体id") String projectOuId,
                                         @RequestParam(required = false) @ApiParam(value = "0：待下达， 1：已下达") Integer status,
                                         @RequestParam(required = false) @ApiParam(value = "交货日期") Date deliveryTime) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("fuzzyErpCode", fuzzyErpCode);
        param.put("fuzzyMaterielDescr", fuzzyMaterielDescr);
        param.put("fuzzyPamCode", fuzzyPamCode);
        param.put("fuzzyProjectName", fuzzyProjectName);
        param.put("fuzzyProjectNum", fuzzyProjectNum);
        param.put("projectOuId", projectOuId);
        param.put("status", status);
        param.put("deliveryTime", deliveryTime);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseMaterialRequirement/selectPageWithDetail", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<PurchaseMaterialRequirementDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<PurchaseMaterialRequirementDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "获取物料需求对应供应商的物料下达情况(采购员)", response = PurchaseMaterialRequirementDto.class, notes = "场景：创建新订单")
    @GetMapping({"getReleasedOrderDetailByRequirementId"})
    public Response getReleasedOrderDetailByRequirementId(@RequestParam @ApiParam(value = "物料采购需求id") String requirementIdStr) {
        final Map<String, Object> param = new HashMap<>();
        param.put("requirementIdStr", requirementIdStr);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseMaterialRequirement/getReleasedOrderDetailByRequirementId", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<PurchaseMaterialRequirementDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<PurchaseMaterialRequirementDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "加入待下达订单", response = PurchaseMaterialRequirementDto.class)
    @PostMapping({"addPendingOrder"})
    public Response addPendingOrder(@RequestBody PurchaseMaterialRequirementDto dto) {
        String url = String.format("%spurchaseMaterialRequirement/addPendingOrder", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, dto, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "关闭或打开采购需求", response = PurchaseMaterialRequirementDto.class)
    @PostMapping({"closeOrOpenRequirement"})
    public Response closeOrOpenRequirement(@RequestBody PurchaseMaterialRequirementDto dto) {
        String url = String.format("%spurchaseMaterialRequirement/closeOrOpenRequirement", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, dto, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "关闭或打开采购需求", response = PurchaseMaterialRequirementDto.class)
    @PostMapping({"closeOrOpenRequirementWbs"})
    public Response closeOrOpenRequirementWbs(@RequestBody PurchaseMaterialRequirementDto dto) {
        String url = String.format("%spurchaseMaterialRequirement/closeOrOpenRequirementWbs", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, dto, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "非柔性单位公共关闭或打开采购需求")
    @PostMapping({"closeOrOpenRequirementCommon"})
    public Response closeOrOpenRequirementCommon(@RequestBody PurchaseMaterialRequirementDto dto) {
        String url = String.format("%spurchaseMaterialRequirement/closeOrOpenRequirementCommon", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, dto, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "获取外购物料需求信息")
    @GetMapping("getOutRequirementInfo")
    public Response getOutRequirementInfo(@RequestParam Long projectId, @RequestParam(required = false) Long headerId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        param.put("headerId", headerId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseMaterialRequirement/getOutRequirementInfo", param);
        return restTemplate.getForObject(url, DataResponse.class);
    }

    @ApiOperation(value = "获取物料需求对应供应商的物料下达情况(采购员)", response = PurchaseMaterialRequirementDto.class, notes = "场景：加入已生效的订单")
    @GetMapping({"getChangeOrderDetailByRequirementId"})
    public Response getChangeOrderDetailByRequirementId(@RequestParam @ApiParam(value = "物料采购需求id") String requirementIdStr) {
        final Map<String, Object> param = new HashMap<>();
        param.put("requirementIdStr", requirementIdStr);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseMaterialRequirement/getChangeOrderDetailByRequirementId", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<PurchaseMaterialRequirementDto>>>() {
        });
    }

    @ApiOperation(value = "加入待变更订单", response = PurchaseMaterialRequirementDto.class)
    @PostMapping({"addChangingOrder"})
    public Response addChangingOrder(@RequestBody PurchaseMaterialRequirementDto dto) {
        String url = String.format("%spurchaseMaterialRequirement/addChangingOrder", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, dto, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PurchaseMaterialRequirementDto>>() {
        });
    }

    @ApiOperation(value = "运维接口-校正采购需求行和采购需求头", response = MilepostDesignPlanDetailDto.class)
    @PostMapping({"correctRequirement"})
    public Response correctRequirement(@RequestBody MilepostDesignPlanDetailDto dto) {
        String url = String.format("%spurchaseMaterialRequirement/correctRequirement", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, dto, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<MilepostDesignPlanDetailDto>>() {
        });
    }

    @ApiOperation(value = "采购需求地址重复校验")
    @PostMapping("deliverAddressRepeatCheck")
    public Response deliverAddressRepeatCheck(@RequestBody RequirementDeliverAddressRepeatCheckDTO dto) {
        BeanValidationResult beanValidationResult = ValidationUtil.warpValidate(dto);
        if(!beanValidationResult.isSuccess()){
            return Response.err(beanValidationResult.getErrorMessages().get(0).getMessage());
        }
        String url = String.format("%spurchaseMaterialRequirement/deliverAddressRepeatCheck", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, dto, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "批量修改采购需求收货信息")
    @PostMapping("batchUpdateRequirementDeliverAddress")
    public Response batchUpdateRequirementDeliverAddress(@RequestBody BatchEditRequirementDeliveryAddressDto dto) {
        BeanValidationResult beanValidationResult = ValidationUtil.warpValidate(dto);
        if(!beanValidationResult.isSuccess()){
            return Response.err(beanValidationResult.getErrorMessages().get(0).getMessage());
        }
        String url = String.format("%spurchaseMaterialRequirement/batchUpdateRequirementDeliverAddress", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, dto, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "物料采购需求冻结")
    @PostMapping({"freeze"})
    public Response materialRequirementFreeze(@RequestBody PurchaseMaterialRequirementDto dto) {
        String url = String.format("%spurchaseMaterialRequirement/freeze", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, dto, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "物料采购需求冻结")
    @PostMapping({"thaw"})
    public Response materialRequirementThaw(@RequestBody PurchaseMaterialRequirementDto dto) {
        String url = String.format("%spurchaseMaterialRequirement/thaw", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, dto, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "物料采购需求（WBS）修改需求类型")
    @PostMapping({"wbsTypeUpdate"})
    public Response wbsTypeUpdate(@RequestBody PurchaseMaterialRequirementDto dto) {
        String url = String.format("%spurchaseMaterialRequirement/wbsTypeUpdate", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, dto, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "外包（整包）需求 修改需求类型")
    @PostMapping({"requirementTypeUpdate"})
    public Response requirementTypeUpdate(@RequestBody PurchaseMaterialRequirementDto dto) {
        String url = String.format("%spurchaseMaterialRequirement/requirementTypeUpdate", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, dto, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
    }



}
