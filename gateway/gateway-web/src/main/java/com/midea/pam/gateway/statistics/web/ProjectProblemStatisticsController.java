package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.ProjectProblemStatisticsDto;
import com.midea.pam.common.ctc.vo.ProjectProblemExcelVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
/**
 * <AUTHOR>
 * @date 2021/04/07
 * @description
 */
@Api("项目问题")
@RestController
@RequestMapping("statistics/projectProblem")
public class ProjectProblemStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查询项目问题列表")
    @GetMapping("page")
    public Response list(ProjectProblemStatisticsDto projectProblemStatisticsDto,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(projectProblemStatisticsDto);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectProblem/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<ProjectProblemStatisticsDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ProjectProblemStatisticsDto>>>() {
        });

        return response;
    }

    private Map buildParam(ProjectProblemStatisticsDto projectProblemStatisticsDto) {
        final Map<String, Object> params = new HashMap<>();
        params.put("projectId",projectProblemStatisticsDto.getProjectId());
        params.put("projectName", projectProblemStatisticsDto.getProjectName());
        params.put("projectCode", projectProblemStatisticsDto.getProjectCode());
        params.put("statusStr", projectProblemStatisticsDto.getStatusStr());
        params.put("auditorUserName", projectProblemStatisticsDto.getAuditorUserName());
        params.put("problemSolverUserName", projectProblemStatisticsDto.getProblemSolverUserName());
        params.put("expectedSettlementStartDate", projectProblemStatisticsDto.getExpectedSettlementStartDate());
        params.put("expectedSettlementEndDate", projectProblemStatisticsDto.getExpectedSettlementEndDate());
        params.put("plannedFinishStartDate", projectProblemStatisticsDto.getPlannedFinishStartDate());
        params.put("plannedFinishEndDate", projectProblemStatisticsDto.getPlannedFinishEndDate());
        params.put("actualStartStartTime", projectProblemStatisticsDto.getActualStartStartTime());
        params.put("actualStartEndTime", projectProblemStatisticsDto.getActualStartEndTime());
        params.put("actualFinishStartDate", projectProblemStatisticsDto.getActualFinishStartDate());
        params.put("actualFinishEndDate", projectProblemStatisticsDto.getActualFinishEndDate());
        params.put("residenceMinTime", projectProblemStatisticsDto.getResidenceMinTime());
        params.put("residenceMaxTime", projectProblemStatisticsDto.getResidenceMaxTime());
        params.put("overdueMinDays", projectProblemStatisticsDto.getOverdueMinDays());
        params.put("overdueMaxDays", projectProblemStatisticsDto.getOverdueMaxDays());
        params.put("createAtStartDate", projectProblemStatisticsDto.getCreateAtStartDate());
        params.put("createAtEndDate", projectProblemStatisticsDto.getCreateAtEndDate());
        params.put("updateAtStartDate", projectProblemStatisticsDto.getUpdateAtStartDate());
        params.put("updateAtEndDate", projectProblemStatisticsDto.getUpdateAtEndDate());
        params.put("resouce", projectProblemStatisticsDto.getResouce());
        params.put("title", projectProblemStatisticsDto.getTitle());
        params.put("code", projectProblemStatisticsDto.getCode());
        params.put("severityLevel", projectProblemStatisticsDto.getSeverityLevel());
        params.put("problemType", projectProblemStatisticsDto.getProblemType());
        params.put("createByUserName", projectProblemStatisticsDto.getCreateByUserName());
        params.put("severityLevelStr", projectProblemStatisticsDto.getSeverityLevelStr());
        params.put("currentHandlerUserName", projectProblemStatisticsDto.getCurrentHandlerUserName());
        params.put("expectedSettlementDateAsc", projectProblemStatisticsDto.getExpectedSettlementDateAsc());
        params.put("expectedSettlementDateDesc", projectProblemStatisticsDto.getExpectedSettlementDateDesc());
        params.put("plannedFinishDateAsc", projectProblemStatisticsDto.getPlannedFinishDateAsc());
        params.put("plannedFinishDateDesc", projectProblemStatisticsDto.getPlannedFinishDateDesc());
        params.put("actualFinishDateAsc", projectProblemStatisticsDto.getActualFinishDateAsc());
        params.put("actualFinishDateDesc", projectProblemStatisticsDto.getActualFinishDateDesc());
        params.put("createAtDateAsc", projectProblemStatisticsDto.getCreateAtDateAsc());
        params.put("createAtDateDesc", projectProblemStatisticsDto.getCreateAtDateDesc());
        return params;
    }


    @ApiOperation(value = "项目问题列表导出", response = ResponseMap.class)
    @GetMapping("export")
    public void listExport(HttpServletResponse response, ProjectProblemStatisticsDto projectProblemStatisticsDto) {
        final Map<String, Object> params = buildParam(projectProblemStatisticsDto);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectProblem/v1/export", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("项目问题_"+ DateUtils.format(new Date(),"yyyyMMddHHmmss"));
        fileName.append(".xls");

        JSONArray materialGetArr = (JSONArray) resultMap.get("projectProblemList");

        List<ProjectProblemExcelVO> projectProblemExcelVOS = new ArrayList<>();
        if(materialGetArr != null){
            projectProblemExcelVOS = JSONObject.parseArray(materialGetArr.toJSONString(), ProjectProblemExcelVO.class);
            for (int i = 0; i < projectProblemExcelVOS.size(); i++) {
                ProjectProblemExcelVO projectProblemExcelVO = projectProblemExcelVOS.get(i);
                projectProblemExcelVO.setNum(i + 1);
            }
        }

        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(projectProblemExcelVOS, ProjectProblemExcelVO.class, null, "项目问题", true);

        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    @ApiOperation(value = "项目详情-项目问题-问题状态Tab")
    @GetMapping("statusTab")
    public Response statusTab(@RequestParam Long projectId) {
        final Map<String, Object> params = new HashMap<>(1);
        params.put("projectId", projectId);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectProblem/v1/statusTab", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<HashMap<String, String>>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<HashMap<String, String>>>>() {
        });

        return response;
    }
}
