package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.ApplicationIndustryDTO;
import com.midea.pam.common.ctc.entity.ApplicationIndustry;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("应用行业设置")
@RestController
@RequestMapping("applicationIndustry")
public class ApplicationIndustryController extends ControllerHelper {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private RestTemplate restTemplate;

    /**
     * 新增和修改共用一个接口，根据提交的数据是否有id来做逻辑处理
     *
     * @param applicationIndustry
     * @return
     */
    @ApiOperation("新增/修改应用行业")
    @PostMapping("persistence")
    public Response persistence(@RequestBody ApplicationIndustry applicationIndustry) {
        String url = String.format("%s/applicationIndustry/persistence", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, applicationIndustry, String.class);
        DataResponse<Long> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<Long>>() {
        });
        return response;
    }

    @ApiOperation(value = " 分页应用行业", response = ApplicationIndustryDTO.class)
    @GetMapping("page")
    public Response page(@RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize,
                         @RequestParam(required = false) Long unitId,
                         @RequestParam(required = false) String name,
                         @RequestParam(required = false) Integer enabled) throws IllegalAccessException {
        final Map<String, Object> param = new HashMap<>(2);
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        param.put("unitId", unitId);
        param.put("name", name);
        param.put("enabled", enabled);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/applicationIndustry/page", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<ApplicationIndustryDTO>> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<PageInfo<ApplicationIndustryDTO>>>() {
        });
        return response;
    }

    @ApiOperation(value = " 列表应用行业", response = ApplicationIndustryDTO.class)
    @GetMapping("list")
    public Response list(@RequestParam(required = false) Long unitId,
                         @RequestParam(required = false) String name,
                         @RequestParam(required = false) Integer enabled) throws IllegalAccessException {
        final Map<String, Object> param = new HashMap<>(2);
        param.put("unitId", unitId);
        param.put("name", name);
        param.put("enabled", enabled);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/applicationIndustry/list", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<ApplicationIndustryDTO>> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<List<ApplicationIndustryDTO>>>() {
        });
        return response;
    }
}
