package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.RoleGrantDto;
import com.midea.pam.common.basedata.dto.RoleGrantToUserDto;
import com.midea.pam.common.basedata.dto.RoleGrantUserDto;
import com.midea.pam.common.basedata.entity.Grant;
import com.midea.pam.common.basedata.entity.RoleInfo;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.PageResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("系统管理-角色管理")
@RestController
@RequestMapping("grant/roleInfo")
public class RoleGrantUserController extends ControllerHelper {

	@Autowired
	private RestTemplate restTemplate;

	@ApiOperation("按角色查找用户列表分页")
	@GetMapping("list")
	public Response list(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
						 @RequestParam(required = false, defaultValue = "10") Integer pageSize,
						 @RequestParam(required = false) Long roleID) throws Exception {
		final Map<String, Object> param = new HashMap<>();
		param.put("pageNum", pageNum);
		param.put("pageSize", pageSize);
		param.put("roleID", roleID);
		String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/grant/roleInfo/list", param);
		String res =  restTemplate.getForObject(url , String.class);
		res = cleanStr(res);
		PageInfo<Map<String, Object>> data = JSON.parseObject(res, new TypeReference<PageInfo<Map<String, Object>> >(){});
		PageResponse<Map<String, Object>> response = Response.pageResponse();
		return response.convert(data);
	}


	@ApiOperation("按角色查找用户列表分页")
	@GetMapping("getlistByUserId")
	public Response getlistByUserId(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
						 @RequestParam(required = false, defaultValue = "10") Integer pageSize,
						 @RequestParam(required = false) Long userId) throws Exception {
		final Map<String, Object> param = new HashMap<>();
		param.put("pageNum", pageNum);
		param.put("pageSize", pageSize);
		param.put("userId", userId);
		String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/grant/roleInfo/getlistByUserId", param);
		final String res = restTemplate.getForEntity(url , String.class).getBody();
		return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<RoleGrantDto>>>(){});
	}

	@ApiOperation(value = "按角色对应多个用户列表分页",response =RoleGrantToUserDto.class )
	@GetMapping("getRoleListUser")
	public Response getRoleListUser(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
									@RequestParam(required = false, defaultValue = "10") Integer pageSize,
									@RequestParam(required = false) Long roleId,
									@RequestParam(required = false) String userName,
									@RequestParam(required = false) String department,
									@RequestParam(required = false) String mip) throws Exception {
		final Map<String, Object> param = new HashMap<>();
		param.put("pageNum", pageNum);
		param.put("pageSize", pageSize);
		param.put("roleId", roleId);
		param.put("userName", userName);
		param.put("department", department);
		param.put("mip", mip);
		String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/grant/roleInfo/getRoleListUser", param);
		final String res = restTemplate.getForEntity(url , String.class).getBody();
		return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<RoleGrantToUserDto>>>(){});
	}



	@ApiOperation("授权角色给用户")
	@PutMapping("user")
	public Response grantRoleToUser(@RequestBody Grant grant) {
		String url = String.format("%sgrant/roleInfo/user",ModelsEnum.BASEDATA.getBaseUrl());
		restTemplate.put(url, grant);
		DataResponse<String> response = Response.dataResponse();
		return response;
	}




	@ApiOperation("授权角色给用户")
	@PutMapping("userList")
	public Response grantRoleToUser(@RequestBody JSONObject jsonObject) {
		String url = String.format("%sgrant/roleInfo/userList",ModelsEnum.BASEDATA.getBaseUrl());
		restTemplate.put(url, jsonObject, String.class);
		DataResponse<String> response = Response.dataResponse();
		return response;
	}

	@ApiOperation("给角色设置多用户")
	@PutMapping("roleToUserList")
	public Response grantUserToRoleList(@RequestBody JSONObject jsonObject) {
		String url = String.format("%sgrant/roleInfo/roleToUserList",ModelsEnum.BASEDATA.getBaseUrl());
		restTemplate.put(url, jsonObject, String.class);
		DataResponse<String> response = Response.dataResponse();
		return response;
	}




	@ApiOperation("解除用户授权")
	@DeleteMapping("deleteGran")
	public Response deleteGrant(@RequestParam Long roleId, @RequestParam Long userId) {
		final Map<String, Object> param = new HashMap<>();
		param.put("roleId", roleId);
		param.put("userId", userId);
		String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/grant/roleInfo/deleteGran", param);
		restTemplate.delete(url,param);
		DataResponse<Map<String, Object>> response = Response.dataResponse();
		return response;
	}

	@ApiOperation("获取用户的角色")
	@GetMapping("roles/{userId}")
	public Response getUserGrantedRoles(@PathVariable Long userId) {
		String url = String.format("%sgrant/roleInfo/roles/%s",ModelsEnum.BASEDATA.getBaseUrl(),userId);
		String res = cleanStr(restTemplate.getForObject(url , String.class));
		List<RoleInfo> data = JSON.parseObject(res, new TypeReference<List<RoleInfo>>(){});
		DataResponse<List<RoleInfo>> response = Response.dataResponse();
		return response.setData(data);
	}
}
