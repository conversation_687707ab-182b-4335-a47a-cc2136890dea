package com.midea.pam.gateway.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class PurchaseOrderDetailRequest {
    @ApiModelProperty( "Id")
    private String id;

    @ApiModelProperty( "序号")
    private String number;

    @ApiModelProperty( "单价(不含税)")
    private BigDecimal unitPrice;

    @ApiModelProperty( "折扣(%)")
    private BigDecimal discount;

    @ApiModelProperty( "需求日期")
    //@DateTimeFormat(pattern = "yyyy-MM-dd")
    private String deliveryTime;

    @ApiModelProperty( "供方承诺日期")
    //@DateTimeFormat(pattern = "yyyy-MM-dd")
    private String contractAppointDate;

    @ApiModelProperty( "跟踪日期")
    //@DateTimeFormat(pattern = "yyyy-MM-dd")
    private String trackDate;

    @ApiModelProperty( "是否合并行")
    private String mergeRow;

    @ApiModelProperty( "状态")
    private String statusStr;

    @ApiModelProperty( "物料编码")
    private String erpCode;

    @ApiModelProperty( "物料描述")
    private String materielDescr;

    @ApiModelProperty( "图号/型号")
    private String model;

    @ApiModelProperty( "品牌")
    private String brand;

    @ApiModelProperty( "图纸版本号")
    private String chartVersion;

    @ApiModelProperty( "下达数量")
    private BigDecimal orderNum;

    @ApiModelProperty( "单位")
    private String unit;

    @ApiModelProperty( "折后价(不含税)")
    private BigDecimal discountPrice;

    @ApiModelProperty( "折后金额(不含税)")
    private BigDecimal discountMoney;

    @ApiModelProperty( "最新发布日期")
    private String publishTime;

    @ApiModelProperty( "WBS")
    private String wbsSummaryCode;

    @ApiModelProperty( "设计发布批次号")
    private String designReleaseLotNumber;

    @ApiModelProperty( "需求发布单据编号")
    private String requirementCode;

    @ApiModelProperty(name = "PAM物料编码")
    private String pamCode;

    @ApiModelProperty( "项目编号")
    private String projectCode;

    @ApiModelProperty( "项目编号")
    private String projectNum;

    @ApiModelProperty( "项目名称")
    private String projectName;

    @ApiModelProperty( "项目名称")
    private String validResult;

    @ApiModelProperty("历史价")
    private BigDecimal historyPrice;

    @ApiModelProperty("需求ID")
    private String requirementId ;
}
