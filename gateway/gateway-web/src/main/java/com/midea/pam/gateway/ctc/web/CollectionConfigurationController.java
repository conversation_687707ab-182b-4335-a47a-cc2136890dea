package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.CollectionConfigurationDto;
import com.midea.pam.common.ctc.entity.CollectionConfiguration;
import com.midea.pam.common.ctc.excelVo.CollectionConfigurationExcelVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.utils.BeanConverter;
import com.midea.pam.gateway.common.utils.ExportExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("合同回款条件配置")
@RestController
@RequestMapping("collectionConfiguration")
public class CollectionConfigurationController extends ControllerHelper {
    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = " 分页查询合同回款条件配置")
    @GetMapping("page")
    public Response page(@RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10")final Integer pageSize,
                         @RequestParam(required = false) final String name,
                         @RequestParam(required = false) final BigDecimal days,
                         @RequestParam(required = false) final String description,
                         @RequestParam(required = false) final Boolean enable,
                         @RequestParam(required = false) final Integer rule,
                         @RequestParam(required = false) final Long unitId,
                         @RequestParam(required = false) final String code,
                         @RequestParam(required = false) final String ruleDescription,
                         @RequestParam(required = false) final String enableStr) {
        DataResponse<PageInfo<CollectionConfigurationDto>> response = Response.dataResponse();
        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        param.put(Constants.Page.NAME, name);
        param.put("rule", rule);
        param.put("unitId", unitId);
        param.put("enable", enable);
        param.put("type", code);
        param.put("days", days);
        param.put("description", description);
        param.put("ruleDescription", ruleDescription);
        if(StringUtils.isNotEmpty(enableStr)){
            if("1".equals(enableStr)){
                param.put("enable", Boolean.TRUE);
            }else if("0".equals(enableStr)){
                param.put("enable", Boolean.FALSE);
            }else{
                param.put("enable", null);
            }
        }
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/collectionConfiguration/page", param);
        String res = restTemplate.getForEntity(url , String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<CollectionConfigurationDto>>>(){});
    }

    @ApiOperation(value = "新增/修改合同回款条件配置")
    @PostMapping("save")
    public Response persistence(@RequestBody CollectionConfiguration collectionConfiguration) {
        final String url = String.format("%scollectionConfiguration/save", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, collectionConfiguration, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Long>>(){});
    }

    @ApiOperation(value = " 查询合同回款条件配置")
    @GetMapping("findCollectionConfiguration")
    public Response findCollectionConfiguration(@RequestParam(required = false) final String name,
                                 @RequestParam(required = false) final BigDecimal days,
                                 @RequestParam(required = false) final String description,
                                 @RequestParam(required = false) final Boolean enable,
                                 @RequestParam(required = false) final Integer rule,
                                 @RequestParam(required = false) final Long unitId,
                                 @RequestParam(required = false) final Long id,
                                 @RequestParam(required = false) final Boolean deletedFlag,
                                 @RequestParam(required = false) final String code,
                                                @RequestParam(required = false) final String ruleDescription,
                                                @RequestParam(required = false) final String enableStr) {
        DataResponse<List<CollectionConfigurationDto>> response = Response.dataResponse();
        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.NAME, name);
        param.put("rule", rule);
        param.put("unitId", unitId);
        param.put("enable", enable);
        param.put("type", code);
        param.put("days", days);
        param.put("description", description);
        param.put("id", id);
        param.put("deletedFlag", deletedFlag);
        param.put("ruleDescription", ruleDescription);
        if(StringUtils.isNotEmpty(enableStr)){
            if("1".equals(enableStr)){
                param.put("enable", Boolean.TRUE);
            }else if("0".equals(enableStr)){
                param.put("enable", Boolean.FALSE);
            }else{
                param.put("enable", null);
            }
        }
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/collectionConfiguration/findCollectionConfiguration", param);
        String res = restTemplate.getForEntity(url , String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<CollectionConfigurationDto>>>(){});
    }

    @ApiOperation(value = "合同回款条件配置导出")
    @GetMapping("export")
    public void exportDeliverableValidResult(HttpServletResponse response,
                                             @RequestParam(required = false) final String name,
                                             @RequestParam(required = false) final BigDecimal days,
                                             @RequestParam(required = false) final String description,
                                             @RequestParam(required = false) final Boolean enable,
                                             @RequestParam(required = false) final Integer rule,
                                             @RequestParam(required = false) final Long unitId,
                                             @RequestParam(required = false) final Long id,
                                             @RequestParam(required = false) final String code,
                                             @RequestParam(required = false) final String ruleDescription,
                                             @RequestParam(required = false) final String enableStr) throws Exception {

        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.NAME, name);
        param.put("rule", rule);
        param.put("unitId", unitId);
        param.put("enable", enable);
        param.put("type", code);
        param.put("days", days);
        param.put("description", description);
        param.put("id", id);
        param.put("deletedFlag", Boolean.FALSE);
        param.put("ruleDescription", ruleDescription);
        if(StringUtils.isNotEmpty(enableStr)){
            if("1".equals(enableStr)){
                param.put("enable", Boolean.TRUE);
            }else if("0".equals(enableStr)){
                param.put("enable", Boolean.FALSE);
            }else{
                param.put("enable", null);
            }
        }
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/collectionConfiguration/findCollectionConfiguration", param);
        String responseEntity = restTemplate.getForEntity(url , String.class).getBody();
        DataResponse<List<CollectionConfigurationDto>> dataResponse = JSON.parseObject(responseEntity, new TypeReference<DataResponse<List<CollectionConfigurationDto>>>() {
        });

        List<CollectionConfigurationDto> dataList = dataResponse.getData();
        if (CollectionUtils.isEmpty(dataList)) {
            throw new Exception("没有数据");
        }
        List<CollectionConfigurationExcelVo> excelVos = BeanConverter.copy(dataList, CollectionConfigurationExcelVo.class);

        //导出操作
        ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", CollectionConfigurationExcelVo.class, "合同回款条件配置.xls", response);
    }

    @ApiOperation(value = "查询账期")
    @GetMapping("getPaymentTermList")
    public Response getPaymentTermList(@RequestParam Integer category){
        Map map = restTemplate.getForObject(ModelsEnum.CTC.getBaseUrl() + "collectionConfiguration/getPaymentTermList?category="+category, Map.class);
        return Response.dataResponse(map);
    }
}
