package com.midea.pam.gateway.mdw.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.mdw.dto.DmSubjectStandardDto;
import com.midea.pam.common.mdw.dto.DmSubjectUnitDto;
import com.midea.pam.common.mdw.excelVo.DmSubjectStandardExcelVo;
import com.midea.pam.common.mdw.excelVo.DmSubjectStandardImportCheckExcelVo;
import com.midea.pam.common.mdw.excelVo.DmSubjectStandardImportResponseExcelVo;
import com.midea.pam.common.util.BeanConverter;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExcelUtil;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.utils.FileUtil;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.DVConstraint;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFDataValidation;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFPalette;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/02/08
 * @description 指标标准值维护
 */
@Api("指标标准值维护")
@RestController
@RequestMapping("/dm/subjectStandard")
public class DmSubjectStandardController extends ControllerHelper {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final String[] args = Stream.of("项目号", "项目名称", "指标名称", "标准值").toArray(String[]::new);

    private static final int maxtextStyle = 10000;

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "列表查询")
    @GetMapping("page")
    public Response page(
            @RequestParam(required = false) @ApiParam("项目号") String projectCode,
            @RequestParam(required = false) @ApiParam("项目名称") String projectName,
            @RequestParam(required = false) @ApiParam("项目经理") String managerName,
            @RequestParam(required = false) @ApiParam("项目类型") String typesStr,
            @RequestParam(required = false) @ApiParam("业务分类") String businessCategories,
            @RequestParam(required = false) @ApiParam("业务实体") String ouIdsStr,
            @RequestParam(required = false) @ApiParam("使用单位id") Long unitId,
            @RequestParam(required = false) @ApiParam("创建人名称") String createByName,
            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize) throws Exception {
        final Map<String, Object> params = new HashMap<>();
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        params.put("unitId", unitId);
        params.put("projectCode", projectCode);
        params.put("projectName", projectName);
        params.put("managerName", managerName);
        params.put("typesStr", typesStr);
        params.put("businessCategories", businessCategories);
        params.put("ouIdsStr", ouIdsStr);
        params.put("createByName", createByName);
        String url = buildGetUrl(ModelsEnum.MDW.getBaseUrl(), "/dm/subjectStandard/page", params);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<DmSubjectStandardDto>>>() {
        });
    }

    @ApiOperation(value = "项目列表导出", response = ResponseMap.class)
    @GetMapping("export")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false) @ApiParam("项目号") String projectCode,
                       @RequestParam(required = false) @ApiParam("项目名称") String projectName,
                       @RequestParam(required = false) @ApiParam("项目经理") String managerName,
                       @RequestParam(required = false) @ApiParam("项目类型") String typesStr,
                       @RequestParam(required = false) @ApiParam("业务分类") String businessCategories,
                       @RequestParam(required = false) @ApiParam("业务实体") String ouIdsStr,
                       @RequestParam(required = false) @ApiParam("使用单位id") Long unitId,
                       @RequestParam(required = false) @ApiParam("创建人名称") String createByName,
                       @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                       @RequestParam(required = false, defaultValue = "10") Integer pageSize) throws Exception {
        final Map<String, Object> params = new HashMap<>();
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        params.put("unitId", unitId);
        params.put("projectCode", projectCode);
        params.put("projectName", projectName);
        params.put("managerName", managerName);
        params.put("typesStr", typesStr);
        params.put("businessCategories", businessCategories);
        params.put("ouIdsStr", ouIdsStr);
        params.put("createByName", createByName);
        String url = buildGetUrl(ModelsEnum.MDW.getBaseUrl(), "/dm/subjectStandard/exportList", params);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<DmSubjectStandardDto>> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<List<DmSubjectStandardDto>>>() {
        });
        List<DmSubjectStandardDto> dmSubjectStandardDtos = dataResponse.getData();
        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("标准值列表导出_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");

        List<DmSubjectStandardExcelVo> dmSubjectStandardExcelVos = BeanConverter.copy(dmSubjectStandardDtos, DmSubjectStandardExcelVo.class);
        for (int i = 0; i < dmSubjectStandardExcelVos.size(); i++) {
            DmSubjectStandardExcelVo projectExcelVO = dmSubjectStandardExcelVos.get(i);
            projectExcelVO.setNum(i + 1);
        }

        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(dmSubjectStandardExcelVos, DmSubjectStandardExcelVo.class, null,
                "列表信息", true);
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    @ApiOperation(value = "标准值导入-模板下载")
    @GetMapping("exportTemplate/{unitId}")
    public void exportTemplate(HttpServletResponse response, @PathVariable Long unitId) {
        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("标准值导入模板_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");

        // 创建新的Excel 工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();

        // 设置字体
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 10);
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        // 设置样式
        HSSFCellStyle cellStyle0 = getHeaderCellStyle(workbook, HSSFCellStyle.ALIGN_LEFT, (short) 10, (byte) 255, (byte) 255, (byte) 255);
        HSSFCellStyle cellStyle2 = getHeaderCellStyle(workbook, HSSFCellStyle.ALIGN_CENTER, (short) 12, (byte) 217, (byte) 217, (byte) 217);
        cellStyle0.setFont(font);
        // 在索引0的位置创建行（最顶端的行）
        HSSFSheet sheet = workbook.createSheet("标准值录入");
        sheet.createFreezePane(0, 4, 0, 4); //固定表头

        ArrayList<String> arrayList = new ArrayList<String>();
        arrayList.add("导入说明:");
        arrayList.add("1、红色字段必填，否则会导入失败;");
        arrayList.add("2、如导入指标的已存在有标准值，则会覆盖掉");
        String content = String.join("\n", arrayList);

        int argsSize = args.length;
        // 第一行 标题
        HSSFRow row0 = ExportExcelUtil.createCell(sheet, content, 0, 0, cellStyle0);
        // 合并单元格
        sheet.addMergedRegion(new CellRangeAddress(0, 2, 0, argsSize - 1));

        HSSFRow row1 = ExportExcelUtil.createCell(sheet, "", 3, 0, cellStyle2);

        for (int i = 0; i < argsSize; i++) {
            // 设置单元格长度
            sheet.setColumnWidth(i, args[i].length() * 2000);
            if (Objects.equals("项目名称", args[i])) {
                ExportExcelUtil.createCell(row1, args[i], i, cellStyle2);
            } else {
                HSSFCellStyle style = getRedFontCellStyle(workbook, (short) 11, (byte) 255, (byte) 0, (byte) 0);
                ExportExcelUtil.createCell(row1, args[i], i, style);
            }
        }

        //单元格设为文本格式，默认10000行
        HSSFCellStyle textStyle = workbook.createCellStyle();
        textStyle.setDataFormat(workbook.createDataFormat().getFormat("@")); //文本格式
        for (int i = 4; i < maxtextStyle; i++) {
            HSSFRow row = sheet.createRow(i);
            for (int j = 0; j < argsSize; j++) {
                HSSFCell cell = row.createCell(j);
                cell.setCellStyle(textStyle);
            }
        }
        final Map<String, Object> params = new HashMap<>();
        params.put("unitId", unitId);
        //只取标准值维护方式为手工的项
        params.put("standardStyle",1);
        String url = buildGetUrl(ModelsEnum.MDW.getBaseUrl(), "/dm/subjectUnit/list", params);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<DmSubjectUnitDto>> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<List<DmSubjectUnitDto>>>() {
        });
        List<DmSubjectUnitDto> list = dataResponse.getData();
        if (CollectionUtils.isNotEmpty(list)) {
            String[] explicitListValues = list.stream().filter(e -> Objects.equals(e.getStandardStyle(), 1)).map(e -> e.getSubjectName()).toArray(String[]::new);
            createCellRangeAddressList(sheet, explicitListValues);
        }
        ExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    @ApiOperation(value = "标准值导入检查", notes = "版本v1")
    @PostMapping("checkImportExcel")
    public Response checkImportExcel(@ApiParam("附件") @RequestParam(value = "file") MultipartFile file, HttpServletResponse response) {

        List<DmSubjectStandardImportCheckExcelVo> excelVoList;
        Long unitId = SystemContext.getUnitId();
        try {
            excelVoList = FileUtil.importExcel(file, DmSubjectStandardImportCheckExcelVo.class, 3, 0);
        } catch (BizException e) {
            if (e.getMessage() == null) {
                throw new BizException(Code.ERROR, "模板解析失败,请检测是否存在空行");
            }
            throw e;
        }
        //  Excel会读取空行，将空行过滤
        Iterator<DmSubjectStandardImportCheckExcelVo> it = excelVoList.iterator();
        while (it.hasNext()) {
            DmSubjectStandardImportCheckExcelVo excelVO = it.next();
            if (excelVO.isNull()) {
                it.remove();
            }
        }

        if (excelVoList.isEmpty()) {
            throw new BizException(Code.ERROR, "excel数据解析为空");
        }
        String url = ModelsEnum.MDW.getBaseUrl() + "dm/subjectStandard/checkImportExcel/" + unitId;
        String res = restTemplate.postForObject(url, excelVoList, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<DmSubjectStandardImportResponseExcelVo>>() {
        });
    }


    /**
     * 校验标准值导入title有效性
     *
     * @param args
     * @param wbsExcelRows
     * @param args
     */
    public static void validityImportWbsBudgetTitle(List<String> wbsExcelRows, String[] args) {
        if (CollectionUtils.isEmpty(wbsExcelRows)) {
            throw new ApplicationBizException("导入模板有误，请检查");
        }
        for (int i = 0; i < wbsExcelRows.size(); i++) {
            if (!StringUtils.equals(args[i], wbsExcelRows.get(i))) {
                throw new ApplicationBizException("导入模板内容有更新，请下载最新模板");
            }
        }
    }

    @ApiOperation(value = "标准值导入生成校验excel文件")
    @PostMapping("generateCheckReport")
    public void generateCheckReport(@RequestBody DmSubjectStandardImportResponseExcelVo responseExcelVo,
                                    HttpServletResponse response) {
        // 创建Excel工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFFont baseFont = workbook.createFont();
        baseFont.setFontName("宋体");
        baseFont.setFontHeightInPoints((short) 11);

        // 换行样式
        HSSFCellStyle wrapCellStyle = workbook.createCellStyle();
        wrapCellStyle.setWrapText(Boolean.TRUE);
        wrapCellStyle.setFont(baseFont);

        // 居中样式
        HSSFCellStyle centerCellStyle = workbook.createCellStyle();
        centerCellStyle.setVerticalAlignment(XSSFCellStyle.VERTICAL_CENTER);
        centerCellStyle.setAlignment(XSSFCellStyle.ALIGN_CENTER);
        centerCellStyle.setFont(baseFont);

        // 靠左样式
        HSSFCellStyle leftCellStyle = workbook.createCellStyle();
        leftCellStyle.setVerticalAlignment(XSSFCellStyle.VERTICAL_CENTER);
        leftCellStyle.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        leftCellStyle.setFont(baseFont);

        generateSheet1(workbook, wrapCellStyle, centerCellStyle, responseExcelVo);
        generateSimpleSheet(workbook, leftCellStyle, responseExcelVo);

        ExcelUtil.downLoadExcel("标准值导入校验信息.xls", response, workbook);
    }

    @ApiOperation(value = "新增/修改项目标准值")
    @PostMapping("save")
    public Response save(@RequestBody List<DmSubjectStandardDto> dmSubjectStandardDtos) {
        String userName = SystemContext.getUserName();
        dmSubjectStandardDtos.forEach(e -> e.setCreateByName(userName));
        final String url = String.format("%sdm/subjectStandard/save", ModelsEnum.MDW.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dmSubjectStandardDtos, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    private void generateSheet1(HSSFWorkbook workbook, HSSFCellStyle wrapCellStyle, HSSFCellStyle centerCellStyle, DmSubjectStandardImportResponseExcelVo dmSubjectStandardImportCheckExcelVo) {
        HSSFSheet sheet = workbook.createSheet("标准值录入");
        sheet.setColumnWidth(0, 14 * 256);
        sheet.setColumnWidth(1, 18 * 256);
        sheet.setColumnWidth(2, 25 * 256);
        sheet.setColumnWidth(3, 16 * 256);
        sheet.setColumnWidth(4, 16 * 256);
        sheet.setColumnWidth(5, 16 * 256);
        sheet.setColumnWidth(6, 25 * 256);
        sheet.setColumnWidth(7, 30 * 256);
        sheet.setColumnWidth(8, 8 * 256);
        sheet.setColumnWidth(9, 25 * 256);
        // 创建字体
        HSSFFont redFont = workbook.createFont();
        redFont.setFontName("宋体");
        redFont.setFontHeightInPoints((short) 10);
        redFont.setColor(HSSFColor.RED.index);

        HSSFCellStyle centerRedCellStyle = workbook.createCellStyle();
        centerRedCellStyle.cloneStyleFrom(centerCellStyle);
        centerRedCellStyle.setFont(redFont);

        // 创建行
        HSSFRow row0 = sheet.createRow(0);
        HSSFRow row1 = sheet.createRow(1);
        // 设置行的高度
        row0.setHeightInPoints(45);
        row1.setHeightInPoints(16);
        String tip = "导入说明：\n" +
                "1、红色字段必填，否则会导入失败;\n" +
                "2、如导入指标的已存在有标准值，则会覆盖掉";
        generateRegionCell(sheet, 0, 0, 0, 9, tip, wrapCellStyle);
        HSSFCellStyle cellStyle;
        int row = 1;
        int col = 0;
        for (String title : args) {
            if ("项目名称".equals(title)) {
                cellStyle = centerCellStyle;
            } else {
                cellStyle = centerRedCellStyle;
            }
            generateCell(sheet, row, col++, title, cellStyle);
        }
        for (DmSubjectStandardDto vo : dmSubjectStandardImportCheckExcelVo.getDmSubjectStandardDtos()) {
            col = 0;
            row++;
            generateCell(sheet, row, col++, vo.getProjectCode(), centerCellStyle);
            generateCell(sheet, row, col++, vo.getProjectName(), centerCellStyle);
            generateCell(sheet, row, col++, vo.getSubjectName(), centerCellStyle);
            generateCell(sheet, row, col, vo.getNormValue(), centerCellStyle);
        }
    }

    private void generateSimpleSheet(HSSFWorkbook workbook, HSSFCellStyle leftCellStyle, DmSubjectStandardImportResponseExcelVo dmSubjectStandardImportCheckExcelVo) {
        HSSFSheet sheet2 = workbook.createSheet("错误信息");
        sheet2.setColumnWidth(0, 100 * 256);
        int row = 0;
        for (String errMsg : dmSubjectStandardImportCheckExcelVo.getImportErrorMsgs()) {
            generateCell(sheet2, row++, 0, errMsg, leftCellStyle);
        }
    }

    private HSSFCellStyle getHeaderCellStyle(HSSFWorkbook workbook, short align, short index, byte red, byte green, byte blue) {
        HSSFPalette palette = workbook.getCustomPalette(); //调色板实例
        palette.setColorAtIndex(index, red, green, blue);
        HSSFColor color = palette.findColor(red, green, blue);
        // 设置字体
        HSSFFont boldFont = workbook.createFont();
        boldFont.setFontName("宋体");
        boldFont.setFontHeightInPoints((short) 10);

        HSSFCellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFillForegroundColor(color.getIndex());
        cellStyle.setAlignment(align);//水平居中等
        cellStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);//垂直居中
        cellStyle.setWrapText(Boolean.TRUE);//换行
        cellStyle.setFont(boldFont);
        cellStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        cellStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
        cellStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        cellStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
        cellStyle.setLeftBorderColor(HSSFColor.BLACK.index);
        cellStyle.setRightBorderColor(HSSFColor.BLACK.index);
        cellStyle.setBottomBorderColor(HSSFColor.BLACK.index);
        cellStyle.setTopBorderColor(HSSFColor.BLACK.index);
        return cellStyle;
    }

    private HSSFCellStyle getRedFontCellStyle(HSSFWorkbook workbook, short index, byte red, byte green, byte blue) {
        HSSFPalette palette = workbook.getCustomPalette(); //调色板实例
        palette.setColorAtIndex(index, red, green, blue);
        HSSFColor color = palette.findColor(red, green, blue);
        // 设置字体
        HSSFFont boldFont = workbook.createFont();
        boldFont.setFontName("宋体");
        boldFont.setFontHeightInPoints((short) 10);
        boldFont.setColor(color.getIndex());

        HSSFCellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);//水平居中
        cellStyle.setFont(boldFont);
        return cellStyle;
    }

    /**
     * 创建不跨多行多列的单元格
     *
     * @param sheet：sheet页
     * @param row：行
     * @param col：列
     * @param value：值
     * @param cellStyle：样式
     */
    private void generateCell(HSSFSheet sheet, int row, int col, String value, HSSFCellStyle cellStyle) {
        HSSFCell cell = getOrCreateRow(sheet, row).createCell(col);
        if (cellStyle != null) {
            cell.setCellStyle(cellStyle);
        }
        cell.setCellValue(value);
    }

    /**
     * 创建跨多行多列的单元格
     *
     * @param sheet：sheet页
     * @param firstRow：开始行
     * @param lastRow：结束行
     * @param firstCol：开始列
     * @param lastCol：结束列
     * @param value：值
     * @param cellStyle：样式
     */
    private void generateRegionCell(HSSFSheet sheet, int firstRow, int lastRow, int firstCol, int lastCol, String value, HSSFCellStyle cellStyle) {
        for (int i = firstRow; i <= lastRow; i++) {
            HSSFRow row = getOrCreateRow(sheet, firstRow);
            for (int j = firstCol; j <= lastCol; j++) {
                row.createCell(firstCol);
            }
        }
        HSSFCell cell = sheet.getRow(firstRow).getCell(firstCol);
        if (cellStyle != null) {
            cell.setCellStyle(cellStyle);
        }
        cell.setCellValue(value);
        sheet.addMergedRegion(new CellRangeAddress(firstRow, lastRow, firstCol, lastCol));
    }

    private HSSFRow getOrCreateRow(HSSFSheet sheet, int index) {
        HSSFRow row = sheet.getRow(index);
        if (row == null) {
            row = sheet.createRow(index);
        }
        return row;
    }

    public void createCellRangeAddressList(Sheet sheet, String[] explicitListValues) {
        // 四个参数依次的意思是：开始行，结束行，开始列，结束列 此处表示sheet页中的第一个单元格
        CellRangeAddressList addressList = new CellRangeAddressList(4, maxtextStyle - 1, 2, 2);
        // 创建一个列表约束
        DVConstraint dvConstraint = DVConstraint.createExplicitListConstraint(explicitListValues);
        // 创建一个数据的有效性
        DataValidation dataValidation = new HSSFDataValidation(addressList, dvConstraint);
        // false表示下拉选
        dataValidation.setSuppressDropDownArrow(false);
        dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
        // 当单元格中的值不是下拉选中的值的一个提示
        dataValidation.createErrorBox("出错啦", "请从下拉选中选择");
        // 鼠标移到单元格上的提示
        dataValidation.createPromptBox("提示", "请选择下拉选中的值");
        sheet.addValidationData(dataValidation);
    }

}