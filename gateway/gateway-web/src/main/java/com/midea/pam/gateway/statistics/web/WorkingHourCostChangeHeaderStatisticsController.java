package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.WorkingHourCostChangeHeaderDTO;
import com.midea.pam.common.ctc.vo.WorkingHourCostChangeDetailExcelVO;
import com.midea.pam.common.ctc.vo.WorkingHourCostChangeHeaderExcelVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/9/30
 * @description
 */
@Api("统计-工时变更单")
@RestController
@RequestMapping("statistics/workingHourCostChangeHeader")
public class WorkingHourCostChangeHeaderStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查询工时变更单列表")
    @GetMapping("page")
    public Response list(WorkingHourCostChangeHeaderDTO workingHourCostChangeHeaderDTO,
                         @RequestParam(required = false) final String createAtStart,
                         @RequestParam(required = false) final String createAtEnd,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(workingHourCostChangeHeaderDTO);
        params.put("list", Boolean.TRUE);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        params.put("createAtStart", createAtStart);
        params.put("createAtEnd", createAtEnd);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/workingHourCostChangeHeader/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<WorkingHourCostChangeHeaderDTO>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<WorkingHourCostChangeHeaderDTO>>>() {
        });

        return response;
    }

    private Map buildParam(WorkingHourCostChangeHeaderDTO workingHourCostChangeHeaderDTO) {
        final Map<String, Object> params = new HashMap<>();
        params.put("code", workingHourCostChangeHeaderDTO.getCode());//变更单号
        params.put("changeTypeStr", workingHourCostChangeHeaderDTO.getChangeTypeStr());//变更类型
        params.put("resourceFlagStr", workingHourCostChangeHeaderDTO.getResourceFlagStr());//来源方式
        params.put("ouIdStr", workingHourCostChangeHeaderDTO.getOuIdStr());//业务实体
        params.put("historyProjectCode", workingHourCostChangeHeaderDTO.getHistoryProjectCode());//变更前项目号
        params.put("changeProjectCode", workingHourCostChangeHeaderDTO.getChangeProjectCode());//变更后项目号
        params.put("createByName", workingHourCostChangeHeaderDTO.getCreateByName());//创建人
        return params;
    }


    @ApiOperation(value = "工时变更单列表导出", response = ResponseMap.class)
    @GetMapping("export")
    public void listExport(HttpServletResponse response, WorkingHourCostChangeHeaderDTO workingHourCostChangeHeaderDTO,
                           @RequestParam(required = false) final String createAtStart,
                           @RequestParam(required = false) final String createAtEnd) {
        final Map<String, Object> params = buildParam(workingHourCostChangeHeaderDTO);
        params.put("createAtStart", createAtStart);
        params.put("createAtEnd", createAtEnd);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/workingHourCostChangeHeader/v1/export", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("工时变更单_"+ DateUtils.format(new Date(),"yyyyMMddHHmmss"));
        fileName.append(".xls");

        JSONArray workingHourCostChangeHeaderArr = (JSONArray) resultMap.get("workingHourCostChangeHeaderList");
        JSONArray workingHourCostChangeDetailArr = (JSONArray) resultMap.get("workingHourCostChangeDetailList");

        List<WorkingHourCostChangeHeaderExcelVO> workingHourCostChangeHeaderExcelVOS = new ArrayList<>();
        if(workingHourCostChangeHeaderArr != null){
            workingHourCostChangeHeaderExcelVOS = JSONObject.parseArray(workingHourCostChangeHeaderArr.toJSONString(), WorkingHourCostChangeHeaderExcelVO.class);
            for (int i = 0; i < workingHourCostChangeHeaderExcelVOS.size(); i++) {
                WorkingHourCostChangeHeaderExcelVO workingHourCostChangeHeaderExcelVO = workingHourCostChangeHeaderExcelVOS.get(i);
                workingHourCostChangeHeaderExcelVO.setNum(i + 1);
            }
        }

        List<WorkingHourCostChangeDetailExcelVO> workingHourCostChangeDetailExcelVOS = new ArrayList<>();
        if(workingHourCostChangeDetailArr != null){
            workingHourCostChangeDetailExcelVOS = JSONObject.parseArray(workingHourCostChangeDetailArr.toJSONString(), WorkingHourCostChangeDetailExcelVO.class);
            for (int i = 0; i < workingHourCostChangeDetailExcelVOS.size(); i++) {
                WorkingHourCostChangeDetailExcelVO workingHourCostChangeDetailExcelVO = workingHourCostChangeDetailExcelVOS.get(i);
                workingHourCostChangeDetailExcelVO.setNum(i + 1);
            }
        }

        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(workingHourCostChangeHeaderExcelVOS, WorkingHourCostChangeHeaderExcelVO.class, null, "工时变更列表", true);
        ExportExcelUtil.addSheet(workbook, workingHourCostChangeDetailExcelVOS, WorkingHourCostChangeDetailExcelVO.class, null, "工时变更明细", true);
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

}
