package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.PaymentApplyDto;
import com.midea.pam.common.ctc.query.PaymentApplyQuery;
import com.midea.pam.common.ctc.vo.PaymentApplyExcelVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/3/1
 * @description
 */
@Api("统计-付款申请")
@RestController
@RequestMapping("statistics/paymentApply")
public class PaymentApplyStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查询付款申请列表【分页带参】")
    @GetMapping("pageQueryPayApply")
    public Response pageQueryPayApply(@ApiParam("付款申请编号") @RequestParam(required = false) String paymentApplyCode,
                                      @ApiParam("付款申请状态") @RequestParam(required = false) String bizStatusStr,
                                      @ApiParam("是否预付款(1:是 0否)") @RequestParam(required = false) Integer isCharge,
                                      @ApiParam("是否查询待同步超时的数据") @RequestParam(required = false) Boolean syncFlag,
                                      @ApiParam("提交人姓名") @RequestParam(required = false) String submitName,
                                      @ApiParam("采购合同付款计划编号") @RequestParam(required = false) String paymentPlanCode,
                                      @ApiParam("采购合同编号") @RequestParam(required = false) String purchaseContractCode,
                                      @ApiParam("采购合同名称") @RequestParam(required = false) String purchaseContractName,
                                      @ApiParam("供应商名称") @RequestParam(required = false) String vendorName,
                                      @ApiParam("供应商编码") @RequestParam(required = false) String vendorCode,
                                      @ApiParam("项目编码") @RequestParam(required = false) String projectCode,
                                      @ApiParam("项目名称") @RequestParam(required = false) String projectName,
                                      @ApiParam("同步状态") @RequestParam(required = false) String esbStatusStr,
                                      @ApiParam("币种") @RequestParam(required = false) String currency,
                                      @RequestParam(required = false) Integer negative,
                                      @RequestParam(required = false) String startDate,
                                      @RequestParam(required = false) String endDate,
                                      @RequestParam(required = false) String auditDateStart,
                                      @RequestParam(required = false) String auditDateEnd,
                                      @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                      @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        Map<String, Object> params = new HashMap<>(8);
        if (StringUtils.isNotEmpty(paymentApplyCode)) {
            params.put("paymentApplyCode", paymentApplyCode);
        }
        if (StringUtils.isNotEmpty(bizStatusStr)) {
            params.put("bizStatusStr", bizStatusStr);
        }
        if (!ObjectUtils.isEmpty(isCharge)) {
            params.put("isCharge", isCharge);
        }
        if (!ObjectUtils.isEmpty(syncFlag)) {
            params.put("syncFlag", syncFlag);
        }
        if (StringUtils.isNotEmpty(submitName)) {
            params.put("submitName", submitName);
        }
        if (StringUtils.isNotEmpty(paymentPlanCode)) {
            params.put("paymentPlanCode", paymentPlanCode);
        }
        if (StringUtils.isNotEmpty(purchaseContractCode)) {
            params.put("purchaseContractCode", purchaseContractCode);
        }
        if (StringUtils.isNotEmpty(purchaseContractName)) {
            params.put("purchaseContractName", purchaseContractName);
        }
        if (StringUtils.isNotEmpty(vendorName)) {
            params.put("vendorName", vendorName);
        }
        if (StringUtils.isNotEmpty(vendorCode)) {
            params.put("vendorCode", vendorCode);
        }
        if (StringUtils.isNotEmpty(vendorCode)) {
            params.put("projectCode", projectCode);
        }
        if (StringUtils.isNotEmpty(projectName)) {
            params.put("projectName", projectName);
        }
        if (StringUtils.isNotEmpty(esbStatusStr)) {
            params.put("esbStatusStr", esbStatusStr);
        }
        if (StringUtils.isNotEmpty(currency)) {
            params.put("currency", currency);
        }
        if (!ObjectUtils.isEmpty(negative)) {
            params.put("negative", negative);
        }
        params.put("list", Boolean.TRUE);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        if (!ObjectUtils.isEmpty(startDate)) {
            params.put("startDate", startDate);//申请日期
        }
        if (!ObjectUtils.isEmpty(endDate)) {
            params.put("endDate", endDate);//申请日期
        }
        if (!ObjectUtils.isEmpty(auditDateStart)) {
            params.put("auditDateStart", auditDateStart);//审批通过日期
        }
        if (!ObjectUtils.isEmpty(auditDateEnd)) {
            params.put("auditDateEnd", auditDateEnd);//审批通过日期
        }
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/paymentApply/v1/list", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<PaymentApplyDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<PaymentApplyDto>>>() {
        });

        return response;
    }

    @ApiOperation(value = "查询付款申请列表")
    @GetMapping("page")
    public Response list(PaymentApplyQuery paymentApplyQuery,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false) final String startDate,
                         @RequestParam(required = false) final String endDate,
                         @RequestParam(required = false) String auditDateStart,
                         @RequestParam(required = false) String auditDateEnd,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(paymentApplyQuery);
        params.put("list", Boolean.TRUE);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        params.put("startDate", startDate);//申请日期
        params.put("auditDateStart", auditDateStart);//审批通过日期
        params.put("endDate", endDate);//申请日期
        params.put("auditDateEnd", auditDateEnd);//审批通过日期

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/paymentApply/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<PaymentApplyDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<PaymentApplyDto>>>() {
        });

        return response;
    }

    @ApiOperation(value = "查询所有数据")
    @GetMapping("findAll")
    public Response findAll(PaymentApplyQuery paymentApplyQuery,
                            @RequestParam(required = false) String auditDateStart,
                            @RequestParam(required = false) String auditDateEnd,
                         @RequestParam(required = false) final String startDate,
                         @RequestParam(required = false) final String endDate) {
        final Map<String, Object> params = buildParam(paymentApplyQuery);
        params.put("list", Boolean.TRUE);
        params.put("startDate", startDate);//申请日期
        params.put("endDate", endDate);//申请日期
        params.put("auditDateStart", auditDateStart);//审批通过日期
        params.put("auditDateEnd", auditDateEnd);//审批通过日期

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/paymentApply/v1/findAll", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<PaymentApplyDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<PaymentApplyDto>>>() {
        });

        return response;
    }

    private Map buildParam(PaymentApplyQuery paymentApplyQuery) {
        final Map<String, Object> params = new HashMap<>();
        params.put("paymentApplyCode", paymentApplyQuery.getPaymentApplyCode());//付款申请编号
        params.put("bizStatusStr", paymentApplyQuery.getBizStatusStr());//付款申请状态
        params.put("isCharge", paymentApplyQuery.getIsCharge());//是否预付款
        params.put("syncFlag", paymentApplyQuery.getSyncFlag());//是否查询待同步超时的数据
        params.put("submitName", paymentApplyQuery.getSubmitName());//提交人
        params.put("paymentPlanCode", paymentApplyQuery.getPaymentPlanCode());//付款计划编号
        params.put("purchaseContractCode", paymentApplyQuery.getPurchaseContractCode());//采购合同编号
        params.put("purchaseContractName", paymentApplyQuery.getPurchaseContractName());//采购合同名称
        params.put("vendorName", paymentApplyQuery.getVendorName());//供应商名称
        params.put("vendorCode", paymentApplyQuery.getVendorCode());//供应商编码
        params.put("projectCode", paymentApplyQuery.getProjectCode());//项目编码
        params.put("projectName", paymentApplyQuery.getProjectName());//项目名称
        params.put("esbStatusStr", paymentApplyQuery.getEsbStatusStr());//同步状态
        params.put("currency", paymentApplyQuery.getCurrency());//同步状态
        params.put("negative",paymentApplyQuery.getNegative());
        params.put("paymentMethodName",paymentApplyQuery.getPaymentMethodName());
        return params;
    }


    @ApiOperation(value = "付款申请列表导出", response = ResponseMap.class)
    @GetMapping("export")
    public void listExport(HttpServletResponse response, PaymentApplyQuery paymentApplyQuery,
                           @RequestParam(required = false) String auditDateStart,
                           @RequestParam(required = false) String auditDateEnd,
                           @RequestParam(required = false) final String startDate,
                           @RequestParam(required = false) final String endDate) {
        final Map<String, Object> params = buildParam(paymentApplyQuery);
        params.put("startDate", startDate);//申请付款日期
        params.put("endDate", endDate);//申请付款日期
        params.put("auditDateStart", auditDateStart);//审批通过日期
        params.put("auditDateEnd", auditDateEnd);//审批通过日期
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/paymentApply/v1/export", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("付款申请_"+ DateUtils.format(new Date(),"yyyyMMddHHmmss"));
        fileName.append(".xls");

        JSONArray paymentApplyArr = (JSONArray) resultMap.get("paymentApplyList");

        List<PaymentApplyExcelVO> paymentApplyExcelVOS = new ArrayList<>();
        if(paymentApplyArr != null){
            paymentApplyExcelVOS = JSONObject.parseArray(paymentApplyArr.toJSONString(), PaymentApplyExcelVO.class);
            for (int i = 0; i < paymentApplyExcelVOS.size(); i++) {
                PaymentApplyExcelVO paymentApplyExcelVO = paymentApplyExcelVOS.get(i);
                paymentApplyExcelVO.setNumb(i + 1);
            }
        }

        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(paymentApplyExcelVOS, PaymentApplyExcelVO.class, null, "付款申请", true);
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

}
