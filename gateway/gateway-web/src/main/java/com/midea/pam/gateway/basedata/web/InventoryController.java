package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.InventoryDto;
import com.midea.pam.common.basedata.query.InventoryQuery;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.PageResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.HashMap;
import java.util.Map;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

@RestController
@RequestMapping("inventory")
@Api("字库及货位")
public class InventoryController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "字库及货位分页查询")
    @GetMapping("list")
    public Response list(InventoryQuery query){
        final Map<String, Object> param = new HashMap<>();
        param.put("organizationId", query.getOrganizationId());
        param.put("organizationName", query.getOrganizationName());
        param.put("secondaryInventoryName", query.getSecondaryInventoryName());
        param.put("organizationCode", query.getOrganizationCode());
        param.put("locator", query.getLocator());
        param.put("locatorDisableDateIsNull", true);//默认有效(locatorDisableDate为空即有效)
        param.put("description",query.getDescription());
        param.put("locDescription",query.getLocDescription());
        param.put("isAccurate",query.getIsAccurate());//是否精确查询,针对领/退/转移单查询货位使用精确查询

        param.put("pageNum", query.getPageNum());
        param.put("pageSize",query.getPageSize());

        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "inventory/list",param);
        String res = cleanStr(restTemplate.getForObject(url, String.class,query));
        PageInfo<InventoryDto> data = JSON.parseObject(res, new TypeReference<PageInfo<InventoryDto>>(){});
        PageResponse<InventoryDto> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation(value = "ERP子库及货位数据拉取")
    @GetMapping("getInventoryFromErp")
    public Response getInventoryFromErp(@RequestParam(required = false) String inventoryLastUpdateDate,
                                        @RequestParam(required = false) String locatorLastUpdateDate){
        final Map<String, Object> param = new HashMap<>();
        param.put("inventoryLastUpdateDate", inventoryLastUpdateDate);
        param.put("locatorLastUpdateDate", locatorLastUpdateDate);

        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "inventory/getInventoryFromErp",param);
        String res = cleanStr(restTemplate.getForObject(url, String.class,param));
        PageInfo<InventoryDto> data = JSON.parseObject(res, new TypeReference<PageInfo<InventoryDto>>(){});
        PageResponse<InventoryDto> response = Response.pageResponse();
        return response.convert(data);
    }
}
