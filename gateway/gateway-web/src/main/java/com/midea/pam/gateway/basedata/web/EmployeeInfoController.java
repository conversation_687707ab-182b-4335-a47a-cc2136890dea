package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.LaborCostDto;
import com.midea.pam.common.basedata.dto.UserInfoDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 用户信息controller
 * @date 2019-4-10
 */
@Api("用户信息")
@RestController
@RequestMapping("employeeInfo")
public class EmployeeInfoController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "查询用户信息")
    @GetMapping("/queryLevelAndFee/{id}/{priceType}/{bizUnitId}")
    public JSONObject queryLevelAndFee(@PathVariable Long id, @PathVariable String priceType, @PathVariable Long bizUnitId) {
        final String url =
                buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/employeeInfo/queryLevelAndFee/" + id + "/" + priceType + "/" + bizUnitId, null);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<JSONObject>() {
        });
    }

    @ApiOperation(value = "从ldap同步数据至接口表中")
    @GetMapping("/ucSyncLdapData")
    public JSONObject ucSyncLdapData() {
        final String url =
                buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/employeeInfo/ucSyncLdapData", null);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<JSONObject>() {
        });
    }

    @ApiOperation(value = "idm指定用户同步")
    @GetMapping("/ucSyncIdmData")
    public JSONObject ucSyncIdmData(String mipAccounts, String lastUpdateDate) {
        final Map<String, Object> param = new HashMap<>();
        param.put("mipAccounts", mipAccounts);
        param.put("lastUpdateDate", lastUpdateDate);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/employeeInfo/ucSyncIdmData", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<JSONObject>() {
        });
    }

    @ApiOperation(value = "从ldap同步原邮箱信息")
    @GetMapping("/ucSyncEmployeeMail")
    public JSONObject ucSyncEmployeeMail() {
        final String url =
                buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/employeeInfo/ucSyncEmployeeMail", null);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<JSONObject>() {
        });
    }

    @ApiOperation(value = "增量同步用户")
    @GetMapping("/ucSyncIncrementUser")
    public JSONObject ucSyncIncrementUser() {
        final String url =
                buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/employeeInfo/ucSyncIncrementUser", null);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<JSONObject>() {
        });
    }

    @ApiOperation(value = "增量同步用户")
    @GetMapping("/ucSyncExternalEmployee")
    public JSONObject ucSyncExternalEmployee(@RequestParam String mipAccounts) {
        final Map<String, Object> param = new HashMap<>();
        param.put("mipAccounts", mipAccounts);
        final String url =
                buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/employeeInfo/ucSyncExternalEmployee", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<JSONObject>() {
        });
    }

    @ApiOperation(value = "用户同步处理已同步数据2")
    @GetMapping("/ucManageData")
    public JSONObject ucManageData() {
        final String url =
                buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/employeeInfo/ucManageData", null);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<JSONObject>() {
        });
    }

    @ApiOperation(value = "查询现阶段担任的工作角色", notes = "场景：我的工时-个人配置")
    @GetMapping("/queryPersonalConfig")
    public Response queryPersonalConfig() {
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/employeeInfo/queryPersonalConfig", null);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<LaborCostDto>>>() {
        });
    }

    @ApiOperation(value = "保存个人配置", notes = "场景：我的工时-个人配置")
    @PostMapping({"savePersonalConfig"})
    public Response submit(@RequestBody UserInfoDto dto) {
        String url = String.format("%semployeeInfo/savePersonalConfig", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<Boolean>>() {
        });
    }
}
