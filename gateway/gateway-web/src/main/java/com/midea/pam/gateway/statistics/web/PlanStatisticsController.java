package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.crm.dto.PlanStaDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;


@Api("方案模块")
@RestController
@RequestMapping("statistics/plan")
public class PlanStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查询方案列表")
    @GetMapping("page")
    public Response list(PlanStaDto planStaDto,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(planStaDto);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/plan/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<PlanStaDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<PlanStaDto>>>() {
        });

        return response;
    }

    private Map buildParam(PlanStaDto planStaDto) {

        final Map<String, Object> params = new HashMap<>();
        params.put("num", planStaDto.getNum());
        params.put("name", planStaDto.getName());
        params.put("statusStr", planStaDto.getStatusStr());
        params.put("designerName", planStaDto.getDesignerName());
        params.put("businessCode", planStaDto.getBusinessCode());
        params.put("businessName", planStaDto.getBusinessName());
        params.put("businessStatusStr", planStaDto.getBusinessStatusStr());
        params.put("saleDeptStr", planStaDto.getSaleDeptStr());
        params.put("unitIdStr", planStaDto.getUnitIdStr());
        params.put("crmCode", planStaDto.getCrmCode());
        params.put("customerName", planStaDto.getCustomerName());

        params.put("createAtStart", planStaDto.getCreateAtStart());
        params.put("createAtEnd", planStaDto.getCreateAtEnd());

        return params;
    }

}
