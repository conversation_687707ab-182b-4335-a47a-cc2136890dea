package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.PaymentRecordDto;
import com.midea.pam.common.ctc.vo.PaymentWriteOffExcelVO;
import com.midea.pam.common.ctc.vo.PaymentWriteOffRecordExcelVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2020/3/1
 * @description
 */
@Api("统计-付款核销")
@RestController
@RequestMapping("statistics/paymentWriteOff")
public class PaymentWriteOffStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查询预付款核销行列表")
    @GetMapping("page")
    public Response list(PaymentRecordDto paymentRecordDto,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false) final String invoiceDateStart,
                         @RequestParam(required = false) final String invoiceDateEnd,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(paymentRecordDto);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        params.put("invoiceDateStart", invoiceDateStart);//发票入账日期
        params.put("invoiceDateEnd", invoiceDateEnd);//发票入账日期
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/paymentWriteOff/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<PaymentRecordDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<PaymentRecordDto>>>() {
        });

        return response;
    }

    private Map buildParam(PaymentRecordDto paymentRecordDto) {
        final Map<String, Object> params = new HashMap<>();
        params.put("erpInvoiceCode", paymentRecordDto.getErpInvoiceCode());//发票号
        params.put("paymentStatusStr", paymentRecordDto.getPaymentStatusStr());//核销状态
        params.put("vendorCode", paymentRecordDto.getVendorCode());//供应商编号
        params.put("vendorName", paymentRecordDto.getVendorName());//供应商名称
        params.put("vendorSiteCode", paymentRecordDto.getVendorSiteCode());//供应商地点
        params.put("paymentApplyCode", paymentRecordDto.getPaymentApplyCode());//付款申请编号
        params.put("purchaseContractCode", paymentRecordDto.getPurchaseContractCode());//采购合同编码
        params.put("purchaseContractName", paymentRecordDto.getPurchaseContractName());//采购合同名称
        params.put("projectCode", paymentRecordDto.getProjectCode());//项目编号
        params.put("projectName", paymentRecordDto.getProjectName());//项目名称
        params.put("syncStatus", paymentRecordDto.getSyncStatus());//同步异常
        params.put("invoiceType", paymentRecordDto.getInvoiceType());//发票类型：null,0-预付款;1-正负发票
        return params;
    }


    @ApiOperation(value = "付款核销列表导出", response = ResponseMap.class)
    @GetMapping("export")
    public void listExport(HttpServletResponse response, PaymentRecordDto paymentRecordDto,
                           @RequestParam(required = false) final String invoiceDateStart,
                           @RequestParam(required = false) final String invoiceDateEnd) {
        final Map<String, Object> params = buildParam(paymentRecordDto);
        params.put("invoiceDateStart", invoiceDateStart);//发票入账日期
        params.put("invoiceDateEnd", invoiceDateEnd);//发票入账日期
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/paymentWriteOff/v1/export", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("应付发票核销_");
        fileName.append(DateUtils.format(new Date(),"yyyyMMddHHmmss"));
        fileName.append(".xls");

        JSONArray paymentRecordArr = (JSONArray) resultMap.get("paymentRecordList");
        JSONArray paymentWriteOffRecordArr = (JSONArray) resultMap.get("paymentWriteOffRecordList");

        List<PaymentWriteOffExcelVO> paymentInvoiceExcelVOS = new ArrayList<>();
        if(paymentRecordArr != null){
            paymentInvoiceExcelVOS = JSONObject.parseArray(paymentRecordArr.toJSONString(), PaymentWriteOffExcelVO.class);
            for (int i = 0; i < paymentInvoiceExcelVOS.size(); i++) {
                PaymentWriteOffExcelVO paymentWriteOffExcelVO = paymentInvoiceExcelVOS.get(i);
                paymentWriteOffExcelVO.setNumb(i + 1);
            }
        }

        List<PaymentWriteOffRecordExcelVO> paymentWriteOffRecordExcelVOS = new ArrayList<>();
        if(paymentWriteOffRecordArr != null){
            paymentWriteOffRecordExcelVOS = JSONObject.parseArray(paymentWriteOffRecordArr.toJSONString(), PaymentWriteOffRecordExcelVO.class);
            for (int i = 0; i < paymentWriteOffRecordExcelVOS.size(); i++) {
                PaymentWriteOffRecordExcelVO paymentWriteOffRecordExcelVO = paymentWriteOffRecordExcelVOS.get(i);
                paymentWriteOffRecordExcelVO.setNumb(i + 1);
            }
        }
        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(paymentInvoiceExcelVOS, PaymentWriteOffExcelVO.class, null, "应付发票核销", true);
        ExportExcelUtil.addSheet(workbook, paymentWriteOffRecordExcelVOS, PaymentWriteOffRecordExcelVO.class, null, "核销明细", true);
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

}
