package com.midea.pam.gateway.interceptor;

import com.midea.pam.common.gateway.entity.SqlLog;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMap;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2020/7/24
 * @description 记录查询sql执行时间
 */
@Intercepts({
        @Signature(type = Executor.class, method = "update", args = {MappedStatement.class,
                Object.class}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class,
                Object.class, RowBounds.class, ResultHandler.class})})
@SuppressWarnings({"unchecked", "rawtypes"})
public class SqlInterceptor implements Interceptor {

    private Logger logger = LoggerFactory.getLogger(this.getClass());
    
    @Override
    public Object intercept(Invocation invocation) throws Throwable {

        long start = System.currentTimeMillis();
        Object returnValue = null;

        try {
            // 执行
            returnValue = invocation.proceed();
        } catch (Exception e) {
            throw e;
        } finally {
            // 记录日志
            saveLog(invocation, returnValue, System.currentTimeMillis() - start);
        }

        return returnValue;
    }

    private void saveLog(Invocation invocation, Object returnValue, Long executeTime) {
        SqlLog sqlLog = new SqlLog();
        try {
            MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
            SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();

            if (SqlCommandType.SELECT.name().equals(sqlCommandType.name())) {
                ParameterMap parameterMap = mappedStatement.getParameterMap();
            }

            logger.info("sql执行:" + mappedStatement.getId() + "-" + executeTime);
        } catch (Exception e) {
            logger.error("sql执行收集异常", e);
        }
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
    }
}
