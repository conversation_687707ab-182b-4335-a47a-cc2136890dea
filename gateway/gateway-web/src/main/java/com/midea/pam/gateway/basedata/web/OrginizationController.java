package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.basedata.entity.Orginization;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 部门组织控制器
 */
@RestController
@RequestMapping("orginization")
@Api("组织机构")
public class OrginizationController extends ControllerHelper {

	@Autowired
	private RestTemplate restTemplate;


	/**
	 * 获取部门组织
	 *
	 * @return
	 */
	@GetMapping("orginizationTree")
	@ApiOperation("获取组织结构树")
	public Response findOrganization(@RequestParam(required = false) Long pOrgId) throws Exception {
		final Map<String, Object> param = new HashMap<>();
		param.put("pOrgId", pOrgId);
		final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "orginization/orginizationTree",param);
		final String res = restTemplate.getForEntity(url , String.class).getBody();
		return JSON.parseObject(res, new TypeReference<DataResponse<List<Orginization>>>(){});
	}


    @GetMapping("findTwoAndThreePath")
    @ApiOperation("获取二级和三级部门全路径")
    public Response findTwoAndThreePath(@RequestParam(required = false) String pathName ) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("pathName", pathName);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "orginization/findTwoAndThreePath",param);
        final String res = restTemplate.getForEntity(url , String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<String>>>(){});
    }

	@GetMapping("findTwoAndThreePathByUnit")
	@ApiOperation("获取本单位的二级和三级部门全路径")
	public Response findTwoAndThreePathByUnit(@RequestParam(required = false) String pathName ) throws Exception {
		final Map<String, Object> param = new HashMap<>();
		param.put("pathName", pathName);
		final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "orginization/findTwoAndThreePathByUnit",param);
		final String res = restTemplate.getForEntity(url , String.class).getBody();
		return JSON.parseObject(res, new TypeReference<DataResponse<List<String>>>(){});
	}

	@ApiOperation(value = "从idm同步数据至接口表中，并统计待处理数据至待处理表中")
	@GetMapping("/ucSyncIdmData")
	public Response ucSyncIdmData(@RequestParam String departmentNumber) {
		final Map<String, Object> param = new HashMap<>();
		param.put("departmentNumber", departmentNumber);
		final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "orginization/ucSyncIdmData", param);
		String res = restTemplate.getForEntity(url, String.class).getBody();
		return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {});
	}

}
