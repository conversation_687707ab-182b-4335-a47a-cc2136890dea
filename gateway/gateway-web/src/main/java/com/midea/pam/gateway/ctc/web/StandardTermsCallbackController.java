package com.midea.pam.gateway.ctc.web;


import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

@Api("标准条款回调")
@RestController
@RequestMapping("standardTerms/callback")
public class StandardTermsCallbackController {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "审批中")
    @PutMapping("draftSubmit/skipSecurityInterceptor")
    public Response draftSubmit(@RequestParam(required = false) Long formInstanceId) {
        final String url = String.format("%sstandardTerms/callback/draftSubmit/skipSecurityInterceptor?formInstanceId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "驳回")
    @PutMapping("refused/skipSecurityInterceptor")
    public Response refused(@RequestParam(required = false) Long formInstanceId) {
        final String url = String.format("%sstandardTerms/callback/refused/skipSecurityInterceptor?formInstanceId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "撤回")
    @PutMapping("draftReturn/skipSecurityInterceptor")
    public Response draftReturn(@RequestParam(required = false) Long formInstanceId) {
        final String url = String.format("%sstandardTerms/callback/draftReturn/skipSecurityInterceptor?formInstanceId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "流程通过")
    @PutMapping("approved/skipSecurityInterceptor")
    public Response approved(@RequestParam(required = false) Long formInstanceId) {
        final String url = String.format("%sstandardTerms/callback/approved/skipSecurityInterceptor?formInstanceId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "流程作废")
    @PutMapping("abandon/skipSecurityInterceptor")
    public Response abandon(@RequestParam(required = false) Long formInstanceId) {
        final String url = String.format("%sstandardTerms/callback/abandon/skipSecurityInterceptor?formInstanceId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "流程删除")
    @PutMapping("delete/skipSecurityInterceptor")
    public Response delete(@RequestParam(required = false) Long formInstanceId) {
        final String url = String.format("%sstandardTerms/callback/delete/skipSecurityInterceptor?formInstanceId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }


}
