package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.query.GlPeriodQuery;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.Utils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.PageResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Api("会计期间")
@RequestMapping({"glPeriod"})
public class GlPeriodController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "会计期间页面查询")
    @PostMapping("selectList")
    public Response selectList(@RequestBody(required = false)GlPeriodQuery query) throws Exception {
        String url = String.format("%sglPeriod/selectList",ModelsEnum.BASEDATA.getBaseUrl());
        String res = cleanStr(restTemplate.postForObject(url, query, String.class));
        List<Map<String, Object>> dataList =  Utils.jsonStr2List(res);
        DataResponse<List<Map<String, Object>> > response = Response.dataResponse();
        return response.setData(dataList);
    }

    @ApiOperation(value = "会计期间页面分页查询")
    @PostMapping("selectPage")
    public Response selectPage(@RequestBody(required = false)GlPeriodQuery query) throws Exception {
        String url = String.format("%sglPeriod/selectPage",ModelsEnum.BASEDATA.getBaseUrl());
        String res = cleanStr(restTemplate.postForObject(url, query, String.class));
        PageInfo<Map<String, Object>> data = JSON.parseObject(res, new TypeReference<PageInfo<Map<String, Object>> >(){});
        PageResponse<Map<String, Object>> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation(value = "会计期间erp同步")
    @GetMapping("getGlPeriodFromErp")
    public Response getGlPeriodFromErp(@RequestParam(required = false) String lastUpdateDate){
        final Map<String, Object> param = new HashMap<>();
        param.put("lastUpdateDate", lastUpdateDate);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "glPeriod/getGlPeriodFromErp",param);
        restTemplate.getForObject(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        response.setData("0");
        return response;
    }

    @ApiOperation(value = "库存会计期间缓存更新")
    @GetMapping("cacheOrgPeriod")
    public Response cacheOrgPeriod(){
        final Map<String, Object> param = new HashMap<>();
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "glPeriod/cacheOrgPeriod",param);
        restTemplate.getForObject(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        response.setData("0");
        return response;
    }
}
