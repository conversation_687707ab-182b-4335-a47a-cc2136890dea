package com.midea.pam.gateway.config;

import com.midea.mcomponent.core.util.TraceContext;
import com.midea.mcomponent.monitoring.WebAsyncPusher;
import com.midea.pam.common.gateway.entity.AccessLog;
import com.midea.pam.gateway.common.AccessLogCollection;
import com.midea.pam.gateway.common.utils.PamCurrentUserUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.annotation.Resource;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/6/29
 * @description 记录接口访问信息
 */
@Component
@Order(1)
public class RequestTimeConsumingFilter extends OncePerRequestFilter {

    Logger logger = LoggerFactory.getLogger(RequestTimeConsumingFilter.class);

    @Resource
    private AccessLogCollection accessLogCollection;

    public RequestTimeConsumingFilter() {
    }

    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        long start = System.currentTimeMillis();
        TraceContext.generatorTraceId();
        TraceContext.setLocaleWeb(Boolean.TRUE.toString());
        TraceContext.startSpans();
        TraceContext.requestBegin();
        String tranceId = TraceContext.getTraceId();

        String username = "";
        try {
            filterChain.doFilter(request, response);
            username = PamCurrentUserUtil.getCurrentUserName();
        } catch (ServletException e) {
            throw e;
        } finally {
            long times = System.currentTimeMillis() - start;
            if (this.logger.isInfoEnabled()) {
                String requestURL = request.getRequestURL().toString();
                this.logger.info("Web请求user:[{}], [tranceId:{}]URL:{}],请求耗时:({})ms", new Object[]{username, tranceId, requestURL, times});

                try {
                    AccessLog accessLog = new AccessLog();
                    accessLog.setTranceId(tranceId);
                    accessLog.setUrl(requestURL.substring(0, Math.min(requestURL.length(),200)));
                    StringBuilder sb = new StringBuilder();
                    sb.append("ipfrom:")
                            .append(request.getRemoteAddr() == null ? "" : request.getRemoteAddr())
                            .append(" ipcur:")
                            .append(request.getLocalAddr() == null ? "" : request.getLocalAddr())
                            .append(" method:")
                            .append(request.getMethod() == null ? "UNKNOWN" : request.getMethod());
                    if (request.getMethod().equals(HttpMethod.GET.name())) {
                        String query = request.getQueryString() == null ? "" : request.getQueryString();
                        sb.append(" param:").append(query, 0, Math.min(query.length(),200)).append("...");

                    }
                    accessLog.setParam(sb.toString());
                    accessLog.setCostTime(times);
                    accessLog.setCreateAt(new Date());
                    accessLog.setUsername(username);
                    accessLogCollection.write(accessLog);
                } catch (Exception e) {
                    logger.info("记录访问记录异常:{}", e);
                }
            }

            WebAsyncPusher.webCallPush(request, times);
        }

    }
}
