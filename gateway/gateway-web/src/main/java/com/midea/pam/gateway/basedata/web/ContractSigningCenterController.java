package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.ContractSigningCenterDTO;
import com.midea.pam.common.basedata.entity.ContractIndustry;
import com.midea.pam.common.basedata.entity.ContractSigningCenter;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.PageResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.common.utils.PamCurrentUserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description 签约中心controller
 * @date 2023-5-25
 */
@Api("签约中心")
@RestController
@RequestMapping("contractSigningCenter")
public class ContractSigningCenterController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查询签约中心列表分页", response = ContractSigningCenterDTO.class)
    @GetMapping("selectPage")
    public Response selectPage(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                               @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize,
                               @RequestParam(required = false) @ApiParam(value = "签约中心代码") String contractSigningCenterCode) {

        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("contractSigningCenterCode", contractSigningCenterCode);

        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "contractSigningCenter/selectPage", param);
        String res = cleanStr(restTemplate.getForObject(url, String.class));
        PageInfo<ContractSigningCenterDTO> data = JSON.parseObject(res, new TypeReference<PageInfo<ContractSigningCenterDTO>>() {
        });
        PageResponse<ContractSigningCenterDTO> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation(value = "查询签约中心层级信息", response = ContractSigningCenterDTO.class)
    @GetMapping("tree")
    public Response tree(@RequestParam @ApiParam(value = "业务实体") Long ouId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("ouId", ouId);

        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "contractSigningCenter/tree", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<ContractSigningCenterDTO>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<ContractSigningCenterDTO>>>() {
        });
        return response;
    }

    @ApiOperation(value = "新增签约中心", response = Response.class)
    @PostMapping("add")
    public Response add(@RequestBody @ApiParam(name = "ContractSigningCenter", value = "签约中心信息") ContractSigningCenter contractSigningCenter) {
        String url = String.format("%scontractSigningCenter/add", ModelsEnum.BASEDATA.getBaseUrl());

        UserInfo user = CacheDataUtils.findUserByMip(PamCurrentUserUtil.getCurrentUserName());
        contractSigningCenter.setCreateBy(user.getId());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, contractSigningCenter, String.class);
        String res = cleanStr(responseEntity.getBody());
        DataResponse<String> response = Response.dataResponse();
        return response.setData(res);
    }

    @ApiOperation(value = "修改签约中心", response = Response.class)
    @PutMapping("update")
    public Response update(@RequestBody @ApiParam(name = "ContractSigningCenter", value = "签约中心信息") ContractSigningCenter contractSigningCenter) {
        String url = String.format("%scontractSigningCenter/update", ModelsEnum.BASEDATA.getBaseUrl());
        UserInfo user = CacheDataUtils.findUserByMip(PamCurrentUserUtil.getCurrentUserName());
        contractSigningCenter.setUpdateBy(user.getId());
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, new HttpEntity<ContractSigningCenter>(contractSigningCenter), String.class);
        String res = cleanStr(responseEntity.getBody());
        DataResponse<String> response = Response.dataResponse();
        return response.setData(res);
    }

    @ApiOperation(value = "删除签约中心", response = Response.class)
    @DeleteMapping("{id}")
    public Response delete(@PathVariable Long id) {
        String loginUserName = PamCurrentUserUtil.getCurrentUserName();
        UserInfo user = CacheDataUtils.findUserByMip(loginUserName);
        String url = String.format("%scontractSigningCenter/delete" + id + "/" + user.getId(), ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.DELETE, null, String.class);
        String res = cleanStr(responseEntity.getBody());
        DataResponse<String> response = Response.dataResponse();
        return response.setData(res);
    }

    @ApiOperation(value = "查询签约中心", response = ContractIndustry.class)
    @GetMapping("view/{id}")
    public Response view(@PathVariable Long id) {
        String url = String.format("%scontractSigningCenter/view/" + id, ModelsEnum.BASEDATA.getBaseUrl());
        String res = cleanStr(restTemplate.getForObject(url, String.class));
        return Response.dataResponse().setData(JSON.parseObject(res, new TypeReference<ContractSigningCenter>() {
        }));
    }

    @ApiOperation(value = "查询签约中心列表", response = ContractSigningCenter.class)
    @PostMapping("viewList")
    public Response viewList(@RequestBody List<Long> signingCenterIds) {
        String url = String.format("%scontractSigningCenter/viewList", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, signingCenterIds, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<ContractSigningCenter>>>() {
        });
    }


}
