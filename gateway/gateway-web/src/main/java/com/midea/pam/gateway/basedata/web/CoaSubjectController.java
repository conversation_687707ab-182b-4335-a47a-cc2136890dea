package com.midea.pam.gateway.basedata.web;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.CoaSubjectDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("COA科目值集")
@RestController
@RequestMapping("coaSubject")
public class CoaSubjectController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "COA科目值集列表", response = CoaSubjectDto.class)
    @GetMapping({"selectList"})
    public Response selectList(@RequestParam(required = false) @ApiParam("弹性域值集ID") Long flexValueSetId,
                               @RequestParam(required = false) @ApiParam("弹性域值集名称") String flexValueSetName,
                               @RequestParam(required = false) @ApiParam("弹性域值集ID") Long flexValueId,
                               @RequestParam(required = false) @ApiParam("值") String  flexValue,
                               @RequestParam(required = false) @ApiParam("描述") String description) {
        final Map<String, Object> param = new HashMap<>();
        param.put("description", description);
        param.put("flexValueId", flexValueId);
        param.put("flexValue", flexValue);
        param.put("flexValueSetId", flexValueSetId);
        param.put("flexValueSetName", flexValueSetName);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "coaSubject/selectList", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<CoaSubjectDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<CoaSubjectDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "COA科目值集列表分页", response = CoaSubjectDto.class)
    @GetMapping({"selectPage"})
    public Response selectPage(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                               @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                               @RequestParam(required = false) @ApiParam("弹性域值集ID") Long flexValueSetId,
                               @RequestParam(required = false) @ApiParam("弹性域值集名称") String flexValueSetName,
                               @RequestParam(required = false) @ApiParam("弹性域值集ID") Long flexValueId,
                               @RequestParam(required = false) @ApiParam("值") String  flexValue,
                               @RequestParam(required = false) @ApiParam("描述") String description,
                               @RequestParam(required = false) @ApiParam("是否父级") String summaryFlag
                                ){
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("description", description);
        param.put("flexValueId", flexValueId);
        param.put("flexValue", flexValue);
        param.put("flexValueSetId", flexValueSetId);
        param.put("flexValueSetName", flexValueSetName);
        param.put("summaryFlag",summaryFlag);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "coaSubject/selectPage", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<CoaSubjectDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<CoaSubjectDto>>>() {
        });
        return response;
    }


    @ApiOperation(value = "COA科目值集列表详情", response = CoaSubjectDto.class)
    @GetMapping({"view"})
    public Response view(@RequestParam(required = true) Long id){
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "coaSubject/view", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<CoaSubjectDto> response = JSON.parseObject(res, new TypeReference<DataResponse<CoaSubjectDto>>() {
        });
        return response;
    }


    @ApiOperation(value = "coaSubject拉erp的数据", response = CoaSubjectDto.class)
    @GetMapping({"getCoaSubjectFromErp"})
    public Response gegCoaSubjectFromErp(@RequestParam(required = false) Long flexValueId,@RequestParam(required = false) String lastUpdateDate){
        final Map<String, Object> param = new HashMap<>();
        param.put("flexValueId", flexValueId);
        param.put("lastUpdateDate", lastUpdateDate);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "coaSubject/getCoaSubjectFromErp", param);
       restTemplate.getForEntity(url, String.class).getBody();
       DataResponse<String> response = Response.dataResponse();
       return response;
    }
}
