package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.AnnouncementDto;
import com.midea.pam.common.basedata.entity.Announcement;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: common-module
 * @description: 通知公告网关
 * @author:zhongpeng
 * @create:2020-02-18 15:19
 **/
@RestController
@Api("通知公告")
@RequestMapping({"announcement"})
public class AnnouncementController extends ControllerHelper {
    @Resource
    private RestTemplate restTemplate;

    @ApiOperation("新增通知公告")
    @PostMapping({"add"})
    public Response add(@RequestBody AnnouncementDto dto) {
        String url = String.format("%sannouncement/add", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<AnnouncementDto> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<AnnouncementDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "暂存通知公告")
    @PostMapping("infoTemporary")
    public Response infoTemporary(@RequestBody AnnouncementDto announcementDto) {
        String url = String.format("%sannouncement/infoTemporary", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, announcementDto, String.class);
        DataResponse<AnnouncementDto> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<AnnouncementDto>>() {
        });
        return response;
    }

    @ApiOperation("修改通知公告")
    @PostMapping({"update"})
    public Response update(@RequestBody AnnouncementDto dto) {
        String url = String.format("%sannouncement/update", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<AnnouncementDto> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<AnnouncementDto>>() {
        });
        return dataResponse;
    }

    @ApiOperation("删除通知公告")
    @GetMapping({"delete"})
    public Response delete(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/announcement/delete", param);
        DataResponse<String> response = Response.dataResponse();
        response.setData(restTemplate.getForObject(url, String.class));
        return response;
    }

    @ApiOperation("通知公告分页")
    @GetMapping({"selectPage"})
    public Response selectPage(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                               @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                               @RequestParam(required = false) String name,
                               @RequestParam(required = false) String manyReadStatus,
                               @RequestParam(required = false) String manyType,
                               @RequestParam(required = false) String publisher,
                               @RequestParam(required = false) String department,
                               @RequestParam(required = false) String updatedName,
                               @RequestParam(required = false) String manyStatus,
                               @RequestParam(required = false) String createAt,
                               @RequestParam(required = false) String expirationTime,
                               @RequestParam(required = false) String manyIgnoreStatus) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("name", name);
        param.put("manyReadStatus", manyReadStatus);
        param.put("manyType", manyType);
        param.put("publisher", publisher);
        param.put("department", department);
        param.put("updatedName", updatedName);
        param.put("manyStatus", manyStatus);
        param.put("createAt", createAt);
        param.put("expirationTime", expirationTime);
        param.put("manyIgnoreStatus", manyIgnoreStatus);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/announcement/selectPage", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<AnnouncementDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<AnnouncementDto>>>() {
        });
        return response;
    }

    @ApiOperation("个人主页通知公告")
    @GetMapping({"queryForPortal"})
    public Response queryForPortal(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                   @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                                   @RequestParam(required = false) String name,
                                   @RequestParam(required = false) Integer type) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("name", name);
        param.put("type", type);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/announcement/queryForPortal", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<AnnouncementDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<AnnouncementDto>>>() {
        });
        return response;
    }

    @ApiOperation("查询单个通知公告")
    @GetMapping({"findById"})
    public Response findById(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/announcement/findById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Announcement> response = JSON.parseObject(res, new TypeReference<DataResponse<Announcement>>() {
        });
        return response;
    }

    @ApiOperation("查询最新的通知公告")
    @GetMapping({"findNewestInfo"})
    public Response findNewestInfo() {
        final Map<String, Object> param = new HashMap<>();
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/announcement/findNewestInfo", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<Announcement>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<Announcement>>>() {
        });
        return response;
    }

    @ApiOperation("查询全局提醒，前端获取name")
    @GetMapping({"checkGlobalAlert"})
    public Response checkGlobalAlert() {
        final Map<String, Object> param = new HashMap<>();
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/announcement/checkGlobalAlert", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Announcement> response = JSON.parseObject(res, new TypeReference<DataResponse<Announcement>>() {
        });
        return response;
    }

    @ApiOperation("批量修改忽略状态")
    @GetMapping({"updateIgnoreStatus"})
    public Response updateIgnoreStatus(@RequestParam(required = false) String ids) {
        final Map<String, Object> param = new HashMap<>();
        param.put("ids", ids);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/announcement/updateIgnoreStatus", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<Announcement>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<Announcement>>>() {
        });
        return response;
    }

    @ApiOperation("修改阅读状态")
    @GetMapping("updateReadStatus")
    public Response updateReadStatus(@RequestParam(required = false) String id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/announcement/updateReadStatus", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<Announcement>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<Announcement>>>() {
        });
        return response;
    }

}
