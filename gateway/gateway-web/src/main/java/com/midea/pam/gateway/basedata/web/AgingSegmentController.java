package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.AgingSegmentHeaderDto;
import com.midea.pam.common.basedata.entity.AgingSegmentHeader;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@RestController
@Api("账龄规则")
@RequestMapping({"agingSegment"})
public class AgingSegmentController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation("新增单位下账龄规则")
    @PostMapping({"addAgingSegmentHeader"})
    public com.midea.pam.common.base.Response addAgingSegmentHeader(@RequestBody AgingSegmentHeader agingSegmentHeader) {
        String url = String.format("%sagingSegment/addAgingSegmentHeader", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, agingSegmentHeader, String.class);
        return JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<com.midea.pam.common.base.DataResponse<Boolean>>() {
        });
    }


    @ApiOperation(value = "查询账龄规则列表")
    @GetMapping("selectAgingSegmentHeaderPage")
    public Response selectAgingSegmentHeaderPage(
            @RequestParam(required = false) @ApiParam("状态") Integer status,
            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put("unitId", SystemContext.getUnitId());
        param.put("status", status);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "agingSegment/selectAgingSegmentHeaderPage", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<AgingSegmentHeaderDto>>>() {
        });
    }

    @ApiOperation(value = "查询账龄规则关联详情列表")
    @GetMapping("selectAgingSegmentHeaderDto/{id}")
    public Response selectAgingSegmentHeaderDto(@PathVariable Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "agingSegment/selectAgingSegmentHeaderDto", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<AgingSegmentHeaderDto>>() {
        });
    }

    @ApiOperation("编辑账龄规则详情")
    @PostMapping({"saveAgingSegmentDetails"})
    public Response saveAgingSegmentDetails(@RequestBody AgingSegmentHeaderDto dto) {
        String url = String.format("%sagingSegment/saveAgingSegmentDetails", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<AgingSegmentHeaderDto>>() {
        });
    }

}
