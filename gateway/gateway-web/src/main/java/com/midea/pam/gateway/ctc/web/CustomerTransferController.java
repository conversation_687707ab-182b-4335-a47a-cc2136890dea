package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.ctc.dto.BankAccountNumByCustomTransferDto;
import com.midea.pam.common.ctc.dto.CustomerTransferDto;
import com.midea.pam.common.ctc.dto.CustomerTransferRelationDto;
import com.midea.pam.common.ctc.dto.CustomerTransferReverseDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.ProcessTemplate;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.service.MipWorkflowInnerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("客户间转款")
@RestController
@RequestMapping("customerTransfer")
public class CustomerTransferController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private MipWorkflowInnerService mipWorkflowInnerService;


    @ApiOperation(value = "查看详情")
    @GetMapping("view")
    public DataResponse view(@RequestParam Long id) {
        final String url = String.format("%scustomerTransfer/view?id=%s", ModelsEnum.CTC.getBaseUrl(), id);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<CustomerTransferDto>>() {
        });
    }

    @ApiOperation(value = "保存")
    @PostMapping("save")
    public DataResponse save(@RequestBody CustomerTransferDto dto) {
        final String url = String.format("%scustomerTransfer/save", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<CustomerTransferDto>>() {
        });
    }

    @ApiOperation(value = "冲销")
    @PostMapping("reverse")
    public DataResponse save(@RequestBody CustomerTransferReverseDto dto) {
        final String url = String.format("%scustomerTransfer/reverse", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Long>>() {
        });
    }

    @ApiOperation(value = "冲销详情")
    @GetMapping("reverseDetail")
    public DataResponse reverseDetail(@RequestParam Long customerTransferReverseId) {
        final String url = String.format("%scustomerTransfer/reverseDetail?customerTransferReverseId=%s", ModelsEnum.CTC.getBaseUrl(), customerTransferReverseId);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<CustomerTransferDto>>() {
        });
    }

    @ApiOperation(value = "取消冲销")
    @GetMapping("reverseCancel")
    public DataResponse reverseCancel(@RequestParam Long id) {
        final String url = String.format("%scustomerTransfer/reverseCancel?id=%s", ModelsEnum.CTC.getBaseUrl(), id);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<CustomerTransferDto> response = JSON.parseObject(res, new TypeReference<DataResponse<CustomerTransferDto>>() {
        });
        //同时作废工作流
        if (response.getCode() == 0) {
            mipWorkflowInnerService.draftAbandon(ProcessTemplate.CUSTOMER_TRANSFER_REVERSE_APP.getCode(), id);
        }
        return response;
    }

    @ApiOperation(value = "冲销校验")
    @GetMapping("reverseCheck")
    public DataResponse reverseCheck(@RequestParam Long id) {
        final String url = String.format("%scustomerTransfer/reverseCheck?id=%s", ModelsEnum.CTC.getBaseUrl(), id);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "获取转入方银行帐户")
    @GetMapping("getBankAccountNumByCustomTransfer")
    public DataResponse getBankAccountNumByCustomTransfer(@ApiParam("业务类型") @RequestParam() Integer transferType,
                                                          @ApiParam("转入方业务实体id") @RequestParam() Long transferInOuId,
                                                          @ApiParam("币种") @RequestParam() String currency) {
        Map<String, Object> params = new HashMap<>();
        params.put("transferType", transferType);
        params.put("transferInOuId", transferInOuId);
        params.put("currency", currency);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "customerTransfer/getBankAccountNumByCustomTransfer", params);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<List<BankAccountNumByCustomTransferDto>>>() {
        });
    }

    @ApiOperation(value = "作废")
    @PutMapping("cancel/{id}")
    public DataResponse cancel(@PathVariable Long id) {
        String url = String.format("%scustomerTransfer/cancel/" + id, ModelsEnum.CTC.getBaseUrl());
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
    }


    @ApiOperation(value = "查询供需关系信息")
    @GetMapping("relationInfo")
    public DataResponse getRelationInfo(@ApiParam("供方业务实体id") @RequestParam(required = false) Long transferOutOuId,
                                        @ApiParam("需方业务实体id") @RequestParam(required = false) Long transferInOuId) {
        Map<String, Object> params = new HashMap<>();
        params.put("transferOutOuId", transferOutOuId);
        params.put("transferInOuId", transferInOuId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "customerTransfer/relationInfo", params);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<List<CustomerTransferRelationDto>>>() {
        });
    }

    @ApiOperation(value = "获取业务类型列表(权限控制)")
    @GetMapping("typeList")
    public DataResponse getTransferTypeList() {
        final String url = String.format("%scustomerTransfer/typeList", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<List<CustomerTransferDto>>>() {
        });
    }

}
