package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.basedata.dto.PlanRuleConfigurationDto;
import com.midea.pam.common.basedata.dto.SystemParamDto;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Api("系统参数配置")
@RequestMapping({"systemParam"})
public class SystemParamController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "保存系统配置", response = SystemParamDto.class)
    @PostMapping({"saveBatch"})
    public Response saveBatch(@RequestBody List<SystemParamDto> dtos) {
        String url = String.format("%ssystemParam/saveBatch",ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dtos, String.class);
        DataResponse<Boolean> response= JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<Boolean>>(){});
        return response;
    }

    @ApiOperation(value = "系统参数", response = SystemParamDto.class)
    @GetMapping({"selectList"})
    public Response selectList(@RequestParam(required = false) @ApiParam("虚拟部门") Long unitId,
                               @RequestParam(required = false) @ApiParam("模块") String module,
                               @RequestParam(required = false) @ApiParam("参数名") String sysKey) {
        final Map<String, Object> param = new HashMap<>();
        param.put("unitId", unitId);
        param.put("module", module);
        param.put("sysKey", sysKey);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/systemParam/selectList", param);
        String res = restTemplate.getForEntity(url , String.class).getBody();
        DataResponse<List<SystemParamDto>> response= JSON.parseObject(cleanStr(res), new TypeReference<DataResponse<List<SystemParamDto>>>(){});
        return response;
    }



    /*方案规则配置*/
    @ApiOperation(value = "保存方案规则配置", response = PlanRuleConfigurationDto.class)
    @PostMapping({"planRuleConfiguration/save"})
    public Response savePlanRuleConfiguration(@RequestBody PlanRuleConfigurationDto dto) {
        String url = String.format("%ssystemParam/planRuleConfiguration/save",ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<Boolean> response= JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<Boolean>>(){});
        return response;
    }

    @ApiOperation(value = "方案规则配置详情", response = PlanRuleConfigurationDto.class)
    @GetMapping({"planRuleConfiguration/detail"})
    public Response planRuleConfigurationDetail(@RequestParam Long unitId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("unitId", unitId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/systemParam/planRuleConfiguration/detail", param);
        String res = restTemplate.getForEntity(url , String.class).getBody();
        DataResponse<PlanRuleConfigurationDto> response= JSON.parseObject(cleanStr(res), new TypeReference<DataResponse<PlanRuleConfigurationDto>>(){});
        return response;
    }

    @ApiOperation(value = "当前用户所选部门的方案规则配置详情", response = PlanRuleConfigurationDto.class)
    @GetMapping({"planRuleConfiguration/detailForUser"})
    public Response planRuleConfigurationDetailForUser() {
        final Map<String, Object> param = new HashMap<>();
        param.put("unitId", SystemContext.getUnitId());
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/systemParam/planRuleConfiguration/detail", param);
        String res = restTemplate.getForEntity(url , String.class).getBody();
        DataResponse<PlanRuleConfigurationDto> response= JSON.parseObject(cleanStr(res), new TypeReference<DataResponse<PlanRuleConfigurationDto>>(){});
        return response;
    }


    /*方案字段配置*/
    @ApiOperation(value = "保存方案字段配置", response = SystemParamDto.class)
    @PostMapping({"planFieldConfiguration/save"})
    public Response savePlanFieldConfiguration(@RequestBody PlanRuleConfigurationDto dto) {
        String url = String.format("%ssystemParam/planFieldConfiguration/save",ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<Boolean> response= JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<Boolean>>(){});
        return response;
    }

    @ApiOperation(value = "方案字段配置详情", response = SystemParamDto.class)
    @GetMapping({"planFieldConfiguration/detail"})
    public Response planFieldConfigurationDetail(@RequestParam Long unitId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("unitId", unitId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/systemParam/planFieldConfiguration/detail", param);
        String res = restTemplate.getForEntity(url , String.class).getBody();
        DataResponse<PlanRuleConfigurationDto> response= JSON.parseObject(cleanStr(res), new TypeReference<DataResponse<PlanRuleConfigurationDto>>(){});
        return response;
    }

    @ApiOperation(value = "当前用户所选部门的方案字段配置详情", response = SystemParamDto.class)
    @GetMapping({"planFieldConfiguration/detailForUser"})
    public Response planFieldConfigurationDetailForUser() {
        final Map<String, Object> param = new HashMap<>();
        param.put("unitId", SystemContext.getUnitId());
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/systemParam/planFieldConfiguration/detail", param);
        String res = restTemplate.getForEntity(url , String.class).getBody();
        DataResponse<PlanRuleConfigurationDto> response= JSON.parseObject(cleanStr(res), new TypeReference<DataResponse<PlanRuleConfigurationDto>>(){});
        return response;
    }
}
