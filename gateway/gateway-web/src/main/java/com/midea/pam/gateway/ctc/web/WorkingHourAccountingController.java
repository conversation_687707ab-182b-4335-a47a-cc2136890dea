package com.midea.pam.gateway.ctc.web;

import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.CostCollectionDto;
import com.midea.pam.common.ctc.dto.LaborCostDetailDto;
import com.midea.pam.common.ctc.dto.WorkingHourAccountingDto;
import com.midea.pam.common.ctc.entity.WorkingHourAccountingDetail;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;

/**
 * @program: pam
 * @description: WorkingHourAccountingController
 * @author: gaojh1
 * @create: 2019-6-28 10:51
 **/
@Api("成本归集")
@RestController
@RequestMapping("ctc/workingHourAccounting")
public class WorkingHourAccountingController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "工时成本统计分页查询", response = CostCollectionDto.class)
    @GetMapping("/collectionSummaryPage")
    public Response collectionSummaryPage(@RequestParam(required = false) Long ouId,
                                          @RequestParam(required = false) final Long projectId,
                                          @RequestParam(required = false) String projectCode,
                                          @RequestParam(required = false) String projectName,
                                          @RequestParam(required = false) String projectFuzzyLike,
                                          @RequestParam(required = false) String applyMonth,
                                          @RequestParam(required = true, defaultValue = "1") final Integer pageNum,
                                          @RequestParam(required = true, defaultValue = "10") final Integer pageSize) {
        return null;
    }

    @ApiOperation(value = "工时成本统计明细分页查询", response = LaborCostDetailDto.class)
    @GetMapping("/laborCostSummaryPage")
    public Response laborCostSummaryPage(@RequestParam(required = true) final Long projectId,
                                         @RequestParam(required = false, defaultValue = "1") final String applyMonth,
                                         @RequestParam(required = true, defaultValue = "1") final Integer pageNum,
                                         @RequestParam(required = true, defaultValue = "10") final Integer pageSize) {
        return null;
    }

    @ApiOperation(value = "工时成本入账单分页查询", response = WorkingHourAccountingDto.class)
    @GetMapping("/page")
    public Response page(@RequestParam(required = false) final Long id,
                         @RequestParam(required = false) String projectCode,
                         @RequestParam(required = false) String projectName,
                         @RequestParam(required = false) Long ouId,
                         @RequestParam(required = false) String code,
                         @RequestParam(required = false) Integer erpStatus,
                         @RequestParam(required = true, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = true, defaultValue = "10") final Integer pageSize) {
        return null;
    }

    @ApiOperation(value = "工时成本入账单详细分页查询", response = WorkingHourAccountingDetail.class)
    @GetMapping("/detailPage")
    public Response detailPage(@RequestParam(required = true) final Long workingHourAccountingId,
                               @RequestParam(required = true, defaultValue = "1") final Integer pageNum,
                               @RequestParam(required = true, defaultValue = "10") final Integer pageSize) {
        return null;
    }

    @ApiOperation(value = "工时成本入账单详细科目分页查询")
    @GetMapping("/subjectPage")
    public Response subjectPage(@RequestParam(required = true) final Long workingHourAccountingId,
                                @RequestParam(required = true, defaultValue = "1") final Integer pageNum,
                                @RequestParam(required = true, defaultValue = "10") final Integer pageSize) {
        return null;
    }

    @ApiOperation(value = "批量入账统计")
    @PostMapping("/batchSummary")
    public Response batchSummary(@RequestBody WorkingHourAccountingDto workingHourAccountingDto) {
        return null;
    }

    @ApiOperation(value = "批量入账")
    @PostMapping("/batchSave")
    public Response batchSave(@RequestBody WorkingHourAccountingDto workingHourAccountingDto) {
        return null;
    }

    @ApiOperation(value = "入账(推送ERP)")
    @GetMapping("/pushToERP")
    public Response pushToERP(@RequestParam(required = false) Long id) {
        return null;
    }

    @ApiOperation(value = "入账(推送ERP)Test")
    @GetMapping("/pushToERPTest")
    public Response pushToERPTest(@RequestParam(required = false) Long id) {
        return null;
    }

    @ApiOperation(value = "工时成本信息导出")
    @GetMapping("/export")
    public void export(HttpServletResponse response,
                       @RequestParam(required = true) Long id) {
    }

    @ApiOperation(value = "工时成本入账单冲销跳转数据")
    @GetMapping("/getWorkingAccountWriteOff")
    public Response getWorkingAccountWriteOff(@RequestParam(required = true) final String projectStr,
                                              @RequestParam(required = false) final String userStr,
                                              @RequestParam(required = false) final Date applyDateStart,
                                              @RequestParam(required = false) final Date applyDateEnd) {
        return null;
    }

    @ApiOperation(value = "工时成本入账单冲销")
    @PostMapping("/workingAccountWriteOff")
    public Response workingAccountWriteOff(@RequestBody WorkingHourAccountingDto workingHourAccountingDto) {
        return null;
    }

    @ApiOperation(value = "工时入账单红冲")
    @GetMapping("/redDashed")
    public Response redDashed(@RequestParam final Long id,
                              @RequestParam final String writeOffReason,
                              @RequestParam final String glPeriod) {
        return null;
    }

    @ApiOperation(value = "工时成本入账单作废")
    @GetMapping("abandon")
    public Response abandon(@RequestParam Long id) {
        return null;
    }

    @ApiOperation(value = "工时成本冲销信息导出")
    @GetMapping("/exportWriteOff")
    public void exportWriteOff(HttpServletResponse response, @RequestParam Long id, @RequestParam String code, @RequestParam String applyMonth) {
    }
}
