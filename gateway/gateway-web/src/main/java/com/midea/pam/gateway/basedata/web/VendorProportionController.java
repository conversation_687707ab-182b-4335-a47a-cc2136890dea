package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.entity.VendorProportion;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.PageResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 供货比例controller
 * @date 2019-3-20
 */
@RestController
@RequestMapping("vendorProportion")
@Api("供货比例")
public class VendorProportionController extends ControllerHelper {

    @Autowired
    private RestTemplate restTemplate;


    @ApiOperation(value = "查询供货比例列表分页", response = VendorProportion.class)
    @GetMapping("selectPage")
    public Response selectPage(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                             @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize,
                             @RequestParam(required = false) @ApiParam(value = "库存组织") String org,
                             @RequestParam(required = false) @ApiParam(value = "物料编码") String materialCode
    ) {

        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("org", org);
        param.put("materialCode", materialCode);

        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "vendorProportion/selectPage", param);
        String res = cleanStr(restTemplate.getForObject(url, String.class));
        PageInfo<VendorProportion> data = JSON.parseObject(res, new TypeReference<PageInfo<VendorProportion>>() {
        });
        PageResponse<VendorProportion> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation(value = "ERP供货比例数据拉取")
    @GetMapping("getVendorProportionFromErp")
    public Response getVendorProportionFromErp(@RequestParam(required = false) String lastUpdateDate,
                                               @RequestParam(required = false) String lastUpdateDateEnd,
                                               @RequestParam(required = false) String materialCode) {
        final Map<String, Object> param = new HashMap<>();
        param.put("lastUpdateDate", lastUpdateDate);
        param.put("lastUpdateDateEnd", lastUpdateDateEnd);
        param.put("materialCode", materialCode);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "vendorProportion/getVendorProportionFromErp", param);
        restTemplate.getForObject(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        response.setData("0");
        return response;
    }

}
