package com.midea.pam.gateway.config;

import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.gateway.entity.CurrentContext;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.gateway.common.utils.PamCurrentUserUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021-07-19
 * @description 验证用户页面使用单位和上下文使用单位是否一致
 */
public class UnitAuthInterceptor implements Filter {

    private Logger logger = LoggerFactory.getLogger(this.getClass());


    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        final HttpServletRequest request = (HttpServletRequest) servletRequest;
        final HttpServletResponse response = (HttpServletResponse) servletResponse;
        final String requestURI = request.getRequestURI();
        // 临时白名单，工作流放行
        if (requestURI.contains("workflow/callback")) {
            filterChain.doFilter(request, response);
            return;
        }
        final String contestUnitId = request.getHeader(Constants.CENTEST_UNIT_ID);
        String username = PamCurrentUserUtil.getCurrentUserName();
        CurrentContext context = CacheDataUtils.getContextByMip(username);
        final Long unitId = context != null ? context.getCurrentOrgId() : null;
        if (unitId != null && StringUtils.isNotBlank(contestUnitId)
                && !Objects.equals(Long.valueOf(contestUnitId), unitId)) {
            logger.info("当前登录使用单位ID: {}, 用户上下文使用单位ID: {}", contestUnitId, unitId);
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        } else {
            filterChain.doFilter(request, response);
        }
    }

    @Override
    public void destroy() {

    }
}
