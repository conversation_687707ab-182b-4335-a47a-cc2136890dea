package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.basedata.entity.Grant;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("菜单授权")
@RestController
@RequestMapping("grant/menuManage")
public class MenuManageGrantController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation("授权菜单给角色")
    @PutMapping
    public Response grantMenuToRole(@RequestBody Grant grant) {
        String url = String.format("%sgrant/menuManage", ModelsEnum.BASEDATA.getBaseUrl());
        restTemplate.put(url, grant);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation("复制授权菜单给角色")
    @GetMapping("copyMenuToRole")
    public Response copyMenuToRole(@RequestParam Long roleId, @RequestParam Long copyRoleId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("roleId", roleId);
        param.put("copyRoleId", copyRoleId);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "grant/menuManage/copyMenuToRole", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<Long>>>() {
        });

    }

    @ApiOperation("查询角色授权的菜单列表")
    @GetMapping("{roleId}")
    public Response grantedMenus(@PathVariable final Long roleId) {
        String url = String.format("%sgrant/menuManage/%s", ModelsEnum.BASEDATA.getBaseUrl(), roleId);
        String res = cleanStr(restTemplate.getForObject(url, String.class));
        List<Long> data = JSON.parseObject(res, new TypeReference<List<Long>>() {
        });
        DataResponse<List<Long>> response = Response.dataResponse();
        return response.setData(data);
    }

    @ApiOperation("按菜单查角色复制给其他菜单")
    @GetMapping("copyRoleToOtherMenuByMenu")
    public Response copyRoleToOtherMenuByMenu(@RequestParam @ApiParam("源菜单ID") Long sourceMenuId,
                                              @RequestParam @ApiParam("目标菜单ID") Long otherMenuId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("sourceMenuId", sourceMenuId);
        param.put("otherMenuId", otherMenuId);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "grant/menuManage/copyRoleToOtherMenuByMenu", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

}
