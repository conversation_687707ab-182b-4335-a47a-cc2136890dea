package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.MaterialAdjustHeaderDTO;
import com.midea.pam.common.ctc.excelVo.MaterialChangeTypeExcelVo;
import com.midea.pam.common.ctc.vo.MaterialChangeTypeVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.common.utils.BeanConverter;
import com.midea.pam.gateway.common.utils.DateUtil;
import com.midea.pam.gateway.common.utils.ExportExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: 物料新增和变更(物料类型变更)
 * @author: ex_xuwj4
 * @create: 2021-03-11
 **/
@Api("物料类型变更")
@RestController
@RequestMapping(value = {"/materialChangeType", "mobile/app/materialChangeType"})
public class MaterialChangeTypeController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查找物料类型变更详情")
    @GetMapping("findMaterialChangeTypeInfo")
    public com.midea.pam.common.base.Response findMaterialChangeTypeInfo(@RequestParam Long id) {

        Map<String, Object> param = new HashMap<>();
        param.put("id", id);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "materialChangeType/findMaterialChangeTypeInfo", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        com.midea.pam.common.base.DataResponse<MaterialAdjustHeaderDTO> response = JSON.parseObject(res, new TypeReference<com.midea.pam.common.base.DataResponse<MaterialAdjustHeaderDTO>>() {
        });
        return response;
    }


    @ApiOperation(value = "物料类型变更保存或编辑")
    @PostMapping("materialChangeTypeSaveOrUpdate")
    public com.midea.pam.common.base.Response materialChangeTypeSaveOrUpdate(@RequestBody MaterialAdjustHeaderDTO dto) {
        final String url = String.format("%smaterialChangeType/materialChangeTypeSaveOrUpdate", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, dto, String.class).getBody();
        com.midea.pam.common.base.DataResponse<MaterialAdjustHeaderDTO> response = JSON.parseObject(res, new TypeReference<com.midea.pam.common.base.DataResponse<MaterialAdjustHeaderDTO>>() {
        });
        return response;
    }

    @ApiOperation(value = "物料类型变更保存或编辑作废（将草稿状态改为废弃状态）")
    @GetMapping("deleteDraft")
    public Response deleteDraft(@RequestParam @ApiParam("ID") Long id) {
        String url = ModelsEnum.CTC.getBaseUrl() + "/materialChangeType/deleteDraft?id=" + id;
        restTemplate.getForObject(url, String.class);
        return Response.response();
    }

    @ApiOperation(value = "发起审批")
    @PutMapping("updateStatusChecking/skipSecurityInterceptor")
    public Response updateStatusChecking(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                         @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                         @RequestParam(required = false) String handlerId,
                                         @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialChangeType/updateStatusChecking/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "驳回")
    @PutMapping("updateStatusReject/skipSecurityInterceptor")
    public Response updateStatusReject(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                       @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                       @RequestParam(required = false) String handlerId,
                                       @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialChangeType/updateStatusReject/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "通过")
    @PutMapping("updateStatusPass/skipSecurityInterceptor")
    public Response updateStatusPass(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                     @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                     @RequestParam(required = false) String handlerId,
                                     @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialChangeType/updateStatusPass/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "撤回")
    @PutMapping("updateStatusReturn/skipSecurityInterceptor")
    public Response updateStatusReturn(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                       @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                       @RequestParam(required = false) String handlerId,
                                       @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialChangeType/updateStatusReturn/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "作废")
    @PutMapping("abandon/skipSecurityInterceptor")
    public Response abandon(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                            @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                            @RequestParam(required = false) String handlerId,
                            @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialChangeType/abandon/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "删除")
    @PutMapping("delete/skipSecurityInterceptor")
    public Response delete(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                           @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                           @RequestParam(required = false) String handlerId,
                           @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialChangeType/delete/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "导出物料类型变更报告")
    @GetMapping("exportMaterialChangeTypeDetails")
    public void exportMaterialChangeTypeDetails(HttpServletResponse response, @RequestParam("materialId") @ApiParam("物料id") Long materialId,
                                                @RequestParam("operatingUnitId") @ApiParam("业务实体id") Long operatingUnitId,
                                                @RequestParam("organizationId") @ApiParam("库存组织id") Long organizationId) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("materialId", materialId);
        param.put("operatingUnitId", operatingUnitId);
        param.put("organizationId", organizationId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/materialChangeType/exportMaterialChangeTypeDetails", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<MaterialChangeTypeVo>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<MaterialChangeTypeVo>>>() {
        });
        List<MaterialChangeTypeVo> dataList = dataResponse.getData();
        List<MaterialChangeTypeExcelVo> excelVos = BeanConverter.copy(dataList, MaterialChangeTypeExcelVo.class);
        for (int i = 0; i < dataList.size(); i++) {
            excelVos.get(i).setSerialNumber(i + 1);
        }
        //导出操作
        ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", MaterialChangeTypeExcelVo.class, "物料类型变更报告" + DateUtil.format(DateUtil.getCurrentDate(), DateUtil.DATE_INTEGER_PATTERN) + ".xls", response);

    }

    @ApiOperation(value = "移动审批获取物料类型变更详情")
    @GetMapping({"getMaterialChangeTypeApp"})
    public com.midea.pam.common.base.Response getBusinessApp(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/materialChangeType/getMaterialChangeTypeApp", param);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<ResponseMap> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<ResponseMap>>() {
        });
        return dataResponse;
    }
}
