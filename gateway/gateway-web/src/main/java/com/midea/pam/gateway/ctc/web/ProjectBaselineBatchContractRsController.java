package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.ProjectBaselineBatchContractRsDto;
import com.midea.pam.common.ctc.dto.ProjectContractRsDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.List;


@Api("基线批次关联合同")
@RestController
@RequestMapping("projectBaselineBatchContractRs")
public class ProjectBaselineBatchContractRsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 查询项目关联合同
     *
     * @param param
     * @return
     */
    @ApiOperation(value = "查询项目关联合同")
    @PostMapping("/v1/getContractRsList")
    public Response getContractRsList(@RequestBody ProjectBaselineBatchContractRsDto param) {
        String url = String.format("%sprojectBaselineBatchContractRs/v1/getContractRsList", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, param, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<ProjectContractRsDto>>>() {
        });
    }

    /**
     * 查询基线批次关联合同列表
     *
     * @param param
     * @return
     */
    @ApiOperation(value = "查询基线批次关联合同列表")
    @PostMapping("/v1/list")
    public Response list(@RequestBody ProjectBaselineBatchContractRsDto param) {
        String url = String.format("%sprojectBaselineBatchContractRs/v1/list", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, param, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<ProjectBaselineBatchContractRsDto>>>() {
        });
    }

    /**
     * 保存基线批次关联合同列表
     *
     * @param rsList
     * @return
     */
    @ApiOperation(value = "保存基线批次关联合同列表")
    @PostMapping("/v1/save")
    public Response save(@RequestBody List<ProjectBaselineBatchContractRsDto> rsList) {
        String url = String.format("%sprojectBaselineBatchContractRs/v1/save", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, rsList, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }
}