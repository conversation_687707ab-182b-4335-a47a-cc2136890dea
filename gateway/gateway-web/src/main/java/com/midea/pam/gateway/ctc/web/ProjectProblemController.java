package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.ProjectProblemDto;
import com.midea.pam.common.ctc.vo.ProjectProblemParticipantVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("项目问题")
@RestController
@RequestMapping({"projectProblem"})
@Validated
public class ProjectProblemController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "暂存项目问题")
    @PostMapping("temporaryStorage")
    public Response temporaryStorage(@RequestBody ProjectProblemDto projectProblemDto) {
        final String url = String.format("%sprojectProblem/temporaryStorage", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, projectProblemDto, DataResponse.class).getBody();
    }

    @ApiOperation(value = "提交项目问题")
    @PostMapping("submitProblem")
    public Response submitProblem(@RequestBody @Valid ProjectProblemDto projectProblemDto) {
        final String url = String.format("%sprojectProblem/submitProblem", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, projectProblemDto, DataResponse.class).getBody();
    }

    @ApiOperation(value = "废弃项目问题")
    @PostMapping("delete")
    public Response delete(@RequestBody ProjectProblemDto projectProblemDto) {
        final String url = String.format("%sprojectProblem/delete", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, projectProblemDto, DataResponse.class).getBody();
    }

    @ApiOperation(value = "根据id查找问题 -- 审核")
    @GetMapping("findById/{id}")
    public Response findById(@PathVariable("id") Long id) {
        final String url = String.format("%sprojectProblem/findById/%s", ModelsEnum.CTC.getBaseUrl(), id);
        String forEntity = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(forEntity, new TypeReference<DataResponse<ProjectProblemDto>>() {
        });
    }

    @ApiOperation(value = "审核 -- 驳回问题")
    @PostMapping("reject")
    public Response reject(@RequestBody ProjectProblemDto projectProblemDto) {
        final String url = String.format("%sprojectProblem/reject", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, projectProblemDto, DataResponse.class).getBody();
    }

    @ApiOperation(value = "审核通过并分配解决人")
    @PostMapping("distribution")
    public Response distribution(@RequestBody ProjectProblemDto projectProblemDto) {
        final String url = String.format("%sprojectProblem/distribution", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, projectProblemDto, DataResponse.class).getBody();
    }

    @ApiOperation(value = "变更问题解决人")
    @PostMapping("changeProblemSolver")
    public Response changeProblemSolver(@RequestBody ProjectProblemDto projectProblemDto){
        final String url = String.format("%sprojectProblem/changeProblemSolver", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, projectProblemDto, DataResponse.class).getBody();
    }

    @ApiOperation(value = "解决问题完成提交")
    @PostMapping("finishSubmit")
    public Response finishSubmit(@RequestBody ProjectProblemDto projectProblemDto){
        final String url = String.format("%sprojectProblem/finishSubmit", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, projectProblemDto, DataResponse.class).getBody();
    }

    @ApiOperation(value = "验证问题")
    @GetMapping("verify")
    public Response confirm(@RequestParam @ApiParam(value = "项目问题ID") Long id,
                            @RequestParam @ApiParam(value = "操作类型：驳回false，验证通过true") Boolean operationType,
                            @RequestParam(required = false) @ApiParam(value = "处理意见") String opinion) {
        final Map params = new HashMap<String, Object>(3);
        params.put("id", id);
        params.put("operationType", operationType);
        params.put("opinion", opinion);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectProblem/verify", params);
        return restTemplate.getForEntity(url, DataResponse.class).getBody();
    }

    @ApiOperation(value = "详情")
    @GetMapping("details/{id}")
    public Response details(@PathVariable("id") Long id){
        final String url = String.format("%sprojectProblem/detail/%s", ModelsEnum.CTC.getBaseUrl(), id);
        String forEntity = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(forEntity, new TypeReference<DataResponse<ProjectProblemDto>>() {
        });
    }

    @ApiOperation(value = "列表")
    @GetMapping("participant/selectList")
    public Response list(@RequestParam @ApiParam("项目问题ID") final Long projectProblemId) {
        final HashMap<String,Object> param = new HashMap<>();
        param.put("projectProblemId",projectProblemId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(),"/projectProblem/participant/selectList",param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url,String.class);
        return JSON.parseObject(responseEntity.getBody(),new TypeReference<DataResponse<List<ProjectProblemParticipantVo>>>(){

        });
    }

    @ApiOperation(value = "添加参与人")
    @PostMapping("participant/add")
    public Response add(@RequestBody ProjectProblemParticipantVo projectProblemParticipantVo){
        final String url = String.format("%sprojectProblem/participant/add",ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url,projectProblemParticipantVo,String.class);
        return JSON.parseObject(responseEntity.getBody(),new TypeReference<DataResponse<Integer>>(){});
    }

    @ApiOperation(value = "删除参与人")
    @GetMapping("participant/del")
    public Response delParticipant(@RequestParam @ApiParam("项目问题ID") final Long projectProblemId,
                                   @RequestParam @ApiParam("参与人ID") final Long participantId,
                                   @RequestParam @ApiParam("操作人姓名") final String operationName){
        final Map params = new HashMap<String, Object>(3);
        params.put("projectProblemId", projectProblemId);
        params.put("participantId", participantId);
        params.put("operationName", operationName);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectProblem/participant/del", params);
        return restTemplate.getForEntity(url, DataResponse.class).getBody();
    }

    @ApiOperation(value = "解决问题暂存原因")
    @PostMapping("solver/saveTemporary")
    public Response saveTemporary(@RequestBody ProjectProblemDto projectProblemDto){
        final String url = String.format("%sprojectProblem/solver/saveTemporary", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, projectProblemDto, DataResponse.class).getBody();
    }

    @ApiOperation(value = "编辑计划解决日期")
    @GetMapping("plannedFinishDate/edit")
    public Response edit(@RequestParam @ApiParam("项目问题ID") final Long projectProblemId,
                                   @RequestParam @ApiParam("计划解决日期") final String planSolveTime,
                                   @RequestParam @ApiParam("操作人姓名") final String operationName){
        final Map params = new HashMap<String, Object>(3);
        params.put("projectProblemId", projectProblemId);
        params.put("planSolveTime", planSolveTime);
        params.put("operationName", operationName);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectProblem/plannedFinishDate/edit", params);
        return restTemplate.getForEntity(url, DataResponse.class).getBody();
    }

    @ApiOperation(value = "编辑保存 项目问题基本信息")
    @PostMapping("editBaseInfo")
    public Response editBaseInfo(@RequestBody ProjectProblemDto projectProblemDto){
        final String url = String.format("%sprojectProblem/editBaseInfo", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, projectProblemDto, DataResponse.class).getBody();
    }

}
