package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.GlegalContractChangeHeaderDto;
import com.midea.pam.common.ctc.entity.GlegalContractChangeHeader;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-11-06
 * @description 法务合同
 */
@Api("法务合同")
@RestController
@RequestMapping("glegalContract")
public class GlegalContractChangeController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "起草人交接列表")
    @GetMapping("drafterChange/page")
    public Response page(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize,
                         @RequestParam(required = false) @ApiParam(value = "标题") String title,
                         @RequestParam(required = false) @ApiParam(value = "业务类型(1销售合同/2采购合同)") Integer businessType,
                         @RequestParam(required = false) @ApiParam(value = "审批状态,多选") String approvalStatusStr,
                         @RequestParam(required = false) @ApiParam(value = "交接人名称") String handoverUserName,
                         @RequestParam(required = false) @ApiParam(value = "承接人名称") String receiverUserName,
                         @RequestParam(required = false) @ApiParam(value = "创建人名称") String createByName,
                         @RequestParam(required = false) @ApiParam(value = "同步法务状态,多选") String syncLegalStatusStr) {
        Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("parentUnitId", SystemContext.getUnitId());
        param.put("title", title);
        param.put("businessType", businessType);
        param.put("approvalStatusStr", approvalStatusStr);
        param.put("handoverUserName", handoverUserName);
        param.put("receiverUserName", receiverUserName);
        param.put("createByName", createByName);
        param.put("syncLegalStatusStr", syncLegalStatusStr);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/glegalContract/drafterChange/page", param);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<GlegalContractChangeHeader>>>() {
        });
    }


    @ApiOperation(value = "起草人交接详情")
    @GetMapping("drafterChange/view/{id}")
    public Response view(@PathVariable Long id) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/glegalContract/drafterChange/view", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<GlegalContractChangeHeaderDto>>() {
        });
    }

    @ApiOperation("起草人交接重新编辑&提交")
    @PostMapping("drafterChange/submit")
    public Response submit(@RequestBody GlegalContractChangeHeaderDto glegalContractChangeHeaderDto) {
        String url = String.format("%sglegalContract/drafterChange/submit", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, glegalContractChangeHeaderDto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<GlegalContractChangeHeaderDto>>() {
        });
    }


    @ApiOperation(value = "起草人交接推送法务")
    @GetMapping("drafterChange/pushGle/{id}")
    public Response pushGle(@PathVariable Long id) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/glegalContract/drafterChange/pushGle", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

}
