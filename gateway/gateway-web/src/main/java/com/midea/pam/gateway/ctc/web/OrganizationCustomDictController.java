package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.UnitDto;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.OrganizationCustomBatchDictDto;
import com.midea.pam.common.ctc.dto.OrganizationCustomDictDto;
import com.midea.pam.common.ctc.entity.OrganizationCustomDict;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.util.PublicUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Api("组织参数模块")
@RestController
@RequestMapping(value = {"organizationCustomDict","mobile/app/organizationCustomDict"})
public class OrganizationCustomDictController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "保存")
    @PostMapping("save")
    public Object save(@RequestBody @ApiParam(name = "OrganizationCustomDictDto", value = "自定义实体") OrganizationCustomDictDto dto) {
        final String url = String.format("%sorganizationCustomDict/save", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, dto, String.class).getBody();
    }

    @ApiOperation(value = "批量保存")
    @PostMapping("batchSave")
    public Object batchSave(@RequestBody @ApiParam(name = "OrganizationCustomBatchDictDto", value = "批量自定义实体") OrganizationCustomBatchDictDto dto) {
        final String url = String.format("%sorganizationCustomDict/batchSave", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, dto, String.class).getBody();
    }


    @ApiOperation(value = "删除")
    @GetMapping({"delete"})
    public Object delete(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/organizationCustomDict/delete", param);
        return restTemplate.getForEntity(url, String.class).getBody();
    }

    @ApiOperation(value = "复制", response = OrganizationCustomDictDto.class)
    @GetMapping("copy")
    public Object copy(@RequestParam @ApiParam(value = "配置对象名id") Long orgId,
                       @RequestParam @ApiParam(value = "配置对象名") String orgName,
                       @RequestParam @ApiParam(value = "配置对来源") String orgFrom,
                       @RequestParam @ApiParam(value = "要复制的参数id") Long pzId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("orgId", orgId);
        param.put("orgName", orgName);
        param.put("orgFrom", orgFrom);
        param.put("pzId", pzId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/organizationCustomDict/copy", param);
        return restTemplate.getForEntity(url, String.class).getBody();
    }

    @ApiOperation(value = "批量复制", response = OrganizationCustomDictDto.class)
    @GetMapping("batchCopy")
    public Object copy(@RequestParam @ApiParam(value = "配置对象名id") Long orgId,
                       @RequestParam @ApiParam(value = "配置对象名") String orgName,
                       @RequestParam @ApiParam(value = "配置对来源") String orgFrom,
                       @RequestParam @ApiParam(value = "来源配置对象名id") Long fromOrgId,
                       @RequestParam @ApiParam(value = "来源配置对来源") String fromOrgFrom) {
        final Map<String, Object> param = new HashMap<>();
        param.put("orgId", orgId);
        param.put("orgName", orgName);
        param.put("orgFrom", orgFrom);
        param.put("fromOrgId", fromOrgId);
        param.put("fromOrgFrom", fromOrgFrom);

        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/organizationCustomDict/batchCopy", param);
        return restTemplate.getForEntity(url, String.class).getBody();
    }


    @ApiOperation(value = "查询", response = OrganizationCustomDictDto.class)
    @GetMapping("query")
    public Object query(@RequestParam(required = false) @ApiParam(value = "配置对象名id") Long orgId,
                        @RequestParam(required = false) @ApiParam(value = "配置对象来源") String orgFrom,
                        @RequestParam(required = false) @ApiParam(value = "配置对象") String orgName,
                        @RequestParam(required = false) @ApiParam(value = "参数名称") String name,
                        @RequestParam(required = false) @ApiParam(value = "参数描述") String description,
                        @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                        @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put("orgId", orgId);
        param.put("orgFrom", orgFrom);
        param.put("orgName", orgName);
        param.put("name", name);
        param.put("description", description);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/organizationCustomDict/query", param);
        return restTemplate.getForEntity(url, String.class).getBody();
    }

    @ApiOperation(value = "查询特定组织参数")
    @GetMapping("queryByName")
    public Response query(@RequestParam @ApiParam(value = "配置对象名id") Long orgId,
                          @RequestParam(required = false) @ApiParam(value = "名称") String name,
                          @RequestParam @ApiParam(value = "类型") String orgFrom) {
        final Map<String, Object> param = new HashMap<>();
        param.put("orgId", orgId);
        param.put("name", name);
        param.put("orgFrom", orgFrom);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/organizationCustomDict/queryByName", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Set<String>> response = JSON.parseObject(res, new TypeReference<DataResponse<Set<String>>>() {
        });
        return response;
    }

    @ApiOperation(value = "查询特定组织参数的所有配置")
    @GetMapping("selectByName")
    public Response selectByName(@RequestParam(required = false) @ApiParam(value = "配置对象名id") Long orgId,
                          @RequestParam @ApiParam(value = "名称") String name,
                          @RequestParam @ApiParam(value = "类型") String orgFrom) {
        final Map<String, Object> param = new HashMap<>();
        param.put("orgId", orgId);
        param.put("name", name);
        param.put("orgFrom", orgFrom);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/organizationCustomDict/selectByName", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Map<Long, Set<String>>> response = JSON.parseObject(res, new TypeReference<DataResponse<Map<Long, Set<String>>>>() {
        });
        return response;
    }

    @ApiOperation(value = "根据配置对象名id查询特定组织参数")
    @GetMapping("queryByOrdId")
    public Response queryByOrdId(@RequestParam(required = false) @ApiParam(value = "配置对象名id") Long orgId,
                                 @RequestParam(required = false) @ApiParam(value = "名称") String name,
                          @RequestParam @ApiParam(value = "类型") String orgFrom) {
        final Map<String, Object> param = new HashMap<>();
        param.put("orgId", orgId);
        param.put("orgFrom", orgFrom);
        param.put("name", name);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/organizationCustomDict/queryByOrdId", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<OrganizationCustomDict>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<OrganizationCustomDict>>>() {
        });
        return response;
    }

    @ApiOperation(value = "根据当前用户公司id查询组织参数值")
    @GetMapping("getValueByOrdId")
    public Response findByOrdId(@RequestParam @ApiParam(value = "名称") String name,
                                @RequestParam @ApiParam(value = "类型") String orgFrom){
        final  Map<String,Object> param = new HashMap<>();
        param.put("name",name);
        param.put("orgFrom",orgFrom);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(),"/organizationCustomDict/getValueByOrdId",param);
        final String res = restTemplate.getForEntity(url,String.class).getBody();
        return JSON.parseObject(res,new TypeReference<DataResponse<OrganizationCustomDictDto>>(){});
    }

    @ApiOperation(value = "查询特定组织参数")
    @GetMapping("queryByOrgForm")
    public Response queryByOrgForm() {
        final Map<String, Object> params = new HashMap<>();
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(),"/organizationCustomDict/queryByOrgForm",params);
        final String res = restTemplate.getForEntity(url,String.class).getBody();
        return JSON.parseObject(res,new TypeReference<DataResponse<List<OrganizationCustomDict>>>(){});
    }

    @ApiOperation(value = "查询特定组织参数")
    @GetMapping("queryByNameNew")
    public Response queryByNameNew(@RequestParam("unitId") Long unitId) {

        final Map<String, Object> params = new HashMap<>();
        unitId = null != unitId ? unitId: SystemContext.getUnitId();
        params.put("name", Constants.SPAREPARTS_MASK);
        params.put("orgId", unitId);
        params.put("orgFrom", OrgCustomDictOrgFrom.COMPANY);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(),"/organizationCustomDict/queryByNameNew",params);
        final String res = restTemplate.getForEntity(url,String.class).getBody();

        return JSON.parseObject(res,new TypeReference<DataResponse<Set<String>>>(){});
    }

}
