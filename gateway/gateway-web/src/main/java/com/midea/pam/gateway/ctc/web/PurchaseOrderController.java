package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.entity.StorageInventory;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.PurchaseDemandOrderIssudeDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderChangeHistoryDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderChangeRecordDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderDto;
import com.midea.pam.common.ctc.dto.SaveBatchPurchaseOrderRecordDto;
import com.midea.pam.common.ctc.dto.SdpCheckDto;
import com.midea.pam.common.ctc.entity.OrganizationCustomDict;
import com.midea.pam.common.ctc.vo.AssociateOccupiedBudgetDetailsVo;
import com.midea.pam.common.ctc.vo.PurchaseDemandOrderIssudeVo;
import com.midea.pam.common.ctc.vo.PurchaseOrderPdfVO;
import com.midea.pam.common.ctc.vo.SdpCheckVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.ProcessTemplate;
import com.midea.pam.common.gateway.dto.ProcessTableInfoDTO;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.FormInstanceExample;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.utils.PamCurrentUserUtil;
import com.midea.pam.gateway.service.FormInstanceService;
import com.midea.pam.gateway.service.MipWorkflowInnerService;
import com.midea.pam.gateway.service.MipWorkflowService;
import com.midea.pam.gateway.service.OssService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Api("物料采购订单")
@RestController
@RequestMapping("purchaseOrder")
@Slf4j
public class PurchaseOrderController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private OssService ossService;
    @Resource
    private MipWorkflowService mipWorkflowService;
    @Resource
    private FormInstanceService formInstanceService;
    @Resource
    private MipWorkflowInnerService mipWorkflowInnerService;


    @ApiOperation(value = "获取待下达订单", response = PurchaseOrderDto.class)
    @GetMapping({"getPendingOrder"})
    public Response getPendingOrder() {
        final Map<String, Object> param = new HashMap<>();
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseOrder/getPendingOrder", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<PurchaseOrderDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<PurchaseOrderDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "采购订单明细移除", response = Long.class)
    @PostMapping({"deleteBatchOrderDetail"})
    public Response deleteBatchOrderDetail(@RequestBody @ApiParam("下达明细id集合") List<Long> ids) {
        String url = String.format("%spurchaseOrder/deleteBatchOrderDetail", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, ids, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "采购订单同步", response = Long.class)
    @GetMapping({"pushToErp"})
    public Response pushToErp(@RequestParam @ApiParam("订单id") Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseOrder/pushToErp", param);
        ;
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "采购订单重推", response = Long.class)
    @GetMapping({"resendToErp"})
    public Response resendToErp(@RequestParam @ApiParam("订单id") Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseOrder/resendToErp", param);
        ;
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "采购订单取消", response = Long.class)
    @GetMapping({"cancelOrder"})
    public Response cancelOrder(@RequestParam @ApiParam("订单id") Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseOrder/cancelOrder", param);
        ;
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "采购订单保存", response = PurchaseOrderDto.class)
    @PostMapping({"saveWithDetailBatch"})
    public Response saveWithDetailBatch(@RequestBody @ApiParam("订单集合") List<PurchaseOrderDto> dtos) {
        String url = String.format("%spurchaseOrder/saveWithDetailBatch", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, dtos, String.class).getBody();
        DataResponse<List<PurchaseOrderDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<PurchaseOrderDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "订单列表", response = PurchaseOrderDto.class)
    @GetMapping({"selectPage"})
    public Response selectPage(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                               @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize,
                               @RequestParam(required = false) @ApiParam(value = "采购订单号") String fuzzyOrderNum,
                               @RequestParam(required = false) @ApiParam(value = "供应商名称") String fuzzyVendorName,
                               @RequestParam(required = false) @ApiParam(value = "供应商编码") String fuzzyVendorNum,
                               @RequestParam(required = false) @ApiParam(value = "供应商地点") String fuzzyVendorSiteCode,
                               @RequestParam(required = false) @ApiParam(value = "采购员") Long buyerId,
                               @RequestParam(required = false) @ApiParam(value = "项目名称") String fuzzyProjectName,
                               @RequestParam(required = false) @ApiParam(value = "项目编号") String fuzzyProjectNum,
                               @RequestParam(required = false) @ApiParam(value = "业务实体") String fuzzyProjectOUName,
                               @RequestParam(required = false) @ApiParam(value = "业务实体ID") String projectOuId,
                               @RequestParam(required = false) @ApiParam(value = "创建日期开始") Date orderCreateAtBegin,
                               @RequestParam(required = false) @ApiParam(value = "创建日期结束") Date orderCreateAtEnd,
                               @RequestParam(required = false) @ApiParam(value = "同步状态") Integer syncStatus,
                               @RequestParam(required = false) @ApiParam(value = "订单状态") Integer orderStatus) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("fuzzyOrderNum", fuzzyOrderNum);
        param.put("fuzzyVendorName", fuzzyVendorName);
        param.put("fuzzyVendorNum", fuzzyVendorNum);
        param.put("fuzzyVendorSiteCode", fuzzyVendorSiteCode);
        param.put("buyerId", buyerId);
        param.put("fuzzyProjectName", fuzzyProjectName);
        param.put("fuzzyProjectNum", fuzzyProjectNum);
        param.put("fuzzyProjectOUName", fuzzyProjectOUName);
        param.put("projectOuId", projectOuId);
        param.put("orderCreateAtBegin", orderCreateAtBegin == null ? "" : orderCreateAtBegin.getTime());
        param.put("orderCreateAtEnd", orderCreateAtEnd == null ? "" : orderCreateAtEnd.getTime());
        param.put("syncStatus", syncStatus);
        param.put("orderStatus", orderStatus);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseOrder/selectPage", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<PurchaseOrderDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<PurchaseOrderDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "采购订单版本历史列表")
    @GetMapping("/versionHistory/list")
    public Response getVersionHistoryList(@RequestParam Long purchaseOrderId) {
        String url = String.format("%spurchaseOrder/versionHistory/list?purchaseOrderId=%s", ModelsEnum.CTC.getBaseUrl(), purchaseOrderId);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<List<PurchaseOrderChangeRecordDto>>>() {
        });
    }

    @ApiOperation(value = "采购订单下载PDF数据封装")
    @GetMapping("/versionHistory/detail")
    public Response getVersionHistoryDetail(@RequestParam Long id, @RequestParam Integer tag) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("tag", tag);
        //跟据采购订单ID获取fd_instance_id
        String fdInstanceId = getDdInstanceId(id, tag);
        String approveInfo = "";
        if (StringUtils.isNotEmpty(fdInstanceId)) {
            JSONObject body = (JSONObject) mipWorkflowService.getProcessTableInfo(PamCurrentUserUtil.getCurrentUserName(), fdInstanceId).get("body");
            if (Objects.nonNull(body.getJSONArray("data"))) {
                List<ProcessTableInfoDTO> processTableInfoDTO = JSON.parseArray(body.getJSONArray("data").toJSONString(), ProcessTableInfoDTO.class);
                approveInfo = processTableInfoDTO.stream().filter(Objects::nonNull)
                        .filter(e -> Objects.equals(e.getActivityType(), "approveNode"))
                        .map(e -> String.format("%s@%s", e.getNodeId(), e.getHandlerIds()))
                        .collect(Collectors.joining(","));
            }
        }
        param.put("approveInfo", approveInfo);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseOrder/versionHistory/detail", param);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<PurchaseOrderPdfVO>>() {
        });
    }

    private String getDdInstanceId(Long id, Integer tag) {
        //tag如果是初始纪录0则id为采购订单号purchaseOrderId，不是，则为purchase_order_change_history.record_id
        if (Objects.equals(0, tag)) {
            Map<String, Object> param = new HashMap<>();
            param.put("id", id);
            param.put("tag", tag);
            String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseOrder/getDdInstanceId", param);
            return restTemplate.getForObject(url, String.class);
        } else {
            return getInstanceFdId(id);
        }
    }

    private String getInstanceFdId(Long formInstanceId) {
        FormInstanceExample condition = new FormInstanceExample();
        condition.createCriteria().andFormUrlEqualTo("purchaseOrderChangeApp").andFormInstanceIdEqualTo(formInstanceId)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<FormInstance> instances = formInstanceService.selectByExample(condition);
        if (!instances.isEmpty()) {
            instances.sort(Comparator.comparing(FormInstance::getId).reversed());
            return instances.get(0).getFdInstanceId();
        } else {
            return null;
        }
    }

    @ApiOperation(value = "订单详情", response = PurchaseOrderDto.class)
    @GetMapping({"getDetailById"})
    public Response getDetailById(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseOrder/getDetailById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PurchaseOrderDto> response = JSON.parseObject(res, new TypeReference<DataResponse<PurchaseOrderDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "订单详情", response = PurchaseOrderDto.class)
    @GetMapping({"getDetailWbsById"})
    public Response getDetailWbsById(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseOrder/getDetailWbsById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PurchaseOrderDto> response = JSON.parseObject(res, new TypeReference<DataResponse<PurchaseOrderDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "订单进展查询")
    @GetMapping("pagePurchaseOrderProgress")
    public Response pagePurchaseOrderProgress(@RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                              @RequestParam(required = false, defaultValue = "10") final Integer pageSize,
                                              @RequestParam(required = false) final Long ouId,
                                              @RequestParam(required = false) final Long buyerId,
                                              @RequestParam(required = false) final String projectCode,
                                              @RequestParam(required = false) final String projectName,
                                              @RequestParam(required = false) final String vendorCode,
                                              @RequestParam(required = false) final String vendorName,
                                              @RequestParam(required = false) final String num
    ) {
        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put("ouId", ouId);
        param.put("buyerId", buyerId);
        param.put("projectCode", projectCode);
        param.put("projectName", projectName);
        param.put("vendorCode", vendorCode);
        param.put("vendorName", vendorName);
        param.put("num", num);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseOrder/pagePurchaseOrderProgress", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<PurchaseOrderDetailDto>>>() {
        });
    }

    @ApiOperation(value = "采购接收信息ERP同步")
    @GetMapping("asynFromErp")
    public Response getInventoryFromErp(@RequestParam(required = false) String lastUpdateDate) {
        final Map<String, Object> param = new HashMap<>();
        param.put("lastUpdateDate", lastUpdateDate);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "purchaseOrder/asynFromErp", param);
        restTemplate.getForObject(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        response.setData("0");
        return response;
    }

    @ApiOperation(value = "采购接收信息ERP同步")
    @GetMapping("asynStatusFromErp")
    public Response asynFromErpNew(@RequestParam(required = false) String lastUpdateDate) {
        final Map<String, Object> param = new HashMap<>();
        param.put("lastUpdateDate", lastUpdateDate);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "purchaseOrder/asynStatusFromErp", param);
        restTemplate.getForObject(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        response.setData("0");
        return response;
    }

    @ApiOperation(value = "SDP供方品质停用检查接口")
    @PostMapping("sdpCheck")
    public Response sdpCheck(@RequestBody(required = false) List<SdpCheckDto> sdpCheckDto) {
        DataResponse<SdpCheckVo> response = new DataResponse<>();
        //库存组织orgId+供应商编码vendorNum+物料编码itemCode
        //if(sdpCheckDto != null && sdpCheckDto.size() > 0) {
        String url = String.format("%spurchaseOrder/sdpCheck", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, sdpCheckDto, String.class).getBody();
        response = JSON.parseObject(res, new TypeReference<DataResponse<SdpCheckVo>>() {
        });
        // }
        return response;
    }

    @ApiOperation(value = "采购订单详情")
    @GetMapping("getDetails")
    public Response getDetails(@RequestParam Long id, Integer editFlag) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("editFlag", editFlag); //是否重新编辑
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseOrder/getDetails", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<SaveBatchPurchaseOrderRecordDto> response = JSON.parseObject(res,
                new TypeReference<DataResponse<SaveBatchPurchaseOrderRecordDto>>() {
                });
        return response;
    }

    @ApiOperation(value = "批量保存采购订单")
    @PostMapping("saveBatchPurchaseOrderRecord")
    public Response saveBatchPurchaseOrderRecord(@RequestBody SaveBatchPurchaseOrderRecordDto purchaseOrderRecordDto) {
        String url = String.format("%spurchaseOrder/saveBatchPurchaseOrderRecord", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, purchaseOrderRecordDto, String.class).getBody();
        DataResponse<Long> response = JSON.parseObject(res, new TypeReference<DataResponse<Long>>() {
        });
        return response;
    }

    @ApiOperation(value = "关联已占用预算详情(采购订单)")
    @GetMapping("associateOccupiedBudgetDetails")
    public Response associateOccupiedBudgetDetails(@RequestParam @ApiParam("需求发布单据id") Long projectWbsReceiptsId,
                                                   @ApiParam("wbs编号") @RequestParam String wbsSummaryCode,
                                                   @RequestParam(required = false) @ApiParam("查询占用预算需要过滤的采购订单编号") String num) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectWbsReceiptsId", projectWbsReceiptsId);
        param.put("wbsSummaryCode", wbsSummaryCode);
        param.put("num", num);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseOrder/associateOccupiedBudgetDetails", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<AssociateOccupiedBudgetDetailsVo>> response = JSON.parseObject(res,
                new TypeReference<DataResponse<List<AssociateOccupiedBudgetDetailsVo>>>() {
                });
        return response;
    }

    @ApiOperation(value = "关联已占用预算详情(采购合同)")
    @GetMapping("associateOccupiedBudgetContractDetails")
    public Response associateOccupiedBudgetContractDetails(@RequestParam @ApiParam("需求发布单据id") Long projectWbsReceiptsId,
                                                           @ApiParam("wbs编号") @RequestParam String wbsSummaryCode) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectWbsReceiptsId", projectWbsReceiptsId);
        param.put("wbsSummaryCode", wbsSummaryCode);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseOrder/associateOccupiedBudgetContractDetails", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<AssociateOccupiedBudgetDetailsVo>> response = JSON.parseObject(res,
                new TypeReference<DataResponse<List<AssociateOccupiedBudgetDetailsVo>>>() {
                });
        return response;
    }

    @ApiOperation(value = "采购需求订单下达数据合并数据获取")
    @GetMapping("getMergeOrder")
    public Response getMergeOrder(@RequestParam(required = false) String id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseOrder/getMergeOrder", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<PurchaseDemandOrderIssudeVo>> response = JSON.parseObject(res,
                new TypeReference<DataResponse<List<PurchaseDemandOrderIssudeVo>>>() {
                });
        return response;
    }

    @ApiOperation(value = "采购需求订单下达数据合并数据保存")
    @PostMapping("setMergeOrder")
    public Response setMergeOrder(@RequestBody List<PurchaseDemandOrderIssudeDto> purchaseDemandOrderIssudeDtos) {
        String url = String.format("%spurchaseOrder/setMergeOrder", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, purchaseDemandOrderIssudeDtos, String.class).getBody();
        DataResponse<Long> response = JSON.parseObject(res, new TypeReference<DataResponse<Long>>() {
        });
        return response;
    }

    @ApiOperation(value = "采购订单行合并保存返回")
    @PostMapping("setGetMergeOrderDetail")
    public Response setGetMergeOrderDetail(@RequestBody List<PurchaseOrderDetailDto> list) {
        String url = String.format("%spurchaseOrder/setGetMergeOrderDetail", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, list, String.class).getBody();
        DataResponse<PurchaseOrderDetailDto> response = JSON.parseObject(res, new TypeReference<DataResponse<PurchaseOrderDetailDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "查询采购订单历史价")
    @PostMapping("queryHistoryPrice")
    public Response queryHistoryPrice(@RequestBody List<PurchaseOrderDetailDto> list) {
        String url = String.format("%spurchaseOrder/queryHistoryPrice", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, list, String.class).getBody();
        DataResponse<List<PurchaseOrderDetailDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<PurchaseOrderDetailDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "修改采购订单状态(作废)")
    @GetMapping("updateByReceiptsIdStatus")
    public Response updateByReceiptsIdStatus(@RequestParam Long receiptsId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("receiptsId", receiptsId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseOrder/updateByReceiptsIdStatus", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<Long>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<Long>>>() {
        });
        log.info("修改采购订单状态(作废)的响应为:{}", response);

        if (response.getCode() == 0 && response.getData().size() > 0) {
            for (Long id : response.getData()) {
                log.info("待流程作废的id为:{}", id);
                mipWorkflowInnerService.draftAbandon(ProcessTemplate.PURCHASE_ORDER_APP.getCode(), id);
            }
        }
        return response;
    }

    @ApiOperation(value = " 移动审批采购订单详情")
    @GetMapping("getMobileApprovalDetail")
    public Response getMobileApprovalDetail(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseOrder/getMobileApprovalDetail", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<ResponseMap> response = JSON.parseObject(res, new TypeReference<DataResponse<ResponseMap>>() {
        });
        return response;
    }

    @ApiOperation(value = "获取待变更订单", notes = "场景：非柔性")
    @GetMapping({"getChangingOrder"})
    public Response getChangingOrder() {
        final Map<String, Object> param = new HashMap<>();
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseOrder/getChangingOrder", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<PurchaseOrderChangeHistoryDto>>>() {
        });
    }

    @ApiOperation(value = "待变更订单移除", notes = "场景：非柔性")
    @PostMapping("deleteBatchChangingOrder")
    public Response deleteBatchChangingOrder(@RequestBody List<Long> ids) {
        String url = String.format("%spurchaseOrder/deleteBatchChangingOrder", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, ids, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "采购订单变更", notes = "场景：非柔性")
    @PostMapping({"changeWithDetailBatch"})
    public Response changeWithDetailBatch(@RequestBody PurchaseOrderChangeRecordDto dto) {
        String url = String.format("%spurchaseOrder/changeWithDetailBatch", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, dto, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "采购订单变更详情", notes = "场景：非柔性")
    @GetMapping("getChangeRecordDetail")
    public Response getChangeRecordDetail(Long recordId, Long createBy) {
        final Map<String, Object> param = new HashMap<>();
        param.put("recordId", recordId);
        param.put("createBy", createBy);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseOrder/getChangeRecordDetail", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PurchaseOrderChangeRecordDto>>() {
        });
    }

    @ApiOperation(value = "批量删除", notes = "场景：采购订单查询(非柔性)")
    @PostMapping("batchDelete")
    public Response batchDelete(@RequestBody PurchaseOrderDto dto) {
        String url = String.format("%spurchaseOrder/batchDelete", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, dto, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "查询订单的子库信息")
    @GetMapping("selectOrderInventory")
    public Response selectOrderInventory(@RequestParam(required = false) Long id,
                                         @RequestParam Long ouId) {
        final Map<String, Object> query = new HashMap<>();
        query.put("ouId", ouId);
        query.put("orgFrom", "inventory_org");
        query.put("name", "采购订单接收子库");
        String url1 = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/organizationCustomDict/queryByOuIdAndNameAndOrgForm", query);
        String res = restTemplate.getForEntity(url1, String.class).getBody();
        DataResponse<OrganizationCustomDict> response = JSON.parseObject(res,
                new TypeReference<DataResponse<OrganizationCustomDict>>() {
                });
        DataResponse<List<StorageInventory>> listDataResponse = new DataResponse<>();
        if (response.getCode() == 0 && response.getData() != null) {
            final Map<String, Object> param = new HashMap<>();
            param.put("ouId", ouId);

            param.put("inventoryNames", response.getData().getValue());
            log.info("获取的参数为:{}", param);
            String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/storageInventory/listByInventoryByOuId", param);
            //String.format("%sstorageInventory/listByInventoryByUnitId", ModelsEnum.BASEDATA.getBaseUrl());

            //log.info("获取请求的url:{}",url);
            String responseEntity = restTemplate.getForEntity(url, String.class).getBody();
            log.info("获取的数据为:{}", responseEntity);
            listDataResponse = JSON.parseObject(responseEntity, new TypeReference<DataResponse<List<StorageInventory>>>() {
            });

        }
        return listDataResponse;


    }

    @ApiOperation(value = "批量更新订单收货地址")
    @PostMapping("batchUpdateDeliveryAddress")
    public Response batchUpdateDeliveryAddress(@RequestBody Map<String, Object> params) {
        try {
            // 参数校验
            List<Long> detailIds = (List<Long>) params.get("detailIds");
            String deliveryAddress = (String) params.get("deliveryAddress");
            String consignee = (String) params.get("consignee");
            String contactPhone = (String) params.get("contactPhone");

            if (CollectionUtils.isEmpty(detailIds)) {
                return Response.err("订单明细ID列表不能为空");
            }
            if (StringUtils.isBlank(deliveryAddress)) {
                return Response.err("收货地址不能为空");
            }
            if (StringUtils.isBlank(consignee)) {
                return Response.err("收货人不能为空");
            }
            if (StringUtils.isBlank(contactPhone)) {
                return Response.err("联系电话不能为空");
            }

            // 构建请求URL
            String url = String.format("%spurchaseOrder/batchUpdateDeliveryAddress",
                    ModelsEnum.CTC.getBaseUrl());

            // 构建请求体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(params);

            // 发送请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            // 解析响应
            return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
            });

        } catch (Exception e) {
            log.error("批量更新订单收货地址失败", e);
            return Response.err("批量更新订单收货地址失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "同步订单收货地址")
    @GetMapping("syncDetailDeliveryAddress")
    public Response syncDetailDeliveryAddress(@RequestParam Long id) {
        try {
            // 参数校验
            if (id == null) {
                return Response.err("订单ID不能为空");
            }

            // 构建请求参数
            final Map<String, Object> params = new HashMap<>();
            params.put("id", id);

            // 构建请求URL
            String url = buildGetUrl(
                    ModelsEnum.CTC.getBaseUrl(),
                    "/purchaseOrder/syncDetailDeliveryAddress",
                    params
            );

            // 发送请求并获取响应
            String responseBody = restTemplate.getForEntity(url, String.class).getBody();

            // 解析响应
            return JSON.parseObject(
                    responseBody,
                    new TypeReference<DataResponse<Boolean>>() {
                    }
            );

        } catch (RestClientException e) {
            log.error("同步订单收货地址请求失败, orderId={}", id, e);
            return Response.err("请求服务失败");
        } catch (Exception e) {
            log.error("同步订单收货地址发生异常, orderId={}", id, e);
            return Response.err("系统异常，请联系管理员");
        }
    }
}
