package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.ctc.dto.VendorPenaltyChangeDto;
import com.midea.pam.common.ctc.dto.VendorPenaltyDto;
import com.midea.pam.common.ctc.excelVo.ErrMsgExcelVo;
import com.midea.pam.common.ctc.excelVo.VendorPenaltyDetailExcelVo;
import com.midea.pam.common.ctc.excelVo.VendorPenaltyDetailExcelVo1;
import com.midea.pam.common.ctc.excelVo.VendorPenaltyExcelVo;
import com.midea.pam.common.ctc.excelVo.VendorPenaltySonExcelVo;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.VendorPenaltyStatus;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.gateway.entity.FileInfo;
import com.midea.pam.common.gateway.entity.FileInfoExample;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.utils.FileUtil;
import com.midea.pam.gateway.mapper.FileInfoMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("vendorPenalty")
@Api("供应商罚扣")
public class VendorPenaltyController {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private FileInfoMapper fileInfoMapper;

    @ApiOperation("同步供应商罚扣数据")
    @GetMapping("syncVendorPenalty")
    public Response syncVendorPenalty(@RequestParam(required = false) Date startDate,
                                      @RequestParam(required = false) Date endDate,
                                      @RequestParam(required = false) Long unitId){
        String url = ModelsEnum.CTC.getBaseUrl() + "vendorPenalty/syncVendorPenalty";
        Map<String,Object> params = new HashMap<>(3);
        if(startDate!=null){
            params.put("startDate",DateUtils.format(startDate));
        }
        if(endDate!=null){
            params.put("endDate",DateUtils.format(endDate));
        }
        if(unitId!=null){
            params.put("unitId",unitId);
        }
        return restTemplate.getForObject(buildUrl(url,params),DataResponse.class);
    }

    @ApiOperation("供应商罚扣分页查询")
    @GetMapping("page")
    public Response page(VendorPenaltyDto dto){
        return restTemplate.getForObject(formPageUrl(dto), DataResponse.class);
    }

    @ApiOperation("查询有对应罚扣记录的罚扣类型")
    @GetMapping("listPenaltyType")
    public Response listPenaltyType(){
        String url = ModelsEnum.CTC.getBaseUrl() + "vendorPenalty/listPenaltyType";
        return restTemplate.getForObject(url, DataResponse.class);
    }

    @ApiOperation("根据erp父罚扣id查询子罚扣")
    @GetMapping("listSonPenalty")
    public Response listSonPenalty(@RequestParam Long erpPenaltyId){
        String url = ModelsEnum.CTC.getBaseUrl() + "vendorPenalty/listSonPenalty";
        return restTemplate.postForObject(url,Collections.singleton(erpPenaltyId), DataResponse.class);
    }

    @ApiOperation("查询有对应罚扣记录的币种")
    @GetMapping("listCurrency")
    public Response listCurrency(){
        String url = ModelsEnum.CTC.getBaseUrl() + "vendorPenalty/listCurrency";
        return restTemplate.getForObject(url, DataResponse.class);
    }

    @ApiOperation("供应商罚扣导出")
    @GetMapping("export")
    public void export(VendorPenaltyDto dto, HttpServletResponse httpServletResponse){
        List<VendorPenaltyExcelVo> penaltyExcelVo = getVendorPenaltyExcelVo(dto);

        List<VendorPenaltyDetailExcelVo1> detailExcelVos = getVendorPenaltyDetailExcelVo(penaltyExcelVo);

        List<VendorPenaltySonExcelVo> vendorPenaltySonExcelVo = getVendorPenaltySonExcelVo(penaltyExcelVo);

        Workbook workbook = ExportExcelUtil.buildDefaultSheet(penaltyExcelVo,VendorPenaltyExcelVo.class, null, "供应商罚扣单据", true);
        ExportExcelUtil.addSheet(workbook, detailExcelVos, VendorPenaltyDetailExcelVo1.class, null, "罚扣分配明细", true);
        ExportExcelUtil.addSheet(workbook, vendorPenaltySonExcelVo, VendorPenaltySonExcelVo.class, null, "ERP罚扣拆分明细", true);
        ExportExcelUtil.downLoadExcel("采购订单罚扣导出excel.xls", httpServletResponse, workbook);
    }

    private List<VendorPenaltyExcelVo> getVendorPenaltyExcelVo(VendorPenaltyDto dto){
        dto.setPageNum(1);
        dto.setPageSize(1000000);
        String res = restTemplate.getForObject(formPageUrl(dto), String.class);
        DataResponse<PageInfo<VendorPenaltyExcelVo>> dataResponse = JSON.parseObject(res,
                new TypeReference<DataResponse<PageInfo<VendorPenaltyExcelVo>>>() {});
        if(dataResponse==null || dataResponse.getCode()!=0){
            throw new BizException(Code.ERROR,"导出失败，罚扣数据查询失败："+(dataResponse!=null?dataResponse.getMsg():res));
        }
        List<VendorPenaltyExcelVo> excelVos = dataResponse.getData().getList();
        excelVos.forEach(e->{
            e.setStatusDesc(VendorPenaltyStatus.getValue(e.getStatus()));
            e.setCollectionDesc(Boolean.TRUE.equals(e.getCollectionFlag())?"已归集":"未归集");
            e.setAmount(new BigDecimal(e.getAmount().stripTrailingZeros().toPlainString()));
            if(e.getConversionRate()!=null) {
                e.setConversionRate(e.getConversionRate().stripTrailingZeros());
            }
        });
        return excelVos;
    }

    private List<VendorPenaltyDetailExcelVo1> getVendorPenaltyDetailExcelVo(List<VendorPenaltyExcelVo> excelVos){
        if(ListUtils.isEmpty(excelVos)){
            return Collections.emptyList();
        }
        List<Long> penaltyIds = excelVos.stream().map(VendorPenaltyExcelVo::getId).collect(Collectors.toList());
        String url = ModelsEnum.CTC.getBaseUrl() + "vendorPenalty/listDetailByPenaltyIds";
        String res = restTemplate.postForObject(url, penaltyIds, String.class);
        DataResponse<List<VendorPenaltyDetailExcelVo1>> dataResponse = JSON.parseObject(res,
                new TypeReference<DataResponse<List<VendorPenaltyDetailExcelVo1>>>() {});
        if(dataResponse==null || dataResponse.getCode()!=0){
            throw new BizException(Code.ERROR,"导出失败，罚扣详情查询失败："+ (dataResponse!=null?dataResponse.getMsg():res));
        }
        List<VendorPenaltyDetailExcelVo1> detailExcelVos = dataResponse.getData();
        Map<Long, VendorPenaltyExcelVo> penaltyExcelVoMap = excelVos.stream().collect(Collectors.toMap(VendorPenaltyExcelVo::getId, Function.identity()));
        Map<Long, List<VendorPenaltyDetailExcelVo1>> detailExcelVoGroup = detailExcelVos.stream().collect(Collectors.groupingBy(VendorPenaltyDetailExcelVo1::getHeadId));
        detailExcelVoGroup.forEach((headId,list)->{
            int i = 1;
            for (VendorPenaltyDetailExcelVo1 detailVo : list) {
                VendorPenaltyExcelVo penaltylVo = penaltyExcelVoMap.get(headId);
                detailVo.setPenaltyCode(penaltylVo.getCode());
                detailVo.setCode(penaltylVo.getCode()+"-"+i++);
                detailVo.setAmount(new BigDecimal(detailVo.getAmount().stripTrailingZeros().toPlainString()));
                detailVo.setProjectCost(new BigDecimal(detailVo.getProjectCost().stripTrailingZeros().toPlainString()));
            }
        });
        return detailExcelVos;
    }

    private List<VendorPenaltySonExcelVo> getVendorPenaltySonExcelVo(List<VendorPenaltyExcelVo> excelVos){
        if(ListUtils.isEmpty(excelVos)){
            return Collections.emptyList();
        }
        List<Long> erpPenaltyIds = excelVos.stream().map(VendorPenaltyExcelVo::getErpPenaltyId).collect(Collectors.toList());
        String url = ModelsEnum.CTC.getBaseUrl() + "vendorPenalty/listSonPenalty";
        String res = restTemplate.postForObject(url, erpPenaltyIds, String.class);
        DataResponse<List<VendorPenaltySonExcelVo>> dataResponse = JSON.parseObject(res,
                new TypeReference<DataResponse<List<VendorPenaltySonExcelVo>>>() {});
        if(dataResponse==null || dataResponse.getCode()!=0){
            throw new BizException(Code.ERROR,"导出失败，erp拆分罚扣明细查询失败："+ (dataResponse!=null?dataResponse.getMsg():res));
        }
        List<VendorPenaltySonExcelVo> sonExcelVos = dataResponse.getData();
        sonExcelVos.forEach(e->{
            e.setStatusDesc(VendorPenaltyStatus.getValue(e.getStatus()));
            e.setAmount(new BigDecimal(e.getAmount().stripTrailingZeros().toPlainString()));
            if(e.getConversionRate()!=null) {
                e.setConversionRate(e.getConversionRate().stripTrailingZeros());
            }
        });
        return sonExcelVos;
    }

    private String formPageUrl(VendorPenaltyDto dto){
        String url = ModelsEnum.CTC.getBaseUrl() + "vendorPenalty/page";
        Map<String,Object> params = new HashMap<>(11);
        params.put("pageNum",dto.getPageNum());
        params.put("pageSize",dto.getPageSize());
        params.put("code",dto.getCode());
        params.put("penaltyType",dto.getPenaltyType());
        params.put("vendorCode",dto.getVendorCode());
        params.put("vendorName",dto.getVendorName());
        params.put("statusStr",dto.getStatusStr());
        params.put("currency",dto.getCurrency());
        params.put("penaltyTitle",dto.getPenaltyTitle());
        params.put("ouName",dto.getOuName());
        params.put("orderNo",dto.getOrderNo());
        params.put("erpCreator",dto.getErpCreator());
        params.put("collectionFlag", dto.getCollectionFlag());
        params.put("erpStatusStr", dto.getErpStatusStr());
        params.put("differenceFlag", dto.getDifferenceFlag());
        if(dto.getPenaltyDateStart()!=null) {
            params.put("penaltyDateStart", DateUtils.format(dto.getPenaltyDateStart()));
        }
        if(dto.getPenaltyDateEnd()!=null) {
            params.put("penaltyDateEnd", DateUtils.format(dto.getPenaltyDateEnd()));
        }
        if(dto.getCreateAtStart()!=null) {
            params.put("createAtStart", DateUtils.format(dto.getCreateAtStart()));
        }
        if(dto.getCreateAtEnd()!=null) {
            params.put("createAtEnd", DateUtils.format(dto.getCreateAtEnd()));
        }
        return buildUrl(url,params);
    }

    @ApiOperation("供应商罚扣详情")
    @GetMapping("detail")
    public Response detail(@RequestParam Long id){
        String url = ModelsEnum.CTC.getBaseUrl() + "vendorPenalty/detail?id="+id;
        String res = restTemplate.getForObject(url,String.class);
        DataResponse<VendorPenaltyDto> response = JSON.parseObject(res, new TypeReference<DataResponse<VendorPenaltyDto>>() {});
        if(response!=null && response.getCode()==0){
            VendorPenaltyDto vendorPenaltyDto = response.getData();
            if(vendorPenaltyDto!=null){
                if (StringUtils.isNotEmpty(vendorPenaltyDto.getAttachmentIds())) {
                    List<Long> ids = Arrays.stream(vendorPenaltyDto.getAttachmentIds().split(","))
                            .map(Long::valueOf).collect(Collectors.toList());
                    vendorPenaltyDto.setAttachments(getFileByIds(ids));
                } else {
                    vendorPenaltyDto.setAttachments(new ArrayList<>(0));
                }
            }
        }
        return response;
    }

    @ApiOperation("获取供应商罚扣提交审批记录id")
    @GetMapping("getVendorPenaltyRecordId")
    public Response getVendorPenaltyRecordId(@RequestParam Long vendorPenaltyId,@RequestParam String formUrl){
        String url = ModelsEnum.CTC.getBaseUrl() + "vendorPenalty/getVendorPenaltyRecordId?vendorPenaltyId="+vendorPenaltyId+"&formUrl="+formUrl;
        String res = restTemplate.getForObject(url,String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<Long>>() {});
    }
    @ApiOperation("获取供应商罚扣主表id")
    @GetMapping("getVendorPenaltyId")
    public Response getVendorPenaltyId(@RequestParam Long vendorPenaltyRecordId){
        String url = ModelsEnum.CTC.getBaseUrl() + "vendorPenalty/getVendorPenaltyId?vendorPenaltyRecordId="+vendorPenaltyRecordId;
        String res = restTemplate.getForObject(url,String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<Long>>() {});
    }

    private List<FileInfo> getFileByIds(List<Long> ids){
        FileInfoExample fileInfoExample = new FileInfoExample();
        fileInfoExample.createCriteria().andIdIn(ids);
        List<FileInfo> fileInfoList = fileInfoMapper.selectByExample(fileInfoExample);
        Map<Long, UserInfo> userMap = new HashMap<>(fileInfoList.size());
        fileInfoList.forEach(f->{
            UserInfo userInfo = userMap.computeIfAbsent(f.getCreateBy(), CacheDataUtils::findUserById);
            if(userInfo!=null) {
                f.setCreateName(userInfo.getName());
            }
        });
        return fileInfoList;
    }

    @ApiOperation("供应商罚扣提交审批")
    @PostMapping("submit")
    public Response submit(@RequestBody VendorPenaltyDto vendorPenaltyDto){
        String url = ModelsEnum.CTC.getBaseUrl() + "vendorPenalty/submit";
        return restTemplate.postForObject(url,vendorPenaltyDto, DataResponse.class);
    }

    @ApiOperation(value = "供应商罚扣明细导入")
    @PostMapping("importVendorPenaltyDetail")
    public Response importVendorPenaltyDetail(@RequestPart MultipartFile file,
                                              @RequestParam Long ouId){
        List<VendorPenaltyDetailExcelVo> excelVoList;
        try {
            excelVoList = FileUtil.importExcel(file, VendorPenaltyDetailExcelVo.class, 0, 0);
        }catch(BizException e){
            if(e.getMessage()==null){
                throw new BizException(Code.ERROR,"模板解析失败,请检测是否存在空行");
            }
            throw e;
        }
        if(excelVoList.isEmpty()){
            throw new BizException(Code.ERROR,"excel数据解析为空");
        }
        String url = ModelsEnum.CTC.getBaseUrl() + "vendorPenalty/importVendorPenaltyDetail?ouId="+ouId;
        return restTemplate.postForObject(url, excelVoList, DataResponse.class);
    }

    @ApiOperation(value = "生成校验excel文件")
    @PostMapping("generateCheckReport")
    public void generateCheckReport(@RequestBody VendorPenaltyDto vendorPenaltyDto,
                                    HttpServletResponse httpServletResponse){
        Workbook workbook = ExportExcelUtil.buildDefaultSheet(vendorPenaltyDto.getExcelVoList(), VendorPenaltyDetailExcelVo.class, null, "Sheet1", true);
        List<ErrMsgExcelVo> errMsgExcelVos = vendorPenaltyDto.getErrMsgList().stream().map(e -> {
            ErrMsgExcelVo vo = new ErrMsgExcelVo();
            vo.setErrMsg(e);
            return vo;
        }).collect(Collectors.toList());
        ExportExcelUtil.addSheet(workbook, errMsgExcelVos, ErrMsgExcelVo.class, null, "错误信息", true);
        ExportExcelUtil.downLoadExcel("罚扣拆分批量导入校验报告.xls", httpServletResponse, workbook);
    }

    @ApiOperation(value = "审批提交回调")
    @PutMapping("workflow/callback/draftSubmit/skipSecurityInterceptor")
    public Response draftSubmitCallback(@RequestParam(required = false) Long formInstanceId,
                                        @RequestParam(required = false) String fdInstanceId,
                                        @RequestParam(required = false) String formUrl,
                                        @RequestParam(required = false) String eventName,
                                        @RequestParam(required = false) String handlerId,
                                        @RequestParam(required = false) Long companyId,
                                        @RequestParam(required = false) Long createUserId) {
        String url = String.format("%svendorPenalty/workflow/callback/draftSubmit/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s" ,
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        logger.info("请求的url为:{}",url);

        return restTemplate.getForObject(url,DataResponse.class);
    }

    @ApiOperation(value = "审批驳回回调")
    @PutMapping("workflow/callback/refuse/skipSecurityInterceptor")
    public Response refuseCallback(@RequestParam(required = false) Long formInstanceId,
                                   @RequestParam(required = false) String fdInstanceId,
                                   @RequestParam(required = false) String formUrl,
                                   @RequestParam(required = false) String eventName,
                                   @RequestParam(required = false) String handlerId,
                                   @RequestParam(required = false) Long companyId,
                                   @RequestParam(required = false) Long createUserId) {
        String url = String.format("%svendorPenalty/workflow/callback/refuse/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s" ,
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        logger.info("请求的url为:{}",url);
        return restTemplate.getForObject(url,DataResponse.class);
    }

    @ApiOperation(value = "审批通过回调")
    @PutMapping("workflow/callback/pass/skipSecurityInterceptor")
    public Response passCallback(@RequestParam(required = false) Long formInstanceId,
                                 @RequestParam(required = false) String fdInstanceId,
                                 @RequestParam(required = false) String formUrl,
                                 @RequestParam(required = false) String eventName,
                                 @RequestParam(required = false) String handlerId,
                                 @RequestParam(required = false) Long companyId,
                                 @RequestParam(required = false) Long createUserId) {
        String url = String.format("%svendorPenalty/workflow/callback/pass/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s" ,
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        logger.info("请求的url为:{}",url);
        return restTemplate.getForObject(url,DataResponse.class);
    }

    @ApiOperation(value = "撤回审批回调")
    @PutMapping("workflow/callback/draftReturn/skipSecurityInterceptor")
    public Response draftReturnCallback(@RequestParam(required = false) Long formInstanceId,
                                        @RequestParam(required = false) String fdInstanceId,
                                        @RequestParam(required = false) String formUrl,
                                        @RequestParam(required = false) String eventName,
                                        @RequestParam(required = false) String handlerId,
                                        @RequestParam(required = false) Long companyId,
                                        @RequestParam(required = false) Long createUserId) {
        String url = String.format("%svendorPenalty/workflow/callback/draftReturn/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s" ,
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        logger.info("请求的url为:{}",url);
        return restTemplate.getForObject(url,DataResponse.class);
    }

    @ApiOperation(value = "作废审批回调")
    @PutMapping("workflow/callback/abandon/skipSecurityInterceptor")
    public Response abandonCallback(@RequestParam(required = false) Long formInstanceId,
                                    @RequestParam(required = false) String fdInstanceId,
                                    @RequestParam(required = false) String formUrl,
                                    @RequestParam(required = false) String eventName,
                                    @RequestParam(required = false) String handlerId,
                                    @RequestParam(required = false) Long companyId,
                                    @RequestParam(required = false) Long createUserId) {
        String url = String.format("%svendorPenalty/workflow/callback/abandon/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s" ,
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        logger.info("请求的url为:{}",url);
        return restTemplate.getForObject(url,DataResponse.class);
    }


    @ApiOperation(value = "删除审批回调")
    @PutMapping("workflow/callback/delete/skipSecurityInterceptor")
    public Response updateStatusDeleteForMaterialReturn(@RequestParam(required = false) Long formInstanceId,
                                                                                          @RequestParam(required = false) String fdInstanceId,
                                                                                          @RequestParam(required = false) String formUrl,
                                                                                          @RequestParam(required = false) String eventName,
                                                                                          @RequestParam(required = false) String handlerId,
                                                                                          @RequestParam(required = false) Long companyId,
                                                                                          @RequestParam(required = false) Long createUserId) {
        String url = String.format("%svendorPenalty/workflow/callback/delete/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        logger.info("请求的url为:{}",url);
        return restTemplate.getForObject(url,DataResponse.class);
    }

    @ApiOperation(value = "处理人通过审批回调")
    @PutMapping("workflow/callback/agree/skipSecurityInterceptor")
    public Response agree(@RequestParam(required = false) Long formInstanceId,
                                                            @RequestParam(required = false) String fdInstanceId,
                                                            @RequestParam(required = false) String formUrl,
                                                            @RequestParam(required = false) String eventName,
                                                            @RequestParam(required = false) String handlerId,
                                                            @RequestParam(required = false) Long companyId,
                                                            @RequestParam(required = false) Long createUserId) {
        String url = String.format("%svendorPenalty/workflow/callback/agree/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        logger.info("请求的url为:{}",url);
        return restTemplate.getForObject(url,DataResponse.class);
    }



    @ApiOperation(value = "保存变更")
    @PostMapping("saveChange")
    public Response saveChange(@RequestBody VendorPenaltyChangeDto vendorPenaltyChangeDto){
        String url = ModelsEnum.CTC.getBaseUrl() + "vendorPenalty/saveChange";
        return restTemplate.postForObject(url,vendorPenaltyChangeDto, DataResponse.class);
    }

    @ApiOperation(value = "变更详情")
    @GetMapping("changeDetail")
    public Response changeDetail(@RequestParam Long changeId){
        String url = ModelsEnum.CTC.getBaseUrl() + "vendorPenalty/changeDetail?changeId="+changeId;
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<VendorPenaltyChangeDto> response = JSON.parseObject(res, new TypeReference<DataResponse<VendorPenaltyChangeDto>>() {});
        if(response!=null && response.getCode()==0){
            VendorPenaltyChangeDto dto = response.getData();
            if(dto!=null){
                if (StringUtils.isNotEmpty(dto.getAttachmentIds())) {
                    List<Long> ids = Arrays.stream(dto.getAttachmentIds().split(","))
                            .map(Long::valueOf).collect(Collectors.toList());
                    dto.setAttachments(getFileByIds(ids));
                } else {
                    dto.setAttachments(new ArrayList<>(0));
                }
            }
        }
        return response;
    }

    @ApiOperation(value = "作废变更")
    @GetMapping("abandonChange")
    public Response abandonChange(@RequestParam Long changeId){
        String url = ModelsEnum.CTC.getBaseUrl() + "vendorPenalty/abandonChange?changeId="+changeId;
        return restTemplate.getForObject(url,DataResponse.class);
    }

    @ApiOperation(value = "变更审批提交回调")
    @PutMapping("change/workflow/callback/draftSubmit/skipSecurityInterceptor")
    public Response changeDraftSubmitCallback(@RequestParam(required = false) String formInstanceId) {
        String url = ModelsEnum.CTC.getBaseUrl() +
                "vendorPenalty/change/workflow/callback/draftSubmit/skipSecurityInterceptor";
        url += "?formInstanceId=" + (formInstanceId!=null?formInstanceId:"");
        return restTemplate.getForObject(url,DataResponse.class);
    }

    @ApiOperation(value = "变更审批驳回回调")
    @PutMapping("change/workflow/callback/refuse/skipSecurityInterceptor")
    public Response changeRefuseCallback(@RequestParam(required = false) String formInstanceId) {
        String url = ModelsEnum.CTC.getBaseUrl() +
                "vendorPenalty/change/workflow/callback/refuse/skipSecurityInterceptor";
        url += "?formInstanceId=" + (formInstanceId!=null?formInstanceId:"");
        return restTemplate.getForObject(url,DataResponse.class);
    }

    @ApiOperation(value = "变更审批通过回调")
    @PutMapping("change/workflow/callback/pass/skipSecurityInterceptor")
    public Response changePassCallback(@RequestParam(required = false) String formInstanceId) {
        String url = ModelsEnum.CTC.getBaseUrl() +
                "vendorPenalty/change/workflow/callback/pass/skipSecurityInterceptor";
        url += "?formInstanceId=" + (formInstanceId!=null?formInstanceId:"");
        return restTemplate.getForObject(url,DataResponse.class);
    }

    @ApiOperation(value = "变更撤回审批回调")
    @PutMapping("change/workflow/callback/draftReturn/skipSecurityInterceptor")
    public Response changeDraftReturnCallback(@RequestParam(required = false) String formInstanceId) {
        String url = ModelsEnum.CTC.getBaseUrl() +
                "vendorPenalty/change/workflow/callback/draftReturn/skipSecurityInterceptor";
        url += "?formInstanceId=" + (formInstanceId!=null?formInstanceId:"");
        return restTemplate.getForObject(url,DataResponse.class);
    }

    @ApiOperation(value = "变更作废审批回调")
    @PutMapping("change/workflow/callback/abandon/skipSecurityInterceptor")
    public Response changeAbandonCallback(@RequestParam(required = false) String formInstanceId) {
        String url = ModelsEnum.CTC.getBaseUrl() +
                "vendorPenalty/change/workflow/callback/abandon/skipSecurityInterceptor";
        url += "?formInstanceId=" + (formInstanceId!=null?formInstanceId:"");
        return restTemplate.getForObject(url,DataResponse.class);
    }

    private String buildUrl(String url, Map<String,Object> params){
        String queryParams = params.entrySet().stream()
                .filter(e -> e.getValue()!=null)
                .map(e -> e.getKey() + "=" + e.getValue())
                .collect(Collectors.joining("&"));
        if(!queryParams.isEmpty()){
            url += "?" + queryParams;
        }
        return url;
    }
}
