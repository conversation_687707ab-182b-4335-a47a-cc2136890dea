package com.midea.pam.gateway.ctc.web;

import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.service.WorkflowCallbackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

@Api("项目变更审批回调")
@RestController
@RequestMapping("projectMilepostChangeCallBack")
public class ProjectMilepostChangeCallBackController implements WorkflowCallbackService {
    @Resource
    private RestTemplate restTemplate;

    /**
     * 流程审批中回调方法.
     *
     * @param formInstanceId 表单实例id
     */
    @Override
    @ApiOperation(value = "审批中")
    @PutMapping("approvaling/skipSecurityInterceptor")
    public Response approvaling(@RequestParam Long formInstanceId) {
        final String url = String.format("%sprojectMilepostChangeCallBack/approvaling/skipSecurityInterceptor?formInstanceId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId);
        restTemplate.put(url,String.class);
        return Response.dataResponse();
    }

    /**
     * 流程审批完成回调方法。
     *
     * @param formInstanceId 表单实例id
     */
    @ApiOperation(value = "审批通过")
    @PutMapping("approved/skipSecurityInterceptor")
    public Response approved(@RequestParam Long formInstanceId) {
        final String url = String.format("%sprojectMilepostChangeCallBack/approved/skipSecurityInterceptor?formInstanceId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId);
        restTemplate.put(url,String.class);
        return Response.dataResponse();
    }

    /**
     * 流程审批驳回回调方法.
     *
     * @param formInstanceId 表单实例id
     */
    @ApiOperation(value = "驳回")
    @PutMapping("refused/skipSecurityInterceptor")
    public Response refused(@RequestParam Long formInstanceId) {
        final String url = String.format("%sprojectMilepostChangeCallBack/refused/skipSecurityInterceptor?formInstanceId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId);
        restTemplate.put(url,String.class);
        return Response.dataResponse();
    }

    /**
     * 流程审批撤回回调方法.
     *
     * @param formInstanceId 表单实例id
     */
    @ApiOperation(value = "撤回")
    @PutMapping("return/skipSecurityInterceptor")
    public Response returned(@RequestParam Long formInstanceId) {
        final String url = String.format("%sprojectMilepostChangeCallBack/return/skipSecurityInterceptor?formInstanceId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId);
        restTemplate.put(url,String.class);
        return Response.dataResponse();
    }


    @ApiOperation(value = "项目里程碑信息变更流程作废")
    @PutMapping("updateStatusAbandonForProjectMilepost/skipSecurityInterceptor")
    public Response updateStatusAbandonForProjectMilepost(@RequestParam(required = false) Long handlerUserId, @RequestParam(required = false) String formInstanceId) {
        String url = String.format("%sprojectMilepostChangeCallBack/updateStatusAbandonForProjectMilepost/skipSecurityInterceptor?formInstanceId=%s&handlerUserId=%d", ModelsEnum.CTC.getBaseUrl(), formInstanceId, handlerUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "项目里程碑信息变更流程删除")
    @PutMapping("updateStatusDeleteForProjectMilepost/skipSecurityInterceptor")
    public Response updateStatusDeleteForProjectMilepost(@RequestParam(required = false) Long handlerUserId, @RequestParam(required = false) String formInstanceId) {
        String url = String.format("%sprojectMilepostChangeCallBack/updateStatusDeleteForProjectMilepost/skipSecurityInterceptor?formInstanceId=%s&handlerUserId=%d", ModelsEnum.CTC.getBaseUrl(), formInstanceId, handlerUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }


    @ApiOperation(value = "项目收入成本计划变更流程作废")
    @PutMapping("updateStatusAbandonForProfit/skipSecurityInterceptor")
    public Response updateStatusAbandonForProfit(@RequestParam(required = false) Long handlerUserId, @RequestParam(required = false) String formInstanceId) {
        String url = String.format("%sprojectMilepostChangeCallBack/updateStatusAbandonForProfit/skipSecurityInterceptor?formInstanceId=%s&handlerUserId=%d", ModelsEnum.CTC.getBaseUrl(), formInstanceId, handlerUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "项目收入成本计划变更流程删除")
    @PutMapping("updateStatusDeleteForProfit/skipSecurityInterceptor")
    public Response updateStatusDeleteForProfit(@RequestParam(required = false) Long handlerUserId, @RequestParam(required = false) String formInstanceId) {
        String url = String.format("%sprojectMilepostChangeCallBack/updateStatusDeleteForProfit/skipSecurityInterceptor?formInstanceId=%s&handlerUserId=%d", ModelsEnum.CTC.getBaseUrl(), formInstanceId, handlerUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

}
