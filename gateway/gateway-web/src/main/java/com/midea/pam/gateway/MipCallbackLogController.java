package com.midea.pam.gateway;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.gateway.entity.MipCallbackLog;
import com.midea.pam.common.gateway.query.MipCallbackLogQuery;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.service.MipCallbackLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * Description 接口回调日志
 * Created by liuqing85
 * Date 2023/3/31 18:00
 */
@Api("接口回调日志")
@RestController
@RequestMapping("mipCallbackLog")
public class MipCallbackLogController {

    @Resource
    private MipCallbackLogService mipCallbackLogService;

    @ApiOperation("获取 pam_system.mip_callback_log 表中的数据")
    @PostMapping("getList")
    public Response getList(@RequestBody MipCallbackLogQuery query) {
        Guard.notNull(query, "查询参数为空");
        Guard.notNull(query.getPageNum(), "分页当前页参数为空");
        Guard.notNull(query.getPageSize(), "分页每页数量参数为空");

        DataResponse<PageInfo<MipCallbackLog>> dataResponse = Response.dataResponse();
        PageInfo<MipCallbackLog> list = mipCallbackLogService.page(query);
        return dataResponse.setData(list);
    }

    @ApiOperation(value = "审批回调导出")
    @PostMapping("excel/export")
    public void listContracts(@RequestBody MipCallbackLogQuery query, HttpServletResponse response) {
        Guard.notNull(query, "查询参数为空");
        Guard.notNull(query.getPageNum(), "分页当前页参数为空");
        Guard.notNull(query.getPageSize(), "分页每页数量参数为空");

        mipCallbackLogService.exportExcel(query, response);
    }


    @ApiOperation(value = "审批回调日志失败记录自动提醒发送邮件")
    @GetMapping("emailSendMipCallbackLog")
    public void emailSendMipCallbackLog() {
        mipCallbackLogService.emailSendMipCallbackLog();
    }

    @ApiOperation("重试回调接口")
    @PostMapping("retry/{id}")
    public Response retry(@PathVariable Long id) {
        DataResponse<JSONObject> response = Response.dataResponse();
        JSONObject jsonObject = mipCallbackLogService.retry(id);
        return response.setData(jsonObject);
    }

    @ApiOperation("删除回调日志")
    @PostMapping("delete/{id}")
    public Response delete(@PathVariable Long id) {
        mipCallbackLogService.delete(id);
        return Response.dataResponse();
    }
}
