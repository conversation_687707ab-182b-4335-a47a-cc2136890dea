package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.statistics.dto.ProjectIncomingCostDto;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/6/18
 * @description
 */
@Api("门户报表")
@RestController
@RequestMapping("statistics/portalreport")
public class PortalReportController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "统计线索状态(转化率)")
    @GetMapping("leadStatusSummary")
    public Response leadStatusSummary(@RequestParam(required = false) @ApiParam("销售部门id") String unitIds,
                                      @RequestParam(required = false) @ApiParam("年份") Date date ) {
        final Map<String, Object> params = new HashMap<>();
        params.put("unitIds", unitIds);
        params.put("date", date);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/portalreport/leadStatusSummary", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {});

        return response;
    }

    @ApiOperation(value = "统计商机状态(赢单分析)")
    @GetMapping("businessStatusSummary")
    public Response businessStatusSummary(@RequestParam(required = false) @ApiParam("销售部门id") String unitIds,
                                          @RequestParam @ApiParam("开始月份: yyyy-mm") String monthBegin,
                                          @RequestParam @ApiParam("结束月份: yyyy-mm") String monthEnd) {
        final Map<String, Object> params = new HashMap<>();
        params.put("unitIds", unitIds);
        params.put("monthBegin", monthBegin);
        params.put("monthEnd", monthEnd);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/portalreport/businessStatusSummary", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {});

        return response;
    }

    @ApiOperation(value = "事业部项目收入成本统计")
    @GetMapping("departmentProjectCostSummary")
    public Response departmentProjectCostSummary(@RequestParam @ApiParam("开始月份: yyyy-mm") String monthBegin,
                                                 @RequestParam @ApiParam("结束月份: yyyy-mm") String monthEnd) {
        final Map<String, Object> params = new HashMap<>();
        params.put("monthBegin", monthBegin);
        params.put("monthEnd", monthEnd);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/portalreport/departmentProjectCostSummary", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<ProjectIncomingCostDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<ProjectIncomingCostDto>>>() {});

        return response;
    }

    @ApiOperation(value = "产品项目收入成本统计")
    @GetMapping("unitProjectCostSummary")
    public Response unitProjectCostSummary(@RequestParam @ApiParam("开始月份: yyyy-mm") String monthBegin,
                                           @RequestParam @ApiParam("结束月份: yyyy-mm") String monthEnd,
                                           @RequestParam(required = false) @ApiParam("0: 外部, 1: 内部") Integer innerFlag) {
        final Map<String, Object> params = new HashMap<>();
        params.put("monthBegin", monthBegin);
        params.put("monthEnd", monthEnd);
        params.put("innerFlag", innerFlag);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/portalreport/unitProjectCostSummary", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<ProjectIncomingCostDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<ProjectIncomingCostDto>>>() {});

        return response;
    }

}
