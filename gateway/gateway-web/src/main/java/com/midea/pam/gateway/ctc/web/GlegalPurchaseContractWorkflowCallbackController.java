package com.midea.pam.gateway.ctc.web;

import com.midea.pam.common.ctc.dto.GlegalContractFileInfoDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020-10-09
 * @description 法务采购合同流程回调
 */
@Api("法务采购合同流程回调")
@RestController
@RequestMapping("glegalPurchaseContract")
public class GlegalPurchaseContractWorkflowCallbackController {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "驳回")
    @PostMapping("updateStatusReject/skipSecurityInterceptor")
    public Response refuseCallback(@RequestBody GlegalContractFileInfoDto glegalContractFileInfoDto) {
        String url = String.format("%sglegalPurchaseContract/workflow/callback/refuse/skipSecurityInterceptor", ModelsEnum.CTC.getBaseUrl());
        restTemplate.postForEntity(url, glegalContractFileInfoDto, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "审批中")
    @PostMapping("draftSubmit/skipSecurityInterceptor")
    public Response draftSubmitCallback(@RequestBody GlegalContractFileInfoDto glegalContractFileInfoDto) {
        String url = String.format("%sglegalPurchaseContract/workflow/callback/draftSubmit/skipSecurityInterceptor", ModelsEnum.CTC.getBaseUrl());
        restTemplate.postForEntity(url, glegalContractFileInfoDto, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "通过")
    @PostMapping("updateStatusPass/skipSecurityInterceptor")
    public Response passCallback(@RequestBody GlegalContractFileInfoDto glegalContractFileInfoDto) {
        String url = String.format("%sglegalPurchaseContract/workflow/callback/pass/skipSecurityInterceptor", ModelsEnum.CTC.getBaseUrl());
        restTemplate.postForEntity(url, glegalContractFileInfoDto, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "撤回")
    @PostMapping("updateStatusDraftReturn/skipSecurityInterceptor")
    public Response draftReturnCallback(@RequestBody GlegalContractFileInfoDto glegalContractFileInfoDto) {
        String url = String.format("%sglegalPurchaseContract/workflow/callback/draftReturn/skipSecurityInterceptor", ModelsEnum.CTC.getBaseUrl());
        restTemplate.postForEntity(url, glegalContractFileInfoDto, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "作废")
    @PostMapping("updateStatusAbandon/skipSecurityInterceptor")
    public Response abandonCallback(@RequestBody GlegalContractFileInfoDto glegalContractFileInfoDto) {
        String url = String.format("%sglegalPurchaseContract/workflow/callback/abandon/skipSecurityInterceptor", ModelsEnum.CTC.getBaseUrl());
        restTemplate.postForEntity(url, glegalContractFileInfoDto, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "变更审批中")
    @PostMapping("approvaling/skipSecurityInterceptor")
    public com.midea.pam.common.base.Response approvaling(@RequestBody GlegalContractFileInfoDto glegalContractFileInfoDto) {
        final String url = String.format("%sglegalPurchaseContract/workflow/callback/approvaling/skipSecurityInterceptor", ModelsEnum.CTC.getBaseUrl());
        restTemplate.postForEntity(url, glegalContractFileInfoDto, String.class);
        return com.midea.pam.common.base.Response.dataResponse();
    }

    @ApiOperation(value = "变更审批通过")
    @PostMapping("updateStatusChangePass/skipSecurityInterceptor")
    public com.midea.pam.common.base.Response approved(@RequestBody GlegalContractFileInfoDto glegalContractFileInfoDto) {
        final String url = String.format("%sglegalPurchaseContract/workflow/callback/passChange/skipSecurityInterceptor", ModelsEnum.CTC.getBaseUrl());
        restTemplate.postForEntity(url, glegalContractFileInfoDto, String.class);
        return com.midea.pam.common.base.Response.dataResponse();
    }


    @ApiOperation(value = "变更驳回")
    @PostMapping("updateStatusChangeReject/skipSecurityInterceptor")
    public com.midea.pam.common.base.Response refused(@RequestBody GlegalContractFileInfoDto glegalContractFileInfoDto) {
        final String url = String.format("%sglegalPurchaseContract/workflow/callback/refusedChange/skipSecurityInterceptor", ModelsEnum.CTC.getBaseUrl());
        restTemplate.postForEntity(url, glegalContractFileInfoDto, String.class);
        return com.midea.pam.common.base.Response.dataResponse();
    }


    @ApiOperation(value = "变更撤回")
    @PostMapping("updateStatusDraftChangeReturn/skipSecurityInterceptor")
    public com.midea.pam.common.base.Response returned(@RequestBody GlegalContractFileInfoDto glegalContractFileInfoDto) {
        final String url = String.format("%sglegalPurchaseContract/workflow/callback/returnChange/skipSecurityInterceptor", ModelsEnum.CTC.getBaseUrl());
        restTemplate.postForEntity(url, glegalContractFileInfoDto, String.class);
        return com.midea.pam.common.base.Response.dataResponse();
    }


    @ApiOperation(value = "变更作废")
    @PostMapping("updateStatusChangeAbandon/skipSecurityInterceptor")
    public com.midea.pam.common.base.Response abandonChange(@RequestBody GlegalContractFileInfoDto glegalContractFileInfoDto) {
        final String url = String.format("%sglegalPurchaseContract/workflow/callback/abandonChange/skipSecurityInterceptor", ModelsEnum.CTC.getBaseUrl());
        restTemplate.postForEntity(url, glegalContractFileInfoDto, String.class);
        return com.midea.pam.common.base.Response.dataResponse();
    }

    @ApiOperation(value = "服务内容变更审批中")
    @PostMapping("approvalingProductChanage/skipSecurityInterceptor")
    public com.midea.pam.common.base.Response approvalingProductChanage(@RequestBody GlegalContractFileInfoDto glegalContractFileInfoDto) {
        final String url = String.format("%sglegalPurchaseContract/workflow/callback/approvalingProductChanage/skipSecurityInterceptor", ModelsEnum.CTC.getBaseUrl());
        restTemplate.postForEntity(url, glegalContractFileInfoDto, String.class);
        return com.midea.pam.common.base.Response.dataResponse();
    }

    @ApiOperation(value = "服务内容变更审批通过")
    @PostMapping("approvedProductChanage/skipSecurityInterceptor")
    public com.midea.pam.common.base.Response approvedProductChanage(@RequestBody GlegalContractFileInfoDto glegalContractFileInfoDto) {
        final String url = String.format("%sglegalPurchaseContract/workflow/callback/approvedProductChanage/skipSecurityInterceptor", ModelsEnum.CTC.getBaseUrl());
        restTemplate.postForEntity(url, glegalContractFileInfoDto, String.class);
        return com.midea.pam.common.base.Response.dataResponse();
    }

    @ApiOperation(value = "服务内容变更驳回")
    @PostMapping("refusedProductChanage/skipSecurityInterceptor")
    public com.midea.pam.common.base.Response refusedProductChanage(@RequestBody GlegalContractFileInfoDto glegalContractFileInfoDto) {
        final String url = String.format("%sglegalPurchaseContract/workflow/callback/refusedProductChanage/skipSecurityInterceptor", ModelsEnum.CTC.getBaseUrl());
        restTemplate.postForEntity(url, glegalContractFileInfoDto, String.class);
        return com.midea.pam.common.base.Response.dataResponse();
    }

    @ApiOperation(value = "服务内容变更撤回")
    @PostMapping("returnProductChanage/skipSecurityInterceptor")
    public com.midea.pam.common.base.Response returnProductChanage(@RequestBody GlegalContractFileInfoDto glegalContractFileInfoDto) {
        final String url = String.format("%sglegalPurchaseContract/workflow/callback/returnProductChanage/skipSecurityInterceptor", ModelsEnum.CTC.getBaseUrl());
        restTemplate.postForEntity(url, glegalContractFileInfoDto, String.class);
        return com.midea.pam.common.base.Response.dataResponse();
    }

    @ApiOperation(value = "服务内容变更作废")
    @PostMapping("abandonProductChanage/skipSecurityInterceptor")
    public com.midea.pam.common.base.Response abandonProductChanage(@RequestBody GlegalContractFileInfoDto glegalContractFileInfoDto) {
        final String url = String.format("%sglegalPurchaseContract/workflow/callback/abandonProductChanage/skipSecurityInterceptor", ModelsEnum.CTC.getBaseUrl());
        restTemplate.postForEntity(url, glegalContractFileInfoDto, String.class);
        return com.midea.pam.common.base.Response.dataResponse();
    }

}
