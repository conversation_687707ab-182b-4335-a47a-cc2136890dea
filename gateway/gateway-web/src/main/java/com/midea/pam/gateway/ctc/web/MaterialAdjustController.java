package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.midea.pam.common.ctc.dto.BatchUpdateStatusPassDto;
import com.midea.pam.common.ctc.dto.MaterialAdjustDetailDTO;
import com.midea.pam.common.ctc.dto.MaterialAdjustHeaderDTO;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.excelVo.MaterialAdjustDetailExcelVo;
import com.midea.pam.common.ctc.excelVo.MaterialAdjustDetailImportExcelVo;
import com.midea.pam.common.ctc.excelVo.MaterialAdjustDetailValidResultExcelVo;
import com.midea.pam.common.ctc.excelVo.MaterialAdjustDetailWithKuka2022ValidResultExcelVo;
import com.midea.pam.common.ctc.excelVo.MaterialAdjustDraftEditDetailExcelVo;
import com.midea.pam.common.ctc.excelVo.MaterialAdjustHeaderExcelVo;
import com.midea.pam.common.ctc.excelVo.MilepostDesignPlanDetailKukaWbsBatchImportExcelVo;
import com.midea.pam.common.ctc.excelVo.MilepostDesignPlanDetailKukaWbsImportExcelVo;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.FormInstanceExample;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.common.utils.ExportExcelUtil;
import com.midea.pam.gateway.common.utils.FileUtil;
import com.midea.pam.gateway.mapper.FormInstanceMapper;
import com.midea.pam.gateway.service.FormInstanceService;
import com.midea.pam.gateway.service.MipWorkflowInnerService;
import com.midea.pam.gateway.service.OssService;
import com.midea.pam.support.utils.BeanConverter;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/2/25
 * @desc 详细设计-物料新增和变更
 */
@Api("物料新增和变更")
@RestController
@RequestMapping("/material/adjust")
public class MaterialAdjustController extends ControllerHelper {

    private static final Logger logger = LoggerFactory.getLogger(MaterialAdjustController.class);

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private OssService ossService;
    @Resource
    private MipWorkflowInnerService mipWorkflowInnerService;
    @Resource
    private FormInstanceMapper formInstanceMapper;
    @Resource
    private FormInstanceService formInstanceService;



    /**
     * 列表分页查询接口
     *
     * @param pageNum           页码
     * @param pageSize          每页数量
     * @param adjustCode        单据号
     * @param adjustTypeStr     变更类型（下拉多选）：新增物料 0，物料属性变更 1，物料类型变更 2
     * @param statusStr         单据流程状态：草稿（撤回，驳回都是） 0，审批中 1，作废 2，审批通过 3
     * @param organizationIdStr 库存组织ID（下拉多选）
     * @return
     */
    @ApiOperation(value = "列表分页查询", response = MaterialAdjustHeaderDTO.class)
    @GetMapping("/page")
    public Response page(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                         @RequestParam(required = false) @ApiParam("单据号") String adjustCode,
                         @RequestParam(required = false) @ApiParam("变更类型（下拉多选）：新增物料 0，物料属性变更 1，物料类型变更 2") String adjustTypeStr,
                         @RequestParam(required = false) @ApiParam("申请人姓名") String applyByName,
                         @RequestParam(required = false) @ApiParam("处理人姓名") String handleByName,
                         @RequestParam(required = false) @ApiParam("单据流程状态：草稿（撤回，驳回都是） 0，审批中 1，作废 2，审批通过 3") String statusStr,
                         @RequestParam(required = false) @ApiParam("库存组织ID（下拉多选）") String organizationIdStr,
                         @RequestParam(required = false) @ApiParam("开始时间（日期控件）") String createAtBegin,
                         @RequestParam(required = false) @ApiParam("结束时间（日期控件）") String createAtEnd,
                         @RequestParam(required = false) @ApiParam("数据来源（下拉多选）：手工 0，详细设计 1") String resourceStr,
                         @RequestParam(required = false) @ApiParam("同步状态（下拉多选）: 0-待同步, 1-同步成功, 2-同步异常, 3-同步中") String syncStatusStr) {
        HashMap<String, Object> param = Maps.newHashMap();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("adjustCode", adjustCode);
        param.put("applyByName", applyByName);
        param.put("handleByName", handleByName);
        param.put("adjustTypeStr", adjustTypeStr);
        param.put("statusStr", statusStr);
        param.put("organizationIdStr", organizationIdStr);
        param.put("createAtBegin", createAtBegin);
        param.put("createAtEnd", createAtEnd);
        param.put("resourceStr", resourceStr);
        param.put("syncStatusStr", syncStatusStr);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/material/adjust/page", param);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<MaterialAdjustHeaderDTO>>>() {
        });
    }

    /**
     * 我的物料新增和变更分页查询接口
     *
     * @param pageNum           页码
     * @param pageSize          每页数量
     * @param adjustCode        单据号
     * @param adjustTypeStr     变更类型（下拉多选）：新增物料 0，物料属性变更 1，物料类型变更 2
     * @param statusStr         单据流程状态：草稿（撤回，驳回都是） 0，审批中 1，作废 2，审批通过 3
     * @param organizationIdStr 库存组织ID（下拉多选）
     * @return
     */
    @ApiOperation(value = "我的物料新增和变更列表分页查询", response = MaterialAdjustHeaderDTO.class)
    @GetMapping("/byMePage")
    public Response byMePage(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                         @RequestParam(required = false) @ApiParam("单据号") String adjustCode,
                         @RequestParam(required = false) @ApiParam("变更类型（下拉多选）：新增物料 0，物料属性变更 1，物料类型变更 2") String adjustTypeStr,
                         @RequestParam(required = false) @ApiParam("申请人姓名") String applyByName,
                         @RequestParam(required = false) @ApiParam("处理人姓名") String handleByName,
                         @RequestParam(required = false) @ApiParam("单据流程状态：草稿（撤回，驳回都是） 0，审批中 1，作废 2，审批通过 3") String statusStr,
                         @RequestParam(required = false) @ApiParam("库存组织ID（下拉多选）") String organizationIdStr,
                         @RequestParam(required = false) @ApiParam("开始时间（日期控件）") String createAtBegin,
                         @RequestParam(required = false) @ApiParam("结束时间（日期控件）") String createAtEnd,
                         @RequestParam(required = false) @ApiParam("数据来源（下拉多选）：手工 0，详细设计 1") String resourceStr,
                         @RequestParam(required = false) @ApiParam("同步状态（下拉多选）: 0-待同步, 1-同步成功, 2-同步异常, 3-同步中") String syncStatusStr) {
        HashMap<String, Object> param = Maps.newHashMap();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("adjustCode", adjustCode);
        param.put("applyByName", applyByName);
        param.put("handleByName", handleByName);
        param.put("adjustTypeStr", adjustTypeStr);
        param.put("statusStr", statusStr);
        param.put("organizationIdStr", organizationIdStr);
        param.put("createAtBegin", createAtBegin);
        param.put("createAtEnd", createAtEnd);
        param.put("resourceStr", resourceStr);
        param.put("syncStatusStr", syncStatusStr);
        param.put("me",Boolean.TRUE);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/material/adjust/page", param);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<MaterialAdjustHeaderDTO>>>() {
        });
    }

    /**
     * 我的物料新增和变更列表数据导出
     *
     * @param adjustCode        单据号
     * @param adjustTypeStr     变更类型（下拉多选）：新增物料 0，物料属性变更 1，物料类型变更 2
     * @param statusStr         单据流程状态：草稿（撤回，驳回都是） 0，审批中 1，作废 2，审批通过 3
     * @param organizationIdStr 库存组织ID（下拉多选）
     * @return
     */
    @ApiOperation(value = "列表分页查询", response = MaterialAdjustHeaderDTO.class)
    @GetMapping("/byMeExport")
    public void byMeExport(HttpServletResponse response,
                       @RequestParam(required = false) @ApiParam("单据号") String adjustCode,
                       @RequestParam(required = false) @ApiParam("变更类型：新增物料 0，物料属性变更 1，物料类型变更 2") String adjustTypeStr,
                       @RequestParam(required = false) @ApiParam("申请人姓名") String applyByName,
                       @RequestParam(required = false) @ApiParam("单据流程状态：草稿（撤回，驳回都是） 0，审批中 1，作废 2，审批通过 3") String statusStr,
                       @RequestParam(required = false) @ApiParam("库存组织ID") String organizationIdStr,
                       @RequestParam(required = false) @ApiParam("开始时间（日期控件）") String createAtBegin,
                       @RequestParam(required = false) @ApiParam("结束时间（日期控件）") String createAtEnd,
                       @RequestParam(required = false) @ApiParam("数据来源（下拉多选）：手工 0，详细设计 1") String resourceStr,
                       @RequestParam(required = false) @ApiParam("同步状态（下拉多选）: 0-待同步, 1-同步成功, 2-同步异常, 3-同步中") String syncStatusStr) {
        HashMap<String, Object> param = Maps.newHashMap();
        param.put("pageNum", 1);
        param.put("pageSize", Integer.MAX_VALUE);
        param.put("adjustCode", adjustCode);
        param.put("applyByName", applyByName);
        param.put("adjustTypeStr", adjustTypeStr);
        param.put("statusStr", statusStr);
        param.put("organizationIdStr", organizationIdStr);
        param.put("createAtBegin", createAtBegin);
        param.put("createAtEnd", createAtEnd);
        param.put("resourceStr", resourceStr);
        param.put("syncStatusStr", syncStatusStr);
        param.put("me",Boolean.TRUE);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/material/adjust/page", param);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<PageInfo<MaterialAdjustHeaderDTO>> dataResponse = JSON.parseObject(res,
                new TypeReference<DataResponse<PageInfo<MaterialAdjustHeaderDTO>>>() {
                });
        List<MaterialAdjustHeaderDTO> list = dataResponse.getData().getList();
        List<MaterialAdjustHeaderExcelVo> excelList = BeanConverter.copy(list, MaterialAdjustHeaderExcelVo.class);
        for (int i = 0; i < excelList.size(); i++) {
            excelList.get(i).setIndex(i + 1);
        }
        ExportExcelUtil.exportExcel(excelList, null, "物料新增和变更", MaterialAdjustHeaderExcelVo.class, "物料新增和变更.xls",
                response);
    }

    /**
     * 列表数据导出
     *
     * @param adjustCode        单据号
     * @param adjustTypeStr     变更类型（下拉多选）：新增物料 0，物料属性变更 1，物料类型变更 2
     * @param statusStr         单据流程状态：草稿（撤回，驳回都是） 0，审批中 1，作废 2，审批通过 3
     * @param organizationIdStr 库存组织ID（下拉多选）
     * @return
     */
    @ApiOperation(value = "列表分页查询", response = MaterialAdjustHeaderDTO.class)
    @GetMapping("/export")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false) @ApiParam("单据号") String adjustCode,
                       @RequestParam(required = false) @ApiParam("变更类型：新增物料 0，物料属性变更 1，物料类型变更 2") String adjustTypeStr,
                       @RequestParam(required = false) @ApiParam("申请人姓名") String applyByName,
                       @RequestParam(required = false) @ApiParam("单据流程状态：草稿（撤回，驳回都是） 0，审批中 1，作废 2，审批通过 3") String statusStr,
                       @RequestParam(required = false) @ApiParam("库存组织ID") String organizationIdStr,
                       @RequestParam(required = false) @ApiParam("开始时间（日期控件）") String createAtBegin,
                       @RequestParam(required = false) @ApiParam("结束时间（日期控件）") String createAtEnd,
                       @RequestParam(required = false) @ApiParam("数据来源（下拉多选）：手工 0，详细设计 1") String resourceStr,
                       @RequestParam(required = false) @ApiParam("同步状态（下拉多选）: 0-待同步, 1-同步成功, 2-同步异常, 3-同步中") String syncStatusStr) {
        HashMap<String, Object> param = Maps.newHashMap();
        param.put("pageNum", 1);
        param.put("pageSize", Integer.MAX_VALUE);
        param.put("adjustCode", adjustCode);
        param.put("applyByName", applyByName);
        param.put("adjustTypeStr", adjustTypeStr);
        param.put("statusStr", statusStr);
        param.put("organizationIdStr", organizationIdStr);
        param.put("createAtBegin", createAtBegin);
        param.put("createAtEnd", createAtEnd);
        param.put("resourceStr", resourceStr);
        param.put("syncStatusStr", syncStatusStr);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/material/adjust/page", param);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<PageInfo<MaterialAdjustHeaderDTO>> dataResponse = JSON.parseObject(res,
                new TypeReference<DataResponse<PageInfo<MaterialAdjustHeaderDTO>>>() {
                });
        List<MaterialAdjustHeaderDTO> list = dataResponse.getData().getList();
        List<MaterialAdjustHeaderExcelVo> excelList = BeanConverter.copy(list, MaterialAdjustHeaderExcelVo.class);
        for (int i = 0; i < excelList.size(); i++) {
            excelList.get(i).setIndex(i + 1);
        }
        ExportExcelUtil.exportExcel(excelList, null, "物料新增和变更", MaterialAdjustHeaderExcelVo.class, "物料新增和变更.xls",
                response);
    }

    /**
     * 物料新增和变更详情
     *
     * @param id 数据ID
     * @return 详情数据
     */
    @ApiOperation(value = "物料新增和变更详情", response = MaterialAdjustHeaderDTO.class)
    @GetMapping("/getDetailById")
    public Response getDetailById(@RequestParam @ApiParam("ID") Long id) {
        String url = ModelsEnum.CTC.getBaseUrl() + "/material/adjust/getDetailById?id=" + id;
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<MaterialAdjustHeaderDTO>>() {
        });
    }

    /**
     * 物料新增和变更详情列表导出
     *
     * @param id 数据ID
     * @return 详情数据
     */
    @ApiOperation(value = "物料新增和变更详情列表导出", response = MaterialAdjustHeaderDTO.class)
    @GetMapping("/exportDetail")
    public void exportDetail(HttpServletResponse response, @RequestParam @ApiParam("ID") Long id) {
        String url = ModelsEnum.CTC.getBaseUrl() + "/material/adjust/getDetailById?id=" + id;
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<MaterialAdjustHeaderDTO> dataResponse = JSON.parseObject(res,
                new TypeReference<DataResponse<MaterialAdjustHeaderDTO>>() {
                });
        List<MaterialAdjustDetailDTO> detailDTOList = dataResponse.getData().getDetailDTOList();
        List<MaterialAdjustDetailExcelVo> excelList = BeanConverter.copy(detailDTOList, MaterialAdjustDetailExcelVo.class);
        for (int i = 0; i < excelList.size(); i++) {
            MaterialAdjustDetailExcelVo excelVo = excelList.get(i);
            excelVo.setIndex(i + 1);
            if (excelVo.getUnitWeight() != null) {
                excelVo.setUnitWeight(excelVo.getUnitWeight().stripTrailingZeros());
            }
        }
        ExportExcelUtil.exportExcel(excelList, null, "物料新增和变更-物料新增", MaterialAdjustDetailExcelVo.class, "物料新增和变更详情" +
                ".xls", response);
    }

    /**
     * 物料新增和变更草稿详情重新编辑列表导出
     *
     * @param id 数据ID
     * @return 详情数据
     */
    @ApiOperation(value = "物料新增和变更草稿详情重新编辑列表导出", response = MaterialAdjustHeaderDTO.class)
    @GetMapping("/exportDraftEditDetail")
    public void exportDraftEditDetail(HttpServletResponse response, @RequestParam @ApiParam("ID") Long id) {
        Guard.notNull(id, "导出的ID为空");
        String url = ModelsEnum.CTC.getBaseUrl() + "/material/adjust/getDetailById?id=" + id;
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<MaterialAdjustHeaderDTO> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<MaterialAdjustHeaderDTO>>() {
        });
        List<MaterialAdjustDetailDTO> detailDTOList = dataResponse.getData().getDetailDTOList();
        List<MaterialAdjustDraftEditDetailExcelVo> excelList = BeanConverter.copy(detailDTOList, MaterialAdjustDraftEditDetailExcelVo.class);
        for (int i = 0; i < excelList.size(); i++) {
            MaterialAdjustDraftEditDetailExcelVo excelVo = excelList.get(i);
            excelVo.setIndex(i + 1);
            if (excelVo.getUnitWeight() != null) {
                excelVo.setUnitWeight(excelVo.getUnitWeight().stripTrailingZeros());
            }
        }
        ExportExcelUtil.exportExcel(excelList, null, "物料新增和变更-物料新增", MaterialAdjustDraftEditDetailExcelVo.class,
                "物料新增和变更详情.xls", response);
    }

    /**
     * 物料新增和变更保存
     *
     * @param dto 数据
     * @return 详情数据
     */
    @ApiOperation(value = "物料新增和变更保存", response = MaterialAdjustHeaderDTO.class)
    @PostMapping("/saveWithDetail")
    public Response saveWithDetail(@RequestBody MaterialAdjustHeaderDTO dto) {
        String url = ModelsEnum.CTC.getBaseUrl() + "/material/adjust/saveWithDetail";
        String res = restTemplate.postForObject(url, dto, String.class);
        DataResponse<MaterialAdjustHeaderDTO> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<MaterialAdjustHeaderDTO>>() {
        });
        MaterialAdjustHeaderDTO data = null;
        if (dataResponse != null) {
            data = dataResponse.getData();
        }
        if(Objects.nonNull(data) && data.getChangeHandleFlag()){
            logger.info("物料新增和变更保存-处理修改处理人后将处理人的流程实例作废,提交参数:{}",JSON.toJSONString(dto));
           //处理修改处理人后将处理人的流程实例作废
            Long formInstanceId = data.getId();
            String formUrl = "materialAdjustAddApp";
            Long companyId = SystemContext.getUnitId();
            String oldHandleUserName = data.getOldHandleUserName();
            //删除流程实例
            boolean result = formInstanceService.logicDeleteAndMipWorkflowDraftAbandon(formInstanceId, formUrl, oldHandleUserName, companyId);
            logger.info("删除流程实例结果:{}",result);
        }
        return dataResponse;
    }

    /**
     * 物料新增导入数据校验
     * 说明：当msg为success时数据校验通过；当msg为fail时数据校验失败，需要导出下载结果
     *
     * @param file           导入的Excel数据
     * @param organizationId 导入的库存组织ID
     * @return 校验结果
     */
    @ApiOperation(value = "物料新增-导入")
    @PostMapping("importMaterialAdjustDetail")
    public Response importMaterialAdjustDetail(@RequestParam(value = "file") MultipartFile file,
                                               @RequestParam Long organizationId) {
        List<MaterialAdjustDetailImportExcelVo> detailImportExcelVoList;
        try {
            detailImportExcelVoList = FileUtil.importExcel(file, MaterialAdjustDetailImportExcelVo.class, 1, 0);
        } catch (Exception e) {
            try {
                detailImportExcelVoList = FileUtil.importExcel(file, MaterialAdjustDetailImportExcelVo.class, 0, 0);
            } catch (Exception exception) {
                logger.error("物料新增-导入，上传失败:", e);
                throw new BizException(100, "导入文件有误或数据类型未按要求填写");
            }
        }

        // 移除空行的数据
        Iterator<MaterialAdjustDetailImportExcelVo> iterator = detailImportExcelVoList.iterator();
        while (iterator.hasNext()) {
            MaterialAdjustDetailImportExcelVo next = iterator.next();
            if (StringUtils.isBlank(next.getMaterialType())
                    && StringUtils.isBlank(next.getMaterialClass())
                    && StringUtils.isBlank(next.getMaterialMiddleClass())
                    && StringUtils.isBlank(next.getMaterialSmallClass())
                    && StringUtils.isBlank(next.getBrand())
                    && StringUtils.isBlank(next.getName())
                    && StringUtils.isBlank(next.getModel())
                    && StringUtils.isBlank(next.getUnit())
                    && StringUtils.isBlank(next.getFigureNumber())
                    && StringUtils.isBlank(next.getChartVersion())
                    && StringUtils.isBlank(next.getMachiningPartType())
                    && StringUtils.isBlank(next.getMaterial())
                    && next.getUnitWeight() == null
                    && StringUtils.isBlank(next.getSurfaceHandle())
                    && StringUtils.isBlank(next.getBrandMaterialCode())
                    && StringUtils.isBlank(next.getOrSparePartsMask())
                    && next.getPerchasingLeadtime() == null
                    && next.getMinPerchaseQuantity() == null
                    && next.getMinPackageQuantity() == null) {
                iterator.remove();
            }
        }
        Asserts.notEmpty(detailImportExcelVoList, ErrorCode.SYSTEM_FILE_EMPTY);

        final String url = String.format("%smaterial/adjust/validImportDetail?organizationId=%d",
                ModelsEnum.CTC.getBaseUrl(), organizationId);
        String res = restTemplate.postForEntity(url, detailImportExcelVoList, String.class).getBody();
        DataResponse<List<MaterialAdjustDetailDTO>> response = JSON.parseObject(res,
                new TypeReference<DataResponse<List<MaterialAdjustDetailDTO>>>() {
                });
        return response;
    }


    /**
     * 物料新增导出明细校验结果
     *
     * @param detailDTOList 导入的Excel数据
     * @return 校验结果
     */
    @ApiOperation(value = "物料新增-导出检查结果")
    @PostMapping("exportDetailValidResult")
    public void exportDetailValidResult(HttpServletResponse response,
                                        @RequestBody List<MaterialAdjustDetailDTO> detailDTOList) {
        List<MaterialAdjustDetailValidResultExcelVo> excelList = BeanConverter.copy(detailDTOList,
                MaterialAdjustDetailValidResultExcelVo.class);
        ExportExcelUtil.exportExcel(excelList, null, "物料新增和变更-物料新增检查结果", MaterialAdjustDetailValidResultExcelVo.class
                , "物料新增和变更-新增检查结果.xls", response);
    }

    /**
     * 物料新增导出明细校验结果
     *
     * @param detailDTOList 导入的Excel数据
     * @return 校验结果
     */
    @ApiOperation(value = "物料新增-导出检查结果")
    @PostMapping("exportDetailValidResultWithKuka2022")
    public void exportDetailValidResultWithKuka2022(HttpServletResponse response,
                                                    @RequestBody List<MaterialAdjustDetailDTO> detailDTOList) {
        List<MaterialAdjustDetailWithKuka2022ValidResultExcelVo> excelList = BeanConverter.copy(detailDTOList,
                MaterialAdjustDetailWithKuka2022ValidResultExcelVo.class);
        ExportExcelUtil.exportExcel(excelList, null, "物料新增和变更-物料新增检查结果",
                MaterialAdjustDetailWithKuka2022ValidResultExcelVo.class
                , "物料新增和变更-新增检查结果.xls", response);
    }

    /**
     * 物料新增和变更作废（将草稿状态改为废弃状态）
     * 只能删除草稿状态的数据
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "物料新增和变更作废（将草稿状态改为废弃状态）")
    @GetMapping("deleteDraft")
    public Response deleteDraft(@RequestParam @ApiParam("ID") Long id) {
        String url = ModelsEnum.CTC.getBaseUrl() + "/material/adjust/deleteDraft?id=" + id;
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<Integer> response = JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
        //同时作废工作流
        if (response.getCode() == 0) {
            mipWorkflowInnerService.draftAbandon("materialAdjustAddApp", id);
        }
        return response;
    }

    @ApiOperation(value = "物料新增和变更撤回", notes = "场景：物料编码规则的参数值=KUKA2022")
    @PutMapping("return/{id}")
    public DataResponse draftReturn(@ApiParam("ID") @PathVariable Long id) {
        String url = String.format("%smaterial/adjust/return/" + id, ModelsEnum.CTC.getBaseUrl());
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation(value = "物料新增和变更退回", notes = "场景：物料编码规则的参数值=KUKA2022")
    @PutMapping("reject/{id}")
    public DataResponse reject(@ApiParam("ID") @PathVariable Long id) {
        String url = String.format("%smaterial/adjust/reject/" + id, ModelsEnum.CTC.getBaseUrl());
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation(value = "发起审批")
    @PutMapping("updateStatusChecking/skipSecurityInterceptor")
    public Response updateStatusChecking(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                         @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                         @RequestParam(required = false) String handlerId,
                                         @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String fileId = "";
        String fileName = "";
        String fileSize = "";
        try {
            MultipartFile multipartFile = createAnnex(formInstanceId);
            if (multipartFile != null) {
                JSONArray result = ossService.upload(multipartFile);
                if (!ObjectUtils.isEmpty(result)) {
                    JSONObject jsonObject = result.getJSONObject(0);
                    fileId = jsonObject.getString("fileId");
                    fileName = jsonObject.getString("fileName");
                    fileSize = jsonObject.getString("fileSize");
                }
            }
        } catch (Exception e) {
            logger.info("物料新增物料明细附件上传失败", e);
        }

        String url = String.format("%smaterial/adjust/updateStatusChecking/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s" +
                        "&fileId=%s" +
                        "&fileName=%s" +
                        "&fileSize=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId, fileId, fileName
                , fileSize);
        logger.info("物料新增提交审批updateStatusChecking的url:{}", url);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    private MultipartFile createAnnex(Long id) {
        String url = ModelsEnum.CTC.getBaseUrl() + "/material/adjust/getDetailById?id=" + id;
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<MaterialAdjustHeaderDTO> dataResponse = JSON.parseObject(res,
                new TypeReference<DataResponse<MaterialAdjustHeaderDTO>>() {
                });
        if (dataResponse == null) {
            throw new BizException(Code.ERROR, "物料详情数据查询失败");
        }
        MaterialAdjustHeaderDTO headerDTO = dataResponse.getData();
        List<MaterialAdjustDetailExcelVo> excelList = BeanConverter.copy(headerDTO.getDetailDTOList(), MaterialAdjustDetailExcelVo.class);
        for (int i = 0; i < excelList.size(); i++) {
            MaterialAdjustDetailExcelVo excelVo = excelList.get(i);
            excelVo.setIndex(i + 1);
            if (excelVo.getUnitWeight() != null) {
                excelVo.setUnitWeight(excelVo.getUnitWeight().stripTrailingZeros());
            }
        }

        Workbook workbook = com.midea.pam.common.util.ExportExcelUtil.buildDefaultSheet(excelList, MaterialAdjustDetailExcelVo.class, null,
                "物料新增和变更-物料新增", true);

        // 附件名称为：{单据号}物料新增和变更详情_{yyyyMMdd HH:mm:ss}
        MultipartFile multipartFile = null;
        String filePath = headerDTO.getAdjustCode() + "物料新增和变更详情_" + DateUtils.format(new Date(), "yyyyMMdd HH:mm:ss") + ".xls";
        File pdfFile = new File("/apps/pam/gateway/file/" + filePath);

        try (
                OutputStream out = Files.newOutputStream(Paths.get("/apps/pam/gateway/file/" + filePath));
                FileInputStream fileInputStream = new FileInputStream(pdfFile);
        ) {
            workbook.write(out);
            multipartFile = new MockMultipartFile(pdfFile.getName(), pdfFile.getName(), ContentType.APPLICATION_OCTET_STREAM.toString(),
                    fileInputStream);
        } catch (IOException e) {
            logger.info("物料新增物料明细附件生成失败:", e);
        }

        return multipartFile;
    }

    @ApiOperation(value = "驳回")
    @PutMapping("updateStatusReject/skipSecurityInterceptor")
    public Response updateStatusReject(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                       @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                       @RequestParam(required = false) String handlerId,
                                       @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterial/adjust/updateStatusReject/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        logger.info("物料新增审批驳回updateStatusReject的url:{}", url);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "通过")
    @PutMapping("updateStatusPass/skipSecurityInterceptor")
    public Response updateStatusPass(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                     @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                     @RequestParam(required = false) String handlerId,
                                     @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterial/adjust/updateStatusPass/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        logger.info("物料新增审批通过updateStatusPass的url:{}", url);
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(600 * 10000);
        httpRequestFactory.setConnectTimeout(600 * 10000);
        httpRequestFactory.setReadTimeout(600 * 10000);
        restTemplate.setRequestFactory(httpRequestFactory);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "撤回")
    @PutMapping("updateStatusReturn/skipSecurityInterceptor")
    public Response updateStatusReturn(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                       @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                       @RequestParam(required = false) String handlerId,
                                       @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterial/adjust/updateStatusReturn/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        logger.info("物料新增审批撤回updateStatusReturn的url:{}", url);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "作废")
    @PutMapping("abandon/skipSecurityInterceptor")
    public Response abandon(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                            @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                            @RequestParam(required = false) String handlerId,
                            @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterial/adjust/abandon/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        logger.info("物料新增审批作废abandon的url:{}", url);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "删除")
    @PutMapping("delete/skipSecurityInterceptor")
    public Response delete(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                           @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                           @RequestParam(required = false) String handlerId,
                           @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterial/adjust/delete/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        logger.info("物料新增审批删除delete的url:{}", url);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "处理人通过")
    @PutMapping("agree/skipSecurityInterceptor")
    public Response agree(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                          @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                          @RequestParam(required = false) String handlerId,
                          @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterial/adjust/agree/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "物料新增-导入(20210906迭代)")
    @PostMapping("importMaterialAdjustDetailNew")
    public Response importMaterialAdjustDetailNew(@RequestParam(value = "file") MultipartFile file,
                                                  @RequestParam Long organizationId,
                                                  @RequestParam(required = false) Long adjustHeaderId) {
        List<MaterialAdjustDetailImportExcelVo> detailImportExcelVoList;
        try {
            detailImportExcelVoList = FileUtil.importExcel(file, MaterialAdjustDetailImportExcelVo.class, 1, 0);
        } catch (BizException e) {
            logger.error("物料新增-导入，上传失败:", e);
            throw new BizException(100, e.getCode().getCode() == ErrorCode.NULL_POINTER.getCode() ?
                    "导入文件存在空行，请删除再重新导入" : "导入文件有误或数据类型未按要求填写");
        }

        // 移除空行的数据
        Iterator<MaterialAdjustDetailImportExcelVo> iterator = detailImportExcelVoList.iterator();
        while (iterator.hasNext()) {
            MaterialAdjustDetailImportExcelVo next = iterator.next();
            if (StringUtils.isBlank(next.getMaterialType())
                    && StringUtils.isBlank(next.getMaterialMiddleClass())
                    && StringUtils.isBlank(next.getMaterialSmallClass())
                    && StringUtils.isBlank(next.getBrand())
                    && StringUtils.isBlank(next.getName())
                    && StringUtils.isBlank(next.getModel())
                    && StringUtils.isBlank(next.getUnit())
                    && StringUtils.isBlank(next.getFigureNumber())
                    && StringUtils.isBlank(next.getChartVersion())
                    && StringUtils.isBlank(next.getMachiningPartType())
                    && StringUtils.isBlank(next.getMaterial())
                    && next.getUnitWeight() == null
                    && StringUtils.isBlank(next.getSurfaceHandle())
                    && StringUtils.isBlank(next.getBrandMaterialCode())
                    && StringUtils.isBlank(next.getOrSparePartsMask())
                    && next.getPerchasingLeadtime() == null
                    && next.getMinPerchaseQuantity() == null
                    && next.getMinPackageQuantity() == null) {
                iterator.remove();
            }
        }
        Asserts.notEmpty(detailImportExcelVoList, ErrorCode.SYSTEM_FILE_EMPTY);

        Map<String, Object> param = new HashMap<>(2);
        param.put("organizationId", organizationId);
        param.put("adjustHeaderId", adjustHeaderId);

        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/material/adjust/validImportDetailNew", param);
        String res = restTemplate.postForEntity(url, detailImportExcelVoList, String.class).getBody();
        DataResponse<List<MaterialAdjustDetailDTO>> response = JSON.parseObject(res,
                new TypeReference<DataResponse<List<MaterialAdjustDetailDTO>>>() {
                });
        return response;
    }

    @ApiOperation(value = "详设根据KUKA2022编码规则-导入")
    @PostMapping("importDesignPlanDetailWithKuka2022")
    public Response importDesignPlanDetailWithKuka2022(@RequestParam(value = "file") MultipartFile file,
                                                       @RequestParam(required = false, value = "storageId") Long storageId,
                                                       @RequestParam(required = false, value = "markId") Long markId,
                                                       @RequestParam(required = false, value = "wbsSummaryCode") String wbsSummaryCode,
                                                       @RequestParam(required = false, value = "designPlanDetailDtoForWebInfo") String designPlanDetailDtoForWebInfo,
                                                       @RequestParam(required = false, value = "wbsEnabled") Boolean wbsEnabled,
                                                       @RequestParam(required = false, value = "projectId") Long projectId,
                                                       @RequestParam(required = false, value = "isChange") Boolean isChange,
                                                       @RequestParam(required = false, value = "detailId") Long detailId,
                                                       @RequestParam(required = false, value = "selectedDesignDetailId") Long selectedDesignDetailId) {
        checkImportFileType(file);
        List<MilepostDesignPlanDetailKukaWbsImportExcelVo> kukaDetailExcelVos;
        try {
            kukaDetailExcelVos = FileUtil.importExcel(file, MilepostDesignPlanDetailKukaWbsImportExcelVo.class, 1, 0);
        } catch (Exception e) {
            kukaDetailExcelVos = FileUtil.importExcel(file, MilepostDesignPlanDetailKukaWbsImportExcelVo.class, 0, 0);
        }
        Guard.notNullOrEmpty(kukaDetailExcelVos, "详细设计导入文件模板数据为空");

        kukaDetailExcelVos = kukaDetailExcelVos.stream().filter(kukaDetailExcelVo ->
                StringUtils.isNotEmpty(kukaDetailExcelVo.getMaterielType())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getMaterialClassification())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getCodingMiddleClass())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getBrand())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getName())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getModel())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getUnit())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getFigureNumber())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getPamCode())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getErpCode())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getMaterialCategory())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getNumberStr())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getDispatchIsStr())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getExtIsStr())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getRequirementTypeStr())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getDescription())
        ).collect(Collectors.toList());
        Guard.notNullOrEmpty(kukaDetailExcelVos, "详细设计导入文件模板数据为空");

        if(Objects.nonNull(selectedDesignDetailId)){
            detailId = selectedDesignDetailId;
            logger.info("projectId:{},wbsSummaryCode:{},selectedDesignDetailId:{}",projectId,wbsSummaryCode,selectedDesignDetailId);
        }

        if (StringUtils.isNotEmpty(designPlanDetailDtoForWebInfo)) {
            kukaDetailExcelVos.get(0).setDesignPlanDetailDtoForWebInfo(JSONObject.parseArray(designPlanDetailDtoForWebInfo,
                    MilepostDesignPlanDetailDto.class));
        }
        Map<String, Object> param = new HashMap<>(2);
        param.put("storageId", storageId);
        param.put("markId", markId);
        param.put("wbsSummaryCode", wbsSummaryCode);
        param.put("wbsEnabled", wbsEnabled);
        param.put("projectId", projectId);
        param.put("isChange", isChange);
        param.put("detailId", detailId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/material/adjust/importDesignPlanDetailWithKuka2022", param);
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(120 * 1000);
        httpRequestFactory.setConnectTimeout(120 * 1000);
        httpRequestFactory.setReadTimeout(120 * 1000);
        restTemplate.setRequestFactory(httpRequestFactory);
        String res = restTemplate.postForEntity(url, kukaDetailExcelVos, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<MilepostDesignPlanDetailDto>>>() {
        });
    }

    @ApiOperation(value = "详设根据KUKA2022编码规则-批量导入")
    @PostMapping("batchImportDesignPlanDetailWithKuka2022")
    public Response batchImportDesignPlanDetailWithKuka2022(@RequestParam(value = "file") MultipartFile file,
                                                       @RequestParam(required = false, value = "storageId") Long storageId,
                                                       @RequestParam(required = false, value = "markId") Long markId,
                                                       @RequestParam(required = false, value = "wbsSummaryCode") String wbsSummaryCode,
                                                       @RequestParam(required = false, value = "designPlanDetailDtoForWebInfo") String designPlanDetailDtoForWebInfo,
                                                       @RequestParam(required = false, value = "wbsEnabled") Boolean wbsEnabled,
                                                       @RequestParam(required = false, value = "projectId") Long projectId,
                                                       @RequestParam(required = false, value = "isChange") Boolean isChange,
                                                       @RequestParam(required = true,value = "operateType")  String operateType) {
        checkImportFileType(file);
        List<MilepostDesignPlanDetailKukaWbsBatchImportExcelVo> kukaDetailExcelVos;
        try {
            kukaDetailExcelVos = FileUtil.importExcel(file, MilepostDesignPlanDetailKukaWbsBatchImportExcelVo.class, 1, 0);
        } catch (Exception e) {
            kukaDetailExcelVos = FileUtil.importExcel(file, MilepostDesignPlanDetailKukaWbsBatchImportExcelVo.class, 0, 0);
        }
        Guard.notNullOrEmpty(kukaDetailExcelVos, "详细设计导入文件模板数据为空");

        //移除空行的数据
        kukaDetailExcelVos = kukaDetailExcelVos.stream().filter(kukaDetailExcelVo ->
                StringUtils.isNotEmpty(kukaDetailExcelVo.getMaterielType())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getMaterialClassification())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getCodingMiddleClass())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getBrand())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getName())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getModel())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getUnit())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getFigureNumber())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getPamCode())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getErpCode())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getMaterialCategory())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getNumberStr())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getDispatchIsStr())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getExtIsStr())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getRequirementTypeStr())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getDescription())
                || StringUtils.isNotEmpty(kukaDetailExcelVo.getBatchImportOperationType())
        ).collect(Collectors.toList());
        Guard.notNullOrEmpty(kukaDetailExcelVos, "详细设计导入文件模板数据为空");

        if (StringUtils.isNotEmpty(designPlanDetailDtoForWebInfo)) {
            kukaDetailExcelVos.get(0).setDesignPlanDetailDtoForWebInfo(JSONObject.parseArray(designPlanDetailDtoForWebInfo,
                    MilepostDesignPlanDetailDto.class));
        }

        Map<String, Object> param = new HashMap<>(2);
        param.put("storageId", storageId);
        param.put("markId", markId);
        param.put("wbsSummaryCode", wbsSummaryCode);
        param.put("wbsEnabled", wbsEnabled);
        param.put("projectId", projectId);
        param.put("isChange", isChange);
        param.put("operateType",operateType);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/material/adjust/batchImportDesignPlanDetailWithKuka2022", param);
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(120 * 1000);
        httpRequestFactory.setConnectTimeout(120 * 1000);
        httpRequestFactory.setReadTimeout(120 * 1000);
        restTemplate.setRequestFactory(httpRequestFactory);
        String res = restTemplate.postForEntity(url, kukaDetailExcelVos, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<MilepostDesignPlanDetailDto>>>() {
        });
    }

    private static void checkImportFileType(MultipartFile file) {
        //加入对file的文件类型判断，如果是xls或xlsx则通过
        String fileName = file.getOriginalFilename();
        String fileType = fileName.substring(fileName.lastIndexOf(".") + 1);
        if (!fileType.equals("xls") && !fileType.equals("xlsx")) {
            throw new BizException(Code.ERROR, "导入文件类型不正确，请导入xls或xlsx格式的文件");
        }
    }

    @ApiOperation(value = "通过")
    @PostMapping("batchUpdateStatusPass/skipSecurityInterceptor")
    public Response batchUpdateStatusPass(@RequestBody BatchUpdateStatusPassDto dto) {

        String url = String.format("%smaterial/adjust/batchUpdateStatusPass/skipSecurityInterceptor", ModelsEnum.CTC.getBaseUrl());

        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(600 * 10000);
        httpRequestFactory.setConnectTimeout(600 * 10000);
        httpRequestFactory.setReadTimeout(600 * 10000);
        restTemplate.setRequestFactory(httpRequestFactory);
        String res = restTemplate.postForEntity(url, dto, String.class).getBody();
        return Response.dataResponse();
    }

    @ApiOperation(value = "物料新增退回")
    @PostMapping("materialAddReject")
    public Response materialAddReject(@RequestBody MaterialAdjustHeaderDTO dto) {
        String url = String.format("%smaterial/adjust/materialAddReject", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, dto, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation(value = "业务删除")
    @PutMapping("deleteById/{id}")
    public Response deleteById(@ApiParam("id") @PathVariable Long id) {
        String url = String.format("%smaterial/adjust/deleteById/" + id, ModelsEnum.CTC.getBaseUrl());
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }
}
