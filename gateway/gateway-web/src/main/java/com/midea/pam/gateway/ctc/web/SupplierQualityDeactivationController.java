package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.ctc.dto.SupplierQualityDeactivationDto;
import com.midea.pam.common.ctc.query.SupplierQualityDeactivationQuery;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.PageResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * Description
 * Created by l<PERSON><PERSON>
 * Date 2022/7/4 11:33
 */
@RestController
@RequestMapping("supplierQualityDeactivation")
@Api("供方品质停用")
public class SupplierQualityDeactivationController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "根据条件查询供方品质停用列表")
    @PostMapping("list")
    public Response list(@RequestBody SupplierQualityDeactivationQuery query) {
        String url = String.format("%ssupplierQualityDeactivation/list", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, query, String.class);
        String res = cleanStr(responseEntity.getBody());
        PageInfo<SupplierQualityDeactivationDto> data = JSON.parseObject(res, new TypeReference<PageInfo<SupplierQualityDeactivationDto>>() {
        });
        PageResponse<SupplierQualityDeactivationDto> response = Response.pageResponse();
        return response.convert(data);
    }
}
