package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.ProjectMilepostDesignDto;
import com.midea.pam.common.ctc.vo.ProjectMilepostDesignExcelVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/03/02
 * @description
 */
@Api("详细设计")
@RestController
@RequestMapping("statistics/projectMIlepostDesign")
public class ProjectMilepostDesignStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查询详细设计列表")
    @GetMapping("page")
    public Response list(ProjectMilepostDesignDto projectMilepostDesignDto,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(projectMilepostDesignDto);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectMilepostDesign/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<ProjectMilepostDesignDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ProjectMilepostDesignDto>>>() {
        });

        return response;
    }

    @ApiOperation(value = "查询我的详细设计列表")
    @GetMapping("createByMePage")
    public Response createByMePage(ProjectMilepostDesignDto projectMilepostDesignDto,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(projectMilepostDesignDto);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        params.put("me", Boolean.TRUE);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectMilepostDesign/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<ProjectMilepostDesignDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ProjectMilepostDesignDto>>>() {
        });

        return response;
    }

    private Map buildParam(ProjectMilepostDesignDto projectMilepostDesignDto) {
        final Map<String, Object> params = new HashMap<>();
        params.put("projectCode", projectMilepostDesignDto.getProjectCode());
        params.put("projectName", projectMilepostDesignDto.getProjectName());
        params.put("managerName", projectMilepostDesignDto.getManagerName());
        params.put("milepostPlanPersonName", projectMilepostDesignDto.getMilepostPlanPersonName());
        params.put("startTimeStr", projectMilepostDesignDto.getStartTimeStr());
        params.put("endTimeStr", projectMilepostDesignDto.getEndTimeStr());
        params.put("projectStatusStr", projectMilepostDesignDto.getProjectStatusStr());
        params.put("milepostStatusStr", projectMilepostDesignDto.getMilepostStatusStr());

        return params;
    }


    @ApiOperation(value = "详细设计列表导出", response = ResponseMap.class)
    @GetMapping("export")
    public void listExport(HttpServletResponse response,
                           ProjectMilepostDesignDto projectMilepostDesignDto) {
        final Map<String, Object> params = buildParam(projectMilepostDesignDto);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectMilepostDesign/v1/export", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("详细设计信息");
        fileName.append(".xls");

        JSONArray projectMilepostDesignsArr = (JSONArray) resultMap.get("projectMilepostDesigns");

        List<ProjectMilepostDesignExcelVO> projectMilepostDesignExcelVOS = JSONObject.parseArray(projectMilepostDesignsArr.toJSONString(), ProjectMilepostDesignExcelVO.class);
        for (int i = 0; i < projectMilepostDesignExcelVOS.size(); i++) {
            ProjectMilepostDesignExcelVO projectMilepostDesignExcelVO = projectMilepostDesignExcelVOS.get(i);
            projectMilepostDesignExcelVO.setNum(i + 1);
        }
        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(projectMilepostDesignExcelVOS, ProjectMilepostDesignExcelVO.class, null, "详细设计信息", true);
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    @ApiOperation(value = "我的详细设计列表导出", response = ResponseMap.class)
    @GetMapping("exportMy")
    public void exportMy(HttpServletResponse response,
                           ProjectMilepostDesignDto projectMilepostDesignDto) {
        final Map<String, Object> params = buildParam(projectMilepostDesignDto);
        params.put("me", Boolean.TRUE);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectMilepostDesign/v1/export", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("我的详细设计信息");
        fileName.append(".xls");

        JSONArray projectMilepostDesignsArr = (JSONArray) resultMap.get("projectMilepostDesigns");

        List<ProjectMilepostDesignExcelVO> projectMilepostDesignExcelVOS = JSONObject.parseArray(projectMilepostDesignsArr.toJSONString(), ProjectMilepostDesignExcelVO.class);
        for (int i = 0; i < projectMilepostDesignExcelVOS.size(); i++) {
            ProjectMilepostDesignExcelVO projectMilepostDesignExcelVO = projectMilepostDesignExcelVOS.get(i);
            projectMilepostDesignExcelVO.setNum(i + 1);
        }
        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(projectMilepostDesignExcelVOS, ProjectMilepostDesignExcelVO.class, null, "我的详细设计信息", true);
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    @ApiOperation(value = "查询详细设计列表")
    @GetMapping("pageWbs")
    public Response listWbs(ProjectMilepostDesignDto projectMilepostDesignDto,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(projectMilepostDesignDto);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectMilepostDesign/v1/listWbs", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<ProjectMilepostDesignDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ProjectMilepostDesignDto>>>() {
        });

        return response;
    }

    @ApiOperation(value = "查询我的详细设计列表")
    @GetMapping("createByMePageWbs")
    public Response createByMePageWbs(ProjectMilepostDesignDto projectMilepostDesignDto,
                                   @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                   @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(projectMilepostDesignDto);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        params.put("me", Boolean.TRUE);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectMilepostDesign/v1/listWbs", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<ProjectMilepostDesignDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ProjectMilepostDesignDto>>>() {
        });

        return response;
    }
}
