package com.midea.pam.gateway.ctc.web;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.constants.WbsBudgetFieldConstant;
import com.midea.pam.common.ctc.cache.ProjectActivityCache;
import com.midea.pam.common.ctc.cache.WbsTemplateRuleCache;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.ProjectWbsBaselineChangeDTO;
import com.midea.pam.common.ctc.dto.ProjectWbsBudgetObjectDto;
import com.midea.pam.common.ctc.excelVo.ProjectWbsBudgetImportResponseExcelVO;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.ProjectWbsBudgetUtils;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.utils.FileUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFPalette;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Api("项目wbs预算基线")
@RestController
@RequestMapping("projectWbsBaseline")
public class ProjectWbsBaselineController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @ApiOperation(value = "WBS基线批量导入", notes = "版本v1")
    @PostMapping("v1/importWbsBaselineFromExcel")
    public Response importWbsBaselineFromExcel(@ApiParam("附件") @RequestParam(value = "file") MultipartFile file,
                                               @ApiParam("WBS预算数据") @RequestParam(name = "data") String data,
                                               HttpServletResponse response) {
        Map<String, String> dataMap = (Map) JSON.parseObject(data);
        // 项目id
        Long projectId = MapUtils.getLong(dataMap, WbsBudgetFieldConstant.PROJECT_ID);
        if (Objects.isNull(projectId)) {
            throw new ApplicationBizException(String.format("参数%s不能为空", WbsBudgetFieldConstant.PROJECT_ID));
        }
        // wbs模板id
        Long wbsTemplateInfoId = MapUtils.getLong(dataMap, WbsBudgetFieldConstant.WBS_TEMPLATE_INFO_ID);
        if (Objects.isNull(wbsTemplateInfoId)) {
            throw new ApplicationBizException(String.format("参数%s不能为空", WbsBudgetFieldConstant.WBS_TEMPLATE_INFO_ID));
        }
        // wbs模板id
        Long projectType = MapUtils.getLong(dataMap, WbsBudgetFieldConstant.PROJECT_TYPE);
        if (Objects.isNull(projectType)) {
            throw new ApplicationBizException(String.format("参数%s不能为空", WbsBudgetFieldConstant.PROJECT_TYPE));
        }
        // 批次号
        String batchCode = MapUtils.getString(dataMap, "batchCode");
        if (StringUtils.isBlank(batchCode)) {
            throw new ApplicationBizException(String.format("参数%s不能为空", "batchCode"));
        }
        // 导入方式（ADD：增量导入、COVER：覆盖导入）
        String type = MapUtils.getString(dataMap, WbsBudgetFieldConstant.TYPE);
        if (StringUtils.isBlank(type)) {
            throw new ApplicationBizException(String.format("参数%s不能为空", WbsBudgetFieldConstant.TYPE));
        }
        // 项目编码
        String projectCode = MapUtils.getString(dataMap, WbsBudgetFieldConstant.PROJECT_CODE);
        // 项目名称
        String projectName = MapUtils.getString(dataMap, WbsBudgetFieldConstant.PROJECT_NAME);

        ProjectWbsBudgetImportResponseExcelVO importResponseExcelVO = new ProjectWbsBudgetImportResponseExcelVO();
        String wbsBudgetStr = MapUtils.getString(dataMap, "wbsBudgetList");
        String wbsBaselineStr = MapUtils.getString(dataMap, "wbsBudgetBaselineList");

        Type fastJsonType = new TypeReference<List<HashMap<String, Object>>>() {
        }.getType();

        // 预算
        importResponseExcelVO.setPageBudgetMapList(JSON.parseArray(wbsBudgetStr).toJavaObject(fastJsonType));
        // 基线
        importResponseExcelVO.setPageBaselineMapList(JSON.parseArray(wbsBaselineStr).toJavaObject(fastJsonType));

        // 获取有效的activity活动事项 预算事项=是 当前时间<结束时间
        List<ProjectActivityCache> projectActivityCaches = ProjectWbsBudgetUtils.getEligibilityActivityCache();

        // 获取有效的wbs动态列
        List<WbsTemplateRuleCache> wbsTemplateRuleCaches = ProjectWbsBudgetUtils.getEligibilityWbsCache(wbsTemplateInfoId);

        // excel有效数据
        List<Map> excelMapList;
        try {
            // 从excel第3行开始读，第3行是activity活动事项编码列
            List<List<String>> wbsExcelRows = FileUtil.importExcel(file, 0, 2, wbsTemplateRuleCaches.size() + projectActivityCaches.size());
            // 校验wbs模板导入title有效性
            ProjectWbsBudgetController.validityImportWbsBudgetTitle(wbsExcelRows.get(0), wbsTemplateRuleCaches, projectActivityCaches);

            excelMapList = ProjectWbsBudgetUtils.wbsExcelRows2Map(wbsExcelRows,
                    projectActivityCaches,
                    wbsTemplateRuleCaches,
                    batchCode,
                    projectName,
                    projectCode);
        } catch (BizException bizException) {
            // 自定义内容不能被Exception拦截
            throw bizException;
        } catch (Exception e) {
            logger.error("wbs导入异常:" + e.getMessage(), e);
            throw new BizException(ErrorCode.CTC_PROJECT_WBS_BUDGET_NOT_TRUE);
        }
        if (CollectionUtils.isEmpty(excelMapList)) {
            throw new ApplicationBizException("当前导入Excel模板中无法匹配有效的记录，请检查");
        }
        importResponseExcelVO.setExcelMapList(excelMapList);
        importResponseExcelVO.setImportType(type);
        importResponseExcelVO.setBatchCode(batchCode);
        importResponseExcelVO.setProjectId(projectId);
        importResponseExcelVO.setWbsTemplateInfoId(wbsTemplateInfoId);
        importResponseExcelVO.setProjectType(projectType);

        final String url = String.format("%sprojectWbsBaseline/v1/importWbsBaselineFromExcel", ModelsEnum.CTC.getBaseUrl());
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(6000 * 1000);
        httpRequestFactory.setConnectTimeout(6000 * 1000);
        httpRequestFactory.setReadTimeout(6000 * 1000);
        restTemplate.setRequestFactory(httpRequestFactory);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, importResponseExcelVO, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse>() {
        });
    }

    @ApiOperation(value = "预立项转正 - 获取前端格式的wbs预算关联信息", notes = "预立项转正预算界面")
    @GetMapping("v1/findPreWbsBudgetInfoByWebfront")
    public Response findPreWbsBudgetInfoByWebfront(@RequestParam Long projectId) {
        Map<String, Object> map = new HashMap();
        map.put(WbsBudgetFieldConstant.PROJECT_ID, projectId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "projectWbsBaseline/v1/findPreWbsBudgetInfoByWebfront", map);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<ProjectWbsBudgetObjectDto>>() {
        });
    }

    @ApiOperation(value = "WBS基线批量导入检查", notes = "预算基线变更")
    @PostMapping("change/importCheck")
    public Response checkImportCheck(@ApiParam("附件") @RequestParam(value = "file") MultipartFile file,
                                     @ApiParam("WBS预算数据") @RequestParam(name = "data") String data,
                                     HttpServletResponse response) {
        Map<String, String> dataMap = (Map) JSON.parseObject(data);
        //因检查xls时未选择导入方式，默认增量导入。若增量场景抛错，覆盖场景一定抛错（金额允许负数带出来的问题）
        dataMap.put(WbsBudgetFieldConstant.TYPE, "ADD");
        ProjectWbsBudgetImportResponseExcelVO importResponseExcelVO = getProjectWbsBudgetImportResponseExcelVO(file, dataMap);
        final String url = String.format("%sprojectWbsBaseline/changeImportCheck", ModelsEnum.CTC.getBaseUrl());
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(6000 * 1000);
        httpRequestFactory.setConnectTimeout(6000 * 1000);
        httpRequestFactory.setReadTimeout(6000 * 1000);
        restTemplate.setRequestFactory(httpRequestFactory);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, importResponseExcelVO, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse>() {
        });
    }

    @ApiOperation(value = "下载错误数据", notes = "场景：预算基线变更")
    @PostMapping("downloadErrorMsg")
    public void downloadErrorMsg(@ApiParam("附件") @RequestParam(value = "file") MultipartFile file,
                                 @ApiParam("WBS预算数据") @RequestParam(name = "errorMsg") String errorMsg,
                                 HttpServletResponse response) throws IOException {
        JSONArray jsonArray = JSON.parseArray(errorMsg);
        if (jsonArray != null && jsonArray.size() > 0) {
            HSSFWorkbook workbook = new HSSFWorkbook(file.getInputStream());
            Sheet sheetAt = workbook.createSheet("报错信息");
            List<Pair<Integer, Integer>> pairList = new ArrayList<Pair<Integer, Integer>>();
            // 创建表头
            for (int i = 0; i < jsonArray.size(); i++) {
                sheetAt.setColumnWidth(0, 10000);
                Row row = sheetAt.createRow(i);
                Cell cell = row.createCell(0);
                HSSFRichTextString textString = new HSSFRichTextString("导入校验信息：" + jsonArray.get(i));
                HSSFCellStyle cellStyle = workbook.createCellStyle();
                cellStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
                cellStyle.setWrapText(true);
                cell.setCellStyle(cellStyle);
                HSSFFont font = workbook.createFont();
                font.setColor(HSSFColor.RED.index);
                textString.applyFont("批量导入校验信息：".length(), textString.length(), font);
                cell.setCellValue(textString);
                buildColorList(pairList, jsonArray.getString(i));
            }
            //chenchong 标红
            if (CollectionUtils.isNotEmpty(pairList) && pairList.size() > 0) {
                for (Pair<Integer, Integer> pair : pairList) {
                    Sheet sheet = workbook.getSheetAt(0);
                    Row firstRow = sheet.getRow(pair.getKey() - 1);
                    Cell titleCell = firstRow.getCell(pair.getValue() - 1);
                    if (Objects.nonNull(titleCell)) {
                        HSSFCellStyle style = getHeaderCellStyle(workbook, IndexedColors.DARK_RED.index, (byte) 255, (byte) 0, (byte) 0);
                        titleCell.setCellStyle(style);
                    }
                }
            }
            // 导入下载文件名称乱码
            ExportExcelUtil.downLoadExcel("报错信息_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls", response, workbook);
        }
    }

    @ApiOperation(value = "WBS基线批量导入", notes = "预算基线变更")
    @PostMapping("change/import")
    public Response changeImport(@ApiParam("附件") @RequestParam(value = "file") MultipartFile file,
                                 @ApiParam("WBS预算数据") @RequestParam(name = "data") String data,
                                 HttpServletResponse response) {
        Map<String, String> dataMap = (Map) JSON.parseObject(data);
        ProjectWbsBudgetImportResponseExcelVO importResponseExcelVO = getProjectWbsBudgetImportResponseExcelVO(file, dataMap);
        final String url = String.format("%sprojectWbsBaseline/changeImport", ModelsEnum.CTC.getBaseUrl());
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(6000 * 1000);
        httpRequestFactory.setConnectTimeout(6000 * 1000);
        httpRequestFactory.setReadTimeout(6000 * 1000);
        restTemplate.setRequestFactory(httpRequestFactory);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, importResponseExcelVO, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse>() {
        });
    }

    @ApiOperation(value = "保存变更", notes = "预算基线变更")
    @PostMapping("saveChange")
    public Response saveChange(@RequestBody ProjectDto projectDto) {
        final String url = String.format("%sprojectWbsBaseline/saveChange", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, projectDto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Long>>() {
        });
    }

    @ApiOperation(value = "审批详情-预算基线变更明细")
    @GetMapping("changeView")
    public Response changeView(@RequestParam Long headerId, @RequestParam(required = false, defaultValue = "false") Boolean reEdit) {
        Map<String, Object> map = new HashMap();
        map.put("headerId", headerId);
        map.put("reEdit", reEdit);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "projectWbsBaseline/changeView", map);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<ProjectWbsBaselineChangeDTO>>() {
        });
    }

    @ApiOperation(value = "移动审批-预算基线变更")
    @GetMapping({"getBudgetBaselineChangeApp"})
    public Response getBudgetBaselineChangeApp(@RequestParam Long id) {
        String url = String.format("%sprojectWbsBaseline/getBudgetBaselineChangeApp?id=%s", ModelsEnum.CTC.getBaseUrl(), id);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<ResponseMap>>() {
        });
    }

    @NotNull
    private ProjectWbsBudgetImportResponseExcelVO getProjectWbsBudgetImportResponseExcelVO(MultipartFile file, Map<String, String> dataMap) {
        // 项目id
        Long projectId = MapUtils.getLong(dataMap, WbsBudgetFieldConstant.PROJECT_ID);
        Guard.notNull(projectId, String.format("参数%s不能为空", WbsBudgetFieldConstant.PROJECT_ID));
        // 项目编码
        String projectCode = MapUtils.getString(dataMap, WbsBudgetFieldConstant.PROJECT_CODE);
        Guard.notNull(projectCode, String.format("参数%s不能为空", WbsBudgetFieldConstant.PROJECT_CODE));
        // 项目名称
        String projectName = MapUtils.getString(dataMap, WbsBudgetFieldConstant.PROJECT_NAME);
        Guard.notNull(projectName, String.format("参数%s不能为空", WbsBudgetFieldConstant.PROJECT_NAME));
        // wbs模板id
        Long wbsTemplateInfoId = MapUtils.getLong(dataMap, WbsBudgetFieldConstant.WBS_TEMPLATE_INFO_ID);
        Guard.notNull(wbsTemplateInfoId, String.format("参数%s不能为空", WbsBudgetFieldConstant.WBS_TEMPLATE_INFO_ID));
        // 项目类型
        Long projectType = MapUtils.getLong(dataMap, WbsBudgetFieldConstant.PROJECT_TYPE);
        Guard.notNull(projectType, String.format("参数%s不能为空", WbsBudgetFieldConstant.PROJECT_TYPE));
        // 批次号
        String batchCode = MapUtils.getString(dataMap, "batchCode");
        Guard.notBlank(batchCode, "参数batchCode不能为空");
        // 导入方式（ADD：增量导入、COVER：覆盖导入）
        String type = MapUtils.getString(dataMap, WbsBudgetFieldConstant.TYPE);
        Guard.notNull(type, String.format("参数%s不能为空", WbsBudgetFieldConstant.TYPE));
        String wbsBaselineStr = MapUtils.getString(dataMap, "wbsBudgetBaselineList");

        // 获取有效的activity活动事项 预算事项=是 当前时间<结束时间
        List<ProjectActivityCache> projectActivityCaches = ProjectWbsBudgetUtils.getEligibilityActivityCache();

        // 获取有效的wbs动态列
        List<WbsTemplateRuleCache> wbsTemplateRuleCaches = ProjectWbsBudgetUtils.getEligibilityWbsCache(wbsTemplateInfoId);

        // excel有效数据
        List<Map> excelMapList;
        try {
            // 从excel第3行开始读，第3行是activity活动事项编码列
            List<List<String>> wbsExcelRows = FileUtil.importExcel(file, 0, 2, wbsTemplateRuleCaches.size() + projectActivityCaches.size());
            // 校验wbs模板导入title有效性
            validityImportWbsBudgetTitle(wbsExcelRows.get(0), wbsTemplateRuleCaches, projectActivityCaches);

            excelMapList = ProjectWbsBudgetUtils.wbsExcelRows2Map(wbsExcelRows,
                    projectActivityCaches,
                    wbsTemplateRuleCaches,
                    batchCode,
                    projectName,
                    projectCode);
        } catch (BizException bizException) {
            // 自定义内容不能被Exception拦截
            throw bizException;
        } catch (Exception e) {
            logger.error("wbs导入异常:" + e.getMessage(), e);
            throw new BizException(ErrorCode.CTC_PROJECT_WBS_BUDGET_NOT_TRUE);
        }
        if (CollectionUtils.isEmpty(excelMapList)) {
            throw new ApplicationBizException("当前导入Excel模板中无法匹配有效的记录，请检查");
        }
        Type fastJsonType = new TypeReference<List<HashMap<String, Object>>>() {
        }.getType();

        ProjectWbsBudgetImportResponseExcelVO importResponseExcelVO = new ProjectWbsBudgetImportResponseExcelVO();
        importResponseExcelVO.setExcelMapList(excelMapList);
        importResponseExcelVO.setImportType(type);
        importResponseExcelVO.setBatchCode(batchCode);
        importResponseExcelVO.setProjectId(projectId);
        importResponseExcelVO.setWbsTemplateInfoId(wbsTemplateInfoId);
        importResponseExcelVO.setProjectType(projectType);
        importResponseExcelVO.setProjectCode(projectCode);
        // 基线
        importResponseExcelVO.setPageBaselineMapList(JSON.parseArray(wbsBaselineStr).toJavaObject(fastJsonType));
        return importResponseExcelVO;
    }


    /**
     * 校验wbs模板导入title有效性
     *
     * @param wbsExcelRows
     * @param wbsTemplateRuleCaches
     * @param projectActivityCaches
     */
    public static void validityImportWbsBudgetTitle(List<String> wbsExcelRows, List<WbsTemplateRuleCache> wbsTemplateRuleCaches, List<ProjectActivityCache> projectActivityCaches) {
        if (CollectionUtils.isEmpty(wbsExcelRows)) {
            throw new ApplicationBizException("导入模板有误，请检查");
        }

        for (int i = 0; i < wbsExcelRows.size(); i++) {
            if (i < wbsTemplateRuleCaches.size()) {
                if (!StringUtils.equals(wbsTemplateRuleCaches.get(i).getRuleName(), wbsExcelRows.get(i))) {
                    throw new ApplicationBizException("导入模板内容有更新，请下载最新模板");
                }
            } else {
                if (!StringUtils.equals(projectActivityCaches.get(i - wbsTemplateRuleCaches.size()).getCode(), wbsExcelRows.get(i))) {
                    throw new ApplicationBizException("导入模板内容有更新，请下载最新模板");
                }
            }
        }
    }

    private void buildColorList(List<Pair<Integer, Integer>> pairList, String str) {
        String[] split = str.split("，");
        Matcher matcher = Pattern.compile("([0-9]+)").matcher(split[0]);
        List<Integer> list = new LinkedList<Integer>();
        while (matcher.find()) {
            list.add(Integer.parseInt(matcher.group().trim()));
        }
        if (list.size() > 1) {
            Pair<Integer, Integer> pair = new ImmutablePair<>(list.get(0), list.get(1));
            pairList.add(pair);
        }
    }

    private HSSFCellStyle getHeaderCellStyle(HSSFWorkbook workbook, short index, byte red, byte green, byte blue) {
        HSSFPalette palette = workbook.getCustomPalette(); //调色板实例
        palette.setColorAtIndex(index, red, green, blue);
        HSSFColor color = palette.findColor(red, green, blue);
        // 设置字体
        HSSFFont boldFont = workbook.createFont();
        boldFont.setFontName("宋体");
        boldFont.setFontHeightInPoints((short) 10);

        HSSFCellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        cellStyle.setFillForegroundColor(color.getIndex());
        cellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);//水平居中
        cellStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);//垂直居中
        cellStyle.setFont(boldFont);
        cellStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        cellStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
        cellStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        cellStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
        cellStyle.setLeftBorderColor(HSSFColor.BLACK.index);
        cellStyle.setRightBorderColor(HSSFColor.BLACK.index);
        cellStyle.setBottomBorderColor(HSSFColor.BLACK.index);
        cellStyle.setTopBorderColor(HSSFColor.BLACK.index);
        return cellStyle;
    }

}
