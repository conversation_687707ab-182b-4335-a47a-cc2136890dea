package com.midea.pam.gateway.config;

import com.midea.mcomponent.security.common.util.UserUtils;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.gateway.entity.CurrentContext;
import com.midea.pam.common.gateway.entity.CurrentContextExample;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.gateway.common.utils.PamCurrentUserUtil;
import com.midea.pam.gateway.service.CurrentContextService;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * 增加用户名拦截.
 * chengzy7
 */

public class ActionTrackInterceptor implements ClientHttpRequestInterceptor {
    @Resource
    private CurrentContextService currentContextService;

    @Override
    public ClientHttpResponse intercept(HttpRequest httpRequest, byte[] body, ClientHttpRequestExecution clientHttpRequestExecution) throws IOException {
        final HttpHeaders headers = httpRequest.getHeaders();
        String username = PamCurrentUserUtil.getCurrentUserName();

        if (!StringUtils.isEmpty(username)) {
            headers.add(Constants.FD_LOGIN_NAME, username);
            headers.add(Constants.FD_LANGUAGE, null != UserUtils.getLocale() ? UserUtils.getLocale().getLanguage() : "");
    
            final UserInfo userInfo = CacheDataUtils.findUserByMip(username);
            if (null != userInfo) {
                headers.add(Constants.FD_LOGIN_ID, String.valueOf(userInfo.getId()));
            }
            CurrentContext context = CacheDataUtils.getContextByMip(username);
            if (null == context) {
                final CurrentContextExample condition = new CurrentContextExample();
                condition.createCriteria().andLoginUserNameEqualTo(username);
                final List<CurrentContext> contexts = currentContextService.selectByExample(condition);
                context = ListUtils.isNotEmpty(contexts) ? contexts.get(0) : null;
            }
            if (null != context) {
                Assert.notNull(context, "获取不到上下文信息");
                headers.add(Constants.CURRENT_UNIT_ID, String.valueOf(context.getCurrentOrgId()));
                headers.add(Constants.CURRENT_UNIT_NAME, context.getCurrentOrgName());
                headers.add(Constants.ANTHORITY_OU, context.getOuIds());
                headers.add(Constants.CURRENT_SECOND_UNIT, context.getSecondUnitIds());
            }
            // 未登录用户清空用户相关数据
        } else {
            headers.remove(Constants.FD_LOGIN_ID);
            headers.remove(Constants.FD_LOGIN_NAME);
            headers.remove(Constants.CURRENT_UNIT_ID);
            headers.remove(Constants.CURRENT_UNIT_NAME);
            headers.remove(Constants.ANTHORITY_OU);
            headers.remove(Constants.CURRENT_SECOND_UNIT);
        }
        return clientHttpRequestExecution.execute(httpRequest, body);
    }
}
