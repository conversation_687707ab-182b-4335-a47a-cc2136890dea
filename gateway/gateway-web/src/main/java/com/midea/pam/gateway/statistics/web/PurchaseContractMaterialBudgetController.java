package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.vo.*;
import com.midea.pam.common.enums.CodePrefix;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.support.utils.BeanConverter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @description: 采购合同关联物料预算
 * @author: ex_xuwj4
 * @create: 2021-07-12
 **/
@Api("采购合同关联物料预算")
@RestController
@RequestMapping("statistics/purchaseContractMaterialBudget")
public class PurchaseContractMaterialBudgetController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "采购合同关联物料预算列表")
    @GetMapping("page")
    public Response page(@RequestParam(required = false) @ApiParam("pam编码") final String pamCode,
                         @RequestParam(required = false) @ApiParam("物料预算名称") final String materialName,
                         @RequestParam(required = false) @ApiParam("供应商名称") final String vendorName,
                         @RequestParam(required = false) @ApiParam("供应商编号") final String vendorCode,
                         @RequestParam(required = false) @ApiParam("项目名称") final String projectName,
                         @RequestParam(required = false) @ApiParam("项目编号") final String projectCode,
                         @RequestParam(required = false) @ApiParam("采购合同编号") final String contractCode,
                         @RequestParam(required = false) @ApiParam("采购合同名称") final String contractName,
                         @RequestParam(required = false) @ApiParam("合同状态(多选)") final String manyStatus,
                         @RequestParam(required = false) @ApiParam("业务实体(多选)") final String manyOuName,
                         @RequestParam(required = false) @ApiParam("开始日期") final String startDate,
                         @RequestParam(required = false) @ApiParam("结束日期") final String endDate,
                         @RequestParam(required = false) @ApiParam("采购跟进人") final String purchasingFollowerName,
                         @RequestParam(required = false) @ApiParam("累计进度执行百分比(下限)") final BigDecimal budgetExecutePercentTotalMin,
                         @RequestParam(required = false) @ApiParam("累计进度执行百分比(上限)") final BigDecimal budgetExecutePercentTotalMax,
                         @RequestParam(required = false) @ApiParam("排序") final String orderParam,
                         @RequestParam(required = false) @ApiParam("排序类型(1:升序；2：降序)") final Integer orderType,
                         @RequestParam(required = false) @ApiParam("合同审批通过时间起始") final Date contractApprovalStartTime,
                         @RequestParam(required = false) @ApiParam("合同审批通过时间结束") final Date contractApprovalEndTime,
                         @RequestParam(required = false) @ApiParam("需求发布日期起始") final Date publishStartTime,
                         @RequestParam(required = false) @ApiParam("需求发布日期结束") final Date publishEndTime,
                         @RequestParam(required = false) @ApiParam("需求发布单据编号") final String requirementCode,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Map<String, Object> param = new HashMap<>();
        param.put("pamCode", pamCode);
        param.put("materialName", materialName);
        param.put("vendorName", vendorName);
        param.put("vendorCode", vendorCode);
        param.put("projectName", projectName);
        param.put("projectCode", projectCode);
        param.put("contractCode", contractCode);
        param.put("contractName", contractName);
        param.put("manyStatus", manyStatus);
        param.put("manyOuName", manyOuName);
        param.put("orderParam", orderParam);
        param.put("orderType", orderType);
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        param.put("purchasingFollowerName", purchasingFollowerName);
        param.put("budgetExecutePercentTotalMin", budgetExecutePercentTotalMin);
        param.put("budgetExecutePercentTotalMax", budgetExecutePercentTotalMax);
        if(contractApprovalStartTime!=null) {
            param.put("contractApprovalStartTime", dateFormat.format(contractApprovalStartTime));
        }
        if(contractApprovalEndTime!=null) {
            param.put("contractApprovalEndTime", dateFormat.format(contractApprovalEndTime));
        }
        if(publishStartTime!=null) {
            param.put("publishStartTime", dateFormat.format(publishStartTime));
        }
        if(publishEndTime!=null) {
            param.put("publishEndTime", dateFormat.format(publishEndTime));
        }
        param.put("requirementCode", requirementCode);

        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/purchaseContractMaterialBudget/page", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<PurchaseContractMaterialBudgetVo>>>() {
        });

    }

    @ApiOperation(value = "采购合同关联预算导出")
    @GetMapping("export")
    public void export(@RequestParam(required = false) @ApiParam("pam编码") final String pamCode,
                              @RequestParam(required = false) @ApiParam("物料预算名称") final String materialName,
                              @RequestParam(required = false) @ApiParam("供应商名称") final String vendorName,
                              @RequestParam(required = false) @ApiParam("供应商编号") final String vendorCode,
                              @RequestParam(required = false) @ApiParam("项目名称") final String projectName,
                              @RequestParam(required = false) @ApiParam("项目编号") final String projectCode,
                              @RequestParam(required = false) @ApiParam("采购合同编号") final String contractCode,
                              @RequestParam(required = false) @ApiParam("采购合同名称") final String contractName,
                              @RequestParam(required = false) @ApiParam("合同状态(多选)") final String manyStatus,
                              @RequestParam(required = false) @ApiParam("业务实体(多选)") final String manyOuName,
                              @RequestParam(required = false) @ApiParam("开始日期") final String startDate,
                              @RequestParam(required = false) @ApiParam("结束日期") final String endDate,
                              @RequestParam(required = false) @ApiParam("采购跟进人") final String purchasingFollowerName,
                              @RequestParam(required = false) @ApiParam("累计进度执行百分比(下限)") final BigDecimal budgetExecutePercentTotalMin,
                              @RequestParam(required = false) @ApiParam("累计进度执行百分比(上限)") final BigDecimal budgetExecutePercentTotalMax,
                              @RequestParam(required = false) @ApiParam("排序") final String orderParam,
                              @RequestParam(required = false) @ApiParam("排序类型(1:升序；2：降序)") final Integer orderType,
                               @RequestParam(required = false) @ApiParam("合同审批通过时间起始") final Date contractApprovalStartTime,
                               @RequestParam(required = false) @ApiParam("合同审批通过时间结束") final Date contractApprovalEndTime,
                               @RequestParam(required = false) @ApiParam("需求发布日期起始") final Date publishStartTime,
                               @RequestParam(required = false) @ApiParam("需求发布日期结束") final Date publishEndTime,
                               @RequestParam(required = false) @ApiParam("需求发布单据编号") final String requirementCode,
                              HttpServletResponse response) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Map<String, Object> param = new HashMap<>();
        param.put("pamCode", pamCode);
        param.put("materialName", materialName);
        param.put("vendorName", vendorName);
        param.put("vendorCode", vendorCode);
        param.put("projectName", projectName);
        param.put("projectCode", projectCode);
        param.put("contractCode", contractCode);
        param.put("contractName", contractName);
        param.put("manyStatus", manyStatus);
        param.put("manyOuName", manyOuName);
        param.put("orderParam", orderParam);
        param.put("orderType", orderType);
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        if(contractApprovalStartTime!=null) {
            param.put("contractApprovalStartTime", dateFormat.format(contractApprovalStartTime));
        }
        if(contractApprovalEndTime!=null) {
            param.put("contractApprovalEndTime", dateFormat.format(contractApprovalEndTime));
        }
        if(publishStartTime!=null) {
            param.put("publishStartTime", dateFormat.format(publishStartTime));
        }
        if(publishEndTime!=null) {
            param.put("publishEndTime", dateFormat.format(publishEndTime));
        }
        param.put("requirementCode", requirementCode);

        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/purchaseContractMaterialBudget/export", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        final DataResponse<List<PurchaseContractMaterialBudgetVo>> dataResponse =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<PurchaseContractMaterialBudgetVo>>>() {
                });
        List<PurchaseContractMaterialBudgetVo> purchaseContractMaterialBudgetVos = dataResponse.getData();
        List<PurchaseContractMaterialBudgetExcelVo> purchaseContractExcelVOList = BeanConverter.copy(purchaseContractMaterialBudgetVos, PurchaseContractMaterialBudgetExcelVo.class);

        for (int i = 0; i < purchaseContractExcelVOList.size(); i++) {
            PurchaseContractMaterialBudgetExcelVo vo = purchaseContractExcelVOList.get(i);
            vo.setNumb(i + 1);
            if(vo.getBudgetExecutePercentTotal()==null || vo.getBudgetExecutePercentTotal().compareTo(BigDecimal.ZERO)==0){
                vo.setBudgetExecutePercentTotalStr("0%");
            }else {
                vo.setBudgetExecutePercentTotalStr(vo.getBudgetExecutePercentTotal().setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString() + "%");
            }
        }
        Workbook workbook = ExportExcelUtil.buildDefaultSheet(purchaseContractExcelVOList, PurchaseContractMaterialBudgetExcelVo.class, null, "采购合同关联预算信息", true);

        ExportExcelUtil.downLoadExcel("外包物料购价查询_"+ DateUtils.format(new Date(),"yyyyMMddHHmm")+
                CacheDataUtils.generateSequence(Constants.CODE_PURCHASE_CONTRACT_MATERIAL_BUDGET_FILE_NAME_LENGTH, CodePrefix.PURCHASE_CONTRACT_MATERIAL_BUDGET_FILE_NAME.code())+".xls", response, workbook);
    }
}
