package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.ProjectRiskSettingDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Description: 项目风险
 * @Date: 2020-12-15 15:16
 * @Version: 1.0
 */
@Api("重大风险项目设置")
@RestController
@RequestMapping({"projectRisk"})
public class ProjectRiskSettingController extends ControllerHelper {
    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "查询重大风险项目列表")
    @GetMapping("list")
    public Response getProjectRiskList(@RequestParam(required = false) @ApiParam("项目编号") String projectCode,
                                       @RequestParam(required = false) @ApiParam("项目名称") String projectName,
                                       @RequestParam(required = false) @ApiParam("是否重大风险项目（1是，0否）") Integer riskFlag,
                                       @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                       @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        Map<String, Object> param = new HashMap<>(5);
        param.put("projectCode", projectCode);
        param.put("projectName", projectName);
        param.put("riskFlag", riskFlag);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "projectRisk/list", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ProjectRiskSettingDto>>>() {
        });
    }

    @ApiOperation(value = "新增重大风险项目")
    @PostMapping("save")
    public Response saveProjectRisk(@RequestBody ProjectRiskSettingDto dto) {
        String url = String.format("%sprojectRisk/save", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Long>>() {
        });
    }

    @ApiOperation(value = "删除重大风险项目")
    @DeleteMapping("delete")
    public Response deleteProductFund(@ApiParam("重大风险项目id") @RequestParam Long id) {
        Map<String, Object> param = new HashMap<>(1);
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "projectRisk/delete", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Integer> response = JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
        return response;
    }

    @ApiOperation(value = "根据重大风险项目id查询重大风险项目详情")
    @GetMapping("getById")
    public Response getById(@ApiParam("重大风险项目id") @RequestParam Long id) {
        Map<String, Object> param = new HashMap<>(1);
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "projectRisk/getById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<ProjectRiskSettingDto> response = JSON.parseObject(res, new TypeReference<DataResponse<ProjectRiskSettingDto>>() {
        });
        return response;
    }

}
