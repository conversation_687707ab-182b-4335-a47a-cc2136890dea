package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.AreaUnitRelDto;
import com.midea.pam.common.basedata.entity.DepartmentDirectorRel;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("省市与销售部门关系")
@RestController
@RequestMapping("areaUnitRel")
public class AreaUnitRelController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation("根据市的地区编码查询")
    @GetMapping("findByAreaCode")
    public Response findByAreaCode(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                   @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize,
                                   @RequestParam(required = false) @ApiParam(value = "areaCode") String areaCode,
                                   @RequestParam(required = false) @ApiParam(value = "province") String province,
                                   @RequestParam(required = false) @ApiParam(value = "city") String city,
                                   @RequestParam(required = false) @ApiParam(value = "department") String department,
                                   @RequestParam(required = false) @ApiParam(value = "salesName") String salesName) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("areaCode", areaCode);
        param.put("province", province);
        param.put("city", city);
        param.put("department", department);
        param.put("salesName", salesName);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/areaUnitRel/findByAreaCode", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<AreaUnitRelDto>>>() {
        });
    }

    @ApiOperation("新增省市与销售部门关系")
    @PostMapping("add")
    public Response add(@RequestBody AreaUnitRelDto areaUnitRelDto) {
        String url = String.format("%sareaUnitRel/add", ModelsEnum.BASEDATA.getBaseUrl());
        String res = restTemplate.postForEntity(url, areaUnitRelDto, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<AreaUnitRelDto>>(){});
    }

    @ApiOperation("删除")
    @GetMapping("delete")
    public Response add(@RequestParam Long id){
        final Map<String,Object> param = new HashMap<>();
        param.put("id",id);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(),"/areaUnitRel/delete",param);
        DataResponse<String> response = Response.dataResponse();
        return response.setData(restTemplate.getForObject(url, String.class));
    }

    @ApiOperation("批量更新")
    @PostMapping("update")
    public Response update(@RequestBody AreaUnitRelDto areaUnitRelDto){
        String url = String.format("%sareaUnitRel/update", ModelsEnum.BASEDATA.getBaseUrl());
        DataResponse<String> response = Response.dataResponse();
        return response.setData(restTemplate.postForEntity(url, areaUnitRelDto, String.class).getBody());
    }

    @ApiOperation("维护区域总监")
    @PostMapping("updateDirector")
    public Response updateDirector(@RequestBody List<DepartmentDirectorRel> directorRelList){
        final String url = String.format("%sareaUnitRel/updateDirector", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, directorRelList, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation("查看区域总监")
    @GetMapping("selectDirector")
    public Response selectDirector(@RequestParam(required = false) @ApiParam(value = "当前使用单位id") Long unitId,
                                   @RequestParam(required = false) @ApiParam(value = "销售部门id") Long departmentId){
        Map<String,Object> param = new HashedMap();
        param.put("unitId",unitId);
        param.put("departmentId",departmentId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(),"/areaUnitRel/selectDirector",param);
        String res = restTemplate.getForEntity(url,String.class).getBody();
        return JSON.parseObject(res,new TypeReference<DataResponse<List<DepartmentDirectorRel>>>(){});
    }
}

