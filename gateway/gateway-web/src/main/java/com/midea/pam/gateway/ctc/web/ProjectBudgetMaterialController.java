package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.crm.excelVo.ProjectBudgetMaterialImportExcelVO;
import com.midea.pam.common.ctc.dto.ProjectBudgetMaterialDto;
import com.midea.pam.common.ctc.excelVo.ProjectBudgetMaterialImportResponseExcelVO;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.gateway.common.utils.FileUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: pam
 * @description: ProjectBudgetMaterialsController
 * @author: ex_liws6
 * @create: 2022-3-4
 **/
@RestController
@RequestMapping("projectBudgetMaterial")
@Api("项目物料预算")
public class ProjectBudgetMaterialController {

    private static final Logger logger = LoggerFactory.getLogger(ProjectBudgetMaterialController.class);

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "导入物料预算")
    @PostMapping("importBatchFromExcel")
    public com.midea.pam.common.base.Response importMaterialCost(@ApiParam("附件") @RequestParam(value = "file") MultipartFile file,
                                                                 @ApiParam("前端物料预算") @RequestParam String treeNodes,
                                                                 HttpServletResponse response) throws IOException {
        List<ProjectBudgetMaterialImportExcelVO> importExcelVos;
        try {
            importExcelVos = FileUtil.importExcel(file, ProjectBudgetMaterialImportExcelVO.class, 2, 0);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new BizException(ErrorCode.SYSTEM_FILE_ERROR);
        }
        Asserts.isTrue(org.apache.commons.lang3.StringUtils.isNotBlank(treeNodes), ErrorCode.CTC_MATERIAL_ADJUST_DETAIL_NOT_NULL);

        //  Excel会读取空行，将空行过滤
        Iterator<ProjectBudgetMaterialImportExcelVO> it = importExcelVos.iterator();
        while (it.hasNext()) {
            ProjectBudgetMaterialImportExcelVO excelVO = it.next();
            if (excelVO.isNull()) {
                it.remove();
            }
        }

        ProjectBudgetMaterialDto treeNode;
        try {
            treeNode = JSON.parseObject(treeNodes, ProjectBudgetMaterialDto.class);
        } catch (Exception e) {
            throw new BizException(ErrorCode.CTC_MATERIAL_ADJUST_DETAIL_NOT_TRUE);
        }

        ProjectBudgetMaterialDto budgetMaterialDto = new ProjectBudgetMaterialDto();
        budgetMaterialDto.setImportExcelVos(importExcelVos); // 要导入的数据
        budgetMaterialDto.setTreeNodes(treeNode); // 前端物料

        final String url = String.format("%sprojectBudgetMaterial/importBatchFromExcel", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, budgetMaterialDto, String.class).getBody();

        com.midea.pam.common.base.DataResponse<ProjectBudgetMaterialImportResponseExcelVO> dataResponse =
                JSON.parseObject(res, new TypeReference<DataResponse<ProjectBudgetMaterialImportResponseExcelVO>>() {
                });
        ProjectBudgetMaterialImportResponseExcelVO data = dataResponse.getData();
        if (Objects.nonNull(data) && ListUtils.isNotEmpty(data.getErrorMsg())) {
            XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
            Sheet sheetAt = workbook.getSheetAt(0);
            Row row = sheetAt.getRow(1);
            Cell cell = row.getCell(0);
            String errorMsg = data.getErrorMsg().stream().collect(Collectors.joining("； "));
            XSSFRichTextString textString = new XSSFRichTextString("批量导入校验信息：" + errorMsg);
            XSSFCellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cellStyle.setWrapText(true);
            cell.setCellStyle(cellStyle);
            XSSFFont font = workbook.createFont();
            font.setColor(HSSFColor.RED.index);
            textString.applyFont("批量导入校验信息：".length(), textString.length(), font);
            cell.setCellValue(textString);
            ExportExcelUtil.downLoadExcel("错误日志_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xlsx", response, workbook);
        }
        return dataResponse;
    }
}
