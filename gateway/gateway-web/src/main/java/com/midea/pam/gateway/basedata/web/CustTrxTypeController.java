package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.query.CustTrxTypeQuery;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.common.util.Utils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.PageResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Api("应收事务处理类型")
@RequestMapping({"custTrxType"})
public class CustTrxTypeController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "应收事务处理类型页面分页查询")
    @PostMapping("selectPage")
    public Response selectPage(@RequestBody(required = false)CustTrxTypeQuery query) throws Exception {
        String url = String.format("%scustTrxType/selectPage",ModelsEnum.BASEDATA.getBaseUrl());
        String res = cleanStr(restTemplate.postForObject(url, query, String.class));
        PageInfo<Map<String, Object>> data = JSON.parseObject(res, new TypeReference<PageInfo<Map<String, Object>> >(){});
        PageResponse<Map<String, Object>> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation(value = "事务处理类型列表")
    @GetMapping("custTrxTypeList")
    public Response custTrxTypeList() throws Exception {
        String url = String.format("%scustTrxType/custTrxTypeList", ModelsEnum.BASEDATA.getBaseUrl());
        String res = cleanStr(restTemplate.getForObject(url, String.class));
        List<Map<String, Object>> dataList = Utils.jsonStr2List(res);
        DataResponse<List<Map<String, Object>>> response = Response.dataResponse();
        return response.setData(dataList);
    }

    @ApiOperation(value = "事务处理分类列表")
    @GetMapping("trxTypeList")
    public Response trxTypeList() throws Exception {
        String url = String.format("%scustTrxType/trxTypeList", ModelsEnum.BASEDATA.getBaseUrl());
        String res = cleanStr(restTemplate.getForObject(url, String.class));
        List<Map<String, Object>> dataList = Utils.jsonStr2List(res);
        DataResponse<List<Map<String, Object>>> response = Response.dataResponse();
        return response.setData(dataList);
    }

    @ApiOperation(value = "应收发票类型erp同步")
    @GetMapping("getCustTrxTypeFromErp")
    public Response getCustTrxTypeFromErp(@RequestParam(required = false) String lastUpdateDate){
        final Map<String, Object> param = new HashMap<>();
        param.put("lastUpdateDate", lastUpdateDate);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "custTrxType/getCustTrxTypeFromErp",param);
        restTemplate.getForObject(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        response.setData("0");
        return response;
    }

    @ApiOperation(value = "事务处理类型列表")
    @GetMapping("getListByOu")
    public Response getListByOu() throws Exception {
        String url = String.format("%scustTrxType/getListByOu", ModelsEnum.BASEDATA.getBaseUrl());
        String res = cleanStr(restTemplate.getForObject(url, String.class));
        List<Map<String, Object>> dataList = Utils.jsonStr2List(res);
        DataResponse<List<Map<String, Object>>> response = Response.dataResponse();
        return response.setData(dataList);
    }
}
