package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.mcomponent.core.util.BeanUtils;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.StorageDto;
import com.midea.pam.common.basedata.entity.Storage;
import com.midea.pam.common.basedata.entity.StorageInventory;
import com.midea.pam.common.basedata.excelVo.StorageExportVo;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.MaterialGetStatDto;
import com.midea.pam.common.ctc.dto.SdpTradeResultResponseEleDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.PublicUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.utils.ExportExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Api("子库库存量查询")
@RestController
@RequestMapping("storage")
public class StorageController extends ControllerHelper {
    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = " 子库分页查詢", response = StorageDto.class)
    @GetMapping("page")
    public Response page(@RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize,
                         @RequestParam(required = false) final Long organizationId,
                         @RequestParam(required = false) final String subinventoryCode,
                         @RequestParam(required = false) final String locator,
                         @RequestParam(required = false) final String segment1,
                         @RequestParam(required = false) final String description,
                         @RequestParam(required = false) final String subinventoryDescription,
                         @RequestParam(required = false) final String locatorDescription,
                         @RequestParam(required = false) final String organizationCode,
                         @RequestParam(required = false) final String organizationName,
                         @RequestParam(required = false) final String name,
                         @RequestParam(required = false) final String model,
                         @RequestParam(required = false) final String brand) {
        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        param.put("organizationId", organizationId);
        param.put("subinventoryCode", subinventoryCode);
        param.put("locator", locator);
        param.put("segment1", segment1);
        param.put("description", description);
        param.put("subinventoryDescription", subinventoryDescription);
        param.put("locatorDescription", locatorDescription);
        param.put("organizationCode", organizationCode);
        param.put("organizationName", organizationName);
        param.put("name", name);
        param.put("model", model);
        param.put("brand", brand);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/storage/page", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<StorageDto>>>() {
        });

    }

    @ApiOperation(value = "根据物料成本分页", response = StorageDto.class)
    @GetMapping("pageByItemCost")
    public Response pageByItemCost(
            @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") final Integer pageSize,
            @RequestParam(required = false) final Long organizationId,
            @RequestParam(required = false) final String subinventoryCode,
            @RequestParam(required = false) final String locator,
            @RequestParam(required = false) final String fuzzyLike,
            @RequestParam(required = false) final Integer isKanban) {
        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        param.put("organizationId", organizationId);
        param.put("subinventoryCode", subinventoryCode);
        param.put("locator", locator);
        param.put("fuzzyLike", fuzzyLike);
        param.put("isKanban", isKanban);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/storage/pageByItemCost", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<StorageDto>>>() {
        });
    }

    @ApiOperation(value = "根据项目id查询工单行详情")
    @GetMapping("getTicketTasksDetailByProjectId")
    public Response getTicketTasksDetailByProjectId(@RequestParam(required = true) Long projectId,
                                                    @RequestParam(required = true) Long organizationId,
                                                    @RequestParam(required = true) String subinventoryCode,
                                                    @RequestParam(required = false) Integer isKanban) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        param.put("organizationId", organizationId);
        param.put("subinventoryCode", subinventoryCode);
        param.put("isKanban", isKanban);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/storage/getTicketTasksDetailByProjectId", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        String res = cleanStr(responseEntity.getBody());
        return JSON.parseObject(res, new TypeReference<DataResponse<List<StorageDto>>>() {
        });
    }

    @ApiOperation(value = "查询货位", response = Storage.class)
    @GetMapping("queryLocator")
    public Response queryLocator(
            @RequestParam(required = false) final Long organizationId,
            @RequestParam(required = false) final String subinventoryCode,
            @RequestParam(required = false) final String locator) {
        final Map<String, Object> param = new HashMap<>();
        param.put("organizationId", organizationId);
        param.put("subinventoryCode", subinventoryCode);
        param.put("locator", locator);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/storage/queryLocator", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<StorageDto>>>() {
        });
    }

    @ApiOperation(value = "查询字库信息", response = Storage.class)
    @GetMapping("queryInventory")
    public Response queryInventory() {
        final Map<String, Object> param = new HashMap<>();
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/storage/queryInventory", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<StorageInventory>>>() {
        });
    }

    @ApiOperation(value = "统计已制单未领用量", response = StorageDto.class)
    @PostMapping("statForGet")
    public Response statForGet(@RequestBody @ApiParam(name = "MaterialGetStatDto", value = "统计信息") MaterialGetStatDto[] dtos) {
        final String url = String.format("%sstorage/statForGet", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dtos, String.class);
        DataResponse<List<StorageDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<StorageDto>>>() {
        });
        return response;
    }

    //该接口已弃用
    @ApiOperation(value = "导出库存")
    @GetMapping("exportStorageList")
    public void exportLeadList(HttpServletResponse response, @RequestParam(required = false) final Long organizationId,
                               @RequestParam(required = false) final String subinventoryCode,
                               @RequestParam(required = false) final String locator,
                               @RequestParam(required = false) final String segment1,
                               @RequestParam(required = false) final String description,
                               @RequestParam(required = false) final String subinventoryDescription,
                               @RequestParam(required = false) final String locatorDescription,
                               @RequestParam(required = false) final String organizationCode,
                               @RequestParam(required = false) final String organizationName) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("organizationId", organizationId);
        param.put("subinventoryCode", subinventoryCode);
        param.put("locator", locator);
        param.put("segment1", segment1);
        param.put("description", description);
        param.put("subinventoryDescription", subinventoryDescription);
        param.put("locatorDescription", locatorDescription);
        param.put("organizationCode", organizationCode);
        param.put("organizationName", organizationName);

        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/storage/getAll", param);
        DataResponse<List<StorageDto>> dataResponse = JSON.parseObject(restTemplate.getForEntity(url, String.class).getBody(), new TypeReference<DataResponse<List<StorageDto>>>() {
        });
        List<StorageDto> dataList = dataResponse.getData();
        if (PublicUtil.isEmpty(dataList)) {
            throw new Exception("没有数据");
        }
        List<StorageExportVo> dataVoList = dataList.stream().map(storage -> {
            StorageExportVo vo = new StorageExportVo();
            BeanUtils.copyProperties(storage, vo);
            vo.setTransactionQuantity(storage.getTransactionQuantity().stripTrailingZeros().toPlainString());
            vo.setUnfetchQuantity(storage.getUnfetchQuantity().stripTrailingZeros().toPlainString());
            return vo;
        }).collect(Collectors.toList());
        //导出操作
        ExportExcelUtil.exportExcel(dataVoList, null, "Sheet1", StorageExportVo.class, "库存.xls", response);
    }

    @ApiOperation(value = "根据当前用户的库存组织手动同步库存现有量")
    @GetMapping("asynFromErp")
    public Response asynFromErp(@RequestParam Boolean isOperation) {
        final Map<String, Object> param = new HashMap<>();
        param.put("isOperation", isOperation);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/storage/asynFromErp", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "选择物料", response = StorageDto.class)
    @GetMapping("queryMaterialsSelected")
    public Response queryMaterialsSelected(@RequestParam(required = false) final Long organizationId,
                                           @RequestParam(required = false) final String subinventoryCode,
                                           @RequestParam(required = false) final String locator,
                                           @RequestParam(required = false) final String fuzzyLike,
                                           @RequestParam(required = false) final Integer isKanban) {
        final Map<String, Object> param = new HashMap<>();
        param.put("organizationId", organizationId);
        param.put("subinventoryCode", subinventoryCode);
        param.put("locator", locator);
        param.put("fuzzyLike", fuzzyLike);
        param.put("isKanban", isKanban);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/storage/queryMaterialsSelected", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<StorageDto>>>() {
        });
    }

    @ApiOperation(value = "ERP-PAM-036测试接口")
    @GetMapping("sdpServiceTest")
    public Response sdpServiceTest(@RequestParam(required = false) @ApiParam("库存组织ID") Long organizationId,
                                   @RequestParam(required = false) @ApiParam("子库代码") String secondaryInventoryName,
                                   @RequestParam(required = false) @ApiParam("货位") String locator) {
        final Map<String, Object> param = new HashMap<>();
        param.put("organizationId", organizationId);
        param.put("secondaryInventoryName", secondaryInventoryName);
        param.put("locator", locator);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "storage/sdpServiceTest", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        String res = cleanStr(responseEntity.getBody());
        return JSON.parseObject(res, new TypeReference<DataResponse<List<SdpTradeResultResponseEleDto>>>() {
        });
    }

}
