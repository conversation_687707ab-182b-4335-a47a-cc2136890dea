package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.midea.pam.common.basedata.dto.CustomViewConfigDTO;
import com.midea.pam.common.basedata.dto.RoleGrantDto;
import com.midea.pam.common.basedata.dto.UserDetailsDto;
import com.midea.pam.common.basedata.dto.UserInfoDto;
import com.midea.pam.common.basedata.dto.UserUnitRelDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.statistics.dto.ReportGroupGrantUserDTO;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@Api("资源管理")
@RequestMapping({"resource/user"})
public class ResourceController extends ControllerHelper {

    @Autowired
    private RestTemplate restTemplate;

    @ApiOperation("查询用户列表分页")
    @GetMapping("list")
    public Response list(@RequestParam(required = false, defaultValue = "1") Integer start,
                         @RequestParam(required = false, defaultValue = "10") Integer limit,
                         @RequestParam(required = false) String nameOrAccount) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("start", start);
        param.put("limit", limit);
        param.put("nameOrAccount", nameOrAccount);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/resource/user/list", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<UserDetailsDto>>>() {
        });
    }

    /**
     * 根据名称或者部门ID查询用户信息
     */
    @ApiOperation("根据名称或者部门ID查询用户信息")
    @GetMapping("getListUserInfo")
    public Response listInfo(@RequestParam(required = false, defaultValue = "1") Integer start,
                             @RequestParam(required = false, defaultValue = "10") Integer limit,
                             @RequestParam(name = "orgId", required = false) String orgId,
                             @RequestParam(name = "orgName", required = false) String orgName,
                             @RequestParam(name = "name", required = false) String name,
                             @RequestParam(name = "username", required = false) String username,
                             @RequestParam(name = "mipName", required = false) String mipName,
                             @RequestParam(required = false) Long userId) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("start", start);
        param.put("limit", limit);
        param.put("orgId", orgId);
        param.put("name", name);
        param.put("username", username);
        param.put("mipName", mipName);
        param.put("userId", userId);
        param.put("orgName", orgName);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/resource/user/getListUserInfo", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<UserInfoDto>>>() {
        });
    }

    /**
     * 保存用户自定义视图
     *
     * @param customViewConfigDTO 自定义视图DTO
     * @return
     */
    @ApiOperation(value = "保存用户自定义视图", response = CustomViewConfigDTO.class)
    @PostMapping("/saveCustomView")
    public Response saveCustomView(@RequestBody CustomViewConfigDTO customViewConfigDTO) {
        String res = restTemplate.postForEntity(ModelsEnum.BASEDATA.getBaseUrl() + "/resource/user/saveCustomView", customViewConfigDTO, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse>() {
        });
    }

    /**
     * 查询用户自定义视图
     *
     * @return
     */
    @ApiOperation("查询用户自定义视图")
    @GetMapping("/queryCustomView")
    public Response queryCustomView() {
        String res = restTemplate.getForEntity(ModelsEnum.BASEDATA.getBaseUrl() + "/resource/user/queryCustomView", String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse>() {
        });
    }

    @ApiOperation("权限即将失效条数")
    @GetMapping("temporaryPermission")
    public Response temporaryPermission(@RequestParam Long userId) {
        DataResponse<Integer> response = Response.dataResponse();
        Integer noPermissionCount = getNoPermissionCount(userId);
        return response.setData(noPermissionCount);
    }

    private Integer getNoPermissionCount(Long userId) {
        int roleCount = 0;
        int unitRelCount = 0;
        int reportGroupCount = 0;
        //统计权限失效条数
        List<RoleGrantDto> roles = getRoleList(userId);
        if (CollectionUtils.isNotEmpty(roles)) {
            roleCount = Math.toIntExact(roles.stream()
                    .filter(e -> Objects.nonNull(e.getEndDate()) && isExpirationDateWithin7Days(e.getEndDate()))
                    .count());
        }
        List<UserUnitRelDto> userUnitRelDtos = userUnitList(userId);
        if (CollectionUtils.isNotEmpty(userUnitRelDtos)) {
            unitRelCount = Math.toIntExact(userUnitRelDtos.stream()
                    .filter(e -> Objects.nonNull(e.getEndDate()) && isExpirationDateWithin7Days(e.getEndDate()))
                    .count());
        }
        List<ReportGroupGrantUserDTO> reportGroupGrantUserDTOS = reportGroup(userId);
        if (CollectionUtils.isNotEmpty(reportGroupGrantUserDTOS)) {
            reportGroupCount = Math.toIntExact(reportGroupGrantUserDTOS.stream()
                    .filter(e -> Objects.nonNull(e.getEndDate()) && isExpirationDateWithin7Days(e.getEndDate()))
                    .count());
        }
        return roleCount + unitRelCount + reportGroupCount;
    }

    private List<RoleGrantDto> getRoleList(Long userId) {
        // 查询当前用户所有角色
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", 1);
        param.put("pageSize", 1000);
        param.put("userId", userId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/grant/roleInfo/getlistByUserId", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<RoleGrantDto>> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<RoleGrantDto>>>() {
        });
        if (dataResponse != null && dataResponse.getData() != null && ListUtils.isNotEmpty(dataResponse.getData().getList())) {
            return dataResponse.getData().getList();
        }
        return Lists.newArrayList();
    }

    private List<UserUnitRelDto> userUnitList(Long userId) {
        Map<String, Object> param = new HashMap<>();
        param.put("userId", userId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "userUnit/userUnitList", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<UserUnitRelDto>> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<List<UserUnitRelDto>>>() {
        });
        if (dataResponse != null && ListUtils.isNotEmpty(dataResponse.getData())) {
            return dataResponse.getData();
        }
        return Lists.newArrayList();
    }

    private List<ReportGroupGrantUserDTO> reportGroup(Long userId) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("userId", userId);
        paramMap.put("pageNum", 1);
        paramMap.put("pageSize", 999);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/report/reportGroup/user/page", paramMap);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<ReportGroupGrantUserDTO>> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<ReportGroupGrantUserDTO>>>() {
        });
        if (dataResponse != null && dataResponse.getData() != null && ListUtils.isNotEmpty(dataResponse.getData().getList())) {
            return dataResponse.getData().getList();
        }
        return Lists.newArrayList();
    }

    /**
     * 判断日期 0=<失效日期-now()=<7,则返回true
     *
     * @param endDate
     * @return
     */
    public static boolean isExpirationDateWithin7Days(Date endDate) {
        LocalDate expirationDate = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate now = LocalDate.now();
        return ChronoUnit.DAYS.between(now, expirationDate) >= 0 && ChronoUnit.DAYS.between(now, expirationDate) <= 7;
    }


}
