package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.ProjectMilepostDto;
import com.midea.pam.common.ctc.entity.ProjectReopenHeader;
import com.midea.pam.common.ctc.excelVo.MilepostDesignPlanDetailPurchaseExportVo;
import com.midea.pam.common.ctc.vo.MilepostDesignPlanDetailApprovedVO;
import com.midea.pam.common.enums.MilepostStatus;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.ProcessTemplate;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.enums.projectReopenHeaderStatusEnum;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.FormInstanceExample;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.utils.BeanConverter;
import com.midea.pam.gateway.service.FormInstanceService;
import com.midea.pam.gateway.service.OssService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.http.entity.ContentType;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.math.BigDecimal;
import java.util.*;

/**
 * Description
 * Created by lintx
 * Date 2021/12/22 20:23
 */
@Api("项目重新打开项目审批回调")
@RestController
@RequestMapping("projectReopenCallBack")
public class ProjectReopenCallBackController extends ControllerHelper {

    private static final Logger logger = LoggerFactory.getLogger(ProjectReopenCallBackController.class);

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private OssService ossService;

    @Resource
    private FormInstanceService formInstanceService;

    /**
     * 流程审批中回调方法.
     *
     * @param formInstanceId 表单实例id
     */
    @ApiOperation(value = "审批中")
    @PutMapping("approvaling/skipSecurityInterceptor")
    public Response approvaling(@RequestParam Long formInstanceId) {
        //根据formInstanceId查询出projectId
        final Map<String, Object> reopenHeaderParam = new HashMap<>();
        reopenHeaderParam.put("projectReopenHeaderId",formInstanceId);
        String headerUrl = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectReopen/getProjectIdByReopenHeader", reopenHeaderParam);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(headerUrl, String.class);
        DataResponse<Long> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Long>>() {
        });
        Long projectId = response.getData();

        // 取详细设计方案发布(非采购)信息
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        String url1 = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan/getDetailByProjectId", param);
        ResponseEntity<String> responseEntity1 = restTemplate.getForEntity(url1, String.class);
        // 暂用MilepostDesignPlanDetailApprovedVO这个属性
        DataResponse<MilepostDesignPlanDetailApprovedVO> response1 = JSON.parseObject(responseEntity1.getBody(), new TypeReference<DataResponse<MilepostDesignPlanDetailApprovedVO>>() {
        });
        JSONObject jsonObject = new JSONObject();
        String fileId = "";
        String fileName = "";
        String fileSize = "";
        if (response1 != null && response1.getData() != null) {
            //生成附件
            MultipartFile multipartFile = createAnnex(response1.getData());
            try {
                if (multipartFile != null) {
                    JSONArray result = ossService.upload(multipartFile);
                    if (!ObjectUtils.isEmpty(result)) {
                        jsonObject = result.getJSONObject(0);
                        //提交审批
                        fileId = jsonObject.getString("fileId");
                        fileName = jsonObject.getString("fileName");
                        fileSize = jsonObject.getString("fileSize");
                    }
                }
            } catch (Exception e) {
                logger.info("详细设计生成文件，上传失败:", e);
            }
        }
        final String url = String.format("%sprojectReopenCallBack/approvaling/skipSecurityInterceptor?formInstanceId=%s&fileId=%s&fileName=%s&fileSize=%s"
                , ModelsEnum.CTC.getBaseUrl(), projectId, fileId, fileName, fileSize);
        restTemplate.put(url, String.class);

        //修改变更信息状态
        final String url2 = String.format("%sprojectReopen/updateReopenHeaderStatus", ModelsEnum.CTC.getBaseUrl());
        ProjectReopenHeader projectReopenHeader = new ProjectReopenHeader();
        projectReopenHeader.setId(formInstanceId);
        projectReopenHeader.setStatus(projectReopenHeaderStatusEnum.PENDING.getCode());
        restTemplate.postForEntity(url2, projectReopenHeader, String.class);
        return Response.dataResponse();
    }

    //根据数据产生Excel
    public MultipartFile createAnnex(MilepostDesignPlanDetailApprovedVO milepostDesignPlanDetailApprovedVO) {
        // 附件名称为：项目编号+“详设发布明细”+“_”+YYYYMMDD MM:HH:SS;举例：IA19108详设发布明细_20191127 14:09:01.xlsx
        MultipartFile multipartFile = null;
        String filePath = milepostDesignPlanDetailApprovedVO.getProjectCode() + "详设发布明细_" + DateUtils.format(new Date(), "yyyyMMdd HH:mm:ss") + ".xls";
        try {
            ExportParams exportParams = new ExportParams(filePath, "Sheet1");
            List<MilepostDesignPlanDetailDto> designPlanDetailDtos = milepostDesignPlanDetailApprovedVO.getDesignPlanDetailDtos();
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(designPlanDetailDtos)) {
//                List<MilepostDesignPlanDetailApprovedExportVo> excelVos = BeanConverter.copy(designPlanDetailDtos, MilepostDesignPlanDetailApprovedExportVo.class);
                List<MilepostDesignPlanDetailPurchaseExportVo> excelVos = this.buildExcelVos(designPlanDetailDtos);
                Workbook workbook = ExcelExportUtil.exportExcel(exportParams, MilepostDesignPlanDetailPurchaseExportVo.class, excelVos);
                File pdfFile = new File("/apps/pam/gateway/file/" + filePath);
                try (
                        OutputStream out = new FileOutputStream("/apps/pam/gateway/file/" + filePath);
                        FileInputStream fileInputStream = new FileInputStream(pdfFile);
                ) {
                    workbook.write(out);
                    multipartFile = new MockMultipartFile(pdfFile.getName(), pdfFile.getName(),
                            ContentType.APPLICATION_OCTET_STREAM.toString(), fileInputStream);
                } catch (IOException e) {
                    logger.info("详细设计变更生成文件，上传失败:", e);
                }

            }
        } catch (Exception e) {
            logger.info("详细设计生成文件，上传失败:", e);
        }
        return multipartFile;
    }


    // 最多做三层数据封装
    private List<MilepostDesignPlanDetailPurchaseExportVo> buildExcelVos(List<MilepostDesignPlanDetailDto> designPlanDetailDtos) {
        List<MilepostDesignPlanDetailPurchaseExportVo> milepostDesignPlanDetailPurchaseExportVos = new ArrayList<>();
        int i = 0;
        for (MilepostDesignPlanDetailDto designPlanDetailDto : designPlanDetailDtos) {
            i = i + 1;
            MilepostDesignPlanDetailPurchaseExportVo milepostDesignPlanDetailPurchaseExportVo = new MilepostDesignPlanDetailPurchaseExportVo();
            BeanConverter.copy(designPlanDetailDto, milepostDesignPlanDetailPurchaseExportVo);
            milepostDesignPlanDetailPurchaseExportVo.setSerialNumber(String.valueOf(i));
            //去掉小数点后面的0
            milepostDesignPlanDetailPurchaseExportVo.setNumber(designPlanDetailDto.getNumber() == null ? BigDecimal.ZERO : designPlanDetailDto.getNumber().stripTrailingZeros());
            milepostDesignPlanDetailPurchaseExportVos.add(milepostDesignPlanDetailPurchaseExportVo);
            List<MilepostDesignPlanDetailDto> firstSonDtos = designPlanDetailDto.getSonDtos();  //子模组数据
            int j = 0;
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(firstSonDtos)) {
                for (MilepostDesignPlanDetailDto firstSonDto : firstSonDtos) {
                    j = j + 1;
                    MilepostDesignPlanDetailPurchaseExportVo firstSonExportVo = new MilepostDesignPlanDetailPurchaseExportVo();
                    BeanConverter.copy(firstSonDto, firstSonExportVo);
                    firstSonExportVo.setSerialNumber(String.valueOf(i) + "." + String.valueOf(j));
                    firstSonExportVo.setNumber(firstSonDto.getNumber() == null ? BigDecimal.ZERO : firstSonDto.getNumber().stripTrailingZeros());
                    milepostDesignPlanDetailPurchaseExportVos.add(firstSonExportVo);
                    List<MilepostDesignPlanDetailDto> SecSonDtos = firstSonDto.getSonDtos();  //二级子模组数据
                    int k = 0;
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(SecSonDtos)) {
                        for (MilepostDesignPlanDetailDto secSonDto : SecSonDtos) {
                            k = k + 1;
                            MilepostDesignPlanDetailPurchaseExportVo SecSonExportVo = new MilepostDesignPlanDetailPurchaseExportVo();
                            BeanConverter.copy(secSonDto, SecSonExportVo);
                            SecSonExportVo.setSerialNumber(String.valueOf(i) + "." + String.valueOf(j) + "." + String.valueOf(k));
                            SecSonExportVo.setNumber(secSonDto.getNumber() == null ? BigDecimal.ZERO : secSonDto.getNumber().stripTrailingZeros());
                            milepostDesignPlanDetailPurchaseExportVos.add(SecSonExportVo);
                        }
                    }
                }
            }
        }
        return milepostDesignPlanDetailPurchaseExportVos;
    }


    /**
     * 流程审批完成回调方法。
     *
     * @param formInstanceId 表单实例id
     */
    @ApiOperation(value = "审批通过")
    @PutMapping("approved/skipSecurityInterceptor")
    public Response approved(@RequestParam Long formInstanceId, @RequestParam Long companyId) {
        //根据formInstanceId查询出projectId
        final Map<String, Object> reopenHeaderParam = new HashMap<>();
        reopenHeaderParam.put("projectReopenHeaderId",formInstanceId);
        String headerUrl = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectReopen/getProjectIdByReopenHeader", reopenHeaderParam);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(headerUrl, String.class);
        DataResponse<Long> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Long>>() {
        });
        Long projectId = response.getData();

        // 结项的项目：最后一个“通过”的里程碑交付的审批流删除
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        final String url1 = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "projectReopen/getBaseInfoByProjectId", param);
        String res = restTemplate.getForEntity(url1, String.class).getBody();
        DataResponse<ProjectDto> response1 = JSON.parseObject(res, new TypeReference<DataResponse<ProjectDto>>() {
        });

        // 里程碑
        final String url2 = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectDeliveries/selectListWithDetail", param);
        String res2 = restTemplate.getForEntity(url2, String.class).getBody();
        DataResponse<List<ProjectMilepostDto>> listDataResponse = JSON.parseObject(res2, new TypeReference<DataResponse<List<ProjectMilepostDto>>>() {
        });
        List<ProjectMilepostDto> milepostDtos = listDataResponse.getData();

        ProjectDto projectDto = response1.getData();
        if (projectDto.getStatus() == ProjectStatus.CLOSE.getCode()) {
            this.updateFormInstance(milepostDtos);
        }
        // 审批通过,把流程实例删掉 以便重新打开审批
        //this.updateProjectFormInstance(formInstanceId);
        // 判斷是否終止流程
        FormInstanceExample conditionTerminiation = new FormInstanceExample();
        conditionTerminiation.createCriteria().andFormUrlEqualTo(ProcessTemplate.PROJECT_TERMINATE_APP.getCode())
                .andFormInstanceIdEqualTo(projectId)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<FormInstance> formInstances = formInstanceService.selectByExample(conditionTerminiation);
        if(ListUtils.isNotEmpty(formInstances)){
            formInstances.sort(Comparator.comparing(FormInstance::getId).reversed());
            FormInstance formInstance = formInstances.get(0);
            formInstance.setDeletedFlag(Boolean.TRUE);
            logger.info("删除終止流程实例: {}", formInstance);
            formInstanceService.updateByPrimaryKey(formInstance);
        }
        final String url = String.format("%sprojectReopenCallBack/approved/skipSecurityInterceptor?formInstanceId=%s&companyId=%s", ModelsEnum.CTC.getBaseUrl(), projectId, companyId);
        restTemplate.put(url, String.class);

        //修改变更信息状态
        final String url3 = String.format("%sprojectReopen/updateReopenHeaderStatus", ModelsEnum.CTC.getBaseUrl());
        ProjectReopenHeader projectReopenHeader = new ProjectReopenHeader();
        projectReopenHeader.setId(formInstanceId);
        projectReopenHeader.setStatus(projectReopenHeaderStatusEnum.PASS_THROUGH.getCode());
        restTemplate.postForEntity(url3, projectReopenHeader, String.class);
        return Response.dataResponse();
    }

    private void updateFormInstance(List<ProjectMilepostDto> milepostDtos) {
        if (ListUtils.isNotEmpty(milepostDtos)) {
            // 最后一个“通过”的里程碑交付的审批流删除
            milepostDtos.sort(Comparator.comparing(ProjectMilepostDto::getOrderNum).reversed());
            ProjectMilepostDto milepostDto = milepostDtos.get(0);
            if (MilepostStatus.PASSED.getCode().equals(milepostDto.getStatus())) {
                FormInstanceExample condition = new FormInstanceExample();
                condition.createCriteria().andFormInstanceIdEqualTo(milepostDto.getId())
                        .andDeletedFlagEqualTo(Boolean.FALSE)
                        .andFormUrlEqualTo(ProcessTemplate.PROJECT_CLOSE_MILESTONE_APP.getCode());
                List<FormInstance> instances = formInstanceService.selectByExample(condition);
                if (instances.size() > 0) {
                    instances.sort(Comparator.comparing(FormInstance::getId).reversed());
                    FormInstance formInstance = instances.get(0);
                    formInstance.setDeletedFlag(Boolean.TRUE);
                    logger.info("删除流程实例: {}", formInstance);
                    formInstanceService.updateByPrimaryKey(formInstance);
                }
            }
        }
    }

    private void updateProjectFormInstance(Long projectId) {
        FormInstanceExample condition = new FormInstanceExample();
        condition.createCriteria().andFormInstanceIdEqualTo(projectId)
                .andDeletedFlagEqualTo(Boolean.FALSE)
                .andFormUrlEqualTo(ProcessTemplate.PROJECT_REOPEN_APP.getCode());
        List<FormInstance> instances = formInstanceService.selectByExample(condition);
        if (instances.size() > 0) {
            instances.sort(Comparator.comparing(FormInstance::getId).reversed());
            FormInstance formInstance = instances.get(0);
            formInstance.setDeletedFlag(Boolean.TRUE);
            logger.info("删除流程实例: {}", formInstance);
            formInstanceService.updateByPrimaryKey(formInstance);
        }
    }

    /**
     * 流程审批拒绝回调方法.
     *
     * @param formInstanceId 表单实例id
     */
    @ApiOperation(value = "驳回")
    @PutMapping("refused/skipSecurityInterceptor")
    public Response refused(@RequestParam Long formInstanceId) {
        //根据formInstanceId查询出projectId
        final Map<String, Object> reopenHeaderParam = new HashMap<>();
        reopenHeaderParam.put("projectReopenHeaderId",formInstanceId);
        String headerUrl = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectReopen/getProjectIdByReopenHeader", reopenHeaderParam);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(headerUrl, String.class);
        DataResponse<Long> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Long>>() {
        });
        Long projectId = response.getData();

        final String url = String.format("%sprojectReopenCallBack/refused/skipSecurityInterceptor?projectId=%s", ModelsEnum.CTC.getBaseUrl(), projectId);
        restTemplate.put(url, String.class);

        //修改变更信息状态
        final String url2 = String.format("%sprojectReopen/updateReopenHeaderStatus", ModelsEnum.CTC.getBaseUrl());
        ProjectReopenHeader projectReopenHeader = new ProjectReopenHeader();
        projectReopenHeader.setId(formInstanceId);
        projectReopenHeader.setStatus(projectReopenHeaderStatusEnum.REFUSE.getCode());
        restTemplate.postForEntity(url2, projectReopenHeader, String.class);
        return Response.dataResponse();
    }

    /**
     * 流程审批撤回方法.
     *
     * @param formInstanceId 表单实例id
     */
    @ApiOperation(value = "撤回")
    @PutMapping("return/skipSecurityInterceptor")
    public Response returned(@RequestParam Long formInstanceId) {
        //根据formInstanceId查询出projectId
        final Map<String, Object> reopenHeaderParam = new HashMap<>();
        reopenHeaderParam.put("projectReopenHeaderId",formInstanceId);
        String headerUrl = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectReopen/getProjectIdByReopenHeader", reopenHeaderParam);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(headerUrl, String.class);
        DataResponse<Long> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Long>>() {
        });
        Long projectId = response.getData();

        //this.updateProjectFormInstance(formInstanceId);
        final String url = String.format("%sprojectReopenCallBack/return/skipSecurityInterceptor?projectId=%s", ModelsEnum.CTC.getBaseUrl(), projectId);
        restTemplate.put(url, String.class);

        //修改变更信息状态
        final String url2 = String.format("%sprojectReopen/updateReopenHeaderStatus", ModelsEnum.CTC.getBaseUrl());
        ProjectReopenHeader projectReopenHeader = new ProjectReopenHeader();
        projectReopenHeader.setId(formInstanceId);
        projectReopenHeader.setStatus(projectReopenHeaderStatusEnum.RETURN.getCode());
        restTemplate.postForEntity(url2, projectReopenHeader, String.class);
        return Response.dataResponse();
    }

    /**
     * 流程审批废弃方法.
     *
     * @param formInstanceId 表单实例id
     */
    @ApiOperation(value = "废弃")
    @PutMapping("abandon/skipSecurityInterceptor")
    public Response abandon(@RequestParam Long formInstanceId) {
        //根据formInstanceId查询出projectId
        final Map<String, Object> reopenHeaderParam = new HashMap<>();
        reopenHeaderParam.put("projectReopenHeaderId",formInstanceId);
        String headerUrl = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectReopen/getProjectIdByReopenHeader", reopenHeaderParam);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(headerUrl, String.class);
        DataResponse<Long> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Long>>() {
        });
        Long projectId = response.getData();
        //this.updateProjectFormInstance(formInstanceId);
        final String url = String.format("%sprojectReopenCallBack/abandon/skipSecurityInterceptor?projectId=%s", ModelsEnum.CTC.getBaseUrl(), projectId);
        restTemplate.put(url, String.class);

        //修改变更信息状态
        final String url2 = String.format("%sprojectReopen/updateReopenHeaderStatus", ModelsEnum.CTC.getBaseUrl());
        ProjectReopenHeader projectReopenHeader = new ProjectReopenHeader();
        projectReopenHeader.setId(formInstanceId);
        projectReopenHeader.setStatus(projectReopenHeaderStatusEnum.DELETE.getCode());
        restTemplate.postForEntity(url2, projectReopenHeader, String.class);
        return Response.dataResponse();
    }

    /**
     * 流程审批删除方法.
     *
     * @param formInstanceId 表单实例id
     */
    @ApiOperation(value = "删除")
    @PutMapping("delete/skipSecurityInterceptor")
    public Response delete(@RequestParam Long formInstanceId) {
        //根据formInstanceId查询出projectId
        final Map<String, Object> reopenHeaderParam = new HashMap<>();
        reopenHeaderParam.put("projectReopenHeaderId",formInstanceId);
        String headerUrl = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectReopen/getProjectIdByReopenHeader", reopenHeaderParam);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(headerUrl, String.class);
        DataResponse<Long> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Long>>() {
        });
        Long projectId = response.getData();

        //this.updateProjectFormInstance(formInstanceId);
        final String url = String.format("%sprojectReopenCallBack/delete/skipSecurityInterceptor?projectId=%s", ModelsEnum.CTC.getBaseUrl(), projectId);
        restTemplate.put(url, String.class);

        //修改变更信息状态
        final String url2 = String.format("%sprojectReopen/updateReopenHeaderStatus", ModelsEnum.CTC.getBaseUrl());
        ProjectReopenHeader projectReopenHeader = new ProjectReopenHeader();
        projectReopenHeader.setId(formInstanceId);
        projectReopenHeader.setStatus(projectReopenHeaderStatusEnum.DELETE.getCode());
        restTemplate.postForEntity(url2, projectReopenHeader, String.class);
        return Response.dataResponse();
    }
}
