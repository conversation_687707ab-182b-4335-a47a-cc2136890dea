package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.ctc.dto.CarryoverBillAccountingDto;
import com.midea.pam.common.ctc.dto.CarryoverIncomeAccountingDTO;
import com.midea.pam.common.ctc.entity.CarryoverBillAccounting;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: pam
 * @description: CarryoverBillAccountingController
 * @author: gaojh1
 * @create: 2019-6-15 10:51
 **/
@Api("结转记账")
@RestController
@RequestMapping("ctc/carryoverBillAccounting")
public class CarryoverBillAccountingController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "结转记账查询")
    @GetMapping("list")
    public Response list(@RequestParam(required = false) final Long id,
                         @RequestParam(required = false) final Long carryoverBillId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("carryoverBillId", carryoverBillId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "ctc/carryoverBillAccounting/list", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<CarryoverBillAccounting>>>() {
        });
    }

    @ApiOperation(value = "收入结转记账查询")
    @GetMapping("incomeAccountingList")
    public Response incomeAccountingList(@RequestParam(required = true) final Long carryoverBillId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("carryoverBillId", carryoverBillId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "ctc/carryoverBillAccounting/incomeAccountingList", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<CarryoverBillAccounting>>>() {
        });
    }

    @ApiOperation(value = "客户收入结转记账查询")
    @GetMapping("queryIncomeAccounting")
    public Response queryIncomeAccounting(@RequestParam(required = true) final Long carryoverBillId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("carryoverBillId", carryoverBillId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "ctc/carryoverBillAccounting/queryIncomeAccounting", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<CarryoverIncomeAccountingDTO>>>() {
        });
    }

    @ApiOperation(value = "成本结转记账查询")
    @GetMapping("costAccountingList")
    public Response costAccountingList(@RequestParam(required = true) final Long carryoverBillId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("carryoverBillId", carryoverBillId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "ctc/carryoverBillAccounting/costAccountingList", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<CarryoverBillAccountingDto>>>() {
        });
    }

    @ApiOperation(value = "收入成本入账推送ERP")
    @GetMapping("/pushToErp")
    public void pushToErp(HttpServletResponse response, @RequestParam(required = false) Long id) {
    }

    @ApiOperation(value = "saveTest")
    @GetMapping("saveTest")
    public Response saveTest(@RequestParam(required = false) Long carryoverBillId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("lastUpdateDate", carryoverBillId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "ctc/carryoverBillAccounting/saveTest", param);
        restTemplate.getForObject(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        response.setData("0");
        return response;
    }

}
