package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.crm.dto.CustomerDto;
import com.midea.pam.common.ctc.vo.CustomerBankAccountExcelVo;
import com.midea.pam.common.ctc.vo.CustomerContactExcelVo;
import com.midea.pam.common.ctc.vo.CustomerExcVo;
import com.midea.pam.common.ctc.vo.TeamExcelVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Api("客户模块")
@RestController
@RequestMapping("statistics/customer")
public class CustomerStatisticsController extends ControllerHelper {

    private static final String SENSITIVE_WORD_REPLACER = "******";

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查询客户列表")
    @GetMapping("page")
    public Response list(CustomerDto customerDto,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(customerDto);
        params.put("list", Boolean.TRUE);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/customer/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<CustomerDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<CustomerDto>>>() {
        });

        return response;
    }

    @ApiOperation(value = "查询我的客户列表")
    @GetMapping("createByMePage")
    public Response createByMePage(CustomerDto customerDto,
                                   @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                   @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(customerDto);
        params.put("me", Boolean.TRUE);

        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/customer/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<CustomerDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<CustomerDto>>>() {
        });

        return response;
    }

    @ApiOperation(value = "客户列表导出", response = ResponseMap.class)
    @GetMapping("/export")
    public void listExport(HttpServletResponse response, CustomerDto customerDto) {
        final Map<String, Object> param = buildParam(customerDto);
        // param.put("list",customerDto.isList());
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/customer/v1/export", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("客户信息_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");

        JSONArray customerArr = (JSONArray) resultMap.get("customers");
        JSONArray customerContactArr = (JSONArray) resultMap.get("customerContactExcelVOs");
        JSONArray customerBankAccountArr = (JSONArray) resultMap.get("customerBankAccountExcelVos");
        JSONArray teamArr = (JSONArray) resultMap.get("teamExcelVos");

        List<CustomerExcVo> customerExcelVOS = JSONObject.parseArray(customerArr.toJSONString(), CustomerExcVo.class);
        for (int i = 0; i < customerExcelVOS.size(); i++) {
            CustomerExcVo customerExcelVo = customerExcelVOS.get(i);
            customerExcelVo.setNum(i + 1);
        }

        List<CustomerContactExcelVo> customerContacts = JSONObject.parseArray(customerContactArr.toJSONString(), CustomerContactExcelVo.class);
        for (int i = 0; i < customerContacts.size(); i++) {
            CustomerContactExcelVo customerContactExcelVO = customerContacts.get(i);
            customerContactExcelVO.setNum(i + 1);
            desensitizationExport(customerContactExcelVO);
        }

        List<CustomerBankAccountExcelVo> customerBankAccounts = JSONObject.parseArray(customerBankAccountArr.toJSONString(), CustomerBankAccountExcelVo.class);
        for (int i = 0; i < customerBankAccounts.size(); i++) {
            CustomerBankAccountExcelVo customerBankExcelVO = customerBankAccounts.get(i);
            customerBankExcelVO.setNum(i + 1);
        }

        List<TeamExcelVo> teams = JSONObject.parseArray(teamArr.toJSONString(), TeamExcelVo.class);
        for (int i = 0; i < teams.size(); i++) {
            TeamExcelVo teamVo = teams.get(i);
            teamVo.setNum(i + 1);
        }

        final Workbook workbook = com.midea.pam.common.util.ExportExcelUtil.buildDefaultSheet(customerExcelVOS, CustomerExcVo.class, null, "基本信息", true);
        com.midea.pam.common.util.ExportExcelUtil.addSheet(workbook, customerContacts, CustomerContactExcelVo.class, null, "联系人信息", true);
        com.midea.pam.common.util.ExportExcelUtil.addSheet(workbook, customerBankAccounts, CustomerBankAccountExcelVo.class, null, "财务信息", true);
        com.midea.pam.common.util.ExportExcelUtil.addSheet(workbook, teams, TeamExcelVo.class, null, "团队成员", true);

        com.midea.pam.common.util.ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    @ApiOperation(value = "我的客户列表导出", response = ResponseMap.class)
    @GetMapping("/exportMy")
    public void listMyExport(HttpServletResponse response, CustomerDto customerDto) {
        final Map<String, Object> param = buildParam(customerDto);
        param.put("me", Boolean.TRUE);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/customer/v1/export", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("客户信息_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");

        JSONArray customerArr = (JSONArray) resultMap.get("customers");
        JSONArray customerContactArr = (JSONArray) resultMap.get("customerContactExcelVOs");
        JSONArray customerBankAccountArr = (JSONArray) resultMap.get("customerBankAccountExcelVos");
        JSONArray teamArr = (JSONArray) resultMap.get("teamExcelVos");

        List<CustomerExcVo> customerExcelVOS = JSONObject.parseArray(customerArr.toJSONString(), CustomerExcVo.class);
        for (int i = 0; i < customerExcelVOS.size(); i++) {
            CustomerExcVo customerExcelVo = customerExcelVOS.get(i);
            customerExcelVo.setNum(i + 1);
        }

        List<CustomerContactExcelVo> customerContacts = JSONObject.parseArray(customerContactArr.toJSONString(), CustomerContactExcelVo.class);
        for (int i = 0; i < customerContacts.size(); i++) {
            CustomerContactExcelVo customerContactExcelVO = customerContacts.get(i);
            customerContactExcelVO.setNum(i + 1);
            desensitizationExport(customerContactExcelVO);
        }

        List<CustomerBankAccountExcelVo> customerBankAccounts = JSONObject.parseArray(customerBankAccountArr.toJSONString(), CustomerBankAccountExcelVo.class);
        for (int i = 0; i < customerBankAccounts.size(); i++) {
            CustomerBankAccountExcelVo customerBankExcelVO = customerBankAccounts.get(i);
            customerBankExcelVO.setNum(i + 1);
        }

        List<TeamExcelVo> teams = JSONObject.parseArray(teamArr.toJSONString(), TeamExcelVo.class);
        for (int i = 0; i < teams.size(); i++) {
            TeamExcelVo teamVo = teams.get(i);
            teamVo.setNum(i + 1);
        }

        final Workbook workbook = com.midea.pam.common.util.ExportExcelUtil.buildDefaultSheet(customerExcelVOS, CustomerExcVo.class, null, "基本信息", true);
        com.midea.pam.common.util.ExportExcelUtil.addSheet(workbook, customerContacts, CustomerContactExcelVo.class, null, "联系人信息", true);
        com.midea.pam.common.util.ExportExcelUtil.addSheet(workbook, customerBankAccounts, CustomerBankAccountExcelVo.class, null, "财务信息", true);
        com.midea.pam.common.util.ExportExcelUtil.addSheet(workbook, teams, TeamExcelVo.class, null, "团队成员", true);

        com.midea.pam.common.util.ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    private Map buildParam(CustomerDto customerDto) {

        //CRM编码、客户名称、客户编码、省市、所有者使用文本模糊搜索；状态、行业、区域、销售部门使用下拉搜索；创建日期取日期搜索
        final Map<String, Object> params = new HashMap<>();
        params.put("pamCode", customerDto.getPamCode());
        params.put("crmCode", customerDto.getCrmCode());
        params.put("name", customerDto.getName());

        params.put("region", customerDto.getRegion());
        params.put("industry", customerDto.getIndustry());
        params.put("ownerName", customerDto.getOwnerName());
        params.put("provinceCityArea", customerDto.getProvinceCityArea());

        params.put("statusesStr", customerDto.getStatusesStr());
        params.put("syncStatusesStr", customerDto.getSyncStatusesStr());
        params.put("regionStr", customerDto.getRegionStr());
        params.put("industryStr", customerDto.getIndustryStr());
        params.put("unitIdStr", customerDto.getUnitIdStr());
        params.put("rankStr", customerDto.getRankStr());
        params.put("ouIdsStr", customerDto.getOuIdsStr());
        params.put("createAtStart", customerDto.getCreateAtStart());
        params.put("createAtEnd", customerDto.getCreateAtEnd());
        params.put("isRdm", customerDto.getIsRdm());
        params.put("userGroupStr", customerDto.getUserGroupStr());
        params.put("customerGroupStr", customerDto.getCustomerGroupStr());
        params.put("creditRatingStr", customerDto.getCreditRatingStr());

        return params;
    }

    /**
     * 脱敏操作
     */
    private void desensitizationExport(CustomerContactExcelVo customerContactExcelVO) {
        if (Objects.isNull(customerContactExcelVO)) {
            return;
        }
        String cellphone = customerContactExcelVO.getCellphone();
        if (StringUtils.isNotEmpty(cellphone)) {
            customerContactExcelVO.setCellphone(SENSITIVE_WORD_REPLACER);
        }
        String email = customerContactExcelVO.getEmail();
        if (StringUtils.isNotEmpty(email)) {
            customerContactExcelVO.setEmail(SENSITIVE_WORD_REPLACER);
        }
        String wechat = customerContactExcelVO.getWechat();
        if (StringUtils.isNotEmpty(wechat)) {
            customerContactExcelVO.setWechat(SENSITIVE_WORD_REPLACER);
        }
        String qq = customerContactExcelVO.getQq();
        if (StringUtils.isNotEmpty(qq)) {
            customerContactExcelVO.setQq(SENSITIVE_WORD_REPLACER);
        }
    }
}
