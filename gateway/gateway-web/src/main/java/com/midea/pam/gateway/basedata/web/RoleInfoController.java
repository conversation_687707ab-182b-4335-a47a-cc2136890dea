package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.entity.RoleInfo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.Utils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.PageResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Api("系统管理-角色管理")
@RequestMapping({"roleInfo"})
public class RoleInfoController extends ControllerHelper {

    @Autowired
    private RestTemplate restTemplate;

    @ApiOperation("查询角色列表分页")
    @GetMapping("selectPage")
    public Response list(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                         @RequestParam(name = "roleName", required = false) String roleName,
                         @RequestParam(name = "roleDescribe", required = false) String roleDescribe,
                         @RequestParam(name = "unitName", required = false) String unitName) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("roleName", roleName);
        param.put("roleDescribe", roleDescribe);
        param.put("unitName",unitName);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/roleInfo/selectPage", param);
        String res = restTemplate.getForObject(url, String.class);
        PageInfo<Map<String, Object>> data = JSON.parseObject(res, new TypeReference<PageInfo<Map<String, Object>>>() {
        });
        PageResponse<Map<String, Object>> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation(value = "获取所有角色列表")
    @GetMapping
    public Response getAll() throws Exception {
        final Map<String, Object> param = new HashMap<>();
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/roleInfo/getAll", param);
        String res = restTemplate.getForObject(url, String.class);
        res = cleanStr(res);
        List<Map<String, Object>> data = Utils.jsonStr2List(res);
        DataResponse<List<Map<String, Object>>> response = Response.dataResponse();
        return response.setData(data);
    }

    @ApiOperation(value = "通过ID查询角色信息")
    @GetMapping("{id}")
    public Response getById(@PathVariable Long id) throws Exception {
        String url = String.format("%sroleInfo/%s", ModelsEnum.BASEDATA.getBaseUrl(), id);
        String res = restTemplate.getForObject(url, String.class);
        RoleInfo data = JSON.parseObject(res, new TypeReference<RoleInfo>() {
        });
        DataResponse<RoleInfo> response = Response.dataResponse();
        return response.setData(data);
    }

    @ApiOperation(value = "新增角色")
    @PostMapping("addRole")
    public Response addRole(@RequestBody RoleInfo role) throws Exception {
        String url = String.format("%sroleInfo/addRole", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, role, String.class);
        String res = responseEntity.getBody();
        DataResponse<String> response = Response.dataResponse();
        return response.setData(res);
    }

    @ApiOperation(value = "更新角色")
    @PostMapping("updateRole")
    public Response updateRole(@RequestBody RoleInfo role) throws Exception {
        String url = String.format("%sroleInfo/updateRole", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, role, String.class);
        String res = responseEntity.getBody();
        DataResponse<String> response = Response.dataResponse();
        return response.setData(res);
    }

    @ApiOperation(value = "删除角色")
    @DeleteMapping("{id}")
    public Response deleteRole(@PathVariable final Long id) {
        String url = String.format("%sroleInfo/delRole?id=%s", ModelsEnum.BASEDATA.getBaseUrl(), id);
        restTemplate.delete(url);
        DataResponse<Map<String, Object>> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "删除角色")
    @GetMapping("pageUnAuthRoles")
    public Response pageUnAuthRoles(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                    @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                                    @RequestParam(name = "userId", required = false) Long userId,
                                    @RequestParam(name = "name", required = false) String name) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("userId", userId);
        param.put("name", name);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/roleInfo/pageUnAuthRoles", param);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<RoleInfo>>>() {
        });
    }

}
