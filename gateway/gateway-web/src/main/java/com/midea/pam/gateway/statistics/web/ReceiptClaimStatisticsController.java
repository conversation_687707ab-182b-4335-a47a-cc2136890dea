package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.ReceiptClaimDto;
import com.midea.pam.common.ctc.vo.ReceiptClaimExcVo;
import com.midea.pam.common.ctc.vo.ReceiptContractExcVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Api("销售回款认领模块")
@RestController
@RequestMapping("statistics/receiptClaim")
public class ReceiptClaimStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查询销售回款列表")
    @GetMapping("page")
    public Response list(ReceiptClaimDto receiptClaimDto,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(receiptClaimDto);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/receiptClaim/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<ReceiptClaimDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ReceiptClaimDto>>>() {
        });

        return response;
    }

    @ApiOperation(value = "销售回款列表导出", response = ResponseMap.class)
    @GetMapping("/export")
    public void listExport(HttpServletResponse response, ReceiptClaimDto receiptClaimDto) {
        final Map<String, Object> param = buildParam(receiptClaimDto);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/receiptClaim/v1/export", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        if (StringUtils.isNotEmpty(receiptClaimDto.getResource())){
            // 回款认领同步异常的数据
            if (receiptClaimDto.getResource().equals("120")) {
                fileName.append("回款认领同步异常_"+ DateUtils.format(new Date(),"yyyyMMddHHmmss"));
            } else if (receiptClaimDto.getResource().equals("153")) {
                fileName.append("收款销售子合同编号同步异常_"+ DateUtils.format(new Date(),"yyyyMMddHHmmss"));
            }
        } else {
            fileName.append("销售回款认领_"+ DateUtils.format(new Date(),"yyyyMMddHHmmss"));
        }

        fileName.append(".xls");

        JSONArray receiptClaimArr = (JSONArray) resultMap.get("receiptClaim");
        JSONArray receiptContractExcVosArr = (JSONArray) resultMap.get("receiptContractExcVos");

        List<ReceiptClaimExcVo> receiptClaimExcelVOS = JSONObject.parseArray(receiptClaimArr.toJSONString(), ReceiptClaimExcVo.class);
        for (int i = 0; i < receiptClaimExcelVOS.size(); i++) {
            ReceiptClaimExcVo receiptClaimExcelVo = receiptClaimExcelVOS.get(i);
            receiptClaimExcelVo.setNum(i + 1);
        }

        List<ReceiptContractExcVo> receiptContract = JSONObject.parseArray(receiptContractExcVosArr.toJSONString(), ReceiptContractExcVo.class);
        for (int i = 0; i < receiptContract.size(); i++) {
            ReceiptContractExcVo receiptContractExcelVO = receiptContract.get(i);
            receiptContractExcelVO.setNum(i + 1);
        }

        final Workbook workbook = com.midea.pam.common.util.ExportExcelUtil.buildDefaultSheet(receiptClaimExcelVOS, ReceiptClaimExcVo.class, null, "收款认领", true);
        com.midea.pam.common.util.ExportExcelUtil.addSheet(workbook, receiptContract, ReceiptContractExcVo.class, null, "收款分配合同", true);

        com.midea.pam.common.util.ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    private Map buildParam(ReceiptClaimDto receiptClaimDto) {

        final Map<String, Object> params = new HashMap<>();
        params.put("cashReceiptCode", receiptClaimDto.getCashReceiptCode());
        params.put("contractCodeStr", receiptClaimDto.getContractCodeStr());
        params.put("customerCode", receiptClaimDto.getCustomerCode());
        params.put("customerName", receiptClaimDto.getCustomerName());
        params.put("receiptCode", receiptClaimDto.getReceiptCode());
        params.put("claimByName", receiptClaimDto.getClaimByName());
        params.put("recBankCode", receiptClaimDto.getRecBankCode());
        params.put("ouId", receiptClaimDto.getOuId());
        params.put("businessType", receiptClaimDto.getBusinessType());

        params.put("contractStatusStr", receiptClaimDto.getContractStatusStr());
        params.put("claimStatusStr", receiptClaimDto.getClaimStatusStr());
        params.put("erpStatusStr", receiptClaimDto.getErpStatusStr());
        params.put("contractSyncStatusStr", receiptClaimDto.getContractSyncStatusStr());
        params.put("sourceStr", receiptClaimDto.getSourceStr());
        params.put("currencyCodeStr", receiptClaimDto.getCurrencyCodeStr());

        params.put("payDateStart", receiptClaimDto.getPayDateStart());
        params.put("payDateEnd", receiptClaimDto.getPayDateEnd());

        params.put("createAtStart", receiptClaimDto.getCreateAtStart());
        params.put("createAtEnd", receiptClaimDto.getCreateAtEnd());

        params.put("accountingDateStr", receiptClaimDto.getAccountingDateStr());
        params.put("settleWay", receiptClaimDto.getSettleWay());
        params.put("claimDateStr", receiptClaimDto.getClaimDateStr());
        params.put("recMethodName", receiptClaimDto.getRecMethodName());
        params.put("ouIdStr", receiptClaimDto.getOuIdStr());
        params.put("receiptClaimDetailIds",receiptClaimDto.getReceiptClaimDetailIds());
        params.put("resource",receiptClaimDto.getResource());
        if (StringUtils.isNotEmpty(receiptClaimDto.getBusiSceneIdStr())){
            params.put("busiSceneIdStr",receiptClaimDto.getBusiSceneIdStr());
        }
        if (StringUtils.isNotEmpty(receiptClaimDto.getBudgetItemCode())){
            params.put("budgetItemCode",receiptClaimDto.getBudgetItemCode());
        }
        if (StringUtils.isNotEmpty(receiptClaimDto.getCurrencyCode())){
            params.put("currencyCode",receiptClaimDto.getCurrencyCode());
        }
        if (StringUtils.isNotEmpty(receiptClaimDto.getOriginalPaymentCode())){
            params.put("originalPaymentCode",receiptClaimDto.getOriginalPaymentCode());
        }
        return params;
    }
}
