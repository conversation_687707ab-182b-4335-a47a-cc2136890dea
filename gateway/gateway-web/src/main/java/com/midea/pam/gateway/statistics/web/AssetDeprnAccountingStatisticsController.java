package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.ctc.dto.AssetDeprnAccountingDto;
import com.midea.pam.common.ctc.dto.ProjectAssetDeprnCostDetailDto;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.statistics.excelVo.AssetDeprnAccountingDetailExcelVO;
import com.midea.pam.common.statistics.excelVo.AssetDeprnAccountingExcelVO;
import com.midea.pam.common.statistics.excelVo.ProjectAssetDeprnCostDetailStatisticsExcelVO;
import com.midea.pam.common.util.BeanConverter;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;


@Api("资产折旧成本入账")
@RestController
@RequestMapping("statistics/assetDeprnAccounting")
public class AssetDeprnAccountingStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation("资产折旧成本入账单列表（分页）")
    @PostMapping("page")
    public Response page(@RequestBody AssetDeprnAccountingDto dto) {
        String url = String.format("%sstatistics/assetDeprnAccounting/page", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<AssetDeprnAccountingDto>>>() {
        });
    }

    @ApiOperation("资产折旧成本入账单列表导出")
    @PostMapping("export")
    public void listExport(HttpServletResponse response, @RequestBody AssetDeprnAccountingDto dto) {
        final String url = String.format("%sstatistics/assetDeprnAccounting/export", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });
        if (dataResponse == null || dataResponse.getData() == null) {
            throw new BizException(Code.ERROR, "导出数据为空");
        }
        Map<String, Object> resultMap = dataResponse.getData();
        JSONArray arr = (JSONArray) resultMap.get("list");
        JSONArray detailArr = (JSONArray) resultMap.get("detailList");
        List<AssetDeprnAccountingExcelVO> list = new ArrayList<>();
        List<AssetDeprnAccountingDetailExcelVO> detailList = new ArrayList<>();

        if (arr != null) {
            list = JSONObject.parseArray(arr.toString(), AssetDeprnAccountingExcelVO.class);
            for (int i = 0; i < list.size(); i++) {
                AssetDeprnAccountingExcelVO excelVO = list.get(i);
                excelVO.setNum(i + 1);
            }
        }
        if (detailArr != null) {
            detailList = JSONObject.parseArray(detailArr.toString(), AssetDeprnAccountingDetailExcelVO.class);
            for (int i = 0; i < detailList.size(); i++) {
                AssetDeprnAccountingDetailExcelVO excelVO = detailList.get(i);
                excelVO.setNum(i + 1);
            }
        }

        //导出操作
        String fileName = String.format("%s%s%s", "资产折旧成本入账单_", DateUtils.format(new Date(), "yyyyMMddHHmmss"), ".xls");
        Workbook workbook = ExportExcelUtil.buildDefaultSheet(list, AssetDeprnAccountingExcelVO.class, null, "资产折旧成本入账单", Boolean.TRUE);
        ExportExcelUtil.addSheet(workbook, detailList, AssetDeprnAccountingDetailExcelVO.class, null, "资产折旧明细", Boolean.TRUE);
        ExportExcelUtil.downLoadExcel(fileName, response, workbook);
    }


    @ApiOperation("资产折旧成本统计列表（分页）")
    @PostMapping("collectionSummaryPage")
    public Response collectionSummaryPage(@RequestBody ProjectAssetDeprnCostDetailDto dto) {
        String url = String.format("%sstatistics/assetDeprnAccounting/collectionSummaryPage", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ProjectAssetDeprnCostDetailDto>>>() {
        });
    }

    @ApiOperation("资产折旧成本统计列表导出")
    @PostMapping("collectionSummaryExport")
    public void collectionSummaryExport(HttpServletResponse response, @RequestBody ProjectAssetDeprnCostDetailDto dto) {
        final String url = String.format("%sstatistics/assetDeprnAccounting/collectionSummaryList", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<List<ProjectAssetDeprnCostDetailDto>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<ProjectAssetDeprnCostDetailDto>>>() {
        });
        if (dataResponse == null || dataResponse.getData() == null) {
            throw new BizException(Code.ERROR, "导出数据为空");
        }

        List<ProjectAssetDeprnCostDetailStatisticsExcelVO> excelVOList = BeanConverter.copy(dataResponse.getData(), ProjectAssetDeprnCostDetailStatisticsExcelVO.class);
        for (int i = 0; i < excelVOList.size(); i++) {
            ProjectAssetDeprnCostDetailStatisticsExcelVO excelVO = excelVOList.get(i);
            excelVO.setNum(i + 1);
        }
        //导出操作
        String fileName = String.format("%s%s%s", "资产折旧成本统计_", DateUtils.format(new Date(), "yyyyMMddHHmmss"), ".xls");
        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(excelVOList, ProjectAssetDeprnCostDetailStatisticsExcelVO.class, null, "资产折旧成本统计", true);
        ExportExcelUtil.downLoadExcel(fileName, response, workbook);
    }
}
