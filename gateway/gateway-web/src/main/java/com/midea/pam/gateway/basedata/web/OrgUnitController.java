package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.basedata.entity.OrgUnit;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("orgUnit")
@Api("组织关系")
public class OrgUnitController extends ControllerHelper {
    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "查询用户的顶级单位.")
    @GetMapping("selectUserTopUnit")
    public Response selectUserTopUnit(@RequestParam Long userId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("userId", userId);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/orgUnit/selectUserTopUnit", param);
        String res = restTemplate.getForEntity(url , String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<OrgUnit>>>(){});
    }

}
