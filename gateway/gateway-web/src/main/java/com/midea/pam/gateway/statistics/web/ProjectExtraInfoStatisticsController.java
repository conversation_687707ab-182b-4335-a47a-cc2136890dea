package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.ProjectExtraInfoDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @PackageClassName: com.midea.pam.gateway.ctc.web.ProjectExtraInfoController
 * @Description: 项目其他信息
 * @Author: JerryH
 * @Date: 2023-02-02, 0002 上午 10:42:11
 */
@Api("项目其他信息")
@RestController
@RequestMapping({"statistics/projectExtraInfo"})
public class ProjectExtraInfoStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "获取项目其他信息类型")
    @GetMapping("getType")
    public Response getType(@ApiParam("项目id") @RequestParam Long projectId) {
        Map<String, Object> params = new HashMap<>();
        params.put("projectId", projectId);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectExtraInfo/getType", params);
        return restTemplate.getForEntity(url, DataResponse.class).getBody();
    }

    @ApiOperation(value = "获取项目其他信息全部类型列表信息")
    @GetMapping("pageAllList")
    public Response pageAllList(ProjectExtraInfoDto projectExtraInfoDto,
                                @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(projectExtraInfoDto);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectExtraInfo/pageAllList", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<ProjectExtraInfoDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ProjectExtraInfoDto>>>() {
        });

        return response;
    }

    private Map<String, Object> buildParam(ProjectExtraInfoDto projectExtraInfoDto) {
        final Map<String, Object> params = new HashMap<>();
        params.put("projectId",projectExtraInfoDto.getProjectId());
        params.put("title",projectExtraInfoDto.getTitle());
        params.put("code",projectExtraInfoDto.getCode());
        params.put("createByUserName",projectExtraInfoDto.getCreateByUserName());
        params.put("updateByUserName",projectExtraInfoDto.getUpdateByUserName());
        return params;
    }
}
