package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.statistics.dto.RolePortalTargetRelDto;
import com.midea.pam.common.statistics.entity.RolePortalTargetRel;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("角色门户指标配置")
@RestController
@RequestMapping("rolePortalTargetRel")
public class RolePortalTargetRelController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "按角色门户指标配置ID删除")
    @GetMapping("deleteById")
    public Response deleteById(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/rolePortalTargetRel/deleteById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation(value = "新增/修改角色门户指标配置")
    @PostMapping("save")
    public Response save(@RequestBody List<RolePortalTargetRel> rolePortalTargetRelList) {
        final String url = String.format("%srolePortalTargetRel/save", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, rolePortalTargetRelList, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "按id查询角色门户指标配置")
    @GetMapping("findByRoleId/{roleId}")
    public Response findByRoleId(@PathVariable Long roleId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("roleId", roleId);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/rolePortalTargetRel/findByRoleId", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<RolePortalTargetRelDto>>>() {
        });
    }

}
