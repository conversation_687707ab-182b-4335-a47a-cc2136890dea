package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.midea.mip.core.exception.MipException;
import com.midea.pam.common.basedata.dto.MaterialDto;
import com.midea.pam.common.basedata.entity.OrganizationRel;
import com.midea.pam.common.basedata.entity.StorageInventory;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.MaterialCostTransferDetailDto;
import com.midea.pam.common.ctc.dto.MaterialCostTransferDto;
import com.midea.pam.common.ctc.dto.MaterialCostTransferStorageInventoryDto;
import com.midea.pam.common.ctc.dto.MaterialTransferDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.entity.MaterialGetHeader;
import com.midea.pam.common.ctc.excelVo.MaterialCostTransferDetailTempExcelVo;
import com.midea.pam.common.ctc.vo.MaterialCostTransferDetailsExcelVO;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.BeanConverter;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.common.utils.FileUtil;
import com.midea.pam.gateway.service.MipWorkflowInnerService;
import com.midea.pam.gateway.service.OssService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Api("成本转移单模块")
@RestController
@RequestMapping("materialCostTransfer")
public class MaterialCostTransferController extends ControllerHelper {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private OssService ossService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private MipWorkflowInnerService mipWorkflowInnerService;


    @ApiOperation(value = "起草", response = MaterialCostTransferDto.class)
    @PostMapping("draft")
    public Response draft(@RequestBody @ApiParam(name = "materialCostTransferDto", value = "成本转移单信息") MaterialCostTransferDto dto) {
        final String url = String.format("%smaterialCostTransfer/draft", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<MaterialCostTransferDto> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<MaterialCostTransferDto>>() {
        });
        return response;
    }

    @ApiOperation("成本转移单-库存组织下可用子库列表")
    @GetMapping("listInventory")
    public Response listInventory(@RequestParam(required = false) Long transferOutOrganizationId,
                                  @RequestParam(required = false) Long transferInOrganizationId) {
        if (!Objects.equals(transferOutOrganizationId, transferInOrganizationId)) {
            //库存组织不一致
            throw new BizException(Code.ERROR, "转出库存组织必须和转入库存组织相同");
        }

        final Map<String, Object> param = new HashMap<>();
        param.put("organizationId", transferOutOrganizationId);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/storageInventory/listInventory", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<StorageInventory>> listDataResponse = JSON.parseObject(res, new TypeReference<DataResponse<List<StorageInventory>>>() {
        });
        List<StorageInventory> inventories = listDataResponse.getData().stream().filter(x -> !x.getLocatorFlag() && Objects.equals(x.getAssetInventory(), "1")).collect(Collectors.toList());
        List<MaterialCostTransferStorageInventoryDto> dtoList = BeanConverter.copy(inventories, MaterialCostTransferStorageInventoryDto.class);
        if (!CollectionUtils.isEmpty(dtoList)) {
            //该库存组织下的成本法
            String url2 = StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "organizationRel/selectByOrganizationId", param);
            String res2 = restTemplate.getForEntity(url2, String.class).getBody();
            DataResponse<List<OrganizationRel>> dataResponse = JSON.parseObject(res2, new TypeReference<DataResponse<List<OrganizationRel>>>() {
            });
            if (!CollectionUtils.isEmpty(dataResponse.getData())) {
                //原则上只会查到一个,如果有多个默认取第一个
                OrganizationRel organizationRel = dataResponse.getData().get(0);
                //成本法 0-平均成本法,1-标准成本法
                Integer costMethod = organizationRel.getCostMethod();
                dtoList.stream().forEach(x -> x.setCostMethod(costMethod));
            }
        }

        DataResponse response = new DataResponse<>();
        return response.setData(dtoList);
    }

    @ApiOperation(value = "成本转移单-根据库存组织查询物料列表")
    @PostMapping("listMaterialByTransferCost")
    public Response listMaterialByTransferCost(@RequestBody MaterialCostTransferDto dto) {
        Guard.notNull(dto.getTransferOutOrganizationId(), "转出项目库存组织ID不能为空");
        Guard.notNull(dto.getTransferInOrganizationId(), "转入项目库存组织ID不能为空");

        String url = String.format("%smaterial/listMaterialByTransferCostNew", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<MaterialDto>>>() {
        });
    }

    @ApiOperation(value = "成本转移单-原领料单查询", response = MaterialGetHeader.class)
    @GetMapping("queryMaterialGetHeaderByMaterialCode")
    public JSONObject query(@RequestParam(required = true) @ApiParam(value = "物料编号") String materialCode,
                            @RequestParam(required = false) @ApiParam(value = "领料编号") String getCode,
                            @RequestParam(required = false) @ApiParam(value = "项目编号") String projectCode,
                            @RequestParam(required = false) @ApiParam(value = "制单人") String fillUserName,
                            @RequestParam(required = false) @ApiParam(value = "领料人") String getUserName,
                            @RequestParam(required = false) @ApiParam(value = "库存组织名称或编码") String org,
                            @RequestParam(required = false) @ApiParam(value = "库存组织Id") Long organizationId,
                            @RequestParam(required = false) @ApiParam(value = "是否有差异（0否，1是，不传则查全部）") Integer difference,
                            @RequestParam(required = false) @ApiParam(value = "申请开始时间") String applyDateStart,
                            @RequestParam(required = false) @ApiParam(value = "申请结束时间") String applyDateEnd) {
        final Map<String, Object> param = new HashMap<>();
        param.put("materialCode", materialCode);
        param.put("getCode", getCode);
        param.put("projectCode", projectCode);
        param.put("fillUserName", fillUserName);
        param.put("getUserName", getUserName);
        param.put("org", org);
        param.put("organizationId", organizationId);
        param.put("difference", difference);
        param.put("applyDateStart", applyDateStart);
        param.put("applyDateEnd", applyDateEnd);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/materialGet/queryMaterialGetHeaderByMaterialCode", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res);
    }

    @ApiOperation(value = "根据id查询", response = MaterialCostTransferDto.class)
    @GetMapping("queryById")
    public JSONObject query(@RequestParam @ApiParam(value = "成本转移单id") Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/materialCostTransfer/queryById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res);
    }

    @ApiOperation(value = "废弃")
    @GetMapping("discard")
    public Object discard(@RequestParam @ApiParam(value = "成本转移单id") Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/materialCostTransfer/discard", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        if (response.getData()) {
            mipWorkflowInnerService.draftAbandon("materialCostTransferApp", id);
        }
        return JSON.parseObject(res);
    }

    @ApiOperation(value = "移动审批成本转移单详细", response = MaterialTransferDto.class)
    @GetMapping("materialCostTransferApp")
    public Response materialCostTransferApp(
            @RequestParam(required = true) final Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/materialCostTransfer/materialCostTransferApp", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<ResponseMap> response = JSON.parseObject(res, new TypeReference<DataResponse<ResponseMap>>() {
        });
        return response;
    }

//    @ApiOperation(value = "测试审批相关")
//    @GetMapping("test")
//    public void test(@RequestParam @ApiParam(value = "类型") Integer type,
//                     @RequestParam(required = false) Long id,
//                     @RequestParam(required = false) Long auditingUserId){
//        final Map<String, Object> param = new HashMap<>();
//        param.put("id", id);
//        param.put("type", type);
//        param.put("auditingUserId", auditingUserId);
//        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/materialCostTransfer/test", param);
//        restTemplate.getForEntity(url, String.class).getBody();
//    }

    @ApiOperation(value = "起草回调")
    @PutMapping("updateStatusSubmit/skipSecurityInterceptor")
    public Response updateStatusSubmit(@RequestParam(required = false) Long formInstanceId,
                                       @RequestParam(required = false) String fdInstanceId,
                                       @RequestParam(required = false) String formUrl,
                                       @RequestParam(required = false) String eventName,
                                       @RequestParam(required = false) String handlerId,
                                       @RequestParam(required = false) Long companyId,
                                       @RequestParam(required = false) Long createUserId) {
        DataResponse<String> response = Response.dataResponse();
        logger.info("updateStatusSubmit/skipSecurityInterceptor...");

        //提交审批
        String url = String.format("%smaterialCostTransfer/updateStatusSubmit/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        logger.info("请求的url为:{}", url);
        restTemplate.put(url, String.class);
        return response;
    }

    @ApiOperation(value = "通过回调")
    @PutMapping("updateStatusPass/skipSecurityInterceptor")
    public Response updateStatusPass(@RequestParam(required = false) Long formInstanceId,
                                     @RequestParam(required = false) String fdInstanceId,
                                     @RequestParam(required = false) String formUrl,
                                     @RequestParam(required = false) String eventName,
                                     @RequestParam(required = false) String handlerId,
                                     @RequestParam(required = false) Long companyId,
                                     @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialCostTransfer/updateStatusPass/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "驳回回调")
    @PutMapping("updateStatusReject/skipSecurityInterceptor")
    public Response updateStatusReject(@RequestParam(required = false) Long formInstanceId,
                                       @RequestParam(required = false) String fdInstanceId,
                                       @RequestParam(required = false) String formUrl,
                                       @RequestParam(required = false) String eventName,
                                       @RequestParam(required = false) String handlerId,
                                       @RequestParam(required = false) Long companyId,
                                       @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialCostTransfer/updateStatusReject/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "审批人作废回调")
    @PutMapping("updateStatusAbandon/skipSecurityInterceptor")
    public Response updateStatusAbandon(@RequestParam(required = false) Long formInstanceId,
                                        @RequestParam(required = false) String fdInstanceId,
                                        @RequestParam(required = false) String formUrl,
                                        @RequestParam(required = false) String eventName,
                                        @RequestParam(required = false) String handlerId,
                                        @RequestParam(required = false) Long companyId,
                                        @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialCostTransfer/updateStatusAbandon/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "审批人撤回回调")
    @PutMapping("updateStatusDraftReturn/skipSecurityInterceptor")
    public Response updateStatusDraftReturn(@RequestParam(required = false) Long formInstanceId,
                                            @RequestParam(required = false) String fdInstanceId,
                                            @RequestParam(required = false) String formUrl,
                                            @RequestParam(required = false) String eventName,
                                            @RequestParam(required = false) String handlerId,
                                            @RequestParam(required = false) Long companyId,
                                            @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialCostTransfer/updateStatusDraftReturn/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }


    @ApiOperation(value = "项目领料流程删除")
    @PutMapping("updateStatusDeleteForMaterialReturn/skipSecurityInterceptor")
    public Response updateStatusDeleteForMaterialReturn(@RequestParam(required = false) Long formInstanceId,
                                                        @RequestParam(required = false) String fdInstanceId,
                                                        @RequestParam(required = false) String formUrl,
                                                        @RequestParam(required = false) String eventName,
                                                        @RequestParam(required = false) String handlerId,
                                                        @RequestParam(required = false) Long companyId,
                                                        @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialCostTransfer/updateStatusDeleteForMaterialReturn/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "处理人通过")
    @PutMapping("agree/skipSecurityInterceptor")
    public Response agree(@RequestParam(required = false) Long formInstanceId,
                          @RequestParam(required = false) String fdInstanceId,
                          @RequestParam(required = false) String formUrl,
                          @RequestParam(required = false) String eventName,
                          @RequestParam(required = false) String handlerId,
                          @RequestParam(required = false) Long companyId,
                          @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialCostTransfer/agree/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }


    /**
     * 成本转移单导入
     *
     * @param file
     * @param transferOutProjectId
     * @param transferOutOrganizationId
     * @param transferOutMaterialType
     * @param transferOutTicketTaskId
     * @param transferInProjectId
     * @param transferInOrganizationId
     * @param transferInMaterialType
     * @param transferInTicketTaskId
     * @param isOriginal
     * @return
     */
    @ApiOperation(value = "上传文件检查数据", notes = "场景：物料成本转移单批量导入")
    @PostMapping("/importMaterialCostTransferDetail")
    public Response importMaterialTransferDetail(@RequestPart("file") MultipartFile file,
                                                 @RequestParam(required = false) @ApiParam("转出项目编号") Long transferOutProjectId,
                                                 @RequestParam(required = true) @ApiParam("转出项目库存组织") Long transferOutOrganizationId,
                                                 @RequestParam(required = true) @ApiParam("转出项目退料类型") Integer transferOutMaterialType,
                                                 @RequestParam(required = false) @ApiParam("转出项目工单任务id") Long transferOutTicketTaskId,
                                                 @RequestParam(required = false) @ApiParam("转出项目工单任务号") String transferOutTicketTaskCode,
                                                 @RequestParam(required = false) @ApiParam("转入项目编号") Long transferInProjectId,
                                                 @RequestParam(required = true) @ApiParam("转入项目库存组织") Long transferInOrganizationId,
                                                 @RequestParam(required = true) @ApiParam("转入项目退料类型") Integer transferInMaterialType,
                                                 @RequestParam(required = false) @ApiParam("转入项目工单任务id") Long transferInTicketTaskId,
                                                 @RequestParam(required = false) @ApiParam("转入项目工单任务号") String transferInTicketTaskCode,
                                                 @RequestParam(required = true) @ApiParam("是否按原单据成本转移:0-是,1否") Integer isOriginal) {
        List<MaterialCostTransferDetailTempExcelVo> excelVos = null;
        try {
            excelVos = FileUtil.importExcel(file, MaterialCostTransferDetailTempExcelVo.class, 1, 0);
            //祛除空行
            Iterator<MaterialCostTransferDetailTempExcelVo> iterator = excelVos.iterator();
            while (iterator.hasNext()) {
                MaterialCostTransferDetailTempExcelVo vo = iterator.next();
                if (StringUtils.isEmpty(vo.getErpCode()) && StringUtils.isEmpty(vo.getApplyNum())
                        && (Objects.equals(transferOutMaterialType, 2) && StringUtils.isEmpty(vo.getTransferOutWbsSummaryCode()))
                        && (Objects.equals(transferInMaterialType, 2) && StringUtils.isEmpty(vo.getTransferInWbsSummaryCode()))
                        && (Objects.equals(isOriginal, 1) && StringUtils.isEmpty(vo.getOriginalGetCode()))) {
                    iterator.remove();
                }
            }
            logger.info("获取导入的的数据为:{}", JSON.toJSONString(excelVos));
        } catch (Exception e) {
            throw new MipException("模板解析异常");
        }
        if (CollectionUtils.isEmpty(excelVos)) {
            throw new MipException("当前导入Excel模板中无法匹配有效的记录，请检查");
        }
        //业务逻辑
        Map<String, Object> resultMap = buildErrMsgInfo(excelVos, transferOutProjectId, transferOutOrganizationId, transferOutMaterialType,
                transferOutTicketTaskId, transferOutTicketTaskCode, transferInProjectId, transferInOrganizationId, transferInMaterialType, transferInTicketTaskId, transferInTicketTaskCode, isOriginal);
        DataResponse<Map<String, Object>> response = Response.dataResponse();
        return response.setData(resultMap);
    }

    /**
     * 信息构建
     *
     * @param excelVos
     * @param transferOutProjectId
     * @param transferOutOrganizationId
     * @param transferOutMaterialType
     * @param transferOutTicketTaskId
     * @param transferInProjectId
     * @param transferInOrganizationId
     * @param transferInMaterialType
     * @param transferInTicketTaskId
     * @param isOriginal
     * @return
     */
    private Map<String, Object> buildErrMsgInfo(List<MaterialCostTransferDetailTempExcelVo> excelVos,
                                                Long transferOutProjectId,
                                                Long transferOutOrganizationId,
                                                Integer transferOutMaterialType,
                                                Long transferOutTicketTaskId,
                                                String transferOutTicketTaskCode,
                                                Long transferInProjectId,
                                                Long transferInOrganizationId,
                                                Integer transferInMaterialType,
                                                Long transferInTicketTaskId,
                                                String transferInTicketTaskCode,
                                                Integer isOriginal
    ) {
        List<String> errMsgList = new ArrayList<>();
        String itemCodes = excelVos.stream().map(x -> x.getErpCode()).collect(Collectors.joining(","));
        logger.info("导入的物料集合为:{}", itemCodes);
        final Map<String, Object> param = new HashMap<>();
        param.put("transferOutOrganizationId", transferOutOrganizationId);
        param.put("transferOutMaterialType", transferOutMaterialType);
        param.put("transferOutTicketTaskId", transferOutTicketTaskId);
        param.put("transferInOrganizationId", transferInOrganizationId);
        param.put("transferInMaterialType", transferInMaterialType);
        param.put("transferInTicketTaskId", transferInTicketTaskId);
        param.put("itemCodes", itemCodes);

        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/material/listMaterialByTransferCost", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<MaterialDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<MaterialDto>>>() {
        });
        List<MaterialDto> data = response.getData();
        logger.info("获取到物料数据为:{}", JSON.toJSONString(data));
        //ProjectActivity::getCode, e -> e, (e1, e2) -> e1
        Map<String, MaterialDto> materialDtoMap = data.stream().collect(Collectors.toMap(MaterialDto::getItemCode, m -> m, (m1, m2) -> m1));
        //如果是wbs项目,查询wbs相关
        Map<String, MilepostDesignPlanDetailDto> transferOutProjectWbsNoMap = new HashMap<>();
        Map<String, MilepostDesignPlanDetailDto> transferInProjectWbsNoMap = new HashMap<>();
        if (Objects.equals(transferOutMaterialType, 2)) {
            //查询转出项目下所有wbs
            final Map<String, Object> param1 = new HashMap<>();
            param1.put("projectId", transferOutProjectId);
            String url1 = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "milepostDesignPlan/getWbsLastLayer", param1);
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(url1, String.class);
            String res1 = cleanStr(responseEntity.getBody());
            DataResponse<List<MilepostDesignPlanDetailDto>> response1 = JSON.parseObject(res1, new TypeReference<DataResponse<List<MilepostDesignPlanDetailDto>>>() {
            });
            List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailList = response1.getData();
            logger.info("转出项目的wbs号合集:{}", JSON.toJSONString(milepostDesignPlanDetailList));
            transferOutProjectWbsNoMap = milepostDesignPlanDetailList.stream()
                    .filter(e -> org.apache.commons.lang3.StringUtils.isNotEmpty(e.getWbsSummaryCode()))
                    .collect(Collectors.toMap(MilepostDesignPlanDetailDto::getWbsSummaryCode, Function.identity(), (a, b) -> a));
        }

        if (Objects.equals(transferInMaterialType, 2)) {
            //查询转入项目下所有wbs
            final Map<String, Object> param1 = new HashMap<>();
            param1.put("projectId", transferInProjectId);
            String url1 = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "milepostDesignPlan/getWbsLastLayer", param1);
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(url1, String.class);
            String res1 = cleanStr(responseEntity.getBody());
            DataResponse<List<MilepostDesignPlanDetailDto>> response1 = JSON.parseObject(res1, new TypeReference<DataResponse<List<MilepostDesignPlanDetailDto>>>() {
            });
            List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailList = response1.getData();
            logger.info("转入项目的wbs号合集:{}", JSON.toJSONString(milepostDesignPlanDetailList));
            transferInProjectWbsNoMap = milepostDesignPlanDetailList.stream()
                    .filter(e -> org.apache.commons.lang3.StringUtils.isNotEmpty(e.getWbsSummaryCode()))
                    .collect(Collectors.toMap(MilepostDesignPlanDetailDto::getWbsSummaryCode, Function.identity(), (a, b) -> a));
        }

        //原领料单
        Map<String, MaterialGetHeader> getHeaderMap = new HashMap<>();
        if (Objects.equals(isOriginal, 0)) {
            final Map<String, Object> param3 = new HashMap<>();
            param3.put("materialCodes", itemCodes);
            final String url3 = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/materialGet/queryMaterialGetHeaderByMaterialCode", param3);
            String res3 = restTemplate.getForEntity(url3, String.class).getBody();
            DataResponse<List<MaterialGetHeader>> dataResponse = JSON.parseObject(res3, new TypeReference<DataResponse<List<MaterialGetHeader>>>() {
            });
            List<MaterialGetHeader> materialGetHeaderList = dataResponse.getData();
            logger.info("获取的原领料单数据为:{}", JSON.toJSONString(materialGetHeaderList));
            getHeaderMap = materialGetHeaderList.stream().collect(Collectors.toMap(MaterialGetHeader::getGetCode, Function.identity(), (a, b) -> a));
        }

        //校验数据
        List<MaterialCostTransferDetailDto> dataList = new ArrayList<>();
        //项目类型判断标识
        logger.info("转出项目的类型为:{},转入项目的项目类型为:{}", transferOutMaterialType, transferInMaterialType);
        boolean outMaterialTypeFlag = Objects.equals(transferOutMaterialType, 1) || Objects.equals(transferOutMaterialType, 2);
        boolean inMaterialTypeFlag = Objects.equals(transferInMaterialType, 1) || Objects.equals(transferInMaterialType, 2);
        logger.info("当转入或转出项目类型为1或2时标记返回true,转出项目类型标记为:{},转入项目类型标记为:{}", outMaterialTypeFlag, inMaterialTypeFlag);
        for (int i = 0; i < excelVos.size(); i++) {
            MaterialCostTransferDetailTempExcelVo excelVo = excelVos.get(i);
            //校验申请数量
            if (StringUtils.isNotEmpty(excelVo.getApplyNum())) {
                if (BigDecimalUtils.isBigDecimal(excelVo.getApplyNum())) {
                    BigDecimal applyAmount = new BigDecimal(excelVo.getApplyNum());
                    if (applyAmount.compareTo(BigDecimal.ZERO) < 0) {
                        errMsgList.add(String.format("行：%s，申请转移数量填写有误", i + 3));
                    }
                    excelVo.setApplyAmount(applyAmount);
                } else {
                    errMsgList.add(String.format("行：%s，申请转移数量填写有误", i + 3));
                }
            }
            //检验项目wbs号,这个在项目领/退料(Wbs)时检查
            if (Objects.equals(transferOutMaterialType, 2)) {
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(excelVo.getTransferOutWbsSummaryCode())) {
                    MilepostDesignPlanDetailDto milepostDesignPlanDetailDto = transferOutProjectWbsNoMap.get(excelVo.getTransferOutWbsSummaryCode());
                    if (milepostDesignPlanDetailDto == null) {
                        errMsgList.add(String.format("行：%s，转出项目wbs不存在", i + 3));
                    } else {
                        String wbsDescription = milepostDesignPlanDetailDto.getWbsDescription();
                        excelVo.setTransferOutWbsDescription(wbsDescription);
                    }
                }
            }

            if (Objects.equals(transferInMaterialType, 2)) {
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(excelVo.getTransferInWbsSummaryCode())) {
                    MilepostDesignPlanDetailDto milepostDesignPlanDetailDto = transferInProjectWbsNoMap.get(excelVo.getTransferInWbsSummaryCode());
                    if (milepostDesignPlanDetailDto == null) {
                        errMsgList.add(String.format("行：%s，转入项目wbs不存在", i + 3));
                    } else {
                        String wbsDescription = milepostDesignPlanDetailDto.getWbsDescription();
                        excelVo.setTransferInWbsDescription(wbsDescription);
                    }
                }
            }
            //原领料单校验
            if (Objects.equals(isOriginal, 0)) {
                if (!StringUtils.isEmpty(excelVo.getOriginalGetCode())) {
                    //导入是原领料单为空是不校验,页面可以选择
                    MaterialGetHeader header = getHeaderMap.get(excelVo.getOriginalGetCode());
                    if (header == null) {
                        errMsgList.add(String.format("行：%s，原领料单必须满足以下条件：（1）状态为：已处理的领料单（2）同步状态为：已同步（3）领料单包含该物料", i + 3));
                    }
                }
            }

            //校验物料编号
            if (StringUtils.isEmpty(excelVo.getErpCode())) {
                errMsgList.add(String.format("行：%s，物料编码缺失", i + 3));
            } else {
                MaterialDto item = materialDtoMap.get(excelVo.getErpCode());
                if (item == null) {
                    //工单退料:0,项目退料:1,项目退料(WBS):2
                    //转入和转出项目都为项目领料或项目退料(WBS)
                    if (outMaterialTypeFlag && inMaterialTypeFlag) {
                        errMsgList.add(String.format("行：%s，物料在该库存组织下不存在", i + 3));
                    } else if (Objects.equals(transferOutMaterialType, 0) && inMaterialTypeFlag) {
                        errMsgList.add(String.format("行：%s，物料在工单任务: %s中不存在", i + 3, transferOutTicketTaskCode));
                    } else if (Objects.equals(transferInMaterialType, 0) && outMaterialTypeFlag) {
                        errMsgList.add(String.format("行：%s，物料在工单任务: %s中不存在", i + 3, transferInTicketTaskCode));
                    } else {
                        errMsgList.add(String.format("行：%s，物料在转出工单任务: %s,在转入工单任务: %s中不存在", i + 3, transferOutTicketTaskCode, transferInTicketTaskCode));
                    }
                } else {
                    //物料存在,这里需要返回列表信息
                    MaterialCostTransferDetailDto detailDto = new MaterialCostTransferDetailDto();
                    detailDto.setUnitName(item.getUnitName());
                    detailDto.setMaterialId(item.getId());
                    detailDto.setMaterialErpId(item.getItemId());
                    detailDto.setMaterialCode(item.getItemCode());
                    detailDto.setMaterialName(item.getItemInfo());
                    detailDto.setUnit(item.getUnit());
                    detailDto.setApplyAmount(excelVo.getApplyAmount());
                    if (Objects.equals(transferOutMaterialType, 2)) {//wbs项目时才显示,否则输入也不显示
                        detailDto.setTransferOutWbsSummaryCode(excelVo.getTransferOutWbsSummaryCode());
                        detailDto.setTransferOutWbsDescription(excelVo.getTransferOutWbsDescription());
                    }
                    if (Objects.equals(transferInMaterialType, 2)) {//wbs项目时才显示,否则输入也不显示
                        detailDto.setTransferInWbsSummaryCode(excelVo.getTransferInWbsSummaryCode());
                        detailDto.setTransferInWbsDescription(excelVo.getTransferInWbsDescription());
                    }
                    if (Objects.equals(isOriginal, 0)) {//选择原领料单时才展示,否则不展示该字段
                        detailDto.setOriginalGetCode(excelVo.getOriginalGetCode());
                    }

                    detailDto.setRemark(excelVo.getRemark());
                    dataList.add(detailDto);
                }

            }
        }
        Map<String, Object> map = new HashMap();
        map.put("flag", CollectionUtils.isEmpty(errMsgList));
        map.put("errMsg", errMsgList);
        if (CollectionUtils.isEmpty(errMsgList)) {
            map.put("dataList", dataList);
        }

        return map;

    }

    /**
     * 成本转移单错误信息下载
     *
     * @param file
     * @param errMsg
     * @param response
     */
    @ApiOperation(value = "下载错误数据", notes = "场景：物料成本转移单批量导入错误信息下载")
    @PostMapping("/detail/downloadErrorMsg")
    public void downloadErrorMsg(@RequestPart("file") MultipartFile file, @RequestParam String errMsg, HttpServletResponse response) {
        List<String> errMsgList = null;
        try {
            errMsgList = JSONObject.parseArray(errMsg, String.class);
        } catch (Exception e) {
            throw new MipException("参数异常");
        }
        Workbook workbook = null;
        try {
            workbook = WorkbookFactory.create(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(1);
            for (int i = 0; i < errMsgList.size(); ++i) {
                Row row = sheet.createRow(i);
                row.createCell(0).setCellValue(errMsgList.get(i));
            }
        } catch (Exception e) {
            throw new MipException("模板解析异常");
        }
        //导出
        ExportExcelUtil.downLoadExcel("报错信息_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xlsx", response, workbook);
    }

    @ApiOperation(value = "库存转移单明细数据导出")
    @GetMapping("exportDetails")
    public void exportDetails(HttpServletResponse response,
                              @RequestParam(required = true) Long id,
                              @RequestParam(required = true) String costTransferCode) {
        final Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "materialCostTransfer/exportDetails", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<MaterialCostTransferDetailDto>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<MaterialCostTransferDetailDto>>>() {
        });
        List<MaterialCostTransferDetailDto> data = dataResponse.getData();
        logger.info("查询的数据量为:{}", data.size());
        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("成本转移单" + costTransferCode + "_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");
        List<MaterialCostTransferDetailsExcelVO> excelVOS = com.midea.pam.support.utils.BeanConverter.copy(data, MaterialCostTransferDetailsExcelVO.class);
        for (int i = 0; i < excelVOS.size(); i++) {
            MaterialCostTransferDetailsExcelVO excelVO = excelVOS.get(i);
            excelVO.setNum(i + 1);
        }
        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(excelVOS, MaterialCostTransferDetailsExcelVO.class, null, "项目成本转移明细", true);
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }


}
