package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.entity.DifferenceShare;
import com.midea.pam.common.ctc.vo.*;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("差异分摊")
@RestController
@RequestMapping("differenceShare")
public class DifferenceShareController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "差异分摊入账单创建")
    @PostMapping("create")
    public Response createDifferenceShare(@RequestBody DifferenceShare differenceShare) {
        String url = String.format("%sdifferenceShare/create", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, differenceShare, String.class).getBody();
        DataResponse<List<DifferenceShare>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<DifferenceShare>>>() {
        });
        return response;
    }

    @ApiOperation(value = "差异分摊入账单列表")
    @GetMapping("list")
    public Response list(@RequestParam(required = true) final String glPeriod,
                         @RequestParam(required = true) final Long ouId) {
        Map<String, Object> param = new HashMap<>();
        param.put("glPeriod", glPeriod);
        param.put("ouId", ouId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "differenceShare/list", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<DifferenceShare>>>() {
        });
    }

    @ApiOperation(value = "上次差异分摊入账单列表")
    @GetMapping("list/last")
    public Response listLast(@RequestParam(required = true) final Long ouId) {
        Map<String, Object> param = new HashMap<>();
        param.put("ouId", ouId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "differenceShare/list/last", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<DifferenceShare>>>() {
        });
    }

    @ApiOperation(value = "重新执行差异分摊入账单创建")
    @PostMapping("reRun")
    public Response reRun(@RequestBody DifferenceShare differenceShare) {
        String url = String.format("%sdifferenceShare/reRun", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, differenceShare, String.class).getBody();
        DataResponse<String> response = JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
        return response;
    }

    @ApiOperation(value = "发票价差生成批次号")
    @GetMapping("invoicePrice")
    public Response invoicePriceGenerate(@RequestParam(required = true) final String glPeriod,
                                         @RequestParam(required = true) final Long ouId,
                                         @RequestParam Long dataSyncId,
                                         @RequestParam Long batchNum) {
        Map<String, Object> param = new HashMap<>();
        param.put("glPeriod", glPeriod);
        param.put("ouId", ouId);
        param.put("dataSyncId", dataSyncId);
        param.put("batchNum", batchNum);

        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "differenceShare/invoicePrice", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<EsbResponse>>() {
        });
    }

    @ApiOperation(value = "发票价差数据同步")
    @GetMapping("invoicePrice/sync")
    public Response invoicePriceSync(@RequestParam(required = true) final Long id) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);

        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "differenceShare/invoicePrice/sync", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "科目余额生成批次号")
    @GetMapping("subjectBalance")
    public Response subjectBalanceGenerate(@RequestParam(required = true) final String glPeriod,
                                           @RequestParam(required = true) final Long ouId,
                                           @RequestParam Long dataSyncId,
                                           @RequestParam Long batchNum) {
        Map<String, Object> param = new HashMap<>();
        param.put("glPeriod", glPeriod);
        param.put("ouId", ouId);
        param.put("dataSyncId", dataSyncId);
        param.put("batchNum", batchNum);

        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "differenceShare/subjectBalance", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<EsbResponse>>() {
        });
    }

    @ApiOperation(value = "科目余额数据同步")
    @GetMapping("subjectBalance/sync")
    public Response subjectBalanceSync(@RequestParam(required = true) final Long id) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);

        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "differenceShare/subjectBalance/sync", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "同步")
    @GetMapping("differenceShareDataDetailSync")
    public Response differenceShareDataDetailSyncJob() {
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "differenceShare/differenceShareDataDetailSync", null);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "erp同步")
    @GetMapping("differenceShareErpDataSync")
    public Response differenceShareErpDataSync() {
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "differenceShare/differenceShareErpDataSync", null);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "差异分摊导出")
    @GetMapping("export")
    public void export(HttpServletResponse response, @RequestParam Long accountId) throws Exception {
        Map<String, Object> param = new HashMap<>();
        param.put("accountId", accountId);

        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "differenceShare/getExportDetail", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<DifferenceShareAccountExcelVO> differenceShareAccountExcelVODataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<DifferenceShareAccountExcelVO>>() {
        });

        DifferenceShareAccountExcelVO differenceShareAccountExcelVO = differenceShareAccountExcelVODataResponse.getData();
        if (differenceShareAccountExcelVO == null) {
            throw new Exception("没有数据");
        }

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("差异分摊");
        fileName.append(".xls");

        List<InvoicePriceDifferenceRecordExcelVO> invoicePriceDifferenceRecordExcelVOS = differenceShareAccountExcelVO.getInvoicePriceDifferenceRecordExcelVOS();
        List<MaterialUpdateDifferenceRecordExcelVO> materialUpdateDifferenceRecordExcelVOS = differenceShareAccountExcelVO.getMaterialUpdateDifferenceRecordExcelVOS();
        List<PurchasePriceDifferenceRecordExcelVO> purchasePriceDifferenceRecordExcelVOS = differenceShareAccountExcelVO.getPurchasePriceDifferenceRecordExcelVOS();
        List<DifferenceShareAccountSummaryExcelVO> summaryExcelVOS = differenceShareAccountExcelVO.getSummaryExcelVOS();
        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(summaryExcelVOS, DifferenceShareAccountSummaryExcelVO.class, null, "分摊表-汇总", true);
        ExportExcelUtil.addSheet(workbook, invoicePriceDifferenceRecordExcelVOS, InvoicePriceDifferenceRecordExcelVO.class, null, "发票价差", true);
        ExportExcelUtil.addSheet(workbook, materialUpdateDifferenceRecordExcelVOS, MaterialUpdateDifferenceRecordExcelVO.class, null, "成本更新差异", true);
        ExportExcelUtil.addSheet(workbook, purchasePriceDifferenceRecordExcelVOS, PurchasePriceDifferenceRecordExcelVO.class, null, "订单价差", true);


        List<DifferenceShareAccountDetailExcelVO> detailExcelVOS = differenceShareAccountExcelVO.getDetailExcelVOS();
        List<DifferenceShareResultDetailExcelVO> directExcelVOS = differenceShareAccountExcelVO.getDirectExcelVOS();
        List<DifferenceShareResultDetailExcelVO> indirectExcelVOS = differenceShareAccountExcelVO.getIndirectExcelVOS();


        if (directExcelVOS != null) {
            ExportExcelUtil.addSheet(workbook, directExcelVOS, DifferenceShareResultDetailExcelVO.class, null, "直接差异", true);
        }
        ExportExcelUtil.addSheet(workbook, indirectExcelVOS, DifferenceShareResultDetailExcelVO.class, null, "间接差异", true);
        ExportExcelUtil.addSheet(workbook, detailExcelVOS, DifferenceShareAccountDetailExcelVO.class, null, "入账明细", true);

        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }
}
