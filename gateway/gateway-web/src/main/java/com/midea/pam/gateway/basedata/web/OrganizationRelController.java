package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.entity.OrganizationRel;
import com.midea.pam.common.basedata.query.OrganizationRelQuery;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.PageResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("organizationRel")
@Api("组织关系")
public class OrganizationRelController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "组织关系分页查询")
    @GetMapping("list")
    public Response list(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize,
                         @RequestParam(required = false) @ApiParam(value = "事业部名称") String busDeptName,
                         @RequestParam(required = false) @ApiParam(value = "法人主体名称") String legalEntityName,
                         @RequestParam(required = false) @ApiParam(value = "业务实体ID") String operatingUnitId,
                         @RequestParam(required = false) @ApiParam(value = "业务实体名称") String operatingUnitName,
                         @RequestParam(required = false) @ApiParam(value = "库存组织名称") String organizationName,
                         @RequestParam(required = false) @ApiParam(value = "业务实体") String ous,
                         @RequestParam(required = false) @ApiParam(value = "是否启用 0:启用 1:不启用") Integer pamEnabled){
        final Map<String, Object> param = new HashMap<>();
        param.put("busDeptName", busDeptName);
        param.put("legalEntityName", legalEntityName);
        param.put("operatingUnitName", operatingUnitName);
        param.put("operatingUnitId", operatingUnitId);
        param.put("organizationName", organizationName);
        param.put("pamEnabled", pamEnabled);
        param.put("ous", ous);

        param.put("pageNum", pageNum);
        param.put("pageSize",pageSize);

        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "organizationRel/list",param);
        String res = cleanStr(restTemplate.getForObject(url, String.class));
        PageInfo<OrganizationRelDto> data = JSON.parseObject(res, new TypeReference<PageInfo<OrganizationRelDto>>(){});
        PageResponse<OrganizationRelDto> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation(value = "业务实体类型列表")
    @GetMapping("selectOperatingUnit")
    public Response selectOperatingUnit(OrganizationRelQuery query){
        final Map<String, Object> param = new HashMap<>();
        param.put("pamEnabled", query.getPamEnabled());
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "organizationRel/selectOperatingUnit",param);
        String res = cleanStr(restTemplate.getForObject(url, String.class));
        List<OrganizationRelDto> data = JSON.parseObject(res, new TypeReference<List<OrganizationRelDto>>(){});
        DataResponse<List<OrganizationRelDto>> response = Response.dataResponse();
        return response.setData(data);
    }

    @ApiOperation(value = "库存组织列表")
    @GetMapping("selectOrganization")
    public Response selectOrganization(OrganizationRelQuery query){
        final Map<String, Object> param = new HashMap<>();
        param.put("pamEnabled", query.getPamEnabled());
        param.put("organizationName", query.getOrganizationName());
        param.put("organizationId", query.getOrganizationId());
        param.put("ous", query.getOus());
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "organizationRel/selectOrganization",param);
        String res = cleanStr(restTemplate.getForObject(url, String.class));
        List<OrganizationRelDto> data = JSON.parseObject(res, new TypeReference<List<OrganizationRelDto>>(){});
        DataResponse<List<OrganizationRelDto>> response = Response.dataResponse();
        return response.setData(data);
    }

    @ApiOperation("更新组织关系")
    @PutMapping({"/update"})
    public Response update(@RequestBody OrganizationRelDto organizationRelDto) {
        String url = String.format("%sorganizationRel/update",ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, organizationRelDto, String.class);
        String res = cleanStr(responseEntity.getBody());
        OrganizationRelDto data = JSON.parseObject(res, new TypeReference<OrganizationRelDto>(){});
        DataResponse<OrganizationRelDto> response = Response.dataResponse();
        return response.setData(data);
    }

    @ApiOperation(value = "查询有效的库存组织")
    @GetMapping("listOrganizationByUserId")
    public Response listOrganizationByUserId(@RequestParam(required = false) final Long userId) {
        final Map<String, Object> params = new HashMap<>();
        params.put(Constants.Page.USER_ID, userId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "organizationRel/listOrganizationByUserId",params);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<OrganizationRel>>>(){});
    }

    @ApiOperation(value = "查询当前使用单位有效的库存组织")
    @GetMapping("selectCurrentOrganization")
    public Response selectCurrentOrganization() {
        final Map<String, Object> params = new HashMap<>();
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "organizationRel/selectCurrentOrganization",params);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<OrganizationRel>>>(){});
    }

    @ApiOperation(value = "getOrganizationRelFromErp")
    @GetMapping("getOrganizationRelFromErp")
    public Response getOrganizationRelFromErp(@RequestParam(required = false) String lastUpdateDate){
        final Map<String, Object> param = new HashMap<>();
        param.put("lastUpdateDate", lastUpdateDate);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "organizationRel/getOrganizationRelFromErp",param);
        restTemplate.getForObject(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        response.setData("0");
        return response;
    }

    @ApiOperation(value = "查询有效的库存组织")
    @PostMapping("queryByUnitId")
    public Response queryByUnitId(@RequestBody(required = false) final List<Long> unitIds) {
        String url = String.format("%sorganizationRel/unitId",ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, unitIds, String.class);
        String res = cleanStr(responseEntity.getBody());
        return JSON.parseObject(res, new TypeReference<DataResponse<List<OrganizationRel>>>(){});
    }
}
