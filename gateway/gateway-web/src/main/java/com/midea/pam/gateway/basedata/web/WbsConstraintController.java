package com.midea.pam.gateway.basedata.web;

import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/8
 */
@RestController
@RequestMapping("wbsConstraint")
@Api("wbs约束管理")
public class WbsConstraintController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @GetMapping("getByOrgLaborId")
    @ApiOperation(value = "根据HR部门费率类型查找wbs约束")
    public Response getByOrgLaborId(@RequestParam("id") Long id) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "wbsConstraint/getByOrgLaborId?id="+id;
        return Response.dataResponse().setData(restTemplate.getForObject(url, List.class));
    }

    @GetMapping("getUserWbsConstraint")
    @ApiOperation(value = "获取当前用户HR部门费率类型关联的wbs约束")
    public Response getUserWbsConstraint(){
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "wbsConstraint/getUserWbsConstraint";
        return Response.dataResponse().setData(restTemplate.getForObject(url, List.class));
    }
}
