package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.DifferenceShareAccountDTO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-11-22
 * @description
 */
@Api("差异分摊入账单")
@RestController
@RequestMapping("differenceShareAccount")
public class DifferenceShareAccountController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "差异分摊入账单分页查询")
    @GetMapping("page")
    public Response page(@RequestParam(required = false) final String accountNum,
                         @RequestParam(required = false) final String statusStr,
                         @RequestParam(required = false) final String typeStr,
                         @RequestParam(required = false) final String glPeriod,
                         @RequestParam(required = false) final String currency,
                         @RequestParam(required = false) final String woPeriod,
                         @RequestParam(required = false) final String syncStatusStr,
                         @RequestParam(required = false) final Long ouId,
                         @RequestParam(required = false) @ApiParam(value = "业务实体") String ouName,
                         @RequestParam(required = false) final String ids,
                         @RequestParam(required = false) final String ouIdsStr,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {

        Map<String, Object> param = new HashMap<>();
        param.put("accountNum", accountNum);
        param.put("statusStr", statusStr);
        param.put("typeStr", typeStr);
        param.put("glPeriod", glPeriod);
        param.put("currency", currency);
        param.put("woPeriod", woPeriod);
        param.put("syncStatusStr", syncStatusStr);
        param.put("ouId", ouId);
        param.put("ouName", ouName);
        param.put("ids", ids);
        param.put("ouIdsStr", ouIdsStr);
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "differenceShareAccount/page", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<DifferenceShareAccountDTO>>>() {
        });
    }

    @ApiOperation(value = "差异分摊入账单明细查询")
    @GetMapping("detail/{id}")
    public Response page(@PathVariable final Long id) {
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "differenceShareAccount/detail/" + id, null);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<DifferenceShareAccountDTO>>() {
        });
    }

    @ApiOperation(value = "期初差异分摊入账单")
    @GetMapping("beginningCalculate")
    public Response page(@RequestParam(required = true) final String glPeriod,
                         @RequestParam(required = true) final Long ouId) {
        Map<String, Object> param = new HashMap<>();
        param.put("glPeriod", glPeriod);
        param.put("ouId", ouId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "differenceShareAccount/beginningCalculate", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "期初差异分摊同步入账单")
    @GetMapping("sync")
    public Response sync(@RequestParam(required = true) final Long id) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "differenceShareAccount/sync", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "差异分摊同步入账单作废")
    @GetMapping("invalid")
    public Response invalidAccount(@RequestParam(required = true) final Long id) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "differenceShareAccount/invalid", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
    }

}
