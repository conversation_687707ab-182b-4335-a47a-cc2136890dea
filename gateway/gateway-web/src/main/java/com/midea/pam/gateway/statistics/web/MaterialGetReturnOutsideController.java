package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.vo.MaterialGetReturnOutsideVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Api("外围系统查询领退料单信息")
@RestController
@RequestMapping("statistics/materialGetReturnOutside")
public class MaterialGetReturnOutsideController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @GetMapping("query")
    public Response getMaterialGetReturnOutside(@RequestParam("formNumber") String formNumber) {
        final Map<String, Object> params = new HashMap<>();
        params.put("formNumber", formNumber);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/materialGetReturnOutside/query", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<MaterialGetReturnOutsideVO> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<MaterialGetReturnOutsideVO>>() {
        });
        return response;
    }
}
