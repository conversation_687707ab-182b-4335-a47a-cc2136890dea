package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.pam.common.basedata.dto.LaborCostDto;
import com.midea.pam.common.basedata.dto.LaborCostImportDto;
import com.midea.pam.common.basedata.dto.LaborCostTypeDto;
import com.midea.pam.common.basedata.entity.LaborCostType;
import com.midea.pam.common.basedata.entity.LaborExternalCost;
import com.midea.pam.common.basedata.excelVo.LaborCostRoleExcelVo;
import com.midea.pam.common.basedata.excelVo.LaborCostRoleTempExcelVo;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.BudgetHumanDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.common.utils.FileUtil;
import com.midea.pam.support.utils.BeanConverter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@Api("人力费用")
@RequestMapping({"laborCost", "mobile/app/laborCost"})
public class LaborCostController extends ControllerHelper {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation("新增人力费用")
    @PostMapping({"add"})
    public Response add(@RequestBody LaborCostDto dto) {
        String url = String.format("%slaborCost/add", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<LaborCostDto> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<LaborCostDto>>() {
        });
        return response;
    }

    @ApiOperation("修改人力费用")
    @PostMapping({"update"})
    public Response update(@RequestBody LaborCostDto dto) {
        String url = String.format("%slaborCost/update", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<LaborCostDto> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<LaborCostDto>>() {
        });
        return response;
    }

    @ApiOperation("人力费用详情")
    @GetMapping({"detail"})
    public Response detail() {
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "laborCost/detail", new HashMap<>());
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<LaborCostDto> response = JSON.parseObject(cleanStr(res), new TypeReference<DataResponse<LaborCostDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "角色费率列表")
    @GetMapping("detailList")
    public Response detailList(@RequestParam(required = false) Integer selfFlag) {
        final Map<String, Object> param = new HashMap<>();
        param.put("selfFlag", selfFlag);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCost/detailList", param);
        return restTemplate.getForObject(url, DataResponse.class);
    }

    @ApiOperation(value = "人力费用详情，兼容瑞士格")
    @GetMapping("laborCostDetail")
    public Response laborCostDetail(@RequestParam(required = false) Long userId,
                                    @RequestParam(required = false) Long unitId) {
        Map<String, Object> params = new HashMap<>(2);
        params.put("userId", userId);
        params.put("unitId", unitId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "laborCost/laborCostDetail", params);
        return restTemplate.getForObject(url, DataResponse.class);
    }

    @ApiOperation("删除人力费用")
    @GetMapping({"delete"})
    public Response delete(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCost/delete", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(cleanStr(res), new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation("人力费用调换排序")
    @GetMapping({"changeSeq"})
    public Response changeSeq(@RequestParam @ApiParam("更换顺序对象id") Long beginId,
                              @RequestParam @ApiParam("被更换顺序对象id") Long endId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("beginId", beginId);
        param.put("endId", endId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCost/changeSeq", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(cleanStr(res), new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "人力费用分页查询")
    @GetMapping("selectPage")
    public Response selectPage(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                               @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                               @RequestParam(required = false) @ApiParam("类型,1=角色;") Integer type,
                               @RequestParam(required = false) @ApiParam("所属分类id") Long costTypeId,
                               @RequestParam(required = false) @ApiParam("角色名称") String name,
                               @RequestParam(required = false) @ApiParam("角色类型，多个用,隔开") String roleTypeStr,
                               @RequestParam(required = false) @ApiParam("使用单位") Long bizUnitId) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("type", type);
        param.put("costTypeId", costTypeId);
        if (StringUtils.isNotEmpty(name)) {
            param.put("name", URLEncoder.encode(name, StandardCharsets.UTF_8.name()));
        }
        param.put("bizUnitId", bizUnitId);
        param.put("roleTypeStr", roleTypeStr);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCost/selectPage", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<LaborCostDto>> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<PageInfo<LaborCostDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "人力费用列表查询")
    @GetMapping("selectList")
    public Response selectList(@RequestParam(required = false) @ApiParam("类型,1=角色;") Integer type,
                               @RequestParam(required = false) @ApiParam("角色类型：0-内部，1-自招外包") Integer roleType,
                               @RequestParam(required = false) @ApiParam("所属分类id") Long costTypeId,
                               @RequestParam(required = false) @ApiParam(value = "业务实体id") Long ouId,
                               @RequestParam(required = false) @ApiParam(value = "币种") String currencyCode,
                               @RequestParam(required = false) @ApiParam("使用单位") Long bizUnitId) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("type", type);
        param.put("roleType", roleType);
        param.put("costTypeId", costTypeId);
        param.put("bizUnitId", bizUnitId);
        param.put("ouId", ouId);
        param.put("currencyCode", currencyCode);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCost/selectList", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<LaborCostDto>> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<List<LaborCostDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "职级人力费用分页查询")
    @GetMapping("rankLaborCostPage")
    public Response rankLaborCostPage(@RequestParam(required = false) @ApiParam("使用单位") Long bizUnitId,
                                      @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                      @RequestParam(required = false, defaultValue = "10") Integer pageSize) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("bizUnitId", bizUnitId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCost/rankLaborCostPage", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<LaborCostDto>> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<PageInfo<LaborCostDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "人力费用角色分类列表查询")
    @GetMapping("laborCostTypeList")
    public Response laborCostTypeList(@RequestParam(required = false) @ApiParam(value = "顶层使用单位ID") Long unitId) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("unitId", unitId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCost/laborCostTypeList", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<LaborCostTypeDto>> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<List<LaborCostTypeDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "查询有设置过角色的人力费用角色分类列表")
    @GetMapping("getHasRole")
    public Response getHasRole(@RequestParam(required = false) Long bizUnitId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("bizUnitId", bizUnitId);
        String url = String.format("%slaborCost/getHasRole", ModelsEnum.BASEDATA.getBaseUrl(), param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<LaborCostType>> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<List<LaborCostType>>>() {
        });
        return response;
    }

    @ApiOperation(value = "查询自招外包的人力费用角色分类列表")
    @GetMapping("getConfessRole")
    public Response getConfessRole(@RequestParam(required = false) Long bizUnitId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("bizUnitId", bizUnitId);
        String url = String.format("%slaborCost/getConfessRole", ModelsEnum.BASEDATA.getBaseUrl(), param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<LaborCostType>> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<List<LaborCostType>>>() {
        });
        return response;
    }

    @ApiOperation(value = "查询有设置过角色的人力费用角色分类列表(外部)")
    @GetMapping("getExHasRole")
    public Response getExHasRole(@RequestParam(required = false) Long bizUnitId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("bizUnitId", bizUnitId);
        String url = String.format("%slaborCost/getExHasRole", ModelsEnum.BASEDATA.getBaseUrl(), param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<LaborCostType>> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<List<LaborCostType>>>() {
        });
        return response;
    }

    @ApiOperation("批量保存人力费用角色分类")
    @PostMapping({"saveBatchLaborCostType"})
    public Response saveBatchLaborCostType(@RequestBody List<LaborCostTypeDto> dtos) {
        String url = String.format("%slaborCost/saveBatchLaborCostType", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dtos, String.class);
        DataResponse<Boolean> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation("新增人力费用角色分类")
    @PostMapping({"addLaborCostType"})
    public Response addLaborCostType(@RequestBody LaborCostTypeDto dto) {
        String url = String.format("%slaborCost/addLaborCostType", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<LaborCostTypeDto> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<LaborCostTypeDto>>() {
        });
        return response;
    }

    @ApiOperation("删除人力费用角色分类")
    @GetMapping({"deleteLaborCostType"})
    public Response deleteLaborCostType(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCost/deleteLaborCostType", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(cleanStr(res), new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "查询人力职级单价信息")
    @GetMapping("queryLabCostByUserId")
    public Response queryLabCostByUserId(@RequestParam final String priceType,
                                         @RequestParam final String userIds,
                                         @RequestParam(required = false) final String unitId,
                                         @RequestParam(required = false, defaultValue = "true") final Boolean check) {
        final Map<String, Object> param = new HashMap<>();
        param.put("priceType", priceType);
        param.put("userIds", userIds);
        param.put("unitId", unitId);
        param.put("check", check);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCost/queryLabCostByUserId", param), String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<BudgetHumanDto>>>() {
        });
    }

    @ApiOperation(value = "查询职级单价信息")
    @GetMapping("queryLabCost")
    public Response queryLabCost(@RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                 @RequestParam(required = false, defaultValue = "10") final Integer pageSize,
                                 @RequestParam final String priceType,
                                 @RequestParam(required = false) final Integer type,
                                 @RequestParam(required = false) @ApiParam(value = "业务实体id") Long ouId,
                                 @RequestParam(required = false) @ApiParam(value = "币种") String currencyCode,
                                 @RequestParam(required = false) @ApiParam("使用单位") Long bizUnitId) {
        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        param.put("priceType", priceType);
        param.put("type", type);
        param.put("bizUnitId", bizUnitId);
        param.put("ouId", ouId);
        param.put("currencyCode", currencyCode);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCost/queryLabCost", param), String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<BudgetHumanDto>>>() {
        });

    }

    @ApiOperation(value = "查询商机人力职级单价信息")
    @GetMapping("queryLabCostByUserIdAndBusinessId")
    public Response queryLabCostByUserIdAndBusinessId(@RequestParam(required = false) final Long businessId,
                                                      @RequestParam(required = false) final Long customerId,
                                                      @RequestParam final String userIds,
                                                      @RequestParam(required = false) @ApiParam(value = "业务实体id") Long ouId,
                                                      @RequestParam(required = false) @ApiParam(value = "币种") String currencyCode,
                                                      @RequestParam(required = false, defaultValue = "true") final boolean check) {
        final Map<String, Object> param = new HashMap<>();
        param.put("businessId", businessId);
        param.put("customerId", customerId);
        param.put("userIds", userIds);
        param.put("check", check);
        param.put("ouId", ouId);
        param.put("currencyCode", currencyCode);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(),
                "/laborCost/queryLabCostByUserIdAndBusinessId", param), String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<BudgetHumanDto>>>() {
        });
    }

    @ApiOperation(value = "查询商机角色或职级人力单价信息", response = LaborCostDto.class)
    @GetMapping("queryLabCostByBusinessId")
    public Response queryLabCostByBusinessId(@RequestParam(required = false) @ApiParam("商机id") final Long businessId,
                                             @RequestParam(required = false) final Long customerId,
                                             @RequestParam(required = false) @ApiParam(value = "业务实体id") Long ouId,
                                             @RequestParam(required = false) @ApiParam(value = "币种") String currencyCode,
                                             @RequestParam @ApiParam("查询id,1=人力费用id;2=职级id;") final String ids,
                                             @RequestParam @ApiParam("类型,1=角色;2=职级;") final Integer type) {
        final Map<String, Object> param = new HashMap<>();
        param.put("businessId", businessId);
        param.put("customerId", customerId);
        param.put("ids", ids);
        param.put("type", type);
        param.put("ouId", ouId);
        param.put("currencyCode", currencyCode);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(),
                "/laborCost/queryLabCostByBusinessId", param), String.class);
        DataResponse<List<LaborCostDto>> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<List<LaborCostDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "查询商机角色或职级人力单价信息(外部)", response = LaborCostDto.class)
    @GetMapping("queryLabExternalCostByBusinessId")
    public Response queryLabExternalCostByBusinessId(@RequestParam @ApiParam("查询id,1=人力费用id;2=职级id;") final String ids,
                                                     @RequestParam @ApiParam("类型,1=角色;2=职级;") final Integer type) {
        final Map<String, Object> param = new HashMap<>();
        param.put("ids", ids);
        param.put("type", type);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(),
                "/laborCost/queryLabExternalCostByBusinessId", param), String.class);
        DataResponse<List<LaborExternalCost>> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<List<LaborExternalCost>>>() {
        });
        return response;
    }

    @ApiOperation("人力费用详情(瑞士格)")
    @GetMapping({"detailYL"})
    public Response detailYL(@RequestParam(required = false) Long bizUnitId,
                             @RequestParam(required = false) Integer type,
                             @RequestParam(required = false) Long userId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("bizUnitId", bizUnitId);
        param.put("type", type);
        param.put("userId", userId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCost/detailYL", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<LaborCostDto> response = JSON.parseObject(cleanStr(res), new TypeReference<DataResponse<LaborCostDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "人力费用-角色费率导出")
    @GetMapping("exportList")
    public void exportList(HttpServletResponse response,
                           @RequestParam(required = false) @ApiParam("类型,1=角色;") Integer type,
                           @RequestParam(required = false) @ApiParam("所属分类id") Long costTypeId,
                           @RequestParam(required = false) @ApiParam("角色名称") String name,
                           @RequestParam(required = false) @ApiParam("使用单位") Long bizUnitId) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("type", type);
        param.put("costTypeId", costTypeId);
        if (StringUtils.isNotEmpty(name)) {
            param.put("name", URLEncoder.encode(name, StandardCharsets.UTF_8.name()));
        }
        param.put("bizUnitId", bizUnitId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCost/exportList", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<LaborCostDto>> dataResponse = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<List<LaborCostDto>>>() {
        });
        List<LaborCostDto> data = dataResponse.getData();
        List<LaborCostRoleExcelVo> excelVos = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            LaborCostDto dto = data.get(i);
            LaborCostRoleExcelVo excelVo = BeanConverter.copy(dto, LaborCostRoleExcelVo.class);
            excelVo.setNum(i + 1);
            excelVo.setCostTypeName(dto.getCostTypeName());
            excelVo.setName(dto.getName());
            BigDecimal externalQuotation = dto.getExternalQuotation();
            excelVo.setExternalQuotation(Objects.isNull(externalQuotation) ? "" : externalQuotation.toPlainString());
            BigDecimal externalProjectCost = dto.getExternalProjectCost();
            excelVo.setExternalProjectCost(Objects.isNull(externalProjectCost) ? "" : externalProjectCost.toPlainString());
            BigDecimal internalProjectCost = dto.getInternalProjectCost();
            excelVo.setInternalProjectCost(Objects.isNull(internalProjectCost) ? "" : internalProjectCost.toPlainString());
            BigDecimal internalDevelopmentCost = dto.getInternalDevelopmentCost();
            excelVo.setInternalDevelopmentCost(Objects.isNull(internalDevelopmentCost) ? "" : internalDevelopmentCost.toPlainString());
            BigDecimal internalSecondmentCost = dto.getInternalSecondmentCost();
            excelVo.setInternalSecondmentCost(Objects.isNull(internalSecondmentCost) ? "" : internalSecondmentCost.toPlainString());
            excelVo.setProjectActivityCode(dto.getProjectActivityCode());
            excelVo.setErrMsg(null);
            excelVos.add(excelVo);
        }
        //导出操作
        export(response, excelVos);
    }


    private void export(HttpServletResponse response, List<LaborCostRoleExcelVo> excelVos) throws Exception {
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        Sheet sheet = workbook.createSheet("角色费率");
        String str = "导入说明：\n" +
                "1、序号、角色分类、角色名称不能修改，修改后如果系统不存在，系统报错：角色信息有误；\n" +
                "2、对外报价、外部项目成本、内部项目成本、内部研发成本、内部借调成本必须为正数，否则系统报错：人力费率必须为正数；\n" +
                "3、关联活动事项必须存在，否则系统报错：关联活动事项不存在；\n";
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 21));
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(CellStyle.ALIGN_LEFT);
        cellStyle.setWrapText(true);
        Row row0 = sheet.createRow(0);
        row0.setHeight((short) (1500));
        generateCell(row0, cellStyle, 0, str);

        Row row1 = sheet.createRow(1);
        Field[] fields = LaborCostRoleExcelVo.class.getDeclaredFields();
        for (int i = 0; i < fields.length; i++) {
            Field field = fields[i];
            Excel excelField = field.getAnnotation(Excel.class);
            if (excelField == null) {
                continue;
            }
            CellStyle style = workbook.createCellStyle();
            String name = excelField.name();
            logger.info("字段名称为:{},位数:{}", name, i);
            Double width = excelField.width();
            generateTitle(row1, name, style, sheet, i, width.intValue());
        }

        for (int j = 0; j < excelVos.size(); j++) {
            LaborCostRoleExcelVo detailVO = excelVos.get(j);
            Row row = sheet.createRow(j + 2);
            for (int i = 0; i < fields.length; i++) {
                Field field = fields[i];
                Excel excelField = field.getAnnotation(Excel.class);
                if (excelField == null) {
                    continue;
                }
                String fieldName = field.getName();
                Field declaredField = detailVO.getClass().getDeclaredField(fieldName);
                declaredField.setAccessible(true);
                Object obj = declaredField.get(detailVO);
                String objStr = null == obj ? "" : obj.toString();
                generateCell(row, cellStyle, i, objStr);
            }
        }

        String fileName = "角色费率_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xlsx";
        ExportExcelUtil.downLoadExcel(fileName, response, workbook);
    }

    /**
     * 在row的index处生成一个单元格,设置样式和值
     *
     * @param row       : 行
     * @param cellStyle : 单元格样式
     * @param index     : 列索引位
     * @param value     : 值
     */
    private void generateCell(Row row, CellStyle cellStyle, int index, String value) {
        Cell cell = row.createCell(index);
        cell.setCellStyle(cellStyle);
        cell.setCellType(Cell.CELL_TYPE_STRING);
        cell.setCellValue(value);
    }

    private void generateTitle(Row row, String value, CellStyle cellStyle, Sheet sheet, int index, int width) {
        Cell cell = row.createCell(index);
        if (index < 4) {
            cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        } else {
            if (index == 10) {
                cellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
            } else {
                cellStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
            }
        }
        sheet.setColumnWidth(index + 1, width * 256);
        cellStyle.setFillPattern(CellStyle.SOLID_FOREGROUND);
        cellStyle.setBorderBottom(CellStyle.BORDER_THIN);
        cellStyle.setBorderLeft(CellStyle.BORDER_THIN);
        cellStyle.setBorderTop(CellStyle.BORDER_THIN);
        cellStyle.setBorderRight(CellStyle.BORDER_THIN);
        cell.setCellStyle(cellStyle);
        cell.setCellType(Cell.CELL_TYPE_STRING);
        cell.setCellValue(value);

    }

    @ApiOperation(value = "上传文件检查数据", notes = "人力费用-角色费率导入")
    @PostMapping("/checkTemplate")
    public Response checkTemplate(@RequestPart("file") MultipartFile file,
                                  @RequestParam(required = false) @ApiParam("类型,1=角色;") Integer type,
                                  @RequestParam(required = false) @ApiParam("使用单位") Long bizUnitId,
                                  @RequestParam(required = false) @ApiParam("是否批量新增,0-是,1-否;") Integer isAdd) {
        List<LaborCostRoleTempExcelVo> excelVos = null;
        try {
            excelVos = FileUtil.importExcel(file, LaborCostRoleTempExcelVo.class, 1, 0);
            Iterator<LaborCostRoleTempExcelVo> iterator = excelVos.iterator();
            while (iterator.hasNext()) {
                LaborCostRoleTempExcelVo vo = iterator.next();
                if (StringUtils.isEmpty(vo.getCostTypeName()) && StringUtils.isEmpty(vo.getName())
                        && StringUtils.isEmpty(vo.getRoleTypeName())
                        && StringUtils.isEmpty(vo.getExternalQuotation())
                        && StringUtils.isEmpty(vo.getExternalProjectCost()) && StringUtils.isEmpty(vo.getInternalProjectCost())
                        && StringUtils.isEmpty(vo.getInternalDevelopmentCost()) && StringUtils.isEmpty(vo.getInternalSecondmentCost())
                        && StringUtils.isEmpty(vo.getProjectActivityCode())) {
                    iterator.remove();
                }
            }
        } catch (Exception e) {
            throw new MipException("模板解析异常");
        }
        logger.info("待导入的数据集合大小为:{}", excelVos.size());
        if (CollectionUtils.isEmpty(excelVos)) {
            throw new MipException("当前导入Excel模板中无法匹配有效的记录，请检查");
        }
        LaborCostImportDto dto = new LaborCostImportDto();
        dto.setExcelVos(excelVos);
        dto.setType(type);
        dto.setBizUnitId(bizUnitId);
        dto.setIsAdd(isAdd);

        String url = String.format("%slaborCost/handleImportList", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<Map<String, Object>> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<Map<String, Object>>>() {
        });
        return response;
    }

    @ApiOperation(value = "下载错误数据", notes = "人力费用-角色费率导入")
    @PostMapping("/downloadErrorMsg")
    public void downloadErrorMsg(@RequestPart("file") MultipartFile file, @RequestParam String errMsg, HttpServletResponse response) {
        List<LaborCostRoleTempExcelVo> errMsgList = null;
        try {
            errMsgList = JSONObject.parseArray(errMsg, LaborCostRoleTempExcelVo.class);
        } catch (Exception e) {
            throw new MipException("参数异常");
        }
        Workbook workbook = null;
        try {
            workbook = WorkbookFactory.create(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);
            CellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(CellStyle.ALIGN_LEFT);
            cellStyle.setWrapText(true);
            Field[] fields = LaborCostRoleExcelVo.class.getDeclaredFields();
            for (int j = 0; j < errMsgList.size(); j++) {
                LaborCostRoleTempExcelVo excelVo = errMsgList.get(j);
                Row row = sheet.createRow(j + 2);
                for (int i = 0; i < fields.length; i++) {
                    Field field = fields[i];
                    Excel excelField = field.getAnnotation(Excel.class);
                    if (excelField == null) {
                        continue;
                    }
                    String fieldName = field.getName();
                    Field declaredField = excelVo.getClass().getDeclaredField(fieldName);
                    declaredField.setAccessible(true);
                    Object obj = declaredField.get(excelVo);
                    String objStr = null == obj ? "" : obj.toString();
                    generateCell(row, cellStyle, i, objStr);
                }
            }


        } catch (Exception e) {
            throw new MipException("模板解析异常");
        }
        //导出
        ExportExcelUtil.downLoadExcel("报错信息_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xlsx", response, workbook);
    }

    @ApiOperation("人力费率-角色费率批量保存或修改")
    @PostMapping("saveOrUpdate")
    public Response saveOrUpdate(@RequestBody LaborCostImportDto dto) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "laborCost/saveOrUpdate";
        return restTemplate.postForObject(url, dto, DataResponse.class);
    }
}
