package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.MaterialPriceReceiptHeaderDto;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.statistics.excelVo.MaterialPriceReceiptStatisticsExcelVO;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @PackageClassName: com.midea.pam.gateway.statistics.web.MaterialPriceReceiptStatisticsController
 * @Description: 手工单据
 * @Author: JerryH
 * @Date: 2023-05-23, 0023 上午 11:16
 */
@Api("统计-手工单据")
@RestController
@RequestMapping("statistics/materialPriceReceipt")
public class MaterialPriceReceiptStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "物料价格手工单据-列表")
    @GetMapping("page")
    public Response page(
            @RequestParam(required = false) @ApiParam("单据码") String receiptCode,
            @RequestParam(required = false) @ApiParam("状态ID（下拉多选）") String statusStr,
            @RequestParam(required = false) @ApiParam("申请人") String createByName,
            @RequestParam(required = false) @ApiParam("创建时间开始") String createAtStartTime,
            @RequestParam(required = false) @ApiParam("创建时间开始结束") String createAtEndTime,
            @RequestParam(required = false) @ApiParam("库存组织ID（下拉多选）") String organizationIdStr,
            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        final Map<String, Object> param = getParams(receiptCode, statusStr, createByName, createAtStartTime,
                createAtEndTime, organizationIdStr, pageNum, pageSize);

        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "materialPriceStatisticsReceipt/page", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<MaterialPriceReceiptHeaderDto>>>() {
        });
    }

    private Map<String, Object> getParams(String receiptCode, String statusStr, String createByName, String createAtStartTime,
                                          String createAtEndTime, String organizationIdStr, Integer pageNum, Integer pageSize) {
        Map<String, Object> param = new HashMap<>();
        param.put("unitId", SystemContext.getUnitId());
        param.put("receiptCode", receiptCode);
        param.put("statusStr", statusStr);
        param.put("createByName", createByName);
        param.put("createAtStartTime", createAtStartTime);
        param.put("createAtEndTime", createAtEndTime);
        param.put("organizationIdStr", organizationIdStr);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        return param;
    }

    @ApiOperation(value = "物料价格手工单据列表导出", response = ResponseMap.class)
    @GetMapping("export")
    public void listExport(HttpServletResponse response,
                           @RequestParam(required = false) @ApiParam("单据码") String receiptCode,
                           @RequestParam(required = false) @ApiParam("状态ID（下拉多选）") String statusStr,
                           @RequestParam(required = false) @ApiParam("申请人") String createByName,
                           @RequestParam(required = false) @ApiParam("创建时间开始") String createAtStartTime,
                           @RequestParam(required = false) @ApiParam("创建时间开始结束") String createAtEndTime,
                           @RequestParam(required = false) @ApiParam("库存组织ID（下拉多选）") String organizationIdStr,
                           @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                           @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        final Map<String, Object> param = getParams(receiptCode, statusStr, createByName, createAtStartTime,
                createAtEndTime, organizationIdStr, pageNum, pageSize);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "materialPriceStatisticsReceipt/export", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<MaterialPriceReceiptHeaderDto>> dataResponse = JSON.parseObject(res,
                new TypeReference<DataResponse<List<MaterialPriceReceiptHeaderDto>>>() {
        });

        List<MaterialPriceReceiptHeaderDto> resultList = dataResponse == null ? new ArrayList<>() : dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("物料价格维护-" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");


        List<MaterialPriceReceiptStatisticsExcelVO> receiptStatisticsExcelVOS = resultList.stream().map(dto->
                {
                    MaterialPriceReceiptStatisticsExcelVO excelVO = new MaterialPriceReceiptStatisticsExcelVO();
                    excelVO.setStatus("" + dto.getStatus());
                    BeanUtils.copyProperties(dto,excelVO);
                    return excelVO;
                }
        ).collect(Collectors.toList());

        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(receiptStatisticsExcelVOS, MaterialPriceReceiptStatisticsExcelVO.class, null, "物料价格维护", true);

        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }
}
