package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.entity.PurchaseBpaPrice;
import com.midea.pam.common.ctc.excelVo.PurchaseBpaPriceExcelVO;
import com.midea.pam.common.ctc.query.PurchaseBpaPriceQuery;
import com.midea.pam.common.ctc.vo.MaterialPurchaseBpaPriceVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.PageResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * @program: pam
 * @description: 一揽子协议 控制层
 * @author: gaojh1
 * @create: 2019-6-12
 **/
@RestController
@RequestMapping("purchaseBpaPrice")
@Api("一揽子协议")
public class PurchaseBpaPriceController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "Erp一揽子协议查询")
    @GetMapping("asynFromErp")
    public Response asynFromErp(@RequestParam(required = false) String startDate,
                                @RequestParam(required = false) String endDate,
                                @RequestParam(required = true) Boolean isManual){
        final Map<String, Object> param = new HashMap<>();
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        param.put("isManual", isManual);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "purchaseBpaPrice/asynFromErp",param);
        restTemplate.getForObject(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        response.setData("0");
        return response;
    }

    @ApiOperation(value = "一揽子协议列表分页", response = PurchaseBpaPrice.class)
    @GetMapping("page")
    public com.midea.pam.gateway.common.base.Response selectPage(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                                                 @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                                                                 @RequestParam(required = false) String materialCode,
                                                                 @RequestParam(required = false) String unit,
                                                                 @RequestParam(required = false) String quantityStr,
                                                                 @RequestParam(required = false) String currency,
                                                                 @RequestParam(required = false) String vendorCode,
                                                                 @RequestParam(required = false) String vendorSiteCode,
                                                                 @RequestParam(required = false) String vendorName,
                                                                 @RequestParam(required = false) String poNumber,
                                                                 @RequestParam(required = false) String startDateStr,
                                                                 @RequestParam(required = false) String endDateStr,
                                                                 @RequestParam(required = false) String ouIdStr,
                                                                 @RequestParam(required = false) Integer orderUpdateAtType) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("materialCode", materialCode);
        param.put("unit", unit);
        param.put("quantityStr", quantityStr);
        param.put("currency", currency);
        param.put("vendorCode", vendorCode);
        param.put("vendorSiteCode", vendorSiteCode);
        param.put("poNumber", poNumber);
        param.put("startDate", startDateStr);
        param.put("endDateStr", endDateStr);
        param.put("ouIdStr", ouIdStr);
        param.put("orderUpdateAtType", orderUpdateAtType);
        param.put("vendorName", vendorName);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "purchaseBpaPrice/page", param);
        String res = restTemplate.getForObject(url, String.class);
        res = cleanStr(res);
        PageInfo<Map<String, Object>> data = JSON.parseObject(res, new TypeReference<PageInfo<Map<String, Object>>>() {
        });
        PageResponse<Map<String, Object>> response = com.midea.pam.gateway.common.base.Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation(value = "一揽子协议列表导出", response = ResponseMap.class)
    @GetMapping("export")
    public void listExport(HttpServletResponse response, @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                           @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                           @RequestParam(required = false) String materialCode,
                           @RequestParam(required = false) String unit,
                           @RequestParam(required = false) String quantityStr,
                           @RequestParam(required = false) String currency,
                           @RequestParam(required = false) String vendorCode,
                           @RequestParam(required = false) String vendorSiteCode,
                           @RequestParam(required = false) String poNumber,
                           @RequestParam(required = false) String startDateStr,
                           @RequestParam(required = false) String endDateStr,
                           @RequestParam(required = false) String ouIdStr,
                           @RequestParam(required = false) Integer orderUpdateAtType) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("materialCode", materialCode);
        param.put("unit", unit);
        param.put("quantityStr", quantityStr);
        param.put("currency", currency);
        param.put("vendorCode", vendorCode);
        param.put("vendorSiteCode", vendorSiteCode);
        param.put("poNumber", poNumber);
        param.put("startDateStr", startDateStr);
        param.put("endDateStr", endDateStr);
        param.put("ouIdStr", ouIdStr);
        param.put("orderUpdateAtType", orderUpdateAtType);


        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "purchaseBpaPrice/export", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        com.midea.pam.gateway.common.base.DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<com.midea.pam.gateway.common.base.DataResponse<Map<String, Object>>>() {});

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("一揽子协议-"+ DateUtils.format(new Date(),"yyyyMMddHHmmss"));
        fileName.append(".xls");

        JSONArray purchaseBpaPriceArr = (JSONArray) resultMap.get("purchaseBpaPriceList");

        List<PurchaseBpaPriceExcelVO> purchaseBpaPriceArrExcelVOs = new ArrayList<>();
        if(purchaseBpaPriceArr != null){
            purchaseBpaPriceArrExcelVOs = JSONObject.parseArray(purchaseBpaPriceArr.toJSONString(), PurchaseBpaPriceExcelVO.class);
            for (int i = 0; i < purchaseBpaPriceArrExcelVOs.size(); i++) {
                PurchaseBpaPriceExcelVO purchaseBpaPriceExcelVO = purchaseBpaPriceArrExcelVOs.get(i);
                purchaseBpaPriceExcelVO.setNum(i + 1);
            }
        }

        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(purchaseBpaPriceArrExcelVOs, PurchaseBpaPriceExcelVO.class, null, "一揽子协议列表", true);

        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    @ApiOperation(value = "查询物料关联的一揽子协议")
    @PostMapping("getMaterialPurchaseBpaPriceList")
    public Response getPurchaseBpaPriceList(@RequestBody List<PurchaseBpaPriceQuery> query){
        String param = JSON.toJSONString(query);
        String url = ModelsEnum.CTC.getBaseUrl() + "/purchaseBpaPrice/getMaterialPurchaseBpaPriceList";
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, query, String.class);

        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<MaterialPurchaseBpaPriceVO>>() {});
    }

}
