package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.FormConfigDTO;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.crm.dto.LeadDto;
import com.midea.pam.common.ctc.vo.LeadExcVo;
import com.midea.pam.common.ctc.vo.LeadTeamExcelVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.common.utils.FormConfigExcelUtil;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Api("线索模块")
@RestController
@RequestMapping("statistics/lead")
public class LeadStatisticsController extends ControllerHelper {

    private static final String SENSITIVE_WORD_REPLACER = "******";

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查询线索列表")
    @GetMapping("page")
    public Response list(LeadDto leadDto,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(leadDto);
        params.put("list", Boolean.TRUE);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/lead/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<LeadDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<LeadDto>>>() {
        });

        return response;
    }

    @ApiOperation(value = "查询我的线索列表")
    @GetMapping("createByMePage")
    public Response createByMePage(LeadDto leadDto,
                                   @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                   @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(leadDto);
        params.put("me", Boolean.TRUE);

        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/lead/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<LeadDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<LeadDto>>>() {
        });

        return response;
    }

    @ApiOperation(value = "线索列表导出", response = ResponseMap.class)
    @GetMapping("/export")
    public void listExport(HttpServletResponse response, LeadDto leadDto) {
        final Map<String, Object> param = buildParam(leadDto);
        // param.put("list",customerDto.isList());
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/lead/v1/export", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("线索信息_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");

        JSONArray leadsArr = (JSONArray) resultMap.get("leads");
        JSONArray leadTeamArr = (JSONArray) resultMap.get("leadTeamExcelVos");

        List<LeadExcVo> leadExcVOS = JSONObject.parseArray(leadsArr.toJSONString(), LeadExcVo.class);
        for (int i = 0; i < leadExcVOS.size(); i++) {
            LeadExcVo leadExcelVo = leadExcVOS.get(i);
            leadExcelVo.setNum(i + 1);
            if (null != leadExcelVo.getDecisionRatio() && !leadExcelVo.getDecisionRatio().equals("")) {
                StringBuilder str = new StringBuilder();
                str.append(leadExcelVo.getDecisionRatio()).append("%");
                leadExcelVo.setDecisionRatio(str.toString());
            }
            if (null != leadExcelVo.getSupportRatio() && !leadExcelVo.getSupportRatio().equals("")) {
                StringBuilder str1 = new StringBuilder();
                str1.append(leadExcelVo.getSupportRatio()).append("%");
                leadExcelVo.setSupportRatio(str1.toString());
            }
            desensitizationExport(leadExcelVo);
        }

        List<LeadTeamExcelVo> leadContacts = JSONObject.parseArray(leadTeamArr.toJSONString(), LeadTeamExcelVo.class);
        for (int i = 0; i < leadContacts.size(); i++) {
            LeadTeamExcelVo leadContactExcelVO = leadContacts.get(i);
            leadContactExcelVO.setNum(i + 1);
        }


        final Workbook workbook = com.midea.pam.common.util.ExportExcelUtil.buildDefaultSheet(leadExcVOS, LeadExcVo.class, null, "基本信息", true);
        com.midea.pam.common.util.ExportExcelUtil.addSheet(workbook, leadContacts, LeadTeamExcelVo.class, null, "团队成员", true);
        //add by ex_xuwj4 begin
        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put("unitId", SystemContext.getUnitId());
        queryParam.put("module", "lead");
        String buildGetUrl = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "formConfig/listByUnitIdAndModule", queryParam);
        ResponseEntity<String> entity = restTemplate.getForEntity(buildGetUrl, String.class);
        DataResponse<List<FormConfigDTO>> resultResponse = JSON.parseObject(entity.getBody(), new TypeReference<DataResponse<List<FormConfigDTO>>>() {
        });
        List<FormConfigDTO> configDTOS = resultResponse.getData()
                .stream().filter(dto -> dto.getEnabled() == 1).collect(Collectors.toList());
        FormConfigExcelUtil.generateData(workbook, configDTOS);//拼接数据
        //add by ex_xuwj4 end
        com.midea.pam.common.util.ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    @ApiOperation(value = "我的线索列表导出", response = ResponseMap.class)
    @GetMapping("/exportMy")
    public void listMyExport(HttpServletResponse response, LeadDto leadDto) {
        final Map<String, Object> param = buildParam(leadDto);
        // param.put("list",customerDto.isList());
        param.put("me", Boolean.TRUE);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/lead/v1/export", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("线索信息_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");

        JSONArray leadsArr = (JSONArray) resultMap.get("leads");
        JSONArray leadTeamArr = (JSONArray) resultMap.get("leadTeamExcelVos");

        List<LeadExcVo> leadExcVOS = JSONObject.parseArray(leadsArr.toJSONString(), LeadExcVo.class);
        for (int i = 0; i < leadExcVOS.size(); i++) {
            LeadExcVo leadExcelVo = leadExcVOS.get(i);
            leadExcelVo.setNum(i + 1);
            if (null != leadExcelVo.getDecisionRatio() && !leadExcelVo.getDecisionRatio().equals("")) {
                StringBuilder str = new StringBuilder();
                str.append(leadExcelVo.getDecisionRatio()).append("%");
                leadExcelVo.setDecisionRatio(str.toString());
            }
            if (null != leadExcelVo.getSupportRatio() && !leadExcelVo.getSupportRatio().equals("")) {
                StringBuilder str1 = new StringBuilder();
                str1.append(leadExcelVo.getSupportRatio()).append("%");
                leadExcelVo.setSupportRatio(str1.toString());
            }
            desensitizationExport(leadExcelVo);
        }

        List<LeadTeamExcelVo> leadContacts = JSONObject.parseArray(leadTeamArr.toJSONString(), LeadTeamExcelVo.class);
        for (int i = 0; i < leadContacts.size(); i++) {
            LeadTeamExcelVo leadContactExcelVO = leadContacts.get(i);
            leadContactExcelVO.setNum(i + 1);
        }


        final Workbook workbook = com.midea.pam.common.util.ExportExcelUtil.buildDefaultSheet(leadExcVOS, LeadExcVo.class, null, "基本信息", true);
        com.midea.pam.common.util.ExportExcelUtil.addSheet(workbook, leadContacts, LeadTeamExcelVo.class, null, "团队成员", true);
        //add by ex_xuwj4 begin
        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put("unitId", SystemContext.getUnitId());
        queryParam.put("module", "lead");
        String buildGetUrl = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "formConfig/listByUnitIdAndModule", queryParam);
        ResponseEntity<String> entity = restTemplate.getForEntity(buildGetUrl, String.class);
        DataResponse<List<FormConfigDTO>> resultResponse = JSON.parseObject(entity.getBody(), new TypeReference<DataResponse<List<FormConfigDTO>>>() {
        });
        List<FormConfigDTO> configDTOS = resultResponse.getData()
                .stream().filter(dto -> dto.getEnabled() == 1).collect(Collectors.toList());
        FormConfigExcelUtil.generateData(workbook, configDTOS);//拼接数据
        //add by ex_xuwj4 end
        com.midea.pam.common.util.ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    private Map buildParam(LeadDto leadDto) {

        //线索编号、客户名称、客户CRM编码、省市、联系人、所有者使用文本模糊搜索，
        // 线索状态、行业、来源、区域、级别、应用行业使用下拉搜索，创建日期使用日期搜索器
        final Map<String, Object> params = new HashMap<>();
        params.put("num", leadDto.getNum());
        params.put("customerName", leadDto.getCustomerName());
        params.put("crmCode", leadDto.getCrmCode());
        params.put("provinceCityName", leadDto.getProvinceCityName());
        params.put("contactName", leadDto.getContactName());
        params.put("ownerName", leadDto.getOwnerName());
        params.put("followerName", leadDto.getFollowerName());

        params.put("statusStr", leadDto.getStatusStr());
        params.put("sourceStr", leadDto.getSourceStr());
        params.put("industryStr", leadDto.getIndustryStr());
        params.put("districtStr", leadDto.getDistrictStr());
        params.put("roleDeptStr", leadDto.getRoleDeptStr());
        params.put("levelStr", leadDto.getLevelStr());
        params.put("applicationIndustryStr", leadDto.getApplicationIndustryStr());

        params.put("createAtStart", leadDto.getCreateAtStart());
        params.put("createAtEnd", leadDto.getCreateAtEnd());

        params.put("updateAtStart", leadDto.getUpdateAtStart());
        params.put("updateAtEnd", leadDto.getUpdateAtEnd());
        params.put("customerGroupStr", leadDto.getCustomerGroupStr());
        params.put("summary", leadDto.getSummary());
        return params;
    }

    /**
     * 脱敏操作
     */
    private void desensitizationExport(LeadExcVo leadExcelVo) {
        if (Objects.isNull(leadExcelVo)) {
            return;
        }
        String leadAddress = leadExcelVo.getLeadAddress();
        if (StringUtils.isNotEmpty(leadAddress)) {
            leadExcelVo.setLeadAddress(SENSITIVE_WORD_REPLACER);
        }
        String cellphone = leadExcelVo.getCellphone();
        if (StringUtils.isNotEmpty(cellphone)) {
            leadExcelVo.setCellphone(SENSITIVE_WORD_REPLACER);
        }
        String email = leadExcelVo.getEmail();
        if (StringUtils.isNotEmpty(email)) {
            leadExcelVo.setEmail(SENSITIVE_WORD_REPLACER);
        }
        String wechat = leadExcelVo.getWechat();
        if (StringUtils.isNotEmpty(wechat)) {
            leadExcelVo.setWechat(SENSITIVE_WORD_REPLACER);
        }
        String qq = leadExcelVo.getQq();
        if (StringUtils.isNotEmpty(qq)) {
            leadExcelVo.setQq(SENSITIVE_WORD_REPLACER);
        }
    }

}
