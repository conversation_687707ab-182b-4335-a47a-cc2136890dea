package com.midea.pam.gateway.mdw.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @PackageClassName: com.midea.pam.gateway.mdw.web.DwdProjectProblemController
 * @Description: 统计组织用户明细
 * @Author: JerryH
 * @Date: 2023-02-10, 0010 下午 03:14:42
 */
@Api("统计组织用户明细")
@RestController
@RequestMapping("mdw/orgUser")
public class DwdOrgUserController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "统计组织用户明细")
    @GetMapping("statisticsDwdOrgUser")
    public Response statisticsDwdOrgUser() {
        Map<String, Object> param = new HashMap<>();
        final String url = buildGetUrl(ModelsEnum.MDW.getBaseUrl(), "dwd/orgUser/statisticsDwdOrgUser", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
    }
}
