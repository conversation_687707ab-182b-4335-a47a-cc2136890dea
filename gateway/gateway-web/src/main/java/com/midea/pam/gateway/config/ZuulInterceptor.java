package com.midea.pam.gateway.config;

import com.midea.mcomponent.security.common.util.UserUtils;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.gateway.entity.CurrentContext;
import com.midea.pam.common.gateway.entity.CurrentContextExample;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.utils.PamCurrentUserUtil;
import com.midea.pam.gateway.service.CurrentContextService;
import com.netflix.zuul.ZuulFilter;
import com.netflix.zuul.context.RequestContext;
import com.netflix.zuul.exception.ZuulException;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;

public class ZuulInterceptor extends ZuulFilter {
    @Resource
    private CurrentContextService currentContextService;

    @Override
    public String filterType() {
        return "pre";
    }

    @Override
    public int filterOrder() {
        return 0;
    }

    @Override
    public boolean shouldFilter() {
        return true;
    }

    @Override
    public Object run() throws ZuulException {
        String userName = PamCurrentUserUtil.getCurrentUserName();
        RequestContext currentContext = RequestContext.getCurrentContext();
        if (StringUtils.isNotEmpty(userName)) {
            currentContext.addZuulRequestHeader(Constants.FD_LOGIN_NAME, userName);
            currentContext.addZuulRequestHeader(Constants.FD_LANGUAGE, null != UserUtils.getLocale() ? UserUtils.getLocale().getLanguage() : "");
            final UserInfo userInfo = CacheDataUtils.findUserByMip(userName);
            if (null != userInfo) {
                currentContext.addZuulRequestHeader(Constants.FD_LOGIN_ID, String.valueOf(userInfo.getId()));
            }
            CurrentContext context = CacheDataUtils.getContextByMip(userName);;
            if (null == context) {
                final CurrentContextExample condition = new CurrentContextExample();
                condition.createCriteria().andLoginUserNameEqualTo(userName);
                final List<CurrentContext> contexts = currentContextService.selectByExample(condition);
                context = ListUtils.isNotEmpty(contexts) ? contexts.get(0) : null;
            }
            if (null != context) {
                currentContext.addZuulRequestHeader(Constants.CURRENT_UNIT_ID, String.valueOf(context.getCurrentOrgId()));
                currentContext.addZuulRequestHeader(Constants.CURRENT_UNIT_NAME, context.getCurrentOrgName());
                currentContext.addZuulRequestHeader(Constants.ANTHORITY_OU, context.getOuIds());
                currentContext.addZuulRequestHeader(Constants.CURRENT_SECOND_UNIT, context.getSecondUnitIds());
            }

        }
        return null;
    }
}
