package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.PaymentInvoiceDto;
import com.midea.pam.common.ctc.entity.OrganizationCustomDict;
import com.midea.pam.common.ctc.vo.PaymentInvoiceExcelVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Api("统计-应付发票")
@RestController
@RequestMapping("statistics/paymentInvoice")
public class PaymentInvoiceStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "查询应付发票行列表")
    @GetMapping("page")
    public Response list(@RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize,
                         @RequestParam(required = false) @ApiParam("应付发票号") final String apInvoiceCode,
                         @RequestParam(required = false) @ApiParam("采购合同编号") final String purchaseContractCode,
                         @RequestParam(required = false) @ApiParam("采购合同名称") final String purchaseContractName,
                         @RequestParam(required = false) @ApiParam("状态") final String statusStr,
                         @RequestParam(required = false) @ApiParam("供应商名称") final String vendorName,
                         @RequestParam(required = false) @ApiParam("供应商编码") final String vendorCode,
                         @RequestParam(required = false) @ApiParam("项目名称") final String projectName,
                         @RequestParam(required = false) @ApiParam("项目编码") final String projectCode,
                         @RequestParam(required = false) @ApiParam("ERP同步状态") final String erpStatusStr,
                         @RequestParam(required = false) @ApiParam("创建人") final String createByName,
                         @RequestParam(required = false) @ApiParam("创建日期开始") final String createAtStart,
                         @RequestParam(required = false) @ApiParam("创建日期结束") final String createAtEnd,
                         @RequestParam(required = false) @ApiParam("总账日期开始") final String glDateStart,
                         @RequestParam(required = false) @ApiParam("总账日期结束") final String glDateEnd,
                         @RequestParam(required = false) @ApiParam("发票到期日开始") final String dueDateStart,
                         @RequestParam(required = false) @ApiParam("发票到期日结束") final String dueDateEnd,
                         @RequestParam(required = false) @ApiParam("冻结状态") final String freezeStatusStr,
                         @RequestParam(required = false) @ApiParam("ERP取消状态") final String erpCancelStatusStr,
                         @RequestParam(required = false) @ApiParam("入账类型") final String accountEntryTypeStr ) {
        Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("apInvoiceCode", apInvoiceCode);
        param.put("purchaseContractCode", purchaseContractCode);
        param.put("purchaseContractName", purchaseContractName);
        param.put("statusStr", statusStr);
        param.put("vendorName", vendorName);
        param.put("vendorCode", vendorCode);
        param.put("projectName", projectName);
        param.put("projectCode", projectCode);
        param.put("erpStatusStr", erpStatusStr);
        param.put("createByName", createByName);
        param.put("createAtStart", createAtStart);
        param.put("createAtEnd", createAtEnd);
        param.put("glDateStart", glDateStart);
        param.put("glDateEnd", glDateEnd);
        param.put("dueDateStart", dueDateStart);
        param.put("dueDateEnd", dueDateEnd);
        param.put("erpCancelStatusStr", erpCancelStatusStr);
        param.put("freezeStatusStr", freezeStatusStr);
        param.put("accountEntryTypeStr", accountEntryTypeStr);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/paymentInvoice/list", param);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<PaymentInvoiceDto>>>() {
        });
    }

    @ApiOperation(value = "采购发票列表导出", response = ResponseMap.class)
    @GetMapping("export")
    public void listExport(HttpServletResponse response, @RequestParam(required = false) @ApiParam("应付发票号") final String apInvoiceCode,
                           @RequestParam(required = false) @ApiParam("采购合同编号") final String purchaseContractCode,
                           @RequestParam(required = false) @ApiParam("采购合同名称") final String purchaseContractName,
                           @RequestParam(required = false) @ApiParam("状态") final String statusStr,
                           @RequestParam(required = false) @ApiParam("供应商名称") final String vendorName,
                           @RequestParam(required = false) @ApiParam("供应商编码") final String vendorCode,
                           @RequestParam(required = false) @ApiParam("项目名称") final String projectName,
                           @RequestParam(required = false) @ApiParam("项目编码") final String projectCode,
                           @RequestParam(required = false) @ApiParam("ERP同步状态") final String erpStatusStr,
                           @RequestParam(required = false) @ApiParam("创建人") final String createByName,
                           @RequestParam(required = false) @ApiParam("创建日期开始") final String createAtStart,
                           @RequestParam(required = false) @ApiParam("创建日期结束") final String createAtEnd,
                           @RequestParam(required = false) @ApiParam("发票到期日开始") final String dueDateStart,
                           @RequestParam(required = false) @ApiParam("发票到期日结束") final String dueDateEnd,
                           @RequestParam(required = false) @ApiParam("总账日期开始") final String glDateStart,
                           @RequestParam(required = false) @ApiParam("总账日期结束") final String glDateEnd,
                           @RequestParam(required = false) @ApiParam("冻结状态") final String freezeStatusStr,
                           @RequestParam(required = false) @ApiParam("ERP取消状态") final String erpCancelStatusStr,
                           @RequestParam(required = false) @ApiParam("入账类型") final String accountEntryTypeStr
    ) {
        Map<String, Object> param = new HashMap<>();
        param.put("apInvoiceCode", apInvoiceCode);
        param.put("purchaseContractCode", purchaseContractCode);
        param.put("purchaseContractName", purchaseContractName);
        param.put("statusStr", statusStr);
        param.put("vendorName", vendorName);
        param.put("vendorCode", vendorCode);
        param.put("projectName", projectName);
        param.put("projectCode", projectCode);
        param.put("erpStatusStr", erpStatusStr);
        param.put("createByName", createByName);
        param.put("createAtStart", createAtStart);
        param.put("createAtEnd", createAtEnd);
        param.put("dueDateStart", dueDateStart);
        param.put("dueDateEnd", dueDateEnd);
        param.put("glDateStart", glDateStart);
        param.put("glDateEnd", glDateEnd);
        param.put("erpCancelStatusStr", erpCancelStatusStr);
        param.put("freezeStatusStr", freezeStatusStr);
        param.put("accountEntryTypeStr", accountEntryTypeStr);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/paymentInvoice/export", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("应付发票_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");
        JSONArray paymentInvoiceArr = (JSONArray) resultMap.get("paymentInvoiceList");

        List<PaymentInvoiceExcelVO> paymentInvoiceExcelVOS = new ArrayList<>();
        if (paymentInvoiceArr != null) {
            paymentInvoiceExcelVOS = JSONObject.parseArray(paymentInvoiceArr.toJSONString(), PaymentInvoiceExcelVO.class);
            for (int i = 0; i < paymentInvoiceExcelVOS.size(); i++) {
                PaymentInvoiceExcelVO paymentInvoiceExcelVO = paymentInvoiceExcelVOS.get(i);
                paymentInvoiceExcelVO.setNumb(i + 1);
            }
        }

        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(paymentInvoiceExcelVOS, PaymentInvoiceExcelVO.class, null, "应付发票", true);

        // 查询组织参数，如果没有启用应付发票冻结功能，导出不显示冻结状态列
        final Map<String, Object> query = new HashMap<>();
        query.put("orgId", SystemContext.getUnitId());
        query.put("orgFrom", "company");
        query.put("name", "应付发票冻结");
        String url2 = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/organizationCustomDict/queryByOrdId", query);
        String res2 = restTemplate.getForEntity(url2, String.class).getBody();
        com.midea.pam.common.base.DataResponse<List<OrganizationCustomDict>> response2 = JSON.parseObject(res2, new TypeReference<com.midea.pam.common.base.DataResponse<List<OrganizationCustomDict>>>() {
        });
        if (!(ListUtils.isNotEmpty(response2.getData()) && Objects.equals(response2.getData().get(0).getValue(), "1"))) {
            com.midea.pam.gateway.common.utils.ExportExcelUtil.dynamicExportFirstSheetExclude(workbook, Arrays.asList("冻结状态"));
        }
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

}
