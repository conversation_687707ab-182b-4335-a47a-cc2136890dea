package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.ctc.dto.AssetDeprnDto;
import com.midea.pam.common.ctc.vo.AssetStatisticsExcelVO;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.BeanConverter;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;


@Api("资产折旧信息")
@RestController
@RequestMapping("statistics/assetDeprn")
public class AssetStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation("资产折旧信息列表(分页)")
    @PostMapping("page")
    public Response page(@RequestBody AssetDeprnDto dto) {
        String url = String.format("%sstatistics/assetDeprn/page", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<AssetDeprnDto>>>() {
        });
    }

    @ApiOperation("资产折旧信息列表导出")
    @PostMapping("export")
    public void listExport(HttpServletResponse response, @RequestBody AssetDeprnDto dto) {
        final String url = String.format("%sstatistics/assetDeprn/list", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<List<AssetDeprnDto>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<AssetDeprnDto>>>() {
        });
        if (dataResponse == null || dataResponse.getData() == null) {
            throw new BizException(Code.ERROR, "导出数据为空");
        }

        List<AssetStatisticsExcelVO> excelVOList = BeanConverter.copy(dataResponse.getData(), AssetStatisticsExcelVO.class);
        for (int i = 0; i < excelVOList.size(); i++) {
            AssetStatisticsExcelVO projectExcelVO = excelVOList.get(i);
            projectExcelVO.setNum(i + 1);
        }
        //导出操作
        String fileName = String.format("%s%s%s", "资产折旧信息列表_", DateUtils.format(new Date(), "yyyyMMddHHmmss"), ".xls");
        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(excelVOList, AssetStatisticsExcelVO.class, null, "资产折旧信息", true);
        ExportExcelUtil.downLoadExcel(fileName, response, workbook);
    }
}
