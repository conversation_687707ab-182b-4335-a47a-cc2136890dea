package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.excelVo.IHRAttendDetailExcelVo;
import com.midea.pam.common.ctc.entity.AsyncRequestResult;
import com.midea.pam.common.ctc.entity.IhrAttendDetail;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.utils.FileUtil;
import com.midea.pam.gateway.config.RestTemplateConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;


@Api("同步IHR的考勤数据")
@RestController
@RequestMapping("ihrAttendDetail")
public class IhrAttendDetailController extends ControllerHelper {

    private static final Logger logger = LoggerFactory.getLogger(IhrAttendDetailController.class);

    @Resource
    private RestTemplateConfig restTemplateConfig;

    @ApiOperation(value = "异步请求结果详情", response = AsyncRequestResult.class)
    @GetMapping({"syncIHRAttendDetail"})
    public Response syncIHRAttendDetail(@RequestParam(required = true) @ApiParam(value = "开始日期") String attendBeginDate,
                                        @RequestParam(required = true) @ApiParam(value = "结束日期") String attendEndDate,
                                        @RequestParam(required = true) @ApiParam(value = "单次请求用户个数") int batchSize,
                                        @RequestParam(required = true) @ApiParam(value = "单次请求时间间隔") int days,
                                        @RequestParam(required = false) @ApiParam(value = "mip集合") String codes) {
        final Map<String, Object> param = new HashMap<>();
        param.put("attendBeginDate", attendBeginDate);
        param.put("attendEndDate", attendEndDate);
        param.put("batchSize", batchSize);
        param.put("days", days);
        param.put("codes", codes);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/ihrAttendDetail/syncIHRAttendDetail", param);
        final String res = restTemplateConfig.restTemplate().getForEntity(url, String.class).getBody();
        DataResponse<Integer> response = JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
        return response;
    }

    @ApiOperation(value = "查询IHR的考勤数据", response = AsyncRequestResult.class)
    @GetMapping({"getIHRAttendDetailByUserNumber"})
    public Response getIHRAttendDetailByUserNumber(@RequestParam(required = true) @ApiParam(value = "开始日期") String attendBeginDate,
                                        @RequestParam(required = true) @ApiParam(value = "结束日期") String attendEndDate,
                                                   @RequestParam(required = true) @ApiParam(value = "mip集合") String codes) {
        final Map<String, Object> param = new HashMap<>();
        param.put("attendBeginDate", attendBeginDate);
        param.put("attendEndDate", attendEndDate);
        param.put("codes", codes);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/ihrAttendDetail/getIHRAttendDetailByUserNumber", param);
        final String res = restTemplateConfig.restTemplate().getForEntity(url, String.class).getBody();
        DataResponse<List<IhrAttendDetail>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<IhrAttendDetail>>>() {
        });
        return response;
    }

    @ApiOperation(value = "考勤-导入")
    @PostMapping("importIHRAttendDetail")
    public Response importIHRAttendDetail(@RequestParam(value = "file") MultipartFile file) {
        List<IHRAttendDetailExcelVo> detailImportExcelVoList;
        try {
            detailImportExcelVoList = FileUtil.importExcel(file, IHRAttendDetailExcelVo.class, 0, 0);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new BizException(100, "导入文件有误或数据类型未按要求填写");
        }
        // 移除空行的数据
        Iterator<IHRAttendDetailExcelVo> iterator = detailImportExcelVoList.iterator();
        while (iterator.hasNext()) {
            IHRAttendDetailExcelVo next = iterator.next();
            if (StringUtils.isBlank(next.getUserNumber())
                    && StringUtils.isBlank(next.getAttendYearAndMonth())
                    && StringUtils.isBlank(next.getUserName())) {
                iterator.remove();
            }
        }
        Asserts.notEmpty(detailImportExcelVoList, ErrorCode.SYSTEM_FILE_EMPTY);

        final String url = String.format("%sihrAttendDetail/validImportDetail", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplateConfig.restTemplate().postForEntity(url, detailImportExcelVoList, String.class).getBody();
        DataResponse<List<IHRAttendDetailExcelVo>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<IHRAttendDetailExcelVo>>>() {
        });
        return response;
    }
}
