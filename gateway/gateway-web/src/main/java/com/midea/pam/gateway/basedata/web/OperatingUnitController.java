package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.basedata.dto.OperatingUnitDto;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.ctc.dto.PaymentApplyDto;
import com.midea.pam.common.ctc.dto.PaymentPlanDTO;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.PageResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("operatingUnit")
@Api("业务实体")
public class OperatingUnitController extends ControllerHelper {
    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "业务实体查询")
    @GetMapping("list")
    public Response list(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") Integer pageSize){
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize",pageSize);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "operatingUnit/list",param);
        String res = cleanStr(restTemplate.getForObject(url, String.class));
        PageInfo<OperatingUnit> data = JSON.parseObject(res, new TypeReference<PageInfo<OperatingUnit>>(){});
        PageResponse<OperatingUnit> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation(value = "查询当前unit下生效的OU")
    @GetMapping("queryCurrentUnitOu")
    public com.midea.pam.common.base.Response queryCurrentUnitOu(@RequestParam Long unitId){
        final Map<String, Object> param = new HashMap<>();
        param.put("unitId", unitId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "operatingUnit/queryCurrentUnitOu",param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<OperatingUnitDto>>>(){});
    }

    @ApiOperation(value = "根据ouId查询使用单位")
    @GetMapping("queryUnitByOuId")
    public com.midea.pam.common.base.Response queryUnitByOuId(@RequestParam(required = false) Long ouId){
        final Map<String, Object> param = new HashMap<>();
        param.put("ouId", ouId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "operatingUnit/queryUnitByOuId",param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<OperatingUnitDto>>>(){});
    }


    @ApiOperation(value = "根据ID获取业务实体对象")
    @GetMapping("findOperatingUnitById")
    public Response findOperatingUnitById(@RequestParam(required = false) @ApiParam(value = "id") Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "operatingUnit/findOperatingUnitById",param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        com.midea.pam.gateway.common.base.DataResponse<OperatingUnit> response = JSON.parseObject(res, new TypeReference<com.midea.pam.gateway.common.base.DataResponse<OperatingUnit>>() {
        });
        return response;
    }
}
