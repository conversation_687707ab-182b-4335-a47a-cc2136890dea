package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.ctc.dto.ContractQuery;
import com.midea.pam.common.ctc.dto.ContractTargetDto;
import com.midea.pam.common.ctc.dto.MilePostDeliveryDto;
import com.midea.pam.common.ctc.dto.OverdueReceiptDTO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Api("经营统计")
@RestController
@RequestMapping("statistics/operating")
public class OperatingStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "根据事业部查询里程碑交付及时率")
    @GetMapping("selectRateByDepartment")
    public Response selectByDepartment(MilePostDeliveryDto milePostDeliveryDto) {
        final Map<String, Object> params = buildParam(milePostDeliveryDto);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/statistics/operating/selectRateByDepartment", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<MilePostDeliveryDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<MilePostDeliveryDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "根据产品维度查询里程碑交付及时率")
    @GetMapping("selectRateByUnit")
    public Response selectByUnit(MilePostDeliveryDto milePostDeliveryDto) {
        final Map<String, Object> params = buildParam(milePostDeliveryDto);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/statistics/operating/selectRateByUnit", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<MilePostDeliveryDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<MilePostDeliveryDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "根据事业部查询里程碑交付情况分析")
    @GetMapping("selectSituationByDepartment")
    public Response selectSituationByDepartment(MilePostDeliveryDto milePostDeliveryDto) {
        final Map<String, Object> params = buildParam(milePostDeliveryDto);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/statistics/operating/selectSituationByDepartment", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<MilePostDeliveryDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<MilePostDeliveryDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "根据产品维度查询里程碑交付情况分析")
    @GetMapping("selectSituationByUnit")
    public Response selectSituationByUnit(MilePostDeliveryDto milePostDeliveryDto) {
        final Map<String, Object> params = buildParam(milePostDeliveryDto);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/statistics/operating/selectSituationByUnit", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<MilePostDeliveryDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<MilePostDeliveryDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "合同目标达成情况分析-销售类")
    @GetMapping("selectSalesContractTargetByUnit")
    public Response selectSalesContractTargetByUnit(ContractQuery query) {
        final Map<String, Object> params = buildContractParam(query);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/statistics/operating/selectSalesContractTargetByUnit", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<ContractTargetDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<ContractTargetDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "合同目标达成情况分析-产品类")
    @GetMapping("selectProjectContractTargetByUnit")
    public Response selectProjectContractTargetByUnit(ContractQuery query) {
        final Map<String, Object> params = buildContractParam(query);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/statistics/operating/selectProjectContractTargetByUnit", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<ContractTargetDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<ContractTargetDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "销售回款情况分析")
    @GetMapping("getContractReceiptClaim")
    public Response getContractReceiptClaim(ContractQuery query) {
        final Map<String, Object> params = buildContractParam(query);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/statistics/operating/getContractReceiptClaim", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<ContractTargetDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<ContractTargetDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "个人业绩达成（机器人单位）")
    @GetMapping("selectPersonalContractTarget")
    public Response selectPersonalContractTarget(ContractQuery query) {
        final Map<String, Object> params = buildContractParam(query);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/statistics/operating/selectPersonalContractTarget", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<ContractTargetDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<ContractTargetDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "内外部合同占比")
    @GetMapping("getContractTargetScale")
    public Response getContractTargetScale(ContractQuery query) {
        final Map<String, Object> params = buildContractParam(query);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/statistics/operating/getContractTargetScale", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<ContractTargetDto> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<ContractTargetDto>>() {
        });
        return response;
    }

    private Map buildContractParam(ContractQuery query) {

        final Map<String, Object> params = new HashMap<>();
        params.put("orgType", query.getOrgType());
        params.put("endTimeStart", query.getEndTimeStart());
        params.put("endTimeEnd", query.getEndTimeEnd());
        params.put("innerFlag", query.getInnerFlag());
        params.put("unitId", query.getUnitId());
        return params;
    }

    private Map buildParam(MilePostDeliveryDto milePostDeliveryDto) {

        final Map<String, Object> params = new HashMap<>();
        params.put("priceType", milePostDeliveryDto.getPriceType());
        params.put("endTimeStart", milePostDeliveryDto.getEndTimeStart());
        params.put("endTimeEnd", milePostDeliveryDto.getEndTimeEnd());
        params.put("year", milePostDeliveryDto.getYear());
        return params;
    }

    @ApiOperation(value = "超期未回款预警")
    @GetMapping("getOverdueReceiptDetails")
    public Response getContractReceiptClaim(OverdueReceiptDTO overdueReceiptDTO) {
        Map params = buildOverdueReceiptParam(overdueReceiptDTO);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/statistics/operating/getOverdueReceiptDetails", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<OverdueReceiptDTO>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<OverdueReceiptDTO>>>() {
        });
        return response;
    }

    @ApiOperation(value = "超期未回款预警邮件")
    @GetMapping("overdueReceiptNotice")
    public Response overdueReceiptNotice(OverdueReceiptDTO overdueReceiptDTO) {
        Map params = buildOverdueReceiptParam(overdueReceiptDTO);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/statistics/operating/overdueReceiptNotice", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Boolean> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    private Map buildOverdueReceiptParam(OverdueReceiptDTO overdueReceiptDTO) {
        final Map<String, Object> params = new HashMap<>();
        params.put("salesManagerId", overdueReceiptDTO.getSalesManagerId());
        return params;
    }

}
