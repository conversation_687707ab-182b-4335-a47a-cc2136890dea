package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.PurchaseOrderDetailDeliveryAddressHistoryDto;
import com.midea.pam.common.ctc.entity.PurchaseOrderDetailDeliveryAddressHistory;
import com.midea.pam.common.enums.ModelsEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

@Api("采购订单行收货地址历史")
@RestController
@RequestMapping("purchaseDetailDeliveryAddressHistory")
public class PurchaseOrderDetailDeliveryAddressHistoryController {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation("分页查询")
    @PostMapping("pageList")
    public Response pageList(@RequestBody PurchaseOrderDetailDeliveryAddressHistoryDto dto) {
        final String url = String.format("%spurchaseDetailDeliveryAddressHistory/pageList", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), 
            new TypeReference<DataResponse<PageInfo<PurchaseOrderDetailDeliveryAddressHistoryDto>>>() {
        });
    }
}