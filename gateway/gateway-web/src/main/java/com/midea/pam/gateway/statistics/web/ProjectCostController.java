package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.FeeItemDto;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.entity.FormalMaterialGet;
import com.midea.pam.common.ctc.vo.DifferenceRecordExcelVO;
import com.midea.pam.common.ctc.vo.FormalMaterialGetListExcelVO;
import com.midea.pam.common.ctc.vo.GetreturnMaterialRecordExcelVO;
import com.midea.pam.common.ctc.vo.OutsourcePurchaseRecordExcelVO;
import com.midea.pam.common.ctc.vo.ProjectCostDetailVO;
import com.midea.pam.common.ctc.vo.ProjectCostHumanDetailRecordExternalExcelVO;
import com.midea.pam.common.ctc.vo.ProjectCostHumanDetailRecordInsideExcelVO;
import com.midea.pam.common.ctc.vo.ProjectCostRevenueOrderRecordExcelVO;
import com.midea.pam.common.ctc.vo.PurchaseOrderRecordExcelVO;
import com.midea.pam.common.ctc.vo.StorageRecordExcelVO;
import com.midea.pam.common.enums.FeeFlagEnum;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.statistics.dto.ProjectCostFeeItemRecordDto;
import com.midea.pam.common.statistics.dto.ProjectCostHumanItemRecordDTO;
import com.midea.pam.common.statistics.dto.ProjectCostMaterialcostSummaryDetailRecordDto;
import com.midea.pam.common.statistics.entity.ProjectCostAssetDetailRecord;
import com.midea.pam.common.statistics.entity.ProjectCostAssetItemRecord;
import com.midea.pam.common.statistics.entity.ProjectCostAssetSummaryRecord;
import com.midea.pam.common.statistics.entity.ProjectCostDifferenceRecord;
import com.midea.pam.common.statistics.entity.ProjectCostEaDetailRecord;
import com.midea.pam.common.statistics.entity.ProjectCostFeeDetailRecord;
import com.midea.pam.common.statistics.entity.ProjectCostFeeItemRecord;
import com.midea.pam.common.statistics.entity.ProjectCostFeeSummaryRecord;
import com.midea.pam.common.statistics.entity.ProjectCostGetreturnMaterialRecord;
import com.midea.pam.common.statistics.entity.ProjectCostHumanDetailRecord;
import com.midea.pam.common.statistics.entity.ProjectCostHumanItemRecord;
import com.midea.pam.common.statistics.entity.ProjectCostHumanSummaryRecord;
import com.midea.pam.common.statistics.entity.ProjectCostMaterialcostSummaryDetailRecord;
import com.midea.pam.common.statistics.entity.ProjectCostMaterialcostSummaryRecord;
import com.midea.pam.common.statistics.entity.ProjectCostOutsourcePurchaseRecord;
import com.midea.pam.common.statistics.entity.ProjectCostPurchaseOrderRecord;
import com.midea.pam.common.statistics.entity.ProjectCostRevenueOrderRecord;
import com.midea.pam.common.statistics.entity.ProjectCostStorageRecord;
import com.midea.pam.common.statistics.entity.ProjectCostSummaryItemRecord;
import com.midea.pam.common.statistics.entity.ProjectCostSummaryRecord;
import com.midea.pam.common.statistics.excelVo.ProjectCostAssetDetailRecordExcelVO;
import com.midea.pam.common.statistics.excelVo.ProjectCostEaDetailRecordExcelVO;
import com.midea.pam.common.statistics.excelVo.ProjectCostFeeDetailRecordExcelVO;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.utils.BeanConverter;
import com.midea.pam.support.utils.DateUtil;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/3/5
 * @description 项目成本
 */
@Api("项目成本")
@RestController
@RequestMapping(value = {"/statistics/project/cost", "/mobile/app/statistics/project/cost"})
public class ProjectCostController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "查询项目成本手动更新是否完成")
    @GetMapping("/checkForTheLatestUpdate")
    public Response checkForTheLatestUpdate(@RequestParam(required = false) Long executeId, @RequestParam Long projectId) {
        Map<String, Object> params = new HashMap<>();
        params.put("executeId", executeId);
        params.put("projectId", projectId);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/statistics/project/cost/checkForTheLatestUpdate", params);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "项目成本手动更新-兼容wbs和非wbs项目")
    @PutMapping("/manualGenerate/{id}")
    public Response manualGenerate(@PathVariable Long id) {
        String url = String.format("%sstatistics/project/cost/manualGenerate/%d", ModelsEnum.STATISTICS.getBaseUrl(), id);
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "项目成本详情")
    @GetMapping("/generate")
    public Response generate(@RequestParam(required = false) String projectIdStr, @RequestParam(required = false) Long ouId) {
        final Map<String, Object> params = new HashMap<>();
        params.put("projectIdStr", projectIdStr);
        params.put("ouId", ouId);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/statistics/project/cost/generate", params);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "项目成本详情")
    @GetMapping("/projectCostJob")
    public Response projectCostJob(@RequestParam(required = false) String projectIdStr, @RequestParam(required = false) Long ouId) {
        final Map<String, Object> params = new HashMap<>();
        params.put("projectIdStr", projectIdStr);
        params.put("ouId", ouId);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/statistics/project/cost/projectCostJob", params);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "项目成本详情")
    @GetMapping("/view/{projectId}")
    public Response view(@PathVariable Long projectId) {
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/statistics/project/cost/view/" + projectId, null);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectCostDetailVO>>() {
        });
    }

    @ApiOperation(value = "项目人力成本汇总")
    @GetMapping("/summaryHumanCost")
    public Response summaryHumanCost(@RequestParam String projectIdStr) {
        List<Long> projectIdList = new ArrayList<>();
        String[] projectIds = projectIdStr.split(",");
        for (String s : projectIds) {
            projectIdList.add(Long.valueOf(s));
        }
        final String url = String.format("%sstatistics/project/cost/summaryHumanCost", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, projectIdList, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "获取项目物料成本汇总信息")
    @GetMapping("/getMaterialCostSummaryRecord")
    public Response getMaterialCostSummaryRecord(@RequestParam Long projectId) {
        final Map<String, Object> params = new HashMap<>();
        params.put("projectId", projectId);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/statistics/project/cost/getMaterialCostSummaryRecord", params);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectCostMaterialcostSummaryRecord>>() {
        });
    }

    @ApiOperation(value = "项目预算控制人员填报工时")
    @GetMapping("/checkBudgetCanSubmit")
    public Response checkBudgetCanSubmit(@RequestParam String projectIdStr) {
        List<Long> projectIdList = new ArrayList<>();
        String[] projectIds = projectIdStr.split(",");
        for (String s : projectIds) {
            projectIdList.add(Long.valueOf(s));
        }
        final String url = String.format("%sstatistics/project/cost/checkBudgetCanSubmit", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, projectIdList, String.class);
        String res = cleanStr(responseEntity.getBody());
        return JSON.parseObject(res, new TypeReference<DataResponse<JSONObject>>() {
        });
    }

    @ApiOperation(value = "项目人力成本详情")
    @GetMapping("human/export")
    public void exportHumanCost(HttpServletResponse response, @RequestParam(required = false) Long projectId) {
        final Map<String, Object> params = new HashMap<>();
        params.put("projectId", projectId);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/project/cost/exportHumanCost/", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });
        Map<String, Object> resultMap = dataResponse.getData();

        //项目编号
        String projectCode = "";

        //导出操作
        StringBuffer fileName = new StringBuffer();


        JSONArray humanDetailRecordInsideList = (JSONArray) resultMap.get("humanDetailRecordInsideList");
        JSONArray humanDetailRecordExternalList = (JSONArray) resultMap.get("humanDetailRecordExternalList");
        JSONArray humanItemRecordList = (JSONArray) resultMap.get("humanItemRecordList");
        JSONArray humanFeeItemRecordList = (JSONArray) resultMap.get("humanFeeItemRecordList");
        JSONArray revenueOrderRecordList = (JSONArray) resultMap.get("revenueOrderRecordList");
        JSONArray humanFeeDetailRecordList = (JSONArray) resultMap.get("humanFeeDetailRecordList");
        JSONArray humanSummaryRecordList = (JSONArray) resultMap.get("humanSummaryRecordList");
        JSONArray projectCostEaDetailRecordList = (JSONArray) resultMap.get("projectCostEaDetailRecordList");


        List<ProjectCostHumanDetailRecordInsideExcelVO> humanDetailRecordExcelVOS = JSONObject.parseArray(humanDetailRecordInsideList.toJSONString(), ProjectCostHumanDetailRecordInsideExcelVO.class);
        if (!CollectionUtils.isEmpty(humanDetailRecordExcelVOS)) {
            for (int i = 0; i < humanDetailRecordExcelVOS.size(); i++) {
                ProjectCostHumanDetailRecordInsideExcelVO humanDetailRecordExcelVO = humanDetailRecordExcelVOS.get(i);
                humanDetailRecordExcelVO.setNum(i + 1);
            }
        }

        List<ProjectCostHumanDetailRecordExternalExcelVO> humanDetailRecordExternalExcelVOS = JSONObject.parseArray(humanDetailRecordExternalList.toJSONString(), ProjectCostHumanDetailRecordExternalExcelVO.class);
        if (!CollectionUtils.isEmpty(humanDetailRecordExternalExcelVOS)) {
            for (int i = 0; i < humanDetailRecordExternalExcelVOS.size(); i++) {
                ProjectCostHumanDetailRecordExternalExcelVO humanDetailRecordExcelVO = humanDetailRecordExternalExcelVOS.get(i);
                humanDetailRecordExcelVO.setNum(i + 1);
            }
        }

        List<ProjectCostRevenueOrderRecordExcelVO> revenueOrderRecordExcelVOS = JSONObject.parseArray(revenueOrderRecordList.toJSONString(), ProjectCostRevenueOrderRecordExcelVO.class);
        List<ProjectCostRevenueOrderRecordExcelVO> revenueOrderRecordExcelVOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(revenueOrderRecordExcelVOS)) {
            int number = 1;
            for (int i = 0; i < revenueOrderRecordExcelVOS.size(); i++) {
                ProjectCostRevenueOrderRecordExcelVO revenueOrderRecordExcelVO = revenueOrderRecordExcelVOS.get(i);
                //如果内部成本和外部成本都为空/0，无需导出
                if ((revenueOrderRecordExcelVO.getInnerLaborCost() == null
                        || revenueOrderRecordExcelVO.getInnerLaborCost().compareTo(BigDecimal.ZERO) == 0)
                        && (revenueOrderRecordExcelVO.getOuterLaborCost() == null
                        || revenueOrderRecordExcelVO.getOuterLaborCost().compareTo(BigDecimal.ZERO) == 0)) {
                    continue;
                }
                revenueOrderRecordExcelVO.setNum(number++);
                revenueOrderRecordExcelVOList.add(revenueOrderRecordExcelVO);
            }
        }

        List<ProjectCostHumanItemRecordDTO> humanItemRecords = JSONObject.parseArray(humanItemRecordList.toJSONString(), ProjectCostHumanItemRecordDTO.class);
        List<ProjectCostFeeItemRecordDto> humanFeeItemRecords = JSONObject.parseArray(humanFeeItemRecordList.toJSONString(), ProjectCostFeeItemRecordDto.class);
        List<ProjectCostHumanSummaryRecord> humanSummaryRecords = JSONObject.parseArray(humanSummaryRecordList.toJSONString(), ProjectCostHumanSummaryRecord.class);
        List<ProjectCostEaDetailRecord> projectCostEaDetailRecords = JSONObject.parseArray(projectCostEaDetailRecordList.toJSONString(), ProjectCostEaDetailRecord.class);
        String name = null;
        // 根据使用单位获取不同的配置 - 人力费用类型
        FeeItemDto humanFeeItem = CacheDataUtils.findFeeItemByFeeFlag(SystemContext.getUnitId(), FeeFlagEnum.HUMAN.getCode());
        if (Objects.nonNull(humanFeeItem)) {
            name = humanFeeItem.getName();
        }
        List<ProjectCostEaDetailRecordExcelVO> projectCostEaDetailRecordExcelVOS = new ArrayList<>();
        if (ListUtils.isNotEmpty(projectCostEaDetailRecords)) {
            int n = 1;
            for (ProjectCostEaDetailRecord projectCostEaDetailRecord : projectCostEaDetailRecords) {
                if (Objects.equals(name, projectCostEaDetailRecord.getFeeItemName())) {
                    ProjectCostEaDetailRecordExcelVO projectCostEaDetailRecordExcelVO = BeanConverter.copyProperties(projectCostEaDetailRecord, ProjectCostEaDetailRecordExcelVO.class);
                    projectCostEaDetailRecordExcelVO.setNum(n++);
                    projectCostEaDetailRecordExcelVOS.add(projectCostEaDetailRecordExcelVO);
                }
            }
        }

        // 创建新的Excel 工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();

        // 设置字体
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 10);

        // 设置样式
        HSSFCellStyle cellLeftStyle = workbook.createCellStyle();
        cellLeftStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cellLeftStyle.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        cellLeftStyle.setFont(font);

        HSSFCellStyle cellRightStyleWithBorder = workbook.createCellStyle();
        cellRightStyleWithBorder.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cellRightStyleWithBorder.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        cellRightStyleWithBorder.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
        cellRightStyleWithBorder.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
        cellRightStyleWithBorder.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
        cellRightStyleWithBorder.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框
        cellRightStyleWithBorder.setFont(font);

        HSSFCellStyle cs = workbook.createCellStyle();
        HSSFFont f = workbook.createFont();
        f.setFontName("宋体");
        f.setFontHeightInPoints((short) 10);
        f.setFontHeight((short) 100);
        cs.setFont(f);
        cs.setAlignment(CellStyle.ALIGN_CENTER);
        cs.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cs.setWrapText(true);

        // 第一行
        // 在索引0的位置创建行（最顶端的行）
        HSSFSheet sheet = workbook.createSheet("项目人力成本汇总");
        // 列宽度
        sheet.setColumnWidth(0, 4500);
        sheet.setColumnWidth(1, 3000);
        sheet.setColumnWidth(2, 4500);
        sheet.setColumnWidth(3, 3000);
        sheet.setColumnWidth(4, 3000);
        sheet.setColumnWidth(5, 6000);
        sheet.setColumnWidth(6, 6000);
        sheet.setColumnWidth(7, 6000);

        HSSFRow row0 = ExportExcelUtil.createCell(sheet, "项目编号", 0, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row0, "项目名称", 2, cellLeftStyle);
        ExportExcelUtil.createCell(row0, "更新时间", 4, cellLeftStyle);

        HSSFRow row2 = ExportExcelUtil.createCell(sheet, "人力成本", 2, 0, cellLeftStyle);

        HSSFRow row_3 = ExportExcelUtil.createCell(sheet, "人力总预算", 3, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row_3, "已发生成本", 2, cellLeftStyle);
        ExportExcelUtil.createCell(row_3, "剩余预算", 4, cellLeftStyle);
        ExportExcelUtil.createCell(row_3, "已发生成本比例", 6, cellLeftStyle);

        if (!CollectionUtils.isEmpty(humanSummaryRecords)) {
            ProjectCostHumanSummaryRecord projectCostHumanSummaryRecord = humanSummaryRecords.get(0);
            projectCode = projectCostHumanSummaryRecord.getProjectCode();
            BigDecimal incurredRatio = projectCostHumanSummaryRecord.getIncurredRatio();
            BigDecimal cost = projectCostHumanSummaryRecord.getCost();
            Date createAt = projectCostHumanSummaryRecord.getCreateAt();
            BigDecimal budget = projectCostHumanSummaryRecord.getBudget();
            BigDecimal incurredCost = projectCostHumanSummaryRecord.getIncurredCost();
            BigDecimal remainderBudget = projectCostHumanSummaryRecord.getRemainderBudget();

            ExportExcelUtil.createCell(row0, projectCostHumanSummaryRecord.getProjectCode(), 1, cellLeftStyle);
            ExportExcelUtil.createCell(row0, projectCostHumanSummaryRecord.getProjectName(), 3, cellLeftStyle);
            ExportExcelUtil.createCell(row0, DateUtil.format(createAt, DateUtil.TIMESTAMP_PATTERN), 5, cellLeftStyle);

            ExportExcelUtil.createCell(row_3, budget == null ? "" : budget.toString(), 1, cellLeftStyle);
            ExportExcelUtil.createCell(row_3, incurredCost == null ? "" : incurredCost.toString(), 3, cellLeftStyle);
            ExportExcelUtil.createCell(row_3, remainderBudget == null ? "" : remainderBudget.toString(), 5, cellLeftStyle);
            ExportExcelUtil.createCell(row_3, bigDecimalRoundRatioFormat(incurredRatio), 7, cellLeftStyle);

            int rowNum = 3;
            HSSFRow row_10 = ExportExcelUtil.createCell(sheet, "属性", ++rowNum, 0, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row_10, "预算", 1, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row_10, "已处理成本", 2, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row_10, "待处理成本", 3, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row_10, "已审核工时(天)", 4, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row_10, "待审核工时(天)", 5, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row_10, "成本合计 ", 6, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row_10, "工时合计(天)", 7, cellRightStyleWithBorder);

            List<ProjectCostHumanItemRecordDTO> row_10_List = humanItemRecords.stream().filter(e -> e.getType() != 3).collect(Collectors.toList());
            List<ProjectCostHumanItemRecordDTO> row_10_1_List = humanItemRecords.stream().filter(e -> e.getType() == 3).collect(Collectors.toList());
            if (ListUtils.isNotEmpty(row_10_List)) {
                for (int i = 0; i < row_10_List.size(); i++) {
                    ProjectCostHumanItemRecordDTO projectCostHumanItemRecord = row_10_List.get(i);
                    String itemName = projectCostHumanItemRecord.getItemName();//属性
                    BigDecimal humanBudget = projectCostHumanItemRecord.getBudget();//预算
                    BigDecimal auditCost = projectCostHumanItemRecord.getAuditCost();//已处理成本
                    BigDecimal auditingCost = projectCostHumanItemRecord.getAuditingCost();//待处理成本
                    BigDecimal auditWorkingHours = projectCostHumanItemRecord.getAuditWorkingHours();//已审核工时(天)
                    BigDecimal auditingWorkingHours = projectCostHumanItemRecord.getAuditingWorkingHours();//待审核工时(天)
                    BigDecimal totalCost = projectCostHumanItemRecord.getTotalCost();//成本合计
                    BigDecimal totalWorkingHours = projectCostHumanItemRecord.getTotalWorkingHours();//工时合计(天)

                    HSSFRow row_i = ExportExcelUtil.createCell(sheet, itemName, ++rowNum, 0, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, bigDecimalRoundFormat(humanBudget), 1, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, bigDecimalFormat(auditCost), 2, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, bigDecimalFormat(auditingCost), 3, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, bigDecimalFormat(auditWorkingHours), 4, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, bigDecimalFormat(auditingWorkingHours), 5, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, bigDecimalFormat(totalCost), 6, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, bigDecimalFormat(totalWorkingHours), 7, cellRightStyleWithBorder);
                }
            }

            ++rowNum;
            ExportExcelUtil.createCell(sheet, "外部工时填报数据参考：", ++rowNum, 0, cellRightStyleWithBorder);
            HSSFRow row_10_1 = ExportExcelUtil.createCell(sheet, "属性", ++rowNum, 0, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row_10_1, "已审核工时参考金额", 1, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row_10_1, "已审核工时(天)", 2, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row_10_1, "待审核工时参考金额", 3, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row_10_1, "待审核工时(天)", 4, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row_10_1, "参考金额合计 ", 5, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row_10_1, "工时合计(天)", 6, cellRightStyleWithBorder);

            if (ListUtils.isNotEmpty(row_10_1_List)) {
                for (int i = 0; i < row_10_1_List.size(); i++) {
                    ProjectCostHumanItemRecordDTO projectCostHumanItemRecord = row_10_1_List.get(i);
                    String itemName = projectCostHumanItemRecord.getItemName();//属性
                    BigDecimal auditCost = projectCostHumanItemRecord.getAuditCost();//已审核工时参考金额
                    BigDecimal auditWorkingHours = projectCostHumanItemRecord.getAuditWorkingHours();//已审核工时(天)
                    BigDecimal auditingCost = projectCostHumanItemRecord.getAuditingCost();//待审核工时参考金额
                    BigDecimal auditingWorkingHours = projectCostHumanItemRecord.getAuditingWorkingHours();//待审核工时(天)
                    BigDecimal totalCost = projectCostHumanItemRecord.getTotalCost();//参考金额合计
                    BigDecimal totalWorkingHours = projectCostHumanItemRecord.getTotalWorkingHours();//工时合计(天)

                    HSSFRow row_i = ExportExcelUtil.createCell(sheet, itemName, ++rowNum, 0, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, bigDecimalFormat(auditCost), 1, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, bigDecimalFormat(auditWorkingHours), 2, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, bigDecimalFormat(auditingCost), 3, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, bigDecimalFormat(auditingWorkingHours), 4, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, bigDecimalFormat(totalCost), 5, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, bigDecimalFormat(totalWorkingHours), 6, cellRightStyleWithBorder);
                }
            }

            fileName.append(projectCode + "项目人力成本_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
            fileName.append(".xls");
            if (!CollectionUtils.isEmpty(humanDetailRecordExcelVOS)) {
                ExportExcelUtil.addSheet(workbook, humanDetailRecordExcelVOS, ProjectCostHumanDetailRecordInsideExcelVO.class, null, "内部人力成本明细", true);
            }
            if (!CollectionUtils.isEmpty(humanDetailRecordExternalExcelVOS)) {
                ExportExcelUtil.addSheet(workbook, humanDetailRecordExternalExcelVOS, ProjectCostHumanDetailRecordExternalExcelVO.class, null, "外部人力工时明细", true);
            }

            List<ProjectCostFeeDetailRecordExcelVO> projectCostFeeDetailRecordExcelVOS = new ArrayList<>();
            List<ProjectCostFeeDetailRecord> humanFeeDetailRecords = JSONObject.parseArray(humanFeeDetailRecordList.toJSONString(), ProjectCostFeeDetailRecord.class);
            if (ListUtils.isNotEmpty(humanFeeDetailRecords)) {
                projectCostFeeDetailRecordExcelVOS = BeanConverter.convert(humanFeeDetailRecords, ProjectCostFeeDetailRecordExcelVO.class);
                int n = 1;
                for (ProjectCostFeeDetailRecordExcelVO projectCostFeeDetailRecordExcelVO : projectCostFeeDetailRecordExcelVOS) {
                    projectCostFeeDetailRecordExcelVO.setNum(n++);
                }
                ExportExcelUtil.addSheet(workbook, projectCostFeeDetailRecordExcelVOS, ProjectCostFeeDetailRecordExcelVO.class, null, "外部人力成本明细", true);
            }

            if (!CollectionUtils.isEmpty(revenueOrderRecordExcelVOList)) {
                ExportExcelUtil.addSheet(workbook, revenueOrderRecordExcelVOList, ProjectCostRevenueOrderRecordExcelVO.class, null, "工单成本明细", true);
            }

            if (ListUtils.isNotEmpty(projectCostEaDetailRecordExcelVOS)) {
                ExportExcelUtil.addSheet(workbook, projectCostEaDetailRecordExcelVOS, ProjectCostEaDetailRecordExcelVO.class, null, "EA可用金额明细", true);
            }
            ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
        }
    }

    @ApiOperation(value = "项目费用成本导出", response = ResponseMap.class)
    @GetMapping("/fee/export")
    public void exportFee(HttpServletResponse response, @RequestParam Long projectId) {
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/statistics/project/cost/view/" + projectId, null);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<ProjectCostDetailVO> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<ProjectCostDetailVO>>() {
        });
        ProjectCostDetailVO data = dataResponse.getData();
        //项目编号
        String projectCode = "";

        //导出操作
        StringBuffer fileName = new StringBuffer();

        // 创建新的Excel 工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();

        // 设置字体
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
//        font.setBoldweight(XSSFFont.BOLDWEIGHT_BOLD);
        font.setFontHeightInPoints((short) 10);

        // 设置样式
        HSSFCellStyle cellLeftStyle = workbook.createCellStyle();
        cellLeftStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cellLeftStyle.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        cellLeftStyle.setFont(font);

        HSSFCellStyle cellRightStyleWithBorder = workbook.createCellStyle();
        cellRightStyleWithBorder.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cellRightStyleWithBorder.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        cellRightStyleWithBorder.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
        cellRightStyleWithBorder.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
        cellRightStyleWithBorder.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
        cellRightStyleWithBorder.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框
        cellRightStyleWithBorder.setFont(font);

        HSSFCellStyle cs = workbook.createCellStyle();
        HSSFFont f = workbook.createFont();
        f.setFontName("宋体");
//        f.setBoldweight(XSSFFont.BOLDWEIGHT_BOLD);
        f.setFontHeightInPoints((short) 10);
        f.setFontHeight((short) 100);
        cs.setFont(f);
        cs.setAlignment(CellStyle.ALIGN_CENTER);
        cs.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cs.setWrapText(true);

        // 第一行
        // 在索引0的位置创建行（最顶端的行）
        HSSFSheet sheet = workbook.createSheet("项目费用成本汇总");
        // 列宽度
        sheet.setColumnWidth(0, 6000);
        sheet.setColumnWidth(1, 6000);
        sheet.setColumnWidth(2, 6000);
        sheet.setColumnWidth(3, 6000);
        sheet.setColumnWidth(4, 6000);
        sheet.setColumnWidth(5, 6000);
        sheet.setColumnWidth(6, 6000);
        sheet.setColumnWidth(7, 6000);
        sheet.setColumnWidth(8, 6000);
        sheet.setColumnWidth(9, 6000);

        HSSFRow row0 = ExportExcelUtil.createCell(sheet, "项目编号", 0, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row0, "项目名称", 2, cellLeftStyle);
        ExportExcelUtil.createCell(row0, "更新时间", 4, cellLeftStyle);

        HSSFRow row2 = ExportExcelUtil.createCell(sheet, "费用成本", 2, 0, cellLeftStyle);

        HSSFRow row_3 = ExportExcelUtil.createCell(sheet, "费用总预算", 3, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row_3, "已发生成本", 2, cellLeftStyle);
        ExportExcelUtil.createCell(row_3, "剩余预算", 4, cellLeftStyle);
        ExportExcelUtil.createCell(row_3, "已发生成本比例", 6, cellLeftStyle);

        HSSFRow row_5 = ExportExcelUtil.createCell(sheet, "类型", 4, 0, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_5, "预算", 1, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_5, "已处理成本", 2, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_5, "待处理成本", 3, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_5, "EA可用金额", 4, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_5, "成本合计", 5, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_5, "剩余预算 ", 6, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_5, "已发生成本比例", 7, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_5, "年度预提汇总", 8, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_5, "月度预提汇总", 9, cellRightStyleWithBorder);

        if (data != null) {
            ProjectCostFeeSummaryRecord projectCostFeeSummaryRecord = data.getProjectCostFeeSummaryRecord();
            List<ProjectCostFeeItemRecordDto> projectCostFeeItemRecords = data.getProjectCostFeeItemRecords();
            List<ProjectCostFeeDetailRecord> projectCostFeeDetailRecords = data.getProjectCostFeeDetailRecords();
            List<ProjectCostEaDetailRecord> projectCostEaDetailRecords = data.getProjectCostEaDetailRecords();
            List<ProjectCostRevenueOrderRecord> projectCostRevenueOrderRecords = data.getProjectCostRevenueOrderRecords();

            if (projectCostFeeSummaryRecord != null) {
                projectCode = projectCostFeeSummaryRecord.getProjectCode();
                BigDecimal incurredRatio = projectCostFeeSummaryRecord.getIncurredRatio();
                BigDecimal cost = projectCostFeeSummaryRecord.getCost();
                Date createAt = projectCostFeeSummaryRecord.getCreateAt();
                BigDecimal budget = projectCostFeeSummaryRecord.getBudget();
                BigDecimal incurredCost = projectCostFeeSummaryRecord.getIncurredCost();
                BigDecimal remainderBudget = projectCostFeeSummaryRecord.getRemainderBudget();

                ExportExcelUtil.createCell(row0, projectCostFeeSummaryRecord.getProjectCode(), 1, cellLeftStyle);
                ExportExcelUtil.createCell(row0, projectCostFeeSummaryRecord.getProjectName(), 3, cellLeftStyle);
                ExportExcelUtil.createCell(row0, DateUtil.format(createAt, DateUtil.TIMESTAMP_PATTERN), 5, cellLeftStyle);

//                ExportExcelUtil.createCell(row2, bigDecimalFormat(cost), 1, cellLeftStyle);

                ExportExcelUtil.createCell(row_3, bigDecimalFormat(budget), 1, cellLeftStyle);
                ExportExcelUtil.createCell(row_3, bigDecimalFormat(cost), 3, cellLeftStyle);
                ExportExcelUtil.createCell(row_3, bigDecimalFormat(remainderBudget), 5, cellLeftStyle);
                ExportExcelUtil.createCell(row_3, bigDecimalRoundRatioFormat(incurredRatio), 7, cellLeftStyle);
            }

            if (ListUtils.isNotEmpty(projectCostFeeItemRecords)) {
                for (int i = 0; i < projectCostFeeItemRecords.size(); i++) {
                    ProjectCostFeeItemRecord projectCostFeeItemRecord = projectCostFeeItemRecords.get(i);
                    ProjectCostFeeItemRecordDto projectCostFeeItemRecordDto = projectCostFeeItemRecords.get(i);
                    String feeItemName = projectCostFeeItemRecord.getFeeItemName();
                    BigDecimal budget = projectCostFeeItemRecord.getBudget();
                    BigDecimal incurredCost = projectCostFeeItemRecord.getIncurredCost();
                    BigDecimal pendingCost = projectCostFeeItemRecord.getPendingCost();
                    BigDecimal eaAvaibleAcount = projectCostFeeItemRecord.getEaAvailableAmount();
/*                    if (eaAvaibleAcount==null && !("工单成本(差旅)".equals(feeItemName) && !("工单成本(其他费用)".equals(feeItemName)))) {
                        eaAvaibleAcount=BigDecimal.ZERO;
                        eaAvaibleAcount=eaAvaibleAcount.setScale(2);
                    }*/
                    BigDecimal totalCost = projectCostFeeItemRecord.getTotalCost();
                    BigDecimal remainderBudget = projectCostFeeItemRecord.getRemainderBudget();
                    BigDecimal incurredRatio = projectCostFeeItemRecord.getIncurredRatio();
                    BigDecimal annualAccrual = projectCostFeeItemRecordDto.getAnnual_accrual();
                    BigDecimal monthlyAccrual = projectCostFeeItemRecordDto.getMonthly_accrual();

                    int rowNum = (5 + i);
                    HSSFRow row_i = ExportExcelUtil.createCell(sheet, feeItemName, rowNum, 0, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, bigDecimalFormat(budget), 1, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, bigDecimalFormat(incurredCost), 2, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, bigDecimalFormat(pendingCost), 3, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, bigDecimalFormat(eaAvaibleAcount), 4, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, bigDecimalFormat(totalCost), 5, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, bigDecimalFormat(remainderBudget), 6, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, bigDecimalRoundRatioFormat(incurredRatio), 7, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, bigDecimalFormat(annualAccrual), 8, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, bigDecimalFormat(monthlyAccrual), 9, cellRightStyleWithBorder);
                }
            }

            List<ProjectCostFeeDetailRecordExcelVO> projectCostFeeDetailRecordExcelVOS = new ArrayList<>();
            if (ListUtils.isNotEmpty(projectCostFeeDetailRecords)) {
                projectCostFeeDetailRecordExcelVOS = BeanConverter.convert(projectCostFeeDetailRecords, ProjectCostFeeDetailRecordExcelVO.class);
                int n = 1;
                for (ProjectCostFeeDetailRecordExcelVO projectCostFeeDetailRecordExcelVO : projectCostFeeDetailRecordExcelVOS) {
                    projectCostFeeDetailRecordExcelVO.setNum(n++);
                }
                ExportExcelUtil.addSheet(workbook, projectCostFeeDetailRecordExcelVOS, ProjectCostFeeDetailRecordExcelVO.class, null, "费用成本明细", true);
            }

            List<ProjectCostEaDetailRecordExcelVO> projectCostEaDetailRecordExcelVOS = new ArrayList<>();
            if (ListUtils.isNotEmpty(projectCostEaDetailRecords)) {
                projectCostEaDetailRecordExcelVOS = BeanConverter.convert(projectCostEaDetailRecords, ProjectCostEaDetailRecordExcelVO.class);
                int n = 1;
                for (ProjectCostEaDetailRecordExcelVO projectCostEaDetailRecordExcelVO : projectCostEaDetailRecordExcelVOS) {
                    projectCostEaDetailRecordExcelVO.setNum(n++);
                }
                ExportExcelUtil.addSheet(workbook, projectCostEaDetailRecordExcelVOS, ProjectCostEaDetailRecordExcelVO.class, null, "EA可用金额明细", true);
            }

            List<ProjectCostRevenueOrderRecordExcelVO> projectCostRevenueOrderRecordExcelVOS = new ArrayList<>();
            List<ProjectCostRevenueOrderRecordExcelVO> revenueOrderRecordExcelVOList = new ArrayList<>();
            if (ListUtils.isNotEmpty(projectCostRevenueOrderRecords)) {
                projectCostRevenueOrderRecordExcelVOS = BeanConverter.convert(projectCostRevenueOrderRecords, ProjectCostRevenueOrderRecordExcelVO.class);
                int n = 1;
                for (ProjectCostRevenueOrderRecordExcelVO projectCostRevenueOrderRecordExcelVO : projectCostRevenueOrderRecordExcelVOS) {
                    //如果差旅费用和其他费用都为空/0，无需导出
                    if ((projectCostRevenueOrderRecordExcelVO.getFeeCost() == null
                            || projectCostRevenueOrderRecordExcelVO.getFeeCost().compareTo(BigDecimal.ZERO) == 0) && (projectCostRevenueOrderRecordExcelVO.getOtherCost() == null
                            || projectCostRevenueOrderRecordExcelVO.getOtherCost().compareTo(BigDecimal.ZERO) == 0)) {
                        continue;
                    }
                    projectCostRevenueOrderRecordExcelVO.setNum(n++);
                    revenueOrderRecordExcelVOList.add(projectCostRevenueOrderRecordExcelVO);
                }
                ExportExcelUtil.addSheet(workbook, revenueOrderRecordExcelVOList, ProjectCostRevenueOrderRecordExcelVO.class, null, "工单成本明细", true);
            }
            fileName.append(projectCode + "项目费用成本_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
            fileName.append(".xls");
            ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
        }
    }

    @ApiOperation(value = "项目资产成本导出", response = ResponseMap.class)
    @GetMapping("/asset/export")
    public void exportAsset(HttpServletResponse response, @RequestParam Long projectId) {
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/statistics/project/cost/view/" + projectId, null);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<ProjectCostDetailVO> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<ProjectCostDetailVO>>() {
        });
        ProjectCostDetailVO data = dataResponse.getData();
        //项目编号
        String projectCode = "";

        //导出操作
        StringBuffer fileName = new StringBuffer();

        // 创建新的Excel 工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();

        // 设置字体
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 10);

        // 设置样式
        HSSFCellStyle cellLeftStyle = workbook.createCellStyle();
        cellLeftStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cellLeftStyle.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        cellLeftStyle.setFont(font);

        HSSFCellStyle cellRightStyleWithBorder = workbook.createCellStyle();
        cellRightStyleWithBorder.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cellRightStyleWithBorder.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        cellRightStyleWithBorder.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
        cellRightStyleWithBorder.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
        cellRightStyleWithBorder.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
        cellRightStyleWithBorder.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框
        cellRightStyleWithBorder.setFont(font);

        HSSFCellStyle cs = workbook.createCellStyle();
        HSSFFont f = workbook.createFont();
        f.setFontName("宋体");
        f.setFontHeightInPoints((short) 10);
        f.setFontHeight((short) 100);
        cs.setFont(f);
        cs.setAlignment(CellStyle.ALIGN_CENTER);
        cs.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cs.setWrapText(true);

        // 第一行
        // 在索引0的位置创建行（最顶端的行）
        HSSFSheet sheet = workbook.createSheet("项目资产折旧成本汇总");
        // 列宽度
        sheet.setColumnWidth(0, 6000);
        sheet.setColumnWidth(1, 6000);
        sheet.setColumnWidth(2, 6000);
        sheet.setColumnWidth(3, 6000);
        sheet.setColumnWidth(4, 6000);
        sheet.setColumnWidth(5, 6000);
        sheet.setColumnWidth(6, 6000);
        sheet.setColumnWidth(7, 6000);

        HSSFRow row0 = ExportExcelUtil.createCell(sheet, "项目编号", 0, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row0, "项目名称", 2, cellLeftStyle);
        ExportExcelUtil.createCell(row0, "更新时间", 4, cellLeftStyle);

        HSSFRow row2 = ExportExcelUtil.createCell(sheet, "资产折旧成本", 2, 0, cellLeftStyle);

        HSSFRow row_3 = ExportExcelUtil.createCell(sheet, "原始成本（总预算）", 3, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row_3, "剩余折旧（剩余预算）", 2, cellLeftStyle);
        ExportExcelUtil.createCell(row_3, "累计折旧（已发生成本）", 4, cellLeftStyle);

        HSSFRow row_5 = ExportExcelUtil.createCell(sheet, "资产编号", 4, 0, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_5, "资产说明", 1, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_5, "启用日期", 2, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_5, "折旧月数", 3, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_5, "最新折旧月份", 4, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_5, "原始成本", 5, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_5, "累计折旧 ", 6, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_5, "残值", 7, cellRightStyleWithBorder);

        if (data != null) {
            ProjectCostAssetSummaryRecord projectCostAssetSummaryRecord = data.getProjectCostAssetSummaryRecord();
            List<ProjectCostAssetItemRecord> projectCostAssetItemRecords = data.getProjectCostAssetItemRecords();
            List<ProjectCostAssetDetailRecord> projectCostAssetDetailRecords = data.getProjectCostAssetDetailRecords();

            if (projectCostAssetSummaryRecord != null) {
                projectCode = projectCostAssetSummaryRecord.getProjectCode();
                String projectName = projectCostAssetSummaryRecord.getProjectName();
                Date createAt = projectCostAssetSummaryRecord.getCreateAt();
                BigDecimal budget = projectCostAssetSummaryRecord.getBudget();
                BigDecimal incurredCost = projectCostAssetSummaryRecord.getIncurredCost();
                BigDecimal remainderBudget = projectCostAssetSummaryRecord.getRemainderBudget();

                ExportExcelUtil.createCell(row0, projectCode, 1, cellLeftStyle);
                ExportExcelUtil.createCell(row0, projectName, 3, cellLeftStyle);
                ExportExcelUtil.createCell(row0, DateUtil.format(createAt, DateUtil.TIMESTAMP_PATTERN), 5, cellLeftStyle);

                ExportExcelUtil.createCell(row_3, bigDecimalFormat(budget), 1, cellLeftStyle);
                ExportExcelUtil.createCell(row_3, bigDecimalFormat(remainderBudget), 3, cellLeftStyle);
                ExportExcelUtil.createCell(row_3, bigDecimalFormat(incurredCost), 5, cellLeftStyle);
            }

            if (ListUtils.isNotEmpty(projectCostAssetItemRecords)) {
                for (int i = 0; i < projectCostAssetItemRecords.size(); i++) {
                    ProjectCostAssetItemRecord projectCostAssetItemRecord = projectCostAssetItemRecords.get(i);
                    String assetNumber = projectCostAssetItemRecord.getAssetNumber();
                    String description = projectCostAssetItemRecord.getDescription();
                    Date datePlacedInService = projectCostAssetItemRecord.getDatePlacedInService();
                    Integer lifeInMonths = projectCostAssetItemRecord.getLifeInMonths();
                    String lastPeriodName = projectCostAssetItemRecord.getLastPeriodName();
                    BigDecimal cost = projectCostAssetItemRecord.getCost();
                    BigDecimal deprnReserve = projectCostAssetItemRecord.getDeprnReserve();
                    BigDecimal salvageValue = projectCostAssetItemRecord.getSalvageValue();

                    int rowNum = (5 + i);
                    HSSFRow row_i = ExportExcelUtil.createCell(sheet, assetNumber, rowNum, 0, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, description, 1, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, DateUtil.format(datePlacedInService, DateUtil.TIMESTAMP_PATTERN), 2, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, lifeInMonths == null ? null : String.valueOf(lifeInMonths), 3, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, lastPeriodName, 4, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, bigDecimalFormat(cost), 5, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, bigDecimalFormat(deprnReserve), 6, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, bigDecimalFormat(salvageValue), 7, cellRightStyleWithBorder);
                }
            }

            List<ProjectCostAssetDetailRecordExcelVO> projectCostAssetDetailRecordExcelVOS = new ArrayList<>();
            if (ListUtils.isNotEmpty(projectCostAssetDetailRecords)) {
                projectCostAssetDetailRecordExcelVOS = BeanConverter.convert(projectCostAssetDetailRecords, ProjectCostAssetDetailRecordExcelVO.class);
                int n = 1;
                for (ProjectCostAssetDetailRecordExcelVO projectCostAssetDetailRecordExcelVO : projectCostAssetDetailRecordExcelVOS) {
                    projectCostAssetDetailRecordExcelVO.setNum(n++);
                }
                ExportExcelUtil.addSheet(workbook, projectCostAssetDetailRecordExcelVOS, ProjectCostAssetDetailRecordExcelVO.class, null, "资产折旧成本明细", true);
            }

            fileName.append(projectCode + "项目资产折旧成本_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
            fileName.append(".xls");
            ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
        }
    }

    @ApiOperation(value = "项目物料数据生成")
    @GetMapping("/generateProjectMaterialCost")
    public Response generateProjectMaterialCost(@RequestParam(required = true) Long projectIds, @RequestParam(required = true) Long executeId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectIds", projectIds);
        param.put("executeId", executeId);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/statistics/project/cost/generateProjectMaterialCost", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "项目物料成本导出")
    @GetMapping("material/export")
    public void exportMaterial(HttpServletResponse response, @RequestParam Long projectId) {
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/project/cost/exportMaterialCost/" + projectId, null);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<ProjectCostDetailVO> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<ProjectCostDetailVO>>() {
        });

        ProjectCostDetailVO data = dataResponse.getData();
        //项目编号
        String projectCode = "";
        //导出操作
        StringBuffer fileName = new StringBuffer();

        // 创建新的Excel 工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();
        // 设置字体
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 10);
        // 设置样式
        HSSFCellStyle cellLeftStyle = workbook.createCellStyle();
        cellLeftStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cellLeftStyle.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        cellLeftStyle.setFont(font);

        HSSFCellStyle cellRightStyleWithBorder = workbook.createCellStyle();
        cellRightStyleWithBorder.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cellRightStyleWithBorder.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        cellRightStyleWithBorder.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
        cellRightStyleWithBorder.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
        cellRightStyleWithBorder.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
        cellRightStyleWithBorder.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框
        cellRightStyleWithBorder.setFont(font);

        HSSFCellStyle cs = workbook.createCellStyle();
        HSSFFont f = workbook.createFont();
        f.setFontName("宋体");
        f.setFontHeightInPoints((short) 10);
        f.setFontHeight((short) 100);
        cs.setFont(f);
        cs.setAlignment(CellStyle.ALIGN_CENTER);
        cs.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cs.setWrapText(true);

        // 第一行
        // 在索引0的位置创建行（最顶端的行）
        HSSFSheet sheet = workbook.createSheet("项目物料成本汇总");
        // 列宽度
        sheet.setColumnWidth(0, 4500);
        sheet.setColumnWidth(1, 4500);
        sheet.setColumnWidth(2, 4500);
        sheet.setColumnWidth(3, 5000);
        sheet.setColumnWidth(4, 3000);
        sheet.setColumnWidth(5, 6000);
        sheet.setColumnWidth(6, 6000);

        HSSFRow row0 = ExportExcelUtil.createCell(sheet, "项目编号", 0, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row0, "项目名称", 2, cellLeftStyle);
        ExportExcelUtil.createCell(row0, "更新时间", 4, cellLeftStyle);

        HSSFRow row2 = ExportExcelUtil.createCell(sheet, "物料成本", 2, 0, cellLeftStyle);

        HSSFRow row_3 = ExportExcelUtil.createCell(sheet, "物料总预算", 3, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row_3, "已发生成本", 2, cellLeftStyle);
        ExportExcelUtil.createCell(row_3, "剩余预算", 4, cellLeftStyle);
        ExportExcelUtil.createCell(row_3, "已发生成本比例", 6, cellLeftStyle);

        HSSFRow row_5 = ExportExcelUtil.createCell(sheet, "属性", 4, 0, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_5, "已处理成本", 1, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_5, "待处理成本", 2, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_5, "成本合计", 3, cellRightStyleWithBorder);

        if (data != null) {
            ProjectCostMaterialcostSummaryRecord materialcostSummaryRecord = data.getMaterialcostSummaryRecord();
            List<ProjectCostMaterialcostSummaryDetailRecordDto> materialcostSummaryDetailRecordList = data.getMaterialcostSummaryDetailRecordList();
            List<ProjectCostPurchaseOrderRecord> purchaseOrderRecordList = data.getPurchaseOrderRecordList();
            List<FormalMaterialGet> formalMaterialGetList = data.getFormalMaterialGetList();
            List<ProjectCostStorageRecord> storageRecordList = data.getStorageRecordList();
            List<ProjectCostGetreturnMaterialRecord> getreturnMaterialRecordList = data.getGetreturnMaterialRecordList();
            List<ProjectCostOutsourcePurchaseRecord> outsourcePurchaseRecordList = data.getOutsourcePurchaseRecordList();
            List<ProjectCostDifferenceRecord> differenceRecordList = data.getDifferenceRecordList();
            List<ProjectCostRevenueOrderRecord> revenueOrderRecordList = data.getProjectCostRevenueOrderRecords();
            if (materialcostSummaryRecord != null) {
                projectCode = materialcostSummaryRecord.getProjectCode();
                BigDecimal budgetPriceTotal = materialcostSummaryRecord.getBudgetPriceTotal();//物料总预算
                BigDecimal handledCost = materialcostSummaryRecord.getHandledCost();//已发生成本
                BigDecimal restBudgetPrice = materialcostSummaryRecord.getRestBudgetPrice();//剩余预算
                BigDecimal handledCostRatio = materialcostSummaryRecord.getHandledCostRatio();//已发生成本比例
                Date createAt = materialcostSummaryRecord.getCreateAt();

                ExportExcelUtil.createCell(row0, materialcostSummaryRecord.getProjectCode(), 1, cellLeftStyle);
                ExportExcelUtil.createCell(row0, materialcostSummaryRecord.getProjectName(), 3, cellLeftStyle);
                ExportExcelUtil.createCell(row0, DateUtil.format(createAt, DateUtil.TIMESTAMP_PATTERN), 5, cellLeftStyle);

                ExportExcelUtil.createCell(row_3, budgetPriceTotal == null ? "-" : budgetPriceTotal.toString(), 1, cellLeftStyle);
                ExportExcelUtil.createCell(row_3, handledCost == null ? "-" : handledCost.toString(), 3, cellLeftStyle);
                ExportExcelUtil.createCell(row_3, restBudgetPrice == null ? "-" : restBudgetPrice.toString(), 5, cellLeftStyle);
                ExportExcelUtil.createCell(row_3, bigDecimalRoundRatioFormat(handledCostRatio), 7, cellLeftStyle);
            }

            if (ListUtils.isNotEmpty(materialcostSummaryDetailRecordList)) {
                for (int i = 0; i < materialcostSummaryDetailRecordList.size(); i++) {
                    ProjectCostMaterialcostSummaryDetailRecord materialcostSummaryDetailRecord = materialcostSummaryDetailRecordList.get(i);
                    String attributeType = materialcostSummaryDetailRecord.getAttributeType();//属性
                    BigDecimal dealCost = materialcostSummaryDetailRecord.getDealCost();//已处理成本
                    BigDecimal todoCost = materialcostSummaryDetailRecord.getTodoCost();//待处理成本
                    BigDecimal totalCost = materialcostSummaryDetailRecord.getTotalCost();//成本合计
                    if (attributeType.equals("工单成本") && materialcostSummaryDetailRecord.getDealCost() == null) {
                        continue;
                    }
                    if (attributeType.equals("工单成本") && BigDecimalUtils.equals(materialcostSummaryDetailRecord.getDealCost(), BigDecimal.ZERO)) {
                        continue;
                    }
                    int rowNum = (5 + i);
                    HSSFRow row_i = ExportExcelUtil.createCell(sheet, attributeType, rowNum, 0, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, dealCost == null ? "" : dealCost.toString(), 1, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, todoCost == null ? "" : todoCost.toString(), 2, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, totalCost == null ? "" : totalCost.toString(), 3, cellRightStyleWithBorder);
                }
            }

            List<PurchaseOrderRecordExcelVO> purchaseOrderRecordExcelVOList = new ArrayList<>();
            List<StorageRecordExcelVO> storageRecordExcelVOList = new ArrayList<>();
            List<GetreturnMaterialRecordExcelVO> getreturnMaterialRecordExcelVOList = new ArrayList<>();
            List<OutsourcePurchaseRecordExcelVO> outsourcePurchaseRecordExcelVOList = new ArrayList<>();
            List<DifferenceRecordExcelVO> differenceRecordExcelVOList = new ArrayList<>();
            List<ProjectCostRevenueOrderRecordExcelVO> revenueOrderRecordExcelVOList = new ArrayList<>();
            List<FormalMaterialGetListExcelVO> formalMaterialGetExcelVOList = new ArrayList<>();

            if (ListUtils.isNotEmpty(formalMaterialGetList)) {
                formalMaterialGetExcelVOList = BeanConverter.convert(formalMaterialGetList, FormalMaterialGetListExcelVO.class);
                if (ListUtils.isNotEmpty(formalMaterialGetExcelVOList)) {
                    for (int i = 0; i < formalMaterialGetExcelVOList.size(); i++) {
                        formalMaterialGetExcelVOList.get(i).setNum(i + 1);
                    }
                }
                ExportExcelUtil.addSheet(workbook, formalMaterialGetExcelVOList, FormalMaterialGetListExcelVO.class, null, "ERP销售工单发料成本报表", true);
            }

            if (ListUtils.isNotEmpty(purchaseOrderRecordList)) {
                purchaseOrderRecordExcelVOList = BeanConverter.convert(purchaseOrderRecordList, PurchaseOrderRecordExcelVO.class);
                if (ListUtils.isNotEmpty(purchaseOrderRecordExcelVOList)) {
                    for (int i = 0; i < purchaseOrderRecordExcelVOList.size(); i++) {
                        purchaseOrderRecordExcelVOList.get(i).setNum(i + 1);
                    }
                }
                ExportExcelUtil.addSheet(workbook, purchaseOrderRecordExcelVOList, PurchaseOrderRecordExcelVO.class, null, "采购下单成本明细", true);
            }

            if (ListUtils.isNotEmpty(storageRecordList)) {
                storageRecordExcelVOList = BeanConverter.convert(storageRecordList, StorageRecordExcelVO.class);
                if (ListUtils.isNotEmpty(storageRecordExcelVOList)) {
                    for (int i = 0; i < storageRecordExcelVOList.size(); i++) {
                        storageRecordExcelVOList.get(i).setNum(i + 1);
                    }
                }
                ExportExcelUtil.addSheet(workbook, storageRecordExcelVOList, StorageRecordExcelVO.class, null, "项目库存成本明细", true);
            }

            if (ListUtils.isNotEmpty(getreturnMaterialRecordList)) {
                getreturnMaterialRecordExcelVOList = BeanConverter.convert(getreturnMaterialRecordList, GetreturnMaterialRecordExcelVO.class);
                if (ListUtils.isNotEmpty(getreturnMaterialRecordExcelVOList)) {
                    for (int i = 0; i < getreturnMaterialRecordExcelVOList.size(); i++) {
                        getreturnMaterialRecordExcelVOList.get(i).setNum(i + 1);
                    }
                }
                ExportExcelUtil.addSheet(workbook, getreturnMaterialRecordExcelVOList, GetreturnMaterialRecordExcelVO.class, null, "项目领料成本明细", true);
            }

            if (ListUtils.isNotEmpty(outsourcePurchaseRecordList)) {
                outsourcePurchaseRecordExcelVOList = BeanConverter.convert(outsourcePurchaseRecordList, OutsourcePurchaseRecordExcelVO.class);
                if (ListUtils.isNotEmpty(outsourcePurchaseRecordExcelVOList)) {
                    for (int i = 0; i < outsourcePurchaseRecordExcelVOList.size(); i++) {
                        outsourcePurchaseRecordExcelVOList.get(i).setNum(i + 1);
                    }
                }
                ExportExcelUtil.addSheet(workbook, outsourcePurchaseRecordExcelVOList, OutsourcePurchaseRecordExcelVO.class, null, "外包采购成本明细", true);
            }

            if (ListUtils.isNotEmpty(differenceRecordList)) {
                differenceRecordExcelVOList = BeanConverter.convert(differenceRecordList, DifferenceRecordExcelVO.class);
                if (ListUtils.isNotEmpty(differenceRecordExcelVOList)) {
                    for (int i = 0; i < differenceRecordExcelVOList.size(); i++) {
                        differenceRecordExcelVOList.get(i).setNum(i + 1);
                    }
                }
                ExportExcelUtil.addSheet(workbook, differenceRecordExcelVOList, DifferenceRecordExcelVO.class, null, "差异分摊成本明细", true);
            }

            if (ListUtils.isNotEmpty(revenueOrderRecordList)) {
                List<ProjectCostRevenueOrderRecordExcelVO> revenueOrderRecordExcelVOS = new ArrayList<>();
                revenueOrderRecordExcelVOList = BeanConverter.convert(revenueOrderRecordList, ProjectCostRevenueOrderRecordExcelVO.class);
                if (ListUtils.isNotEmpty(revenueOrderRecordExcelVOList)) {
                    int number = 1;
                    for (int i = 0; i < revenueOrderRecordExcelVOList.size(); i++) {
                        ProjectCostRevenueOrderRecordExcelVO revenueOrderRecordExcelVO = revenueOrderRecordExcelVOList.get(i);
                        //如果物料和物料外包都为空/0，无需导出
                        if ((revenueOrderRecordExcelVO.getMaterialCost() == null
                                || revenueOrderRecordExcelVO.getMaterialCost().compareTo(BigDecimal.ZERO) == 0)
                                && (revenueOrderRecordExcelVO.getMaterialOutsourceCost() == null
                                || revenueOrderRecordExcelVO.getMaterialOutsourceCost().compareTo(BigDecimal.ZERO) == 0)) {
                            continue;
                        }
                        revenueOrderRecordExcelVO.setNum(number++);
                        revenueOrderRecordExcelVOS.add(revenueOrderRecordExcelVO);
                    }
                }
                if (!CollectionUtils.isEmpty(revenueOrderRecordExcelVOS)) {
                    ExportExcelUtil.addSheet(workbook, revenueOrderRecordExcelVOS, ProjectCostRevenueOrderRecordExcelVO.class, null, "工单成本明细", true);
                }
            }

            fileName.append(projectCode + "项目物料成本_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
            fileName.append(".xls");
        }
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }


    public void setDataToCostCollection(HSSFSheet sheet, ProjectCostDetailVO data, HSSFCellStyle cellLeftStyle, HSSFCellStyle cellRightStyleWithBorder) {
        if (data == null) {
            return;
        }

        sheet.setColumnWidth(0, 4500);
        sheet.setColumnWidth(1, 3000);
        sheet.setColumnWidth(2, 4500);
        sheet.setColumnWidth(3, 3000);
        sheet.setColumnWidth(4, 3000);
        sheet.setColumnWidth(5, 6000);
        sheet.setColumnWidth(6, 6000);
        sheet.setColumnWidth(7, 6000);
        sheet.setColumnWidth(8, 6000);

        int rowNum = 0;
        HSSFRow row0 = ExportExcelUtil.createCell(sheet, "项目编号", rowNum++, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row0, "项目名称", 2, cellLeftStyle);
        ExportExcelUtil.createCell(row0, "更新时间", 4, cellLeftStyle);
        ExportExcelUtil.createCell(row0, "本位币币种", 6, cellLeftStyle);

        HSSFRow row2 = ExportExcelUtil.createCell(sheet, "合同币种", ++rowNum, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row2, "项目不含税金额（原币）", 2, cellLeftStyle);
        ExportExcelUtil.createCell(row2, "已确认收入比例（原币）", 4, cellLeftStyle);
        ExportExcelUtil.createCell(row2, "汇兑损益（本位币）", 6, cellLeftStyle);

        HSSFRow row3 = ExportExcelUtil.createCell(sheet, "已确认收入总额（本位币）", ++rowNum, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row3, "已确认成本总额（本位币）", 2, cellLeftStyle);
        ExportExcelUtil.createCell(row3, "已确认毛利率（本位币）", 4, cellLeftStyle);

        HSSFRow row4 = ExportExcelUtil.createCell(sheet, "项目总预算（本位币）", ++rowNum, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row4, "已发生成本（本位币）", 2, cellLeftStyle);
        ExportExcelUtil.createCell(row4, "剩余预算（本位币）", 4, cellLeftStyle);

        ProjectCostSummaryRecord projectCostSummaryRecord = data.getProjectCostSummaryRecord();
        if (projectCostSummaryRecord != null) {
            String currency = data.getCurrency();//合同币种
            BigDecimal confirmedExchangeAmount = projectCostSummaryRecord.getConfirmedExchangeAmount();//汇兑损益（本位币）
            BigDecimal projectAmount = projectCostSummaryRecord.getProjectAmount();//项目不含税金额（原币）
            BigDecimal confirmedIncomeTotalAmount = projectCostSummaryRecord.getConfirmedIncomeTotalAmount();//已确认收入总额
            BigDecimal standardConfirmedIncomeTotalAmount = projectCostSummaryRecord.getStandardConfirmedIncomeTotalAmount();//已确认收入总额（本位币）
            BigDecimal confirmedCostTotalAmount = projectCostSummaryRecord.getConfirmedCostTotalAmount();//已确认成本总额
            BigDecimal confirmedGrossProfitRatio = projectCostSummaryRecord.getConfirmedGrossProfitRatio();//已确认毛利率
            BigDecimal projectBudget = projectCostSummaryRecord.getProjectBudget();//项目总预算
            BigDecimal incurredCost = projectCostSummaryRecord.getIncurredCost();//已发生成本
            BigDecimal remainderBudget = projectCostSummaryRecord.getRemainderBudget();//剩余预算
            //已确认收入比例（原币）= 取结转单中“本期确认收入（原币）”的合计/项目不含税金额（原币）*100
            BigDecimal cumulativeConfirmedIncomeRatio = BigDecimalUtils.divide(BigDecimalUtils.multiply(confirmedIncomeTotalAmount, new BigDecimal(100)), projectAmount);

            ExportExcelUtil.createCell(row2, currency, 1, cellLeftStyle);
            ExportExcelUtil.createCell(row2, projectAmount == null ? "" : projectAmount.toString(), 3, cellLeftStyle);
            ExportExcelUtil.createCell(row2, bigDecimalRoundRatioFormat(cumulativeConfirmedIncomeRatio), 5, cellLeftStyle);
            ExportExcelUtil.createCell(row2, bigDecimalRoundFormat(confirmedExchangeAmount), 7, cellLeftStyle);
            ExportExcelUtil.createCell(row3, standardConfirmedIncomeTotalAmount == null ? "" : standardConfirmedIncomeTotalAmount.toString(), 1, cellLeftStyle);
            ExportExcelUtil.createCell(row3, confirmedCostTotalAmount == null ? "" : confirmedCostTotalAmount.toString(), 3, cellLeftStyle);
            ExportExcelUtil.createCell(row3, bigDecimalRoundRatioFormat(confirmedGrossProfitRatio), 5, cellLeftStyle);
            ExportExcelUtil.createCell(row4, projectBudget == null ? "" : projectBudget.toString(), 1, cellLeftStyle);
            ExportExcelUtil.createCell(row4, incurredCost == null ? "" : incurredCost.toString(), 3, cellLeftStyle);
            ExportExcelUtil.createCell(row4, remainderBudget == null ? "" : remainderBudget.toString(), 5, cellLeftStyle);
        }

        ++rowNum;
        HSSFRow row5 = ExportExcelUtil.createCell(sheet, "物料成本", ++rowNum, 0, cellLeftStyle);

        HSSFRow row_6 = ExportExcelUtil.createCell(sheet, "物料总预算", ++rowNum, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row_6, "已发生成本", 2, cellLeftStyle);
        ExportExcelUtil.createCell(row_6, "剩余预算", 4, cellLeftStyle);
        ExportExcelUtil.createCell(row_6, "已发生成本比例", 6, cellLeftStyle);

        HSSFRow row_7 = ExportExcelUtil.createCell(sheet, "属性", ++rowNum, 0, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_7, "已处理成本", 1, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_7, "待处理成本", 2, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_7, "成本合计", 3, cellRightStyleWithBorder);

        ProjectCostMaterialcostSummaryRecord materialcostSummaryRecord = data.getMaterialcostSummaryRecord();
        List<ProjectCostMaterialcostSummaryDetailRecordDto> materialcostSummaryDetailRecordList = data.getProjectCostMaterialcostSummaryDetailRecords();
        List<ProjectCostRevenueOrderRecord> revenueOrderRecordList = data.getProjectCostRevenueOrderRecords();
        if (materialcostSummaryRecord != null) {
            BigDecimal budgetPriceTotal = materialcostSummaryRecord.getBudgetPriceTotal();//物料总预算
            BigDecimal handledCost = materialcostSummaryRecord.getHandledCost();//已发生成本
            BigDecimal restBudgetPrice = materialcostSummaryRecord.getRestBudgetPrice();//剩余预算
            BigDecimal handledCostRatio = materialcostSummaryRecord.getHandledCostRatio();//已发生成本比例
            Date createAt = materialcostSummaryRecord.getCreateAt();

            ExportExcelUtil.createCell(row0, materialcostSummaryRecord.getProjectCode(), 1, cellLeftStyle);
            ExportExcelUtil.createCell(row0, materialcostSummaryRecord.getProjectName(), 3, cellLeftStyle);
            ExportExcelUtil.createCell(row0, DateUtil.format(createAt, DateUtil.TIMESTAMP_PATTERN), 5, cellLeftStyle);
            ExportExcelUtil.createCell(row0, data.getLocalCurrency(), 7, cellLeftStyle);

            ExportExcelUtil.createCell(row_6, bigDecimalFormat(budgetPriceTotal), 1, cellLeftStyle);
            ExportExcelUtil.createCell(row_6, bigDecimalFormat(handledCost), 3, cellLeftStyle);
            ExportExcelUtil.createCell(row_6, bigDecimalFormat(restBudgetPrice), 5, cellLeftStyle);
            ExportExcelUtil.createCell(row_6, bigDecimalRoundRatioFormat(handledCostRatio), 7, cellLeftStyle);
        }

        if (ListUtils.isNotEmpty(materialcostSummaryDetailRecordList)) {
            for (int i = 0; i < materialcostSummaryDetailRecordList.size(); i++) {
                ProjectCostMaterialcostSummaryDetailRecord materialcostSummaryDetailRecord = materialcostSummaryDetailRecordList.get(i);
                String attributeType = materialcostSummaryDetailRecord.getAttributeType();//属性
                BigDecimal dealCost = materialcostSummaryDetailRecord.getDealCost();//已处理成本
                BigDecimal todoCost = materialcostSummaryDetailRecord.getTodoCost();//待处理成本
                BigDecimal totalCost = materialcostSummaryDetailRecord.getTotalCost();//成本合计
                if (ListUtils.isEmpty(revenueOrderRecordList) && attributeType.equals("工单成本")) {
                    continue;
                }
                HSSFRow row_i = ExportExcelUtil.createCell(sheet, attributeType, ++rowNum, 0, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_i, bigDecimalFormat(dealCost), 1, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_i, bigDecimalFormat(todoCost), 2, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_i, bigDecimalFormat(totalCost), 3, cellRightStyleWithBorder);
            }
        }

        ++rowNum;
        HSSFRow row8 = ExportExcelUtil.createCell(sheet, "人力成本", ++rowNum, 0, cellLeftStyle);

        HSSFRow row_9 = ExportExcelUtil.createCell(sheet, "人力总预算", ++rowNum, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row_9, "已发生成本", 2, cellLeftStyle);
        ExportExcelUtil.createCell(row_9, "剩余预算", 4, cellLeftStyle);
        ExportExcelUtil.createCell(row_9, "已发生成本比例", 6, cellLeftStyle);

        ProjectCostHumanSummaryRecord projectCostHumanSummaryRecord = data.getProjectCostHumanSummaryRecord();
        if (projectCostHumanSummaryRecord != null) {
            BigDecimal budget = projectCostHumanSummaryRecord.getBudget();
            BigDecimal cost = projectCostHumanSummaryRecord.getCost();
            BigDecimal remainderBudget = projectCostHumanSummaryRecord.getRemainderBudget();
            BigDecimal incurredRatio = projectCostHumanSummaryRecord.getIncurredRatio();

            ExportExcelUtil.createCell(row_9, bigDecimalFormat(budget), 1, cellLeftStyle);
            ExportExcelUtil.createCell(row_9, bigDecimalFormat(cost), 3, cellLeftStyle);
            ExportExcelUtil.createCell(row_9, bigDecimalFormat(remainderBudget), 5, cellLeftStyle);
            ExportExcelUtil.createCell(row_9, bigDecimalRoundRatioFormat(incurredRatio), 7, cellLeftStyle);
        }

        HSSFRow row_10 = ExportExcelUtil.createCell(sheet, "属性", ++rowNum, 0, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_10, "预算", 1, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_10, "已处理成本", 2, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_10, "待处理成本", 3, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_10, "已审核工时(天)", 4, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_10, "待审核工时(天)", 5, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_10, "成本合计 ", 6, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_10, "工时合计(天)", 7, cellRightStyleWithBorder);

        List<ProjectCostHumanItemRecordDTO> humanItemRecords = data.getProjectCostHumanItemRecords();
        List<ProjectCostHumanItemRecordDTO> row_10_List = humanItemRecords.stream().filter(e -> e.getType() != 3).collect(Collectors.toList());
        List<ProjectCostHumanItemRecordDTO> row_10_1_List = humanItemRecords.stream().filter(e -> e.getType() == 3).collect(Collectors.toList());
        if (ListUtils.isNotEmpty(row_10_List)) {
            for (int i = 0; i < row_10_List.size(); i++) {
                ProjectCostHumanItemRecordDTO projectCostHumanItemRecord = row_10_List.get(i);
                String itemName = projectCostHumanItemRecord.getItemName();//属性
                BigDecimal budget = projectCostHumanItemRecord.getBudget();//预算
                BigDecimal auditCost = projectCostHumanItemRecord.getAuditCost();//已处理成本
                BigDecimal auditingCost = projectCostHumanItemRecord.getAuditingCost();//待处理成本
                BigDecimal auditWorkingHours = projectCostHumanItemRecord.getAuditWorkingHours();//已审核工时(天)
                BigDecimal auditingWorkingHours = projectCostHumanItemRecord.getAuditingWorkingHours();//待审核工时(天)
                BigDecimal totalCost = projectCostHumanItemRecord.getTotalCost();//成本合计
                BigDecimal totalWorkingHours = projectCostHumanItemRecord.getTotalWorkingHours();//工时合计(天)

                HSSFRow row_i = ExportExcelUtil.createCell(sheet, itemName, ++rowNum, 0, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_i, bigDecimalRoundFormat(budget), 1, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_i, bigDecimalFormat(auditCost), 2, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_i, bigDecimalFormat(auditingCost), 3, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_i, bigDecimalFormat(auditWorkingHours), 4, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_i, bigDecimalFormat(auditingWorkingHours), 5, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_i, bigDecimalFormat(totalCost), 6, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_i, bigDecimalFormat(totalWorkingHours), 7, cellRightStyleWithBorder);
            }
        }

        ++rowNum;
        ExportExcelUtil.createCell(sheet, "外部工时填报数据参考：", ++rowNum, 0, cellRightStyleWithBorder);
        HSSFRow row_10_1 = ExportExcelUtil.createCell(sheet, "属性", ++rowNum, 0, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_10_1, "已审核工时参考金额", 1, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_10_1, "已审核工时(天)", 2, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_10_1, "待审核工时参考金额", 3, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_10_1, "待审核工时(天)", 4, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_10_1, "参考金额合计 ", 5, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_10_1, "工时合计(天)", 6, cellRightStyleWithBorder);

        if (ListUtils.isNotEmpty(row_10_1_List)) {
            for (int i = 0; i < row_10_1_List.size(); i++) {
                ProjectCostHumanItemRecordDTO projectCostHumanItemRecord = row_10_1_List.get(i);
                String itemName = projectCostHumanItemRecord.getItemName();//属性
                BigDecimal auditCost = projectCostHumanItemRecord.getAuditCost();//已审核工时参考金额
                BigDecimal auditWorkingHours = projectCostHumanItemRecord.getAuditWorkingHours();//已审核工时(天)
                BigDecimal auditingCost = projectCostHumanItemRecord.getAuditingCost();//待审核工时参考金额
                BigDecimal auditingWorkingHours = projectCostHumanItemRecord.getAuditingWorkingHours();//待审核工时(天)
                BigDecimal totalCost = projectCostHumanItemRecord.getTotalCost();//参考金额合计
                BigDecimal totalWorkingHours = projectCostHumanItemRecord.getTotalWorkingHours();//工时合计(天)

                HSSFRow row_i = ExportExcelUtil.createCell(sheet, itemName, ++rowNum, 0, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_i, bigDecimalFormat(auditCost), 1, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_i, bigDecimalFormat(auditWorkingHours), 2, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_i, bigDecimalFormat(auditingCost), 3, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_i, bigDecimalFormat(auditingWorkingHours), 4, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_i, bigDecimalFormat(totalCost), 5, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_i, bigDecimalFormat(totalWorkingHours), 6, cellRightStyleWithBorder);
            }
        }

        ++rowNum;
        HSSFRow row11 = ExportExcelUtil.createCell(sheet, "费用成本", ++rowNum, 0, cellLeftStyle);

        HSSFRow row_12 = ExportExcelUtil.createCell(sheet, "费用总预算", ++rowNum, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row_12, "已发生成本", 2, cellLeftStyle);
        ExportExcelUtil.createCell(row_12, "剩余预算", 4, cellLeftStyle);
        ExportExcelUtil.createCell(row_12, "已发生成本比例", 6, cellLeftStyle);

        HSSFRow row_13 = ExportExcelUtil.createCell(sheet, "类型", ++rowNum, 0, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_13, "预算", 1, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_13, "已处理成本", 2, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_13, "待处理成本", 3, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_13, "EA可用金额", 4, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_13, "成本合计", 5, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_13, "剩余预算 ", 6, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_13, "已发生成本比例", 7, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_13, "年度预提汇总", 8, cellRightStyleWithBorder);
        ExportExcelUtil.createCell(row_13, "月度预提汇总", 9, cellRightStyleWithBorder);

        ProjectCostFeeSummaryRecord projectCostFeeSummaryRecord = data.getProjectCostFeeSummaryRecord();
        List<ProjectCostFeeItemRecordDto> projectCostFeeItemRecords = data.getProjectCostFeeItemRecords();
        if (projectCostFeeSummaryRecord != null) {
            BigDecimal incurredRatio = projectCostFeeSummaryRecord.getIncurredRatio();
            BigDecimal cost = projectCostFeeSummaryRecord.getCost();
            Date createAt = projectCostFeeSummaryRecord.getCreateAt();
            BigDecimal budget = projectCostFeeSummaryRecord.getBudget();
            BigDecimal incurredCost = projectCostFeeSummaryRecord.getIncurredCost();
            BigDecimal remainderBudget = projectCostFeeSummaryRecord.getRemainderBudget();

            ExportExcelUtil.createCell(row_12, bigDecimalFormat(budget), 1, cellLeftStyle);
            ExportExcelUtil.createCell(row_12, bigDecimalFormat(cost), 3, cellLeftStyle);
            ExportExcelUtil.createCell(row_12, bigDecimalFormat(remainderBudget), 5, cellLeftStyle);
            ExportExcelUtil.createCell(row_12, bigDecimalRoundRatioFormat(incurredRatio), 7, cellLeftStyle);
        }

        if (ListUtils.isNotEmpty(projectCostFeeItemRecords)) {
            for (int i = 0; i < projectCostFeeItemRecords.size(); i++) {
                ProjectCostFeeItemRecordDto projectCostFeeItemRecord = projectCostFeeItemRecords.get(i);
                String feeItemName = projectCostFeeItemRecord.getFeeItemName();
                BigDecimal budget = projectCostFeeItemRecord.getBudget();
                BigDecimal incurredCost = projectCostFeeItemRecord.getIncurredCost();
                BigDecimal pendingCost = projectCostFeeItemRecord.getPendingCost();
                BigDecimal eaAvailableAmount = projectCostFeeItemRecord.getEaAvailableAmount();
                BigDecimal totalCost = projectCostFeeItemRecord.getTotalCost();
                BigDecimal remainderBudget = projectCostFeeItemRecord.getRemainderBudget();
                BigDecimal incurredRatio = projectCostFeeItemRecord.getIncurredRatio();

                BigDecimal annualAccrual = projectCostFeeItemRecord.getAnnual_accrual();
                BigDecimal monthlyAccrual = projectCostFeeItemRecord.getMonthly_accrual();

                HSSFRow row_i = ExportExcelUtil.createCell(sheet, feeItemName, ++rowNum, 0, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_i, bigDecimalFormat(budget), 1, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_i, bigDecimalFormat(incurredCost), 2, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_i, bigDecimalFormat(pendingCost), 3, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_i, bigDecimalFormat(eaAvailableAmount), 4, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_i, bigDecimalFormat(totalCost), 5, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_i, bigDecimalFormat(remainderBudget), 6, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_i, bigDecimalRoundRatioFormat(incurredRatio), 7, cellRightStyleWithBorder);

                ExportExcelUtil.createCell(row_i, bigDecimalFormat(annualAccrual), 8, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_i, bigDecimalFormat(monthlyAccrual), 9, cellRightStyleWithBorder);
            }
        }

        /** 从row34开始，实际行数均+3，因为修改命名容易出错，在此作注释提示 **/
        ++rowNum;
        if (null != data.getWarrantyProjectCostSummaryRecord() && null != data.getWarrantyProjectCostSummaryRecord().getProjectCode()) {
            //关联质保项目信息
            HSSFRow row34 = ExportExcelUtil.createCell(sheet, "关联质保项目成本信息：", ++rowNum, 0, cellLeftStyle);

            HSSFRow row35 = ExportExcelUtil.createCell(sheet, "项目编号", ++rowNum, 0, cellLeftStyle);
            ExportExcelUtil.createCell(row35, "项目名称", 2, cellLeftStyle);
            ExportExcelUtil.createCell(row35, "更新时间", 4, cellLeftStyle);

            ++rowNum;

            HSSFRow row37 = ExportExcelUtil.createCell(sheet, "已确认收入总额", ++rowNum, 0, cellLeftStyle);
            ExportExcelUtil.createCell(row37, "已确认成本总额", 2, cellLeftStyle);
            ExportExcelUtil.createCell(row37, "毛利率", 4, cellLeftStyle);

            HSSFRow row38 = ExportExcelUtil.createCell(sheet, "项目总预算", ++rowNum, 0, cellLeftStyle);
            ExportExcelUtil.createCell(row38, "已发生成本", 2, cellLeftStyle);
            ExportExcelUtil.createCell(row38, "剩余预算", 4, cellLeftStyle);

            ProjectCostSummaryRecord warrantyProjectCostSummaryRecord = data.getWarrantyProjectCostSummaryRecord();
            BigDecimal confirmedIncomeTotalAmount = warrantyProjectCostSummaryRecord.getConfirmedIncomeTotalAmount();//已确认收入总额
            BigDecimal confirmedCostTotalAmount = warrantyProjectCostSummaryRecord.getConfirmedCostTotalAmount();//已确认成本总额
            BigDecimal confirmedGrossProfitRatio = warrantyProjectCostSummaryRecord.getConfirmedGrossProfitRatio();//已确认毛利率
            BigDecimal projectBudget = warrantyProjectCostSummaryRecord.getProjectBudget();//项目总预算
            BigDecimal incurredCost = warrantyProjectCostSummaryRecord.getIncurredCost();//已发生成本
            BigDecimal remainderBudget = warrantyProjectCostSummaryRecord.getRemainderBudget();//剩余预算

            ExportExcelUtil.createCell(row37, confirmedIncomeTotalAmount == null ? "" : confirmedIncomeTotalAmount.toString(), 1, cellLeftStyle);
            ExportExcelUtil.createCell(row37, confirmedCostTotalAmount == null ? "" : confirmedCostTotalAmount.toString(), 3, cellLeftStyle);
            ExportExcelUtil.createCell(row37, bigDecimalRoundRatioFormat(confirmedGrossProfitRatio), 5, cellLeftStyle);
            ExportExcelUtil.createCell(row38, projectBudget == null ? "" : projectBudget.toString(), 1, cellLeftStyle);
            ExportExcelUtil.createCell(row38, incurredCost == null ? "" : incurredCost.toString(), 3, cellLeftStyle);
            ExportExcelUtil.createCell(row38, remainderBudget == null ? "" : remainderBudget.toString(), 5, cellLeftStyle);

            ++rowNum;
            HSSFRow row40 = ExportExcelUtil.createCell(sheet, "物料成本", ++rowNum, 0, cellLeftStyle);

            HSSFRow row41 = ExportExcelUtil.createCell(sheet, "物料总预算", ++rowNum, 0, cellLeftStyle);
            ExportExcelUtil.createCell(row41, "已发生成本", 2, cellLeftStyle);
            ExportExcelUtil.createCell(row41, "剩余预算", 4, cellLeftStyle);
            ExportExcelUtil.createCell(row41, "已发生成本比例", 6, cellLeftStyle);

            HSSFRow row42 = ExportExcelUtil.createCell(sheet, "属性", ++rowNum, 0, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row42, "已处理成本", 1, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row42, "待处理成本", 2, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row42, "成本合计", 3, cellRightStyleWithBorder);

            ProjectCostMaterialcostSummaryRecord warrantyMaterialcostSummaryRecord = data.getWarrantyMaterialcostSummaryRecord();
            List<ProjectCostMaterialcostSummaryDetailRecordDto> warrantyProjectCostMaterialcostSummaryDetailRecords = data.getWarrantyProjectCostMaterialcostSummaryDetailRecords();
            List<ProjectCostRevenueOrderRecord> warrantyProjectCostRevenueOrderRecords = data.getWarrantyProjectCostRevenueOrderRecords();
            if (warrantyMaterialcostSummaryRecord != null) {
                BigDecimal budgetPriceTotal = warrantyMaterialcostSummaryRecord.getBudgetPriceTotal();//物料总预算
                BigDecimal handledCost = warrantyMaterialcostSummaryRecord.getHandledCost();//已发生成本
                BigDecimal restBudgetPrice = warrantyMaterialcostSummaryRecord.getRestBudgetPrice();//剩余预算
                BigDecimal handledCostRatio = warrantyMaterialcostSummaryRecord.getHandledCostRatio();//已发生成本比例
                Date createAt = warrantyMaterialcostSummaryRecord.getCreateAt();

                ExportExcelUtil.createCell(row35, warrantyMaterialcostSummaryRecord.getProjectCode(), 1, cellLeftStyle);
                ExportExcelUtil.createCell(row35, warrantyMaterialcostSummaryRecord.getProjectName(), 3, cellLeftStyle);
                ExportExcelUtil.createCell(row35, DateUtil.format(createAt, DateUtil.TIMESTAMP_PATTERN), 5, cellLeftStyle);

                ExportExcelUtil.createCell(row41, bigDecimalFormat(budgetPriceTotal), 1, cellLeftStyle);
                ExportExcelUtil.createCell(row41, bigDecimalFormat(handledCost), 3, cellLeftStyle);
                ExportExcelUtil.createCell(row41, bigDecimalFormat(restBudgetPrice), 5, cellLeftStyle);
                ExportExcelUtil.createCell(row41, bigDecimalRoundRatioFormat(handledCostRatio), 7, cellLeftStyle);
            }

            int rowNumY = rowNum;

            if (ListUtils.isNotEmpty(warrantyProjectCostMaterialcostSummaryDetailRecords)) {
                for (int i = 0; i < warrantyProjectCostMaterialcostSummaryDetailRecords.size(); i++) {
                    ProjectCostMaterialcostSummaryDetailRecord materialcostSummaryDetailRecord = warrantyProjectCostMaterialcostSummaryDetailRecords.get(i);
                    String attributeType = materialcostSummaryDetailRecord.getAttributeType();//属性
                    BigDecimal dealCost = materialcostSummaryDetailRecord.getDealCost();//已处理成本
                    BigDecimal todoCost = materialcostSummaryDetailRecord.getTodoCost();//待处理成本
                    BigDecimal totalCost = materialcostSummaryDetailRecord.getTotalCost();//成本合计
                    if (ListUtils.isEmpty(warrantyProjectCostRevenueOrderRecords) && attributeType.equals("工单成本")) {
                        continue;
                    }
                    HSSFRow row_i = ExportExcelUtil.createCell(sheet, attributeType, ++rowNumY, 0, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, bigDecimalFormat(dealCost), 1, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, bigDecimalFormat(todoCost), 2, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_i, bigDecimalFormat(totalCost), 3, cellRightStyleWithBorder);
                }
            }

            ++rowNumY;
            HSSFRow row43 = ExportExcelUtil.createCell(sheet, "人力成本", ++rowNumY, 0, cellLeftStyle);

            HSSFRow row44 = ExportExcelUtil.createCell(sheet, "人力总预算", ++rowNumY, 0, cellLeftStyle);
            ExportExcelUtil.createCell(row44, "已发生成本", 2, cellLeftStyle);
            ExportExcelUtil.createCell(row44, "剩余预算", 4, cellLeftStyle);
            ExportExcelUtil.createCell(row44, "已发生成本比例", 6, cellLeftStyle);

            HSSFRow row45 = ExportExcelUtil.createCell(sheet, "属性", ++rowNumY, 0, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row45, "已审核成本", 1, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row45, "已审核工时(天)", 2, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row45, "待审核成本", 3, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row45, "待审核工时(天)", 4, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row45, "成本合计 ", 5, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row45, "工时合计(天)", 6, cellRightStyleWithBorder);

            ProjectCostHumanSummaryRecord warrantyProjectCostHumanSummaryRecord = data.getWarrantyProjectCostHumanSummaryRecord();
            List<ProjectCostHumanItemRecord> warrantyProjectCostHumanItemRecords = data.getWarrantyProjectCostHumanItemRecords();
            if (warrantyProjectCostHumanSummaryRecord != null) {
                BigDecimal incurredRatio = warrantyProjectCostHumanSummaryRecord.getIncurredRatio();
                BigDecimal cost = warrantyProjectCostHumanSummaryRecord.getCost();
                Date createAt = warrantyProjectCostHumanSummaryRecord.getCreateAt();
                BigDecimal budget = warrantyProjectCostHumanSummaryRecord.getBudget();
                BigDecimal warrantyRemainderBudget = warrantyProjectCostHumanSummaryRecord.getRemainderBudget();

                ExportExcelUtil.createCell(row44, bigDecimalFormat(budget), 1, cellLeftStyle);
                ExportExcelUtil.createCell(row44, bigDecimalFormat(cost), 3, cellLeftStyle);
                ExportExcelUtil.createCell(row44, bigDecimalFormat(warrantyRemainderBudget), 5, cellLeftStyle);
                ExportExcelUtil.createCell(row44, bigDecimalRoundRatioFormat(incurredRatio), 7, cellLeftStyle);

                if (ListUtils.isNotEmpty(warrantyProjectCostHumanItemRecords)) {
                    for (int i = 0; i < warrantyProjectCostHumanItemRecords.size(); i++) {
                        ProjectCostHumanItemRecord projectCostHumanItemRecord = warrantyProjectCostHumanItemRecords.get(i);
                        String itemName = projectCostHumanItemRecord.getItemName();//属性
                        BigDecimal auditCost = projectCostHumanItemRecord.getAuditCost();//已审核成本
                        BigDecimal auditWorkingHours = projectCostHumanItemRecord.getAuditWorkingHours();//已审核工时(天)
                        BigDecimal auditingCost = projectCostHumanItemRecord.getAuditingCost();//待审核成本
                        BigDecimal auditingWorkingHours = projectCostHumanItemRecord.getAuditingWorkingHours();//待审核工时(天)
                        BigDecimal totalCost = projectCostHumanItemRecord.getTotalCost();//成本合计
                        BigDecimal totalWorkingHours = projectCostHumanItemRecord.getTotalWorkingHours();//工时合计(天)

                        HSSFRow row46 = ExportExcelUtil.createCell(sheet, itemName, ++rowNumY, 0, cellRightStyleWithBorder);
                        ExportExcelUtil.createCell(row46, bigDecimalFormat(auditCost), 1, cellRightStyleWithBorder);
                        ExportExcelUtil.createCell(row46, bigDecimalFormat(auditWorkingHours), 2, cellRightStyleWithBorder);
                        ExportExcelUtil.createCell(row46, bigDecimalFormat(auditingCost), 3, cellRightStyleWithBorder);
                        ExportExcelUtil.createCell(row46, bigDecimalFormat(auditingWorkingHours), 4, cellRightStyleWithBorder);
                        ExportExcelUtil.createCell(row46, bigDecimalFormat(totalCost), 5, cellRightStyleWithBorder);
                        ExportExcelUtil.createCell(row46, bigDecimalFormat(totalWorkingHours), 6, cellRightStyleWithBorder);
                    }
                }
            }

            ++rowNumY;
            HSSFRow row47 = ExportExcelUtil.createCell(sheet, "费用成本", ++rowNumY, 0, cellLeftStyle);

            HSSFRow row48 = ExportExcelUtil.createCell(sheet, "费用总预算", ++rowNumY, 0, cellLeftStyle);
            ExportExcelUtil.createCell(row48, "已发生成本", 2, cellLeftStyle);
            ExportExcelUtil.createCell(row48, "剩余预算", 4, cellLeftStyle);
            ExportExcelUtil.createCell(row48, "已发生成本比例", 6, cellLeftStyle);

            HSSFRow row49 = ExportExcelUtil.createCell(sheet, "类型", ++rowNumY, 0, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row49, "预算", 1, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row49, "已处理成本", 2, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row49, "待处理成本", 3, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row49, "成本合计", 4, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row49, "剩余预算 ", 5, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row49, "已发生成本比例", 6, cellRightStyleWithBorder);

            ProjectCostFeeSummaryRecord warrantyProjectCostFeeSummaryRecord = data.getWarrantyProjectCostFeeSummaryRecord();
            List<ProjectCostFeeItemRecord> warrantyProjectCostFeeItemRecords = data.getWarrantyProjectCostFeeItemRecords();
            if (warrantyProjectCostFeeSummaryRecord != null) {
                BigDecimal incurredRatio = warrantyProjectCostFeeSummaryRecord.getIncurredRatio();
                BigDecimal cost = warrantyProjectCostFeeSummaryRecord.getCost();
                Date createAt = projectCostFeeSummaryRecord.getCreateAt();
                BigDecimal budget = warrantyProjectCostFeeSummaryRecord.getBudget();
                BigDecimal warrantyIncurredCost = warrantyProjectCostFeeSummaryRecord.getIncurredCost();
                BigDecimal warrantyRemainderBudget = warrantyProjectCostFeeSummaryRecord.getRemainderBudget();

                ExportExcelUtil.createCell(row48, bigDecimalFormat(budget), 1, cellLeftStyle);
                ExportExcelUtil.createCell(row48, bigDecimalFormat(warrantyIncurredCost), 3, cellLeftStyle);
                ExportExcelUtil.createCell(row48, bigDecimalFormat(warrantyRemainderBudget), 5, cellLeftStyle);
                ExportExcelUtil.createCell(row48, bigDecimalRoundRatioFormat(incurredRatio), 7, cellLeftStyle);
            }

            if (ListUtils.isNotEmpty(warrantyProjectCostFeeItemRecords)) {
                for (int i = 0; i < warrantyProjectCostFeeItemRecords.size(); i++) {
                    ProjectCostFeeItemRecord projectCostFeeItemRecord = warrantyProjectCostFeeItemRecords.get(i);
                    String feeItemName = projectCostFeeItemRecord.getFeeItemName();
                    BigDecimal budget = projectCostFeeItemRecord.getBudget();
                    BigDecimal warrantyIncurredCost = projectCostFeeItemRecord.getIncurredCost();
                    BigDecimal pendingCost = projectCostFeeItemRecord.getPendingCost();
                    BigDecimal totalCost = projectCostFeeItemRecord.getTotalCost();
                    BigDecimal warrantyRemainderBudget = projectCostFeeItemRecord.getRemainderBudget();
                    BigDecimal incurredRatio = projectCostFeeItemRecord.getIncurredRatio();

                    HSSFRow row50 = ExportExcelUtil.createCell(sheet, feeItemName, ++rowNumY, 0, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row50, bigDecimalFormat(budget), 1, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row50, bigDecimalFormat(warrantyIncurredCost), 2, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row50, bigDecimalFormat(pendingCost), 3, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row50, bigDecimalFormat(totalCost), 4, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row50, bigDecimalFormat(warrantyRemainderBudget), 5, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row50, bigDecimalRoundRatioFormat(incurredRatio), 6, cellRightStyleWithBorder);
                }
            }
        }

    }

    @ApiOperation(value = "项目汇总导出", response = ResponseMap.class)
    @GetMapping("/export")
    public void exportAll(HttpServletResponse response, @RequestParam Long projectId) {
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/statistics/project/cost/view/" + projectId, null);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<ProjectCostDetailVO> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<ProjectCostDetailVO>>() {
        });
        ProjectCostDetailVO data = dataResponse.getData();
        //项目编号
        String projectCode = "";

        //导出操作
        StringBuffer fileName = new StringBuffer();

        // 创建新的Excel 工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();

        // 设置字体
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
//        font.setBoldweight(XSSFFont.BOLDWEIGHT_BOLD);
        font.setFontHeightInPoints((short) 10);

        // 设置样式
        HSSFCellStyle cellLeftStyle = workbook.createCellStyle();
        cellLeftStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cellLeftStyle.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        cellLeftStyle.setFont(font);

        // 设置背景色
        HSSFCellStyle foreGroundStyle = workbook.createCellStyle();
        foreGroundStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        foreGroundStyle.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        foreGroundStyle.setFont(font);
        foreGroundStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        foreGroundStyle.setFillForegroundColor(HSSFColor.PALE_BLUE.index);//背景白色
        foreGroundStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        foreGroundStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
        foreGroundStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 上下居中

        HSSFCellStyle cellRightStyleWithBorder = workbook.createCellStyle();
        cellRightStyleWithBorder.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cellRightStyleWithBorder.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        cellRightStyleWithBorder.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
        cellRightStyleWithBorder.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
        cellRightStyleWithBorder.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
        cellRightStyleWithBorder.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框
        cellRightStyleWithBorder.setFont(font);

        HSSFCellStyle cs = workbook.createCellStyle();
        HSSFFont f = workbook.createFont();
        f.setFontName("宋体");
//        f.setBoldweight(XSSFFont.BOLDWEIGHT_BOLD);
        f.setFontHeightInPoints((short) 10);
        f.setFontHeight((short) 100);
        cs.setFont(f);
        cs.setAlignment(CellStyle.ALIGN_CENTER);
        cs.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cs.setWrapText(true);

        // 项目成本汇总
        HSSFSheet sheet = workbook.createSheet("项目成本汇总");
        this.createSummarySheet(sheet, data, cellLeftStyle, cellRightStyleWithBorder, foreGroundStyle);

        // 项目成本分项汇总
        HSSFSheet sheet2 = workbook.createSheet("项目成本分项汇总");
        this.setDataToCostCollection(sheet2, data, cellLeftStyle, cellRightStyleWithBorder);

        List<PurchaseOrderRecordExcelVO> purchaseOrderRecordExcelVOList = new ArrayList<>();
        List<StorageRecordExcelVO> storageRecordExcelVOList = new ArrayList<>();
        List<GetreturnMaterialRecordExcelVO> getreturnMaterialRecordExcelVOList = new ArrayList<>();
        List<OutsourcePurchaseRecordExcelVO> outsourcePurchaseRecordExcelVOList = new ArrayList<>();
        List<DifferenceRecordExcelVO> differenceRecordExcelVOList = new ArrayList<>();
        List<ProjectCostRevenueOrderRecordExcelVO> revenueOrderRecordExcelVOList = new ArrayList<>();

        ProjectCostSummaryRecord projectCostSummaryRecord = data.getProjectCostSummaryRecord();
        if (null != projectCostSummaryRecord) {
            projectCode = projectCostSummaryRecord.getProjectCode();
        }
        List<ProjectCostPurchaseOrderRecord> purchaseOrderRecordList = new ArrayList<>();
        List<FormalMaterialGet> formalMaterialGetList = data.getFormalMaterialGetList();
        List<ProjectCostStorageRecord> storageRecordList = new ArrayList<>();
        List<ProjectCostGetreturnMaterialRecord> getreturnMaterialRecordList = new ArrayList<>();
        List<ProjectCostOutsourcePurchaseRecord> outsourcePurchaseRecordList = new ArrayList<>();
        List<ProjectCostDifferenceRecord> differenceRecordList = new ArrayList<>();
        List<ProjectCostRevenueOrderRecord> revenueOrderRecordList = new ArrayList<>();
        //内部人力成本明细
        List<ProjectCostHumanDetailRecord> humanDetailRecordInsideList = new ArrayList<>();
        //外部人力工时明细
        List<ProjectCostHumanDetailRecord> humanDetailRecordExternalList = new ArrayList<>();
        List<ProjectCostFeeDetailRecord> projectCostFeeDetailRecords = new ArrayList<>();
        List<ProjectCostFeeDetailRecord> humanFeeDetailRecords = new ArrayList<>();
        List<ProjectCostEaDetailRecord> projectCostEaDetailRecords = new ArrayList<>();

        List<ProjectCostPurchaseOrderRecord> originalPurchaseOrderRecordList = data.getPurchaseOrderRecordList();
        List<ProjectCostStorageRecord> originalstorageRecordList = data.getStorageRecordList();
        List<ProjectCostGetreturnMaterialRecord> originalGetreturnMaterialRecordList = data.getGetreturnMaterialRecordList();
        List<ProjectCostOutsourcePurchaseRecord> originalOutsourcePurchaseRecordList = data.getOutsourcePurchaseRecordList();
        List<ProjectCostDifferenceRecord> originalDifferenceRecordList = data.getDifferenceRecordList();
        List<ProjectCostRevenueOrderRecord> originalRevenueOrderRecordList = data.getProjectCostRevenueOrderRecords();
        List<ProjectCostHumanDetailRecord> originalProjectCostHumanDetailRecords = data.getProjectCostHumanDetailRecords();
        List<ProjectCostFeeDetailRecord> originalProjectCostFeeDetailRecords = data.getProjectCostFeeDetailRecords();
        List<ProjectCostFeeDetailRecord> originalHumanFeeDetailRecords = data.getHumanFeeDetailRecords();
        List<ProjectCostEaDetailRecord> originalProjectCostEaDetailRecords = data.getProjectCostEaDetailRecords();

        //质保项目明细项
        List<ProjectCostPurchaseOrderRecord> warrantyPurchaseOrderRecordList = data.getWarrantyPurchaseOrderRecordList();
        List<ProjectCostStorageRecord> warrantyStorageRecordList = data.getWarrantyStorageRecordList();
        List<ProjectCostGetreturnMaterialRecord> warrantyGetreturnMaterialRecordList = data.getWarrantyGetreturnMaterialRecordList();
        List<ProjectCostOutsourcePurchaseRecord> warrantyOutsourcePurchaseRecordList = data.getWarrantyOutsourcePurchaseRecordList();
        List<ProjectCostDifferenceRecord> warrantyDifferenceRecordList = data.getWarrantyDifferenceRecordList();
        List<ProjectCostRevenueOrderRecord> warrantyRevenueOrderRecordList = data.getWarrantyProjectCostRevenueOrderRecords();
        List<ProjectCostHumanDetailRecord> warrantyProjectCostHumanDetailRecords = data.getWarrantyProjectCostHumanDetailRecords();
        List<ProjectCostFeeDetailRecord> warrantyProjectCostFeeDetailRecords = data.getWarrantyProjectCostFeeDetailRecords();
        List<ProjectCostEaDetailRecord> warrantyProjectCostEaDetailRecords = data.getWarrantyProjectCostEaDetailRecords();

        //资产
        List<ProjectCostAssetDetailRecord> projectCostAssetDetailRecords = data.getProjectCostAssetDetailRecords();

        purchaseOrderRecordList.addAll(originalPurchaseOrderRecordList);

        if (ListUtils.isNotEmpty(warrantyPurchaseOrderRecordList)) {
            purchaseOrderRecordList.addAll(warrantyPurchaseOrderRecordList);
        }

        storageRecordList.addAll(originalstorageRecordList);

        if (ListUtils.isNotEmpty(warrantyStorageRecordList)) {
            storageRecordList.addAll(warrantyStorageRecordList);
        }

        getreturnMaterialRecordList.addAll(originalGetreturnMaterialRecordList);

        if (ListUtils.isNotEmpty(warrantyGetreturnMaterialRecordList)) {
            getreturnMaterialRecordList.addAll(warrantyGetreturnMaterialRecordList);
        }

        outsourcePurchaseRecordList.addAll(originalOutsourcePurchaseRecordList);

        if (ListUtils.isNotEmpty(warrantyOutsourcePurchaseRecordList)) {
            outsourcePurchaseRecordList.addAll(warrantyOutsourcePurchaseRecordList);
        }

        differenceRecordList.addAll(originalDifferenceRecordList);

        if (ListUtils.isNotEmpty(warrantyDifferenceRecordList)) {
            differenceRecordList.addAll(warrantyDifferenceRecordList);
        }

        revenueOrderRecordList.addAll(originalRevenueOrderRecordList);

        if (ListUtils.isNotEmpty(warrantyRevenueOrderRecordList)) {
            revenueOrderRecordList.addAll(warrantyRevenueOrderRecordList);
        }

        if (ListUtils.isNotEmpty(originalProjectCostHumanDetailRecords)) {
            for (ProjectCostHumanDetailRecord po : originalProjectCostHumanDetailRecords) {
                //内部人力成本明细
                if (Objects.equals("1", po.getUserType())) {
                    humanDetailRecordInsideList.add(po);
                } else if (Objects.equals("2", po.getUserType())) {
                    //外部人力工时明细
                    humanDetailRecordExternalList.add(po);
                }
            }
        }

        if (ListUtils.isNotEmpty(warrantyProjectCostHumanDetailRecords)) {
            humanDetailRecordInsideList.addAll(warrantyProjectCostHumanDetailRecords);
        }

        projectCostFeeDetailRecords.addAll(originalProjectCostFeeDetailRecords);
        humanFeeDetailRecords.addAll(originalHumanFeeDetailRecords);

        if (ListUtils.isNotEmpty(warrantyProjectCostFeeDetailRecords)) {
            projectCostFeeDetailRecords.addAll(warrantyProjectCostFeeDetailRecords);
        }

        projectCostEaDetailRecords.addAll(originalProjectCostEaDetailRecords);

        if (ListUtils.isNotEmpty(warrantyProjectCostEaDetailRecords)) {
            projectCostEaDetailRecords.addAll(warrantyProjectCostEaDetailRecords);
        }

        storageRecordList.addAll(originalstorageRecordList);

        if (ListUtils.isNotEmpty(warrantyStorageRecordList)) {
            storageRecordList.addAll(warrantyStorageRecordList);
        }

        List<FormalMaterialGetListExcelVO> formalMaterialGetExcelVOList = new ArrayList<>();

        if (ListUtils.isNotEmpty(formalMaterialGetList)) {
            formalMaterialGetExcelVOList = BeanConverter.convert(formalMaterialGetList, FormalMaterialGetListExcelVO.class);
            if (ListUtils.isNotEmpty(formalMaterialGetExcelVOList)) {
                for (int i = 0; i < formalMaterialGetExcelVOList.size(); i++) {
                    formalMaterialGetExcelVOList.get(i).setNum(i + 1);
                }
            }
            ExportExcelUtil.addSheet(workbook, formalMaterialGetExcelVOList, FormalMaterialGetListExcelVO.class, null, "ERP销售工单发料成本报表", true);
        }

        if (ListUtils.isNotEmpty(purchaseOrderRecordList)) {
            purchaseOrderRecordExcelVOList = BeanConverter.convert(purchaseOrderRecordList, PurchaseOrderRecordExcelVO.class);
            for (int i = 0; i < purchaseOrderRecordExcelVOList.size(); i++) {
                PurchaseOrderRecordExcelVO purchaseOrderRecordExcelVO = purchaseOrderRecordExcelVOList.get(i);
                purchaseOrderRecordExcelVO.setNum(i + 1);
            }
            ExportExcelUtil.addSheet(workbook, purchaseOrderRecordExcelVOList, PurchaseOrderRecordExcelVO.class, null, "采购下单成本明细", true);
        }

        if (ListUtils.isNotEmpty(storageRecordList)) {
            storageRecordExcelVOList = BeanConverter.convert(storageRecordList, StorageRecordExcelVO.class);
            for (int i = 0; i < storageRecordExcelVOList.size(); i++) {
                StorageRecordExcelVO storageRecordExcelVO = storageRecordExcelVOList.get(i);
                storageRecordExcelVO.setNum(i + 1);
            }

            ExportExcelUtil.addSheet(workbook, storageRecordExcelVOList, StorageRecordExcelVO.class, null, "项目库存成本明细", true);
        }

        if (ListUtils.isNotEmpty(getreturnMaterialRecordList)) {
            getreturnMaterialRecordExcelVOList = BeanConverter.convert(getreturnMaterialRecordList, GetreturnMaterialRecordExcelVO.class);
            for (int i = 0; i < getreturnMaterialRecordExcelVOList.size(); i++) {
                GetreturnMaterialRecordExcelVO getreturnMaterialRecordExcelVO = getreturnMaterialRecordExcelVOList.get(i);
                getreturnMaterialRecordExcelVO.setNum(i + 1);
            }

            ExportExcelUtil.addSheet(workbook, getreturnMaterialRecordExcelVOList, GetreturnMaterialRecordExcelVO.class, null, "项目领料成本明细", true);
        }

        if (ListUtils.isNotEmpty(outsourcePurchaseRecordList)) {
            outsourcePurchaseRecordExcelVOList = BeanConverter.convert(outsourcePurchaseRecordList, OutsourcePurchaseRecordExcelVO.class);
            for (int i = 0; i < outsourcePurchaseRecordExcelVOList.size(); i++) {
                OutsourcePurchaseRecordExcelVO outsourcePurchaseRecordExcelVO = outsourcePurchaseRecordExcelVOList.get(i);
                outsourcePurchaseRecordExcelVO.setNum(i + 1);
            }

            ExportExcelUtil.addSheet(workbook, outsourcePurchaseRecordExcelVOList, OutsourcePurchaseRecordExcelVO.class, null, "外包采购成本明细", true);
        }

        if (ListUtils.isNotEmpty(differenceRecordList)) {
            differenceRecordExcelVOList = BeanConverter.convert(differenceRecordList, DifferenceRecordExcelVO.class);
            for (int i = 0; i < differenceRecordExcelVOList.size(); i++) {
                DifferenceRecordExcelVO differenceRecordExcelVO = differenceRecordExcelVOList.get(i);
                differenceRecordExcelVO.setNum(i + 1);
            }

            ExportExcelUtil.addSheet(workbook, differenceRecordExcelVOList, DifferenceRecordExcelVO.class, null, "差异分摊成本明细", true);
        }
        //内部人力成本明细
        List<ProjectCostHumanDetailRecordInsideExcelVO> humanDetailRecordExcelVOS = new ArrayList<>();
        if (ListUtils.isNotEmpty(humanDetailRecordInsideList)) {
            humanDetailRecordExcelVOS = BeanConverter.convert(humanDetailRecordInsideList, ProjectCostHumanDetailRecordInsideExcelVO.class);
        }
        for (int i = 0; i < humanDetailRecordExcelVOS.size(); i++) {
            ProjectCostHumanDetailRecordInsideExcelVO humanDetailRecordExcelVO = humanDetailRecordExcelVOS.get(i);
            humanDetailRecordExcelVO.setNum(i + 1);
        }
        ExportExcelUtil.addSheet(workbook, humanDetailRecordExcelVOS, ProjectCostHumanDetailRecordInsideExcelVO.class, null, "内部人力成本明细", true);
        //外部人力工时明细
        List<ProjectCostHumanDetailRecordExternalExcelVO> humanDetailRecordExternalExcelVOS = new ArrayList<>();
        if (ListUtils.isNotEmpty(humanDetailRecordExternalList)) {
            humanDetailRecordExternalExcelVOS = BeanConverter.convert(humanDetailRecordExternalList, ProjectCostHumanDetailRecordExternalExcelVO.class);
        }
        for (int i = 0; i < humanDetailRecordExternalExcelVOS.size(); i++) {
            ProjectCostHumanDetailRecordExternalExcelVO humanDetailRecordExcelVO = humanDetailRecordExternalExcelVOS.get(i);
            humanDetailRecordExcelVO.setNum(i + 1);
        }
        ExportExcelUtil.addSheet(workbook, humanDetailRecordExternalExcelVOS, ProjectCostHumanDetailRecordExternalExcelVO.class, null, "外部人力工时明细", true);

        List<ProjectCostFeeDetailRecordExcelVO> humanFeeDetailRecordExcelVOS = new ArrayList<>();
        if (ListUtils.isNotEmpty(humanFeeDetailRecords)) {
            humanFeeDetailRecordExcelVOS = BeanConverter.convert(humanFeeDetailRecords, ProjectCostFeeDetailRecordExcelVO.class);
            int n = 1;
            for (ProjectCostFeeDetailRecordExcelVO projectCostFeeDetailRecordExcelVO : humanFeeDetailRecordExcelVOS) {
                projectCostFeeDetailRecordExcelVO.setNum(n++);
            }
            ExportExcelUtil.addSheet(workbook, humanFeeDetailRecordExcelVOS, ProjectCostFeeDetailRecordExcelVO.class, null, "外部人力成本明细", true);
        }

        List<ProjectCostFeeDetailRecordExcelVO> projectCostFeeDetailRecordExcelVOS = new ArrayList<>();
        if (ListUtils.isNotEmpty(projectCostFeeDetailRecords)) {
            projectCostFeeDetailRecordExcelVOS = BeanConverter.convert(projectCostFeeDetailRecords, ProjectCostFeeDetailRecordExcelVO.class);
            int n = 1;
            for (ProjectCostFeeDetailRecordExcelVO projectCostFeeDetailRecordExcelVO : projectCostFeeDetailRecordExcelVOS) {
                projectCostFeeDetailRecordExcelVO.setNum(n++);
            }
            ExportExcelUtil.addSheet(workbook, projectCostFeeDetailRecordExcelVOS, ProjectCostFeeDetailRecordExcelVO.class, null, "费用成本明细", true);
        }

        List<ProjectCostEaDetailRecordExcelVO> projectCostEaDetailRecordExcelVOS = new ArrayList<>();
        if (ListUtils.isNotEmpty(projectCostEaDetailRecords)) {
            projectCostEaDetailRecordExcelVOS = BeanConverter.convert(projectCostEaDetailRecords, ProjectCostEaDetailRecordExcelVO.class);
            int n = 1;
            for (ProjectCostEaDetailRecordExcelVO projectCostEaDetailRecordExcelVO : projectCostEaDetailRecordExcelVOS) {
                projectCostEaDetailRecordExcelVO.setNum(n++);
            }
            ExportExcelUtil.addSheet(workbook, projectCostEaDetailRecordExcelVOS, ProjectCostEaDetailRecordExcelVO.class, null, "EA可用金额明细", true);
        }

        if (ListUtils.isNotEmpty(revenueOrderRecordList)) {
            revenueOrderRecordExcelVOList = BeanConverter.convert(revenueOrderRecordList, ProjectCostRevenueOrderRecordExcelVO.class);
            int n = 1;
            for (ProjectCostRevenueOrderRecordExcelVO projectCostRevenueOrderRecordExcelVO : revenueOrderRecordExcelVOList) {
                projectCostRevenueOrderRecordExcelVO.setNum(n++);
            }
            ExportExcelUtil.addSheet(workbook, revenueOrderRecordExcelVOList, ProjectCostRevenueOrderRecordExcelVO.class, null, "工单成本明细", true);
        }

        List<ProjectCostAssetDetailRecordExcelVO> projectCostAssetDetailRecordExcelVOS = new ArrayList<>();
        if (ListUtils.isNotEmpty(projectCostAssetDetailRecords)) {
            projectCostAssetDetailRecordExcelVOS = BeanConverter.convert(projectCostAssetDetailRecords, ProjectCostAssetDetailRecordExcelVO.class);
            int n = 1;
            for (ProjectCostAssetDetailRecordExcelVO projectCostAssetDetailRecordExcelVO : projectCostAssetDetailRecordExcelVOS) {
                projectCostAssetDetailRecordExcelVO.setNum(n++);
            }
            ExportExcelUtil.addSheet(workbook, projectCostAssetDetailRecordExcelVOS, ProjectCostAssetDetailRecordExcelVO.class, null, "资产折旧成本明细", true);
        }

        fileName.append(projectCode + "项目成本_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    private void createSummarySheet(HSSFSheet sheet, ProjectCostDetailVO data,
                                    HSSFCellStyle cellLeftStyle,
                                    HSSFCellStyle cellRightStyleWithBorder,
                                    HSSFCellStyle foreGroundStyle) {

        sheet.setColumnWidth(0, 10000);
        sheet.setColumnWidth(1, 6000);
        sheet.setColumnWidth(2, 6000);
        sheet.setColumnWidth(3, 6000);
        sheet.setColumnWidth(4, 6000);
        sheet.setColumnWidth(5, 6000);
        sheet.setColumnWidth(6, 6000);
        sheet.setColumnWidth(7, 6000);
        sheet.setColumnWidth(8, 6000);
        sheet.setColumnWidth(9, 6000);
        sheet.setColumnWidth(10, 6000);
        sheet.setColumnWidth(11, 6000);
        sheet.setColumnWidth(12, 6000);
        sheet.setColumnWidth(13, 6000);
        sheet.setColumnWidth(14, 6000);
        sheet.setColumnWidth(15, 6000);
        sheet.setColumnWidth(16, 6000);

        HSSFRow row0 = ExportExcelUtil.createCell(sheet, "项目号", 0, 0, foreGroundStyle);
        ExportExcelUtil.createCell(row0, "项目名称", 2, foreGroundStyle);
        ExportExcelUtil.createCell(row0, "更新时间", 4, foreGroundStyle);
        ExportExcelUtil.createCell(row0, "本位币币种", 6, foreGroundStyle);

        HSSFRow row1 = ExportExcelUtil.createCell(sheet, "合同币种", 1, 0, foreGroundStyle);
        ExportExcelUtil.createCell(row1, "项目不含税金额（原币）", 2, foreGroundStyle);
        ExportExcelUtil.createCell(row1, "累计已确认收入比例（原币）", 4, foreGroundStyle);
        ExportExcelUtil.createCell(row1, "汇兑损益（本位币）", 6, foreGroundStyle);

        HSSFRow row2 = ExportExcelUtil.createCell(sheet, "已确认收入总额（本位币）", 2, 0, foreGroundStyle);
        ExportExcelUtil.createCell(row2, "已确认成本总额（本位币）", 2, foreGroundStyle);
        ExportExcelUtil.createCell(row2, "已确认毛利率（本位币）", 4, foreGroundStyle);

        HSSFRow row3 = ExportExcelUtil.createCell(sheet, "目标毛利额（本位币）", 3, 0, foreGroundStyle);
        ExportExcelUtil.createCell(row3, "当前毛利额（本位币）", 2, foreGroundStyle);

        HSSFRow row4 = ExportExcelUtil.createCell(sheet, "目标毛利率（本位币）", 4, 0, foreGroundStyle);
        ExportExcelUtil.createCell(row4, "当前毛利率（本位币）", 2, foreGroundStyle);

        HSSFRow row_7 = ExportExcelUtil.createCell(sheet, "分类", 7, 0, foreGroundStyle);
        ExportExcelUtil.createCell(row_7, "赢单成本", 1, foreGroundStyle);
        ExportExcelUtil.createCell(row_7, "目标成本", 2, foreGroundStyle);
        ExportExcelUtil.createCell(row_7, "内部调整目标成本", 3, foreGroundStyle);
        ExportExcelUtil.createCell(row_7, "增补合同调整目标成本", 4, foreGroundStyle);
        ExportExcelUtil.createCell(row_7, "最新目标成本", 5, foreGroundStyle);
        ExportExcelUtil.createCell(row_7, "内部调整预算", 6, foreGroundStyle);
        ExportExcelUtil.createCell(row_7, "增补合同调整预算", 7, foreGroundStyle);
        ExportExcelUtil.createCell(row_7, "当前预算", 8, foreGroundStyle);
        ExportExcelUtil.createCell(row_7, "已处理成本", 9, foreGroundStyle);
        ExportExcelUtil.createCell(row_7, "待处理成本", 10, foreGroundStyle);
        ExportExcelUtil.createCell(row_7, "成本合计", 11, foreGroundStyle);
        ExportExcelUtil.createCell(row_7, "目标成本余额", 12, foreGroundStyle);
        ExportExcelUtil.createCell(row_7, "剩余预算", 13, foreGroundStyle);
        ExportExcelUtil.createCell(row_7, "后续预计成本偏差", 14, foreGroundStyle);
        ExportExcelUtil.createCell(row_7, "目标成本执行偏差率", 15, foreGroundStyle);
        ExportExcelUtil.createCell(row_7, "已发生成本比例", 16, foreGroundStyle);

        HSSFRow row_8 = ExportExcelUtil.createCell(sheet, "说明", 8, 0, foreGroundStyle);
        ExportExcelUtil.createCell(row_8, "商机报价单成本", 1, foreGroundStyle);
        ExportExcelUtil.createCell(row_8, "立项初始版本目标成本", 2, foreGroundStyle);
        ExportExcelUtil.createCell(row_8, "", 3, foreGroundStyle);
        ExportExcelUtil.createCell(row_8, "", 4, foreGroundStyle);
        ExportExcelUtil.createCell(row_8, "", 5, foreGroundStyle);
        ExportExcelUtil.createCell(row_8, "", 6, foreGroundStyle);
        ExportExcelUtil.createCell(row_8, "", 7, foreGroundStyle);
        ExportExcelUtil.createCell(row_8, "", 8, foreGroundStyle);
        ExportExcelUtil.createCell(row_8, "对应项目成本界面已发生成本", 9, foreGroundStyle);
        ExportExcelUtil.createCell(row_8, "成本界面待处理成本、PO未入库物料单价、已申请未入账费用等", 10, foreGroundStyle);
        ExportExcelUtil.createCell(row_8, "已处理+待处理成本", 11, foreGroundStyle);
        ExportExcelUtil.createCell(row_8, "目标成本-成本合计", 12, foreGroundStyle);
        ExportExcelUtil.createCell(row_8, "当前预算-成本合计", 13, foreGroundStyle);
        ExportExcelUtil.createCell(row_8, "目标成本余额-剩余预算", 14, foreGroundStyle);
        ExportExcelUtil.createCell(row_8, "目标成本余额/最新目标成本*100%", 15, foreGroundStyle);
        ExportExcelUtil.createCell(row_8, "", 16, foreGroundStyle);

        ProjectCostSummaryRecord projectCostSummaryRecord = data.getProjectCostSummaryRecord();
        if (projectCostSummaryRecord != null) {
            BigDecimal projectAmount = projectCostSummaryRecord.getProjectAmount();
            BigDecimal confirmedIncomeTotalAmount = projectCostSummaryRecord.getConfirmedIncomeTotalAmount();
            BigDecimal standardConfirmedIncomeTotalAmount = projectCostSummaryRecord.getStandardConfirmedIncomeTotalAmount();
            BigDecimal confirmedCostTotalAmount = projectCostSummaryRecord.getConfirmedCostTotalAmount();
            BigDecimal confirmedGrossProfitRatio = projectCostSummaryRecord.getConfirmedGrossProfitRatio();
            BigDecimal currentGrassProfitRatio = projectCostSummaryRecord.getCurrentGrassProfitRatio();
            BigDecimal currentGrassProfitAmount = projectCostSummaryRecord.getCurrentGrassProfitAmount();
            BigDecimal confirmedExchangeAmount = projectCostSummaryRecord.getConfirmedExchangeAmount();
            //累计已确认收入比例（原币）= 取结转单中“本期确认收入（原币）”的合计/项目不含税金额（原币）*100
            BigDecimal cumulativeConfirmedIncomeRatio = BigDecimalUtils.divide(BigDecimalUtils.multiply(confirmedIncomeTotalAmount, new BigDecimal(100)), projectAmount);

            BigDecimal targetCostGrassProfitAmount = projectCostSummaryRecord.getTargetCostGrassProfitAmount(); // 目标毛利额
            BigDecimal targetCostGrassProfitRatio = projectCostSummaryRecord.getTargetCostGrassProfitRatio(); // 目标毛利率

            Date createAt = projectCostSummaryRecord.getCreateAt();

            ExportExcelUtil.createCell(row0, projectCostSummaryRecord.getProjectCode(), 1, cellLeftStyle);
            ExportExcelUtil.createCell(row0, projectCostSummaryRecord.getProjectName(), 3, cellLeftStyle);
            ExportExcelUtil.createCell(row0, DateUtil.format(createAt, DateUtil.TIMESTAMP_PATTERN), 5, cellLeftStyle);
            ExportExcelUtil.createCell(row0, data.getLocalCurrency(), 7, cellLeftStyle);

            ExportExcelUtil.createCell(row1, data.getCurrency(), 1, cellLeftStyle);
            ExportExcelUtil.createCell(row1, bigDecimalFormat(projectAmount), 3, cellLeftStyle);
            ExportExcelUtil.createCell(row1, bigDecimalRoundRatioFormat(cumulativeConfirmedIncomeRatio), 5, cellLeftStyle);
            ExportExcelUtil.createCell(row1, bigDecimalRoundFormat(confirmedExchangeAmount), 7, cellLeftStyle);

            ExportExcelUtil.createCell(row2, bigDecimalFormat(standardConfirmedIncomeTotalAmount), 1, cellLeftStyle);
            ExportExcelUtil.createCell(row2, bigDecimalFormat(confirmedCostTotalAmount), 3, cellLeftStyle);
            ExportExcelUtil.createCell(row2, bigDecimalRoundRatioFormat(confirmedGrossProfitRatio), 5, cellLeftStyle);

            ExportExcelUtil.createCell(row3, bigDecimalFormat(targetCostGrassProfitAmount), 1, cellLeftStyle);
            ExportExcelUtil.createCell(row3, bigDecimalFormat(currentGrassProfitAmount), 3, cellLeftStyle);

            ExportExcelUtil.createCell(row4, bigDecimalRoundRatioFormat(targetCostGrassProfitRatio), 1, cellLeftStyle);
            ExportExcelUtil.createCell(row4, bigDecimalRoundRatioFormat(currentGrassProfitRatio), 3, cellLeftStyle);
        }

        List<ProjectCostSummaryItemRecord> projectCostSummaryItemRecords = data.getProjectCostSummaryItemRecords();
        int n = 8;
        int num = 0;
        if (ListUtils.isNotEmpty(projectCostSummaryItemRecords)) {
            for (ProjectCostSummaryItemRecord projectCostSummaryItemRecord : projectCostSummaryItemRecords) {

                String itemName = projectCostSummaryItemRecord.getItemName();

                BigDecimal winBudget = projectCostSummaryItemRecord.getWinBudget(); // 赢单成本
                BigDecimal initTargetCost = projectCostSummaryItemRecord.getInitTargetCost();// 初始目标成本
                BigDecimal innerAdjustTargetCost = projectCostSummaryItemRecord.getInnerAdjustTargetCost();// 内部调整目标成本
                BigDecimal outerAdjustTargetCost = projectCostSummaryItemRecord.getOuterAdjustTargetCost();// 增补合同调整目标成本
                BigDecimal currentTargetCost = projectCostSummaryItemRecord.getCurrentTargetCost(); // 最新目标成本
                BigDecimal innerAdjustTargetBudget = projectCostSummaryItemRecord.getInnerAdjustTargetBudget();// 内部调整预算
                BigDecimal outerAdjustTargetBudget = projectCostSummaryItemRecord.getOuterAdjustTargetBudget();// 增补合同调整预算
                BigDecimal currentBudget = projectCostSummaryItemRecord.getCurrentBudget(); //当前预算
                BigDecimal incurredCost = projectCostSummaryItemRecord.getIncurredCost(); // 已处理成本
                BigDecimal pendingCost = projectCostSummaryItemRecord.getPendingCost(); // 待处理成本
                BigDecimal totalCost = projectCostSummaryItemRecord.getTotalCost(); // 成本合计
                BigDecimal remainTargetCost = projectCostSummaryItemRecord.getRemainTargetCost();// 目标成本余额
                BigDecimal remainderBudget = projectCostSummaryItemRecord.getRemainderBudget(); // 剩余预算
                BigDecimal targetCostDeviation = projectCostSummaryItemRecord.getTargetCostDeviation();// 后续预计成本偏差
                BigDecimal targetCostDeviationRate = projectCostSummaryItemRecord.getTargetCostDeviationRate();// 目标成本执行偏差率
                BigDecimal incurredRatio = projectCostSummaryItemRecord.getIncurredRatio(); // 已发生成本比例

                if (num >= 1) {
                    num++;
                    if (num == 2) {
                        itemName = "其中：" + itemName;
                    } else {
                        int tabNum = (num - 2);
                        for (int i = 0; i < tabNum; i++) {
                            itemName = "  " + itemName;
                        }
                    }
                }

                if (Objects.equals(itemName, "费用成本（非差旅）小计")) {
                    num = 1;
                }
                if (itemName != null && itemName.contains("资产折旧成本")) { //特殊处理一下，不缩进
                    itemName = itemName.replace("  ", "");
                }

                HSSFRow row_n = ExportExcelUtil.createCell(sheet, itemName, ++n, 0, cellRightStyleWithBorder);

                ExportExcelUtil.createCell(row_n, bigDecimalFormat(winBudget), 1, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_n, bigDecimalFormat(initTargetCost), 2, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_n, bigDecimalFormat(innerAdjustTargetCost), 3, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_n, bigDecimalFormat(outerAdjustTargetCost), 4, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_n, bigDecimalFormat(currentTargetCost), 5, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_n, bigDecimalFormat(innerAdjustTargetBudget), 6, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_n, bigDecimalFormat(outerAdjustTargetBudget), 7, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_n, bigDecimalFormat(currentBudget), 8, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_n, bigDecimalFormat(incurredCost), 9, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_n, bigDecimalFormat(pendingCost), 10, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_n, bigDecimalFormat(totalCost), 11, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_n, bigDecimalFormat(remainTargetCost), 12, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_n, bigDecimalFormat(remainderBudget), 13, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_n, bigDecimalFormat(targetCostDeviation), 14, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_n, bigDecimalRoundRatioFormat(targetCostDeviationRate), 15, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_n, bigDecimalRoundRatioFormat(incurredRatio), 16, cellRightStyleWithBorder);
            }
        }

        if (projectCostSummaryRecord != null) {
            BigDecimal winGrassProfitAmount = projectCostSummaryRecord.getWinGrassProfitAmount();
            BigDecimal winGrassProfitRatio = projectCostSummaryRecord.getWinGrassProfitRatio();
            BigDecimal winTotalCost = projectCostSummaryRecord.getWinTotalCost();

            BigDecimal currentGrassProfitAmount = projectCostSummaryRecord.getCurrentGrassProfitAmount();
            BigDecimal currentGrassProfitRatio = projectCostSummaryRecord.getCurrentGrassProfitRatio();
            BigDecimal currentTotalCost = projectCostSummaryRecord.getCurrentTotalCost();

            HSSFRow row_n = ExportExcelUtil.createCell(sheet, "成本小计", ++n, 0, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row_n, bigDecimalFormat(winTotalCost), 1, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row_n, bigDecimalFormat(currentTotalCost), 2, cellRightStyleWithBorder);

            HSSFRow row_n2 = ExportExcelUtil.createCell(sheet, "毛利额", ++n, 0, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row_n2, bigDecimalFormat(winGrassProfitAmount), 1, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row_n2, bigDecimalFormat(currentGrassProfitAmount), 2, cellRightStyleWithBorder);

            HSSFRow row_n3 = ExportExcelUtil.createCell(sheet, "毛利率", ++n, 0, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row_n3, bigDecimalRoundRatioFormat(winGrassProfitRatio), 1, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row_n3, bigDecimalRoundRatioFormat(currentGrassProfitRatio), 2, cellRightStyleWithBorder);
        }

        int warrantyProjectRowNum = 20;

        if (null != data.getWarrantyProjectCostSummaryRecord() && null != data.getWarrantyProjectCostSummaryRecord().getProjectCode()) {
            //拼接质保项目信息
            HSSFRow row15 = ExportExcelUtil.createCell(sheet, "关联质保项目成本信息：", warrantyProjectRowNum, 0, cellLeftStyle);

            HSSFRow row17 = ExportExcelUtil.createCell(sheet, "项目号", warrantyProjectRowNum + 2, 0, cellLeftStyle);
            ExportExcelUtil.createCell(row17, "项目名称", 2, cellLeftStyle);
            ExportExcelUtil.createCell(row17, "更新时间", 4, cellLeftStyle);

            HSSFRow row19 = ExportExcelUtil.createCell(sheet, "项目金额（不含税）", warrantyProjectRowNum + 4, 0, cellLeftStyle);
            ExportExcelUtil.createCell(row19, "已确认收入总额", 2, cellLeftStyle);
            ExportExcelUtil.createCell(row19, "已确认成本总额", 4, cellLeftStyle);
            ExportExcelUtil.createCell(row19, "已确认毛利率", 6, cellLeftStyle);

            HSSFRow row_21 = ExportExcelUtil.createCell(sheet, "分类", warrantyProjectRowNum + 6, 0, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row_21, "赢单成本", 1, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row_21, "当前预算", 2, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row_21, "已发生成本", 3, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row_21, "待处理成本", 4, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row_21, "成本合计", 5, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row_21, "剩余预算", 6, cellRightStyleWithBorder);
            ExportExcelUtil.createCell(row_21, "已发生成本比例", 7, cellRightStyleWithBorder);

            ProjectCostSummaryRecord warrantyProjectCostSummaryRecord = data.getWarrantyProjectCostSummaryRecord();

            BigDecimal projectAmount = warrantyProjectCostSummaryRecord.getProjectAmount();
            BigDecimal confirmedIncomeTotalAmount = warrantyProjectCostSummaryRecord.getConfirmedIncomeTotalAmount();
            BigDecimal confirmedCostTotalAmount = warrantyProjectCostSummaryRecord.getConfirmedCostTotalAmount();
            BigDecimal confirmedGrossProfitRatio = warrantyProjectCostSummaryRecord.getConfirmedGrossProfitRatio();

            Date createAt = warrantyProjectCostSummaryRecord.getCreateAt();

            ExportExcelUtil.createCell(row17, warrantyProjectCostSummaryRecord.getProjectCode(), 1, cellLeftStyle);
            ExportExcelUtil.createCell(row17, warrantyProjectCostSummaryRecord.getProjectName(), 3, cellLeftStyle);
            ExportExcelUtil.createCell(row17, DateUtil.format(createAt, DateUtil.TIMESTAMP_PATTERN), 5, cellLeftStyle);

            ExportExcelUtil.createCell(row19, bigDecimalFormat(projectAmount), 1, cellLeftStyle);
            ExportExcelUtil.createCell(row19, bigDecimalFormat(confirmedIncomeTotalAmount), 3, cellLeftStyle);
            ExportExcelUtil.createCell(row19, bigDecimalFormat(confirmedCostTotalAmount), 5, cellLeftStyle);
            ExportExcelUtil.createCell(row19, bigDecimalRoundRatioFormat(confirmedGrossProfitRatio), 7, cellLeftStyle);

            List<ProjectCostSummaryItemRecord> warrantyProjectCostSummaryItemRecord = data.getWarrantyProjectCostSummaryItemRecord();
            int y = warrantyProjectRowNum + 6;
            int numy = 0;
            if (ListUtils.isNotEmpty(warrantyProjectCostSummaryItemRecord)) {
                for (ProjectCostSummaryItemRecord projectCostSummaryItemRecord : warrantyProjectCostSummaryItemRecord) {

                    String itemName = projectCostSummaryItemRecord.getItemName();
                    BigDecimal winBudget = projectCostSummaryItemRecord.getWinBudget();
                    BigDecimal currentBudget = projectCostSummaryItemRecord.getCurrentBudget();
                    BigDecimal incurredCost = projectCostSummaryItemRecord.getIncurredCost();
                    BigDecimal pendingCost = projectCostSummaryItemRecord.getPendingCost();
                    BigDecimal totalCost = projectCostSummaryItemRecord.getTotalCost();
                    BigDecimal remainderBudget = projectCostSummaryItemRecord.getRemainderBudget();
                    BigDecimal incurredRatio = projectCostSummaryItemRecord.getIncurredRatio();

                    if (numy >= 1) {
                        numy++;
                        if (numy == 2) {
                            itemName = "其中：" + itemName;
                        } else {
                            int tabNum = (numy - 2);
                            for (int i = 0; i < tabNum; i++) {
                                itemName = "  " + itemName;
                            }
                        }
                    }

                    if (Objects.equals(itemName, "费用成本（非差旅）小计")) {
                        numy = 1;
                    }

                    HSSFRow row_y = ExportExcelUtil.createCell(sheet, itemName, ++y, 0, cellRightStyleWithBorder);

                    ExportExcelUtil.createCell(row_y, bigDecimalFormat(winBudget), 1, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_y, bigDecimalFormat(currentBudget), 2, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_y, bigDecimalFormat(incurredCost), 3, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_y, bigDecimalFormat(pendingCost), 4, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_y, bigDecimalFormat(totalCost), 5, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_y, bigDecimalFormat(remainderBudget), 6, cellRightStyleWithBorder);
                    ExportExcelUtil.createCell(row_y, bigDecimalRoundRatioFormat(incurredRatio), 7, cellRightStyleWithBorder);
                }
            }

            if (warrantyProjectCostSummaryRecord != null) {
                BigDecimal winGrassProfitAmount = warrantyProjectCostSummaryRecord.getWinGrassProfitAmount();
                BigDecimal winGrassProfitRatio = warrantyProjectCostSummaryRecord.getWinGrassProfitRatio();
                BigDecimal winTotalCost = warrantyProjectCostSummaryRecord.getWinTotalCost();

                BigDecimal currentGrassProfitAmount = warrantyProjectCostSummaryRecord.getCurrentGrassProfitAmount();
                BigDecimal currentGrassProfitRatio = warrantyProjectCostSummaryRecord.getCurrentGrassProfitRatio();
                BigDecimal currentTotalCost = warrantyProjectCostSummaryRecord.getCurrentTotalCost();

                HSSFRow row_n = ExportExcelUtil.createCell(sheet, "成本小计", ++y, 0, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_n, bigDecimalFormat(winTotalCost), 1, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_n, bigDecimalFormat(currentTotalCost), 2, cellRightStyleWithBorder);

                HSSFRow row_n2 = ExportExcelUtil.createCell(sheet, "毛利额", ++y, 0, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_n2, bigDecimalFormat(winGrassProfitAmount), 1, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_n2, bigDecimalFormat(currentGrassProfitAmount), 2, cellRightStyleWithBorder);

                HSSFRow row_n3 = ExportExcelUtil.createCell(sheet, "毛利率", ++y, 0, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_n3, bigDecimalRoundRatioFormat(winGrassProfitRatio), 1, cellRightStyleWithBorder);
                ExportExcelUtil.createCell(row_n3, bigDecimalRoundRatioFormat(currentGrassProfitRatio), 2, cellRightStyleWithBorder);
            }
        }

    }

    private String bigDecimalFormat(BigDecimal b) {
        if (b == null) {
            return "-";
        } else {
            return b.toString();
        }
    }

    private String bigDecimalRoundFormat(BigDecimal b) {
        if (b == null) {
            return "-";
        } else {
            return b.setScale(2, BigDecimal.ROUND_HALF_UP).toString();
        }
    }

    private String bigDecimalRoundRatioFormat(BigDecimal b) {
        if (b == null) {
            return "-";
        } else {
            return b.setScale(2, BigDecimal.ROUND_HALF_UP).toString() + "%";
        }
    }
}