package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.WorkingHourRemindWhiteListDTO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("工时提醒白名单")
@RestController
@RequestMapping("workingHourRemindWhiteList")
public class WorkingHourRemindWhiteListController extends ControllerHelper {
    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "新增/修改项目角色")
    @PostMapping("persistence")
    public Response persistence(@RequestBody List<WorkingHourRemindWhiteListDTO> remindWhiteListDTOList) {
        final String url = String.format("%sworkingHourRemindWhiteList/persistence", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, remindWhiteListDTOList, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Long>>(){});
    }

    @ApiOperation(value = " 分页查询工时提醒白名单")
    @GetMapping("page")
    public Response page(@RequestParam(required = false) final String userName,
                         @RequestParam(required = false) final String userMip,
                         @RequestParam(required = false) final String userOrg,
                         @RequestParam(required = false) final String createByName,
                         @RequestParam(required = false) final String updateByName,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put("userName", userName);
        param.put("userMip", userMip);
        param.put("userOrg", userOrg);
        param.put("createByName", createByName);
        param.put("updateByName", updateByName);
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/workingHourRemindWhiteList/page", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<WorkingHourRemindWhiteListDTO>>>() {
        });
    }
}
