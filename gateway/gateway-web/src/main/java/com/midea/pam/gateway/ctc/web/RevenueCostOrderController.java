package com.midea.pam.gateway.ctc.web;

import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.RevenueCostOrderDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @since 2019-06-25 17:54
 */
@Api("成本归集")
@RestController
@RequestMapping("ctc/revenueCostOrder")
public class RevenueCostOrderController {

    @ApiOperation(value = "成本收入工单分页查询")
    @GetMapping("page")
    public Response page(@ApiParam(name="orderCode",value="工单编码",required=false) @RequestParam(required = false) String orderCode,
                         @ApiParam(name="projectCode",value="项目编码",required=false) @RequestParam(required = false) String projectCode,
                         @ApiParam(name="projectName",value="项目名称",required=false) @RequestParam(required = false) String projectName,
                         @ApiParam(name="ouId",value="业务实体ID",required=false) @RequestParam(required = false) Long ouId,
                         @ApiParam(name="status",value="状态",required=false) @RequestParam(required = false) Integer status,
                         @ApiParam(name="startTime",value="开始时间",required=false) @RequestParam(required = false) String startTime,
                         @ApiParam(name="endTime",value="结束时间",required=false) @RequestParam(required = false) String endTime,
                         @RequestParam(required = true, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = true, defaultValue = "10")final Integer pageSize) {
        return null;
    }

    @ApiOperation(value = "获取工单明细")
    @GetMapping("detail")
    public Response detail(
            @ApiParam(name="id",value="工单ID",required=false) @RequestParam(required = true) Long id) {
        return null;
    }

    @ApiOperation(value = "获取未结转节点")
    @GetMapping("revenueCostMilepost")
    private Response getRevenueCostMilepost(
            @ApiParam(name="projectId",value="项目Id") @RequestParam(required = true) Long projectId) {
        return null;
    }

    @ApiOperation(value = "保存收入成本工单信息")
    @PostMapping("save")
    public Response save(@RequestBody RevenueCostOrderDto revenueCostOrderDto) {
        return null;
    }

    @ApiOperation(value = "获取项目信息")
    @GetMapping("projectInfo")
    public Response getProjectInfoById(
            @ApiParam(name="projectId",value="项目Id") @RequestParam(required = true) Long projectId) {
        return null;
    }
}
