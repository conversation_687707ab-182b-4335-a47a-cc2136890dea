package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.entity.AccountAlias;
import com.midea.pam.common.basedata.query.AccountAliasQuery;
import com.midea.pam.common.ctc.dto.MaterialGetInventoryDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.PageResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("accountAlias")
@Api("账户别名信息")
public class AccountAliasController extends ControllerHelper {
    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "(带参)查询账户别名")
    @GetMapping("getAccountAliasList")
    public Response getAccountAliasList(@RequestParam(required = false) @ApiParam("账户别名名称") String aliasName,
                                        @RequestParam(required = false) @ApiParam("公司代码") String companyCode,
                                        @RequestParam(required = false) @ApiParam("公司描述") String companyDesc,
                                        @RequestParam(required = false) @ApiParam("库存组织名称") String organizationName,
                                        @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                        @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put("organizationName", organizationName);
        param.put("aliasName", aliasName);
        param.put("companyCode", companyCode);
        param.put("companyDesc", companyDesc);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "accountAlias/list", param);
        String res = cleanStr(restTemplate.getForObject(url, String.class));
        PageInfo<AccountAlias> data = JSON.parseObject(res, new TypeReference<PageInfo<AccountAlias>>() {
        });
        PageResponse<AccountAlias> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation(value = "根据条件查询账户别名")
    @GetMapping("list")
    public Response list(AccountAliasQuery query) {
        final Map<String, Object> param = new HashMap<>();
        param.put("organizationName", query.getOrganizationName());
        param.put("aliasDesc", query.getAliasDesc());
        param.put("aliasName", query.getAliasName());
        param.put("pageNum", query.getPageNum());
        param.put("pageSize", query.getPageSize());
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "accountAlias/list", param);
        String res = cleanStr(restTemplate.getForObject(url, String.class, query));
        PageInfo<AccountAlias> data = JSON.parseObject(res, new TypeReference<PageInfo<AccountAlias>>() {
        });
        PageResponse<AccountAlias> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation(value = "根据库存组织查询", response = MaterialGetInventoryDto.class)
    @GetMapping("queryByOrganizationId/{organizationId}")
    public Response queryByOrganizationId(@PathVariable String organizationId) {
        final Map<String, Object> param = new HashMap<>();
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/accountAlias/queryByOrganizationId/" + organizationId, param);
        String res = restTemplate.getForEntity(url, String.class).getBody();

        DataResponse<List<MaterialGetInventoryDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<MaterialGetInventoryDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "账户别名ERP同步")
    @GetMapping("getAccountAliasFromErp")
    public Response getAccountAliasFromErp(@RequestParam(required = false) String lastUpdateDate) {
        final Map<String, Object> param = new HashMap<>();
        param.put("lastUpdateDate", lastUpdateDate);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "accountAlias/getAccountAliasFromErp", param);
        restTemplate.getForObject(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        response.setData("0");
        return response;
    }

    @ApiOperation(value = "根据组织参数配置查询账户别名", response = MaterialGetInventoryDto.class)
    @GetMapping("queryByOrganizationCustom")
    public Response queryByOrganizationCustom(@RequestParam(required = true) String organizationId,
                                              @RequestParam(required = true) String name) {
        final Map<String, Object> param = new HashMap<>();
        param.put("organizationId", organizationId);
        param.put("name", name);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/accountAlias/queryByOrganizationCustom", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();

        DataResponse<List<MaterialGetInventoryDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<MaterialGetInventoryDto>>>() {
        });
        return response;
    }
}
