package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.pam.common.ctc.dto.TicketWorkingHourImportDTO;
import com.midea.pam.common.ctc.dto.TicketWorkingHourImportDetailDTO;
import com.midea.pam.common.ctc.excelVo.ProjectWorkingHourImportDetailExcelVo;
import com.midea.pam.common.ctc.excelVo.ProjectWorkingHourImportExcelVo;
import com.midea.pam.common.ctc.excelVo.TicketWorkingHourImportDetailExcelVo;
import com.midea.pam.common.ctc.excelVo.TicketWorkingHourImportDetailListExcelVo;
import com.midea.pam.common.ctc.excelVo.TicketWorkingHourTemplateLaborCostExcelVo;
import com.midea.pam.common.ctc.excelVo.TicketWorkingHourTemplateLaborExternalCostExcelVo;
import com.midea.pam.common.ctc.excelVo.WbsWorkingHourImportDetailExcelVo;
import com.midea.pam.common.ctc.excelVo.WbsWorkingHourImportDetailListExcelVo;
import com.midea.pam.common.ctc.excelVo.WbsWorkingHourTemplateDeptExcelVo;
import com.midea.pam.common.ctc.excelVo.WbsWorkingHourTemplateRoleExcelVo;
import com.midea.pam.common.ctc.excelVo.WbsWorkingHourTemplateWbsExcelVo;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.WorkingHourImportType;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.common.utils.BeanConverter;
import com.midea.pam.gateway.common.utils.FileUtil;
import com.midea.pam.gateway.service.MipWorkflowInnerService;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 项目工时导入
 *
 * <AUTHOR>
 * @date 2021/3/11
 */
@Api("项目工时导入")
@RestController
@RequestMapping("/ticketWorkingHourImport")
public class TicketWorkingHourImportController extends ControllerHelper {

    private Logger logger = LoggerFactory.getLogger(TicketWorkingHourImportController.class);

    private static final int maxtextStyle = 10000;

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private MipWorkflowInnerService mipWorkflowInnerService;


    @ApiOperation(value = "列表查询", response = TicketWorkingHourImportDTO.class)
    @GetMapping("/page")
    public Response page(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                         @RequestParam(required = false) @ApiParam("单号") String importCode,
                         @RequestParam(required = false) @ApiParam("导入类型") String importTypeStr,
                         @RequestParam(required = false) @ApiParam("审批状态：草稿 0，审批中 1，作废 2，审批通过 3") String statusStr,
                         @RequestParam(required = false) @ApiParam("开始日期") Date startDate,
                         @RequestParam(required = false) @ApiParam("结束日期") Date endDate,
                         @RequestParam(required = false) @ApiParam("创建人") String createName) {
        HashMap<String, Object> param = Maps.newHashMap();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("importCode", importCode);
        param.put("importTypeStr", importTypeStr);
        param.put("statusStr", statusStr);
        param.put("startDate", DateUtils.format(startDate, DateUtils.FORMAT_LONG));
        param.put("endDate", DateUtils.format(endDate, DateUtils.FORMAT_LONG));
        param.put("createName", createName);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/ticketWorkingHourImport/page", param);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<TicketWorkingHourImportDTO>>>() {
        });
    }

    @ApiOperation(value = "列表导出")
    @GetMapping("/export")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false) @ApiParam("单号") String importCode,
                       @RequestParam(required = false) @ApiParam("导入类型") String importTypeStr,
                       @RequestParam(required = false) @ApiParam("审批状态：草稿 0，审批中 1，作废 2，审批通过 3") String statusStr,
                       @RequestParam(required = false) @ApiParam("开始日期") Date startDate,
                       @RequestParam(required = false) @ApiParam("结束日期") Date endDate,
                       @RequestParam(required = false) @ApiParam("创建人") String createName) {
        HashMap<String, Object> param = Maps.newHashMap();
        param.put("pageNum", 1);
        param.put("pageSize", Integer.MAX_VALUE);
        param.put("importCode", importCode);
        param.put("importTypeStr", importTypeStr);
        param.put("statusStr", statusStr);
        param.put("startDate", DateUtils.format(startDate, DateUtils.FORMAT_LONG));
        param.put("endDate", DateUtils.format(endDate, DateUtils.FORMAT_LONG));
        param.put("createName", createName);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/ticketWorkingHourImport/exportList", param);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<Map<String, Object>>>() {
        });
        Map<String, Object> resultMap = dataResponse.getData();

        List<ProjectWorkingHourImportExcelVo> excelVos = JSONObject.parseArray(resultMap.get("excelVos").toString(), ProjectWorkingHourImportExcelVo.class);
        List<ProjectWorkingHourImportDetailExcelVo> detailExcelVos = JSONObject.parseArray(resultMap.get("detailExcelVos").toString(), ProjectWorkingHourImportDetailExcelVo.class);

        Workbook workbook = ExportExcelUtil.buildDefaultSheet(excelVos, ProjectWorkingHourImportExcelVo.class, null, "工时导入", true);
        ExportExcelUtil.addSheet(workbook, detailExcelVos, ProjectWorkingHourImportDetailExcelVo.class, null, "工时导入明细", true);

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("工时导入_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    @ApiOperation(value = "查询按项目汇总的工时信息", response = TicketWorkingHourImportDTO.class)
    @GetMapping("/getSummaryInfo")
    public Response getSummaryInfo(@RequestParam @ApiParam("导入记录表ID") Long importId) {
        HashMap<String, Object> param = Maps.newHashMap();
        param.put("importId", importId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/ticketWorkingHourImport/getSummaryInfo", param);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<TicketWorkingHourImportDTO>>() {
        });
    }

    @ApiOperation(value = "项目工时导入明细", response = TicketWorkingHourImportDetailDTO.class)
    @GetMapping("/getDetailPage")
    public Response getDetailPage(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                  @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                                  @RequestParam @ApiParam("导入ID") Long importId,
                                  @RequestParam @ApiParam("项目ID") Long projectId,
                                  @RequestParam @ApiParam("导入类型") Integer importType,
                                  @RequestParam(required = false) @ApiParam("出勤日期") Date attendanceDate,
                                  @RequestParam(required = false) @ApiParam("工单任务号") String ticketTaskCode,
                                  @RequestParam(required = false) @ApiParam("装配件描述") String theassemblyDes,
                                  @RequestParam(required = false) @ApiParam("角色") String roleName,
                                  @RequestParam(required = false) @ApiParam("部门") String orgName,
                                  @RequestParam(required = false) @ApiParam("装配件描述") String wbsSummaryCode,
                                  @RequestParam(required = false) @ApiParam("员工类型") String employeeTypeStr,
                                  @RequestParam(required = false) @ApiParam("MIP账号") String userMip,
                                  @RequestParam(required = false) @ApiParam("姓名") String userName) {
        WorkingHourImportType workingHourImportType = WorkingHourImportType.getEnumByCode(importType);
        Guard.notNull(workingHourImportType, "未定义的工时导入类型，请检查数据");

        HashMap<String, Object> param = Maps.newHashMap();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("importId", importId);
        param.put("projectId", projectId);
        param.put("attendanceDate", DateUtils.format(attendanceDate, DateUtils.FORMAT_LONG));
        param.put("ticketTaskCode", ticketTaskCode);
        param.put("theassemblyDes", theassemblyDes);
        param.put("employeeTypeStr", employeeTypeStr);
        param.put("userMip", userMip);
        param.put("userName", userName);
        param.put("importType", importType);
        if (Objects.equals(importType, WorkingHourImportType.WBS.getCode())) {
            param.put("laborWbsCostName", roleName);
        } else {
            param.put("roleName", roleName);
        }
        param.put("orgName", orgName);
        param.put("wbsSummaryCode", wbsSummaryCode);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/ticketWorkingHourImport/getDetailPage", param);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<TicketWorkingHourImportDetailDTO>>>() {
        });
    }

    @ApiOperation(value = "项目工时导入明细导出")
    @GetMapping("/exportDetial")
    public void exportDetial(HttpServletResponse response,
                             @RequestParam @ApiParam("导入ID") Long importId,
                             @RequestParam @ApiParam("项目ID") Long projectId,
                             @RequestParam @ApiParam("导入类型") Integer importType,
                             @RequestParam(required = false) @ApiParam("出勤日期") Date attendanceDate,
                             @RequestParam(required = false) @ApiParam("工单任务号") String ticketTaskCode,
                             @RequestParam(required = false) @ApiParam("装配件描述") String theassemblyDes,
                             @RequestParam(required = false) @ApiParam("角色") String roleName,
                             @RequestParam(required = false) @ApiParam("部门") String orgName,
                             @RequestParam(required = false) @ApiParam("装配件描述") String wbsSummaryCode,
                             @RequestParam(required = false) @ApiParam("员工类型") String employeeTypeStr,
                             @RequestParam(required = false) @ApiParam("MIP账号") String userMip,
                             @RequestParam(required = false) @ApiParam("姓名") String userName) {
        WorkingHourImportType workingHourImportType = WorkingHourImportType.getEnumByCode(importType);
        Guard.notNull(workingHourImportType, "未定义的工时导入类型，请检查数据");

        HashMap<String, Object> param = Maps.newHashMap();
        param.put("pageNum", 1);
        param.put("pageSize", Integer.MAX_VALUE);
        param.put("importId", importId);
        param.put("projectId", projectId);
        param.put("attendanceDate", DateUtils.format(attendanceDate, DateUtils.FORMAT_LONG));
        param.put("ticketTaskCode", ticketTaskCode);
        param.put("theassemblyDes", theassemblyDes);
        param.put("employeeTypeStr", employeeTypeStr);
        param.put("userMip", userMip);
        param.put("userName", userName);
        param.put("importType", importType);
        if (Objects.equals(importType, WorkingHourImportType.WBS)) {
            param.put("laborWbsCostName", roleName);
        } else {
            param.put("roleName", roleName);
        }
        param.put("orgName", orgName);
        param.put("wbsSummaryCode", wbsSummaryCode);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/ticketWorkingHourImport/getDetailPage", param);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<PageInfo<TicketWorkingHourImportDetailDTO>> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<TicketWorkingHourImportDetailDTO>>>() {
        });
        List<TicketWorkingHourImportDetailDTO> list = dataResponse.getData().getList();
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setIndex(i + 1);
        }

        switch (workingHourImportType) {
            case TICKET:
                List<TicketWorkingHourImportDetailListExcelVo> ticketExcelVos = BeanConverter.copy(list, TicketWorkingHourImportDetailListExcelVo.class);
                ExportExcelUtil.exportExcel(ticketExcelVos, null, "工单工时导入明细", TicketWorkingHourImportDetailListExcelVo.class, "工单工时导入明细.xls", response);
                break;
            case WBS:
                List<WbsWorkingHourImportDetailListExcelVo> wbsExcelVos = BeanConverter.copy(list, WbsWorkingHourImportDetailListExcelVo.class);
                ExportExcelUtil.exportExcel(wbsExcelVos, null, "WBS工时导入明细", WbsWorkingHourImportDetailListExcelVo.class, "WBS工时导入明细.xls", response);
                break;
            default:
                throw new MipException("该类型工时暂不支持导出明细");
        }
    }

    @ApiOperation(value = "项目工时导入-模板下载")
    @GetMapping("/exportTemplate")
    public void exportTemplate(HttpServletResponse response, @RequestParam Integer importType) {
        WorkingHourImportType workingHourImportType = WorkingHourImportType.getEnumByCode(importType);
        Guard.notNull(workingHourImportType, "未定义的工时导入类型，请检查数据");
        switch (workingHourImportType) {
            case TICKET:
                exportTemplateWbsTicket(response);
                break;
            case WBS:
                exportTemplateWbs(response);
                break;
            default:
                throw new MipException("该类型工时暂不支持下载模板");
        }
    }

    @ApiOperation(value = "项目工时导入-上传")
    @PostMapping("/importDetail")
    public Response importDetail(@RequestParam MultipartFile file, @RequestParam Integer importType, Long importId) {
        WorkingHourImportType workingHourImportType = WorkingHourImportType.getEnumByCode(importType);
        Guard.notNull(workingHourImportType, "未定义的工时导入类型，请检查数据");
        switch (workingHourImportType) {
            case TICKET:
                return importDetailTicket(file, importId);
            case WBS:
                return importDetailWbs(file, importId);
            default:
                throw new MipException("该类型工时暂不支持导入");
        }
    }

    @ApiOperation(value = "项目工时导入-下载报错信息")
    @PostMapping("exportDetailValidResult")
    public void exportDetailValidResult(@RequestParam String errMsg, @RequestParam Integer importType, HttpServletResponse response) {
        WorkingHourImportType workingHourImportType = WorkingHourImportType.getEnumByCode(importType);
        Guard.notNull(workingHourImportType, "未定义的工时导入类型，请检查数据");

        String fileName = "工时导入结果_" + DateUtils.format(new Date(), "yyyyMMddHH") + ".xls";
        switch (workingHourImportType) {
            case TICKET:
                List<TicketWorkingHourImportDetailExcelVo> ticketErrMsgList = null;
                try {
                    ticketErrMsgList = JSONObject.parseArray(errMsg, TicketWorkingHourImportDetailExcelVo.class);
                } catch (Exception e) {
                    throw new MipException("参数异常");
                }
                ExportExcelUtil.exportExcel(ticketErrMsgList, null, "报错信息", TicketWorkingHourImportDetailExcelVo.class, fileName, response);
                break;
            case WBS:
                List<WbsWorkingHourImportDetailExcelVo> wbsErrMsgList = null;
                try {
                    wbsErrMsgList = JSONObject.parseArray(errMsg, WbsWorkingHourImportDetailExcelVo.class);
                } catch (Exception e) {
                    throw new MipException("参数异常");
                }
                for (int i = 0; i < wbsErrMsgList.size(); i++) {
                    wbsErrMsgList.get(i).setNum(i + 1);
                }
                ExportExcelUtil.exportExcel(wbsErrMsgList, null, "报错信息", WbsWorkingHourImportDetailExcelVo.class, fileName, response);
                break;
            default:
                throw new MipException("该类型工时暂不支持下载报错信息");
        }
    }

    @ApiOperation(value = "项目工时导入保存", response = TicketWorkingHourImportDTO.class)
    @PostMapping("/saveWithDetail")
    public Response saveWithDetail(@RequestBody TicketWorkingHourImportDTO dto) {
        String url = ModelsEnum.CTC.getBaseUrl() + "/ticketWorkingHourImport/saveWithDetail";
        String res = restTemplate.postForObject(url, dto, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<TicketWorkingHourImportDTO>>() {
        });
    }

    @ApiOperation(value = "项目工时导入作废（将草稿状态改为废弃状态）")
    @GetMapping("/dropDraft")
    public Response dropDraft(@RequestParam @ApiParam("ID") Long id, @RequestParam Integer importType) {
        WorkingHourImportType workingHourImportType = WorkingHourImportType.getEnumByCode(importType);
        Guard.notNull(workingHourImportType, "未定义的工时导入类型，请检查数据");

        String url = ModelsEnum.CTC.getBaseUrl() + "/ticketWorkingHourImport/dropDraft?id=" + id;
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<Integer> response = JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
        //同时作废工作流
        if (response.getCode() == 0) {
            if (workingHourImportType != null) {
                switch (workingHourImportType) {
                    case TICKET:
                        mipWorkflowInnerService.draftAbandon("ticketWorkingHourImportApp", id);
                        break;
                    case WBS:
                        mipWorkflowInnerService.draftAbandon("wbsWorkingHourImportApp", id);
                        break;
                    default:
                }
            }
        }
        return response;
    }

    @ApiOperation(value = "发起审批")
    @PutMapping("updateStatusChecking/skipSecurityInterceptor")
    public Response updateStatusChecking(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String handlerId) {
        String url = String.format("%sticketWorkingHourImport/updateStatusChecking/skipSecurityInterceptor?formInstanceId=%s&handlerId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId, handlerId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "驳回")
    @PutMapping("updateStatusReject/skipSecurityInterceptor")
    public Response updateStatusReject(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String handlerId) {
        String url = String.format("%sticketWorkingHourImport/updateStatusReject/skipSecurityInterceptor?formInstanceId=%s&handlerId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId, handlerId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "通过")
    @PutMapping("updateStatusPass/skipSecurityInterceptor")
    public Response updateStatusPass(@RequestParam(required = false) Long formInstanceId,
                                     @RequestParam(required = false) String handlerId,
                                     @RequestParam(required = false) Long companyId) {
        String url = String.format("%sticketWorkingHourImport/updateStatusPass/skipSecurityInterceptor?formInstanceId=%s&handlerId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId, handlerId, companyId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "撤回")
    @PutMapping("updateStatusReturn/skipSecurityInterceptor")
    public Response updateStatusReturn(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String handlerId) {
        String url = String.format("%sticketWorkingHourImport/updateStatusReturn/skipSecurityInterceptor?formInstanceId=%s&handlerId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId, handlerId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "作废")
    @PutMapping("abandon/skipSecurityInterceptor")
    public Response abandon(@RequestParam(required = false) String formInstanceId, @RequestParam(required = false) String handlerId) {
        String url = String.format("%sticketWorkingHourImport/abandon/skipSecurityInterceptor?formInstanceId=%s&handlerId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId, handlerId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "删除")
    @PutMapping("delete/skipSecurityInterceptor")
    public Response delete(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String handlerId) {
        String url = String.format("%sticketWorkingHourImport/delete/skipSecurityInterceptor?formInstanceId=%s&handlerId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId, handlerId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    /**
     * 工单工时导入
     *
     * @param file
     * @param importId
     * @return
     */
    public Response importDetailTicket(MultipartFile file, Long importId) {
        List<TicketWorkingHourImportDetailExcelVo> detailImportExcelVoList;
        try {
            detailImportExcelVoList = FileUtil.importExcel(file, TicketWorkingHourImportDetailExcelVo.class, 1, 0);
        } catch (Exception e) {
            detailImportExcelVoList = setDetailImportExcelVoList(file);
        }
        Asserts.notEmpty(detailImportExcelVoList, ErrorCode.SYSTEM_FILE_EMPTY);
        // 移除空行的数据
        Iterator<TicketWorkingHourImportDetailExcelVo> iterator = detailImportExcelVoList.iterator();
        while (iterator.hasNext()) {
            TicketWorkingHourImportDetailExcelVo next = iterator.next();
            if (next.getAttendanceDate() == null
                    && StringUtils.isBlank(next.getProjectCode())
                    && StringUtils.isBlank(next.getProjectName())
                    && StringUtils.isBlank(next.getTicketTaskCode())
                    && StringUtils.isBlank(next.getEmployeeType())
                    && StringUtils.isBlank(next.getUserMip())
                    && StringUtils.isBlank(next.getUserName())
                    && StringUtils.isBlank(next.getRoleName())
                    && next.getWorkingHour() == null
                    && StringUtils.isBlank(next.getRemark())
            ) {
                iterator.remove();
            }
        }
        Asserts.notEmpty(detailImportExcelVoList, ErrorCode.SYSTEM_FILE_EMPTY);

        String url = String.format("%sticketWorkingHourImport/validImportDetail", ModelsEnum.CTC.getBaseUrl());
        if (importId != null) {
            url = url + "?importId=" + importId;
        }
        String res = restTemplate.postForEntity(url, detailImportExcelVoList, String.class).getBody();
        DataResponse<Object> response = JSON.parseObject(res, new TypeReference<DataResponse<Object>>() {
        });
        return response;
    }

    public List<TicketWorkingHourImportDetailExcelVo> setDetailImportExcelVoList(MultipartFile file) {
        List<TicketWorkingHourImportDetailExcelVo> detailImportExcelVoList = null;
        try {
            detailImportExcelVoList = FileUtil.importExcel(file, TicketWorkingHourImportDetailExcelVo.class, 0, 0);
        } catch (Exception exception) {
            logger.error("工单工时明细导入，上传失败:", exception);
            /*throw new BizException(100, "导入文件有误或数据类型未按要求填写，出勤日期必须为段日期格式，工时必须为数字");*/
            Asserts.isTrue(false, ErrorCode.CTC_FILE_DATA_ERRO);
        }
        return detailImportExcelVoList;
    }

    /**
     * WBS工时导入
     *
     * @param file
     * @param importId
     * @return
     */
    public Response importDetailWbs(MultipartFile file, Long importId) {
        List<WbsWorkingHourImportDetailExcelVo> excelVos = null;
        try {
            excelVos = FileUtil.importExcel(file, WbsWorkingHourImportDetailExcelVo.class, 3, 0);
        } catch (Exception e) {
            throw new MipException("模板解析异常");
        }
        Asserts.notEmpty(excelVos, ErrorCode.SYSTEM_FILE_EMPTY);
        // 移除空行的数据
        Iterator<WbsWorkingHourImportDetailExcelVo> iterator = excelVos.iterator();
        while (iterator.hasNext()) {
            WbsWorkingHourImportDetailExcelVo next = iterator.next();
            if (next.getAttendanceDate() == null
                    && StringUtils.isBlank(next.getProjectCode())
                    && StringUtils.isBlank(next.getUserMip())
                    && StringUtils.isBlank(next.getUserName())
                    && StringUtils.isBlank(next.getOrgName())
                    && StringUtils.isBlank(next.getLaborWbsCostName())
                    && StringUtils.isBlank(next.getWbsSummaryCode())
                    && next.getWorkingHour() == null
            ) {
                iterator.remove();
            }
        }
        Asserts.notEmpty(excelVos, ErrorCode.SYSTEM_FILE_EMPTY);

        String url = String.format("%sticketWorkingHourImport/validImportDetailWbs", ModelsEnum.CTC.getBaseUrl());
        if (importId != null) {
            url = url + "?importId=" + importId;
        }
        String res = restTemplate.postForEntity(url, excelVos, String.class).getBody();
        DataResponse<Object> response = JSON.parseObject(res, new TypeReference<DataResponse<Object>>() {
        });
        return response;
    }

    /**
     * 工单工时导入模板下载
     *
     * @param response
     */
    private void exportTemplateWbsTicket(HttpServletResponse response) {
        final Map<String, Object> params = new HashMap<>();
        params.put("companyId", SystemContext.getUnitId());
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/orgLaborCostTypeSet/getTicketWorkingHourImportTemplateInfo", params);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Map> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<Map>>() {
        });
        Map resultMap = dataResponse.getData();
        List<TicketWorkingHourTemplateLaborCostExcelVo> laborCostVos = JSONObject.parseArray(resultMap.get("laborCostVos").toString(), TicketWorkingHourTemplateLaborCostExcelVo.class);
        List<TicketWorkingHourTemplateLaborExternalCostExcelVo> laborExternalCostExcelVos = JSONObject.parseArray(resultMap.get("laborExternalCostExcelVos").toString(), TicketWorkingHourTemplateLaborExternalCostExcelVo.class);

        //创建新的Excel 工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();

        //设置样式
        HSSFCellStyle cellStyle0 = ExportExcelUtil.getCellStyle(workbook, HSSFCellStyle.ALIGN_CENTER, IndexedColors.RED.getIndex(), null, Boolean.TRUE);
        HSSFCellStyle cellStyle1 = ExportExcelUtil.getCellStyle(workbook, HSSFCellStyle.ALIGN_CENTER, null, null, Boolean.TRUE);
        HSSFCellStyle cellStyle2 = ExportExcelUtil.getCellStyle(workbook, HSSFCellStyle.ALIGN_LEFT, null, null, Boolean.FALSE);
        HSSFCellStyle cellStyle3 = ExportExcelUtil.getCellStyle(workbook, HSSFCellStyle.ALIGN_LEFT, null, IndexedColors.YELLOW.getIndex(), Boolean.FALSE);

        /** sheet1 **/
        HSSFSheet sheet = workbook.createSheet("工单任务工时导入");
        //列数
        int argsSize = 10;
        sheet.setColumnWidth(0, 5000);
        sheet.setColumnWidth(1, 4000);
        sheet.setColumnWidth(2, 10000);
        sheet.setColumnWidth(3, 5000);
        sheet.setColumnWidth(4, 3000);
        sheet.setColumnWidth(5, 6000);
        sheet.setColumnWidth(6, 8000);
        sheet.setColumnWidth(7, 5000);
        sheet.setColumnWidth(8, 2000);
        sheet.setColumnWidth(9, 2000);

        //固定表头
        sheet.createFreezePane(0, 2, 0, 2);

        //标题值
        Map<Integer, String[]> titleRowValues = getTitleRowValueTicket();

        //最顶端的行：导入说明
        String[] row0values = titleRowValues.get(0);
        HSSFRow row0 = ExportExcelUtil.createCell(sheet, row0values[0], 0, 0, cellStyle2);
        row0.setHeightInPoints(60);//行高
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, argsSize - 1)); // 合并单元格

        //单元格设为文本格式，默认10000行
        HSSFCellStyle textStyle = workbook.createCellStyle();
        textStyle.setDataFormat(workbook.createDataFormat().getFormat("@")); //文本格式
        for (int i = 1; i < maxtextStyle; i++) {
            HSSFRow row = sheet.createRow(i);
            for (int j = 0; j < argsSize; j++) {
                //1-2行是固定标题
                if (i <= 2) {
                    String[] values = titleRowValues.get(i);
                    if (i == 0) {
                        ExportExcelUtil.createCell(row, values[j], j, cellStyle2);
                    } else if (i == 1) {
                        ExportExcelUtil.createCell(row, values[j], j, j == 2 || j == 9 ? cellStyle1 : cellStyle0);
                    } else if (i == 2) {
                        ExportExcelUtil.createCell(row, values[j], j, cellStyle3);
                    }
                } else {
                    HSSFCell cell = row.createCell(j);
                    cell.setCellStyle(textStyle);
                }
            }
        }
        sheet.getRow(1).setHeightInPoints(30);//行高

        /** sheet2 **/
        ExportExcelUtil.addSheet(workbook, laborCostVos, TicketWorkingHourTemplateLaborCostExcelVo.class, null, "内部和自招外包角色", true);
        /** sheet3 **/
        ExportExcelUtil.addSheet(workbook, laborExternalCostExcelVos, TicketWorkingHourTemplateLaborExternalCostExcelVo.class, null, "人力点工角色", true);

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("工单工时导入模板_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    /**
     * WBS导入模板信息
     *
     * @return
     */
    private Map<Integer, String[]> getTitleRowValueTicket() {
        ArrayList<String> arrayList = new ArrayList<>();
        arrayList.add("说明：");
        arrayList.add("<1>红色字段为必填项；");
        arrayList.add("<2>员工类型可填值为“内部”、“人力点工”、“自招外包”，如填写值为“人力点工”，则填写供应商编码和供应商名称；");
        arrayList.add("<3>角色请按照人力费率维护的角色名称进行填写。");

        String[] row0values = {String.join("\n", arrayList)};

        String[] row1values = {"出勤日期", "项目号", "项目名称", "工单任务号", "员工类型", "MIP账号/供应商编码", "姓名/供应商名称", "角色", "工时", "备注"};
        String[] row2values = {"2024/2/23(请删除该条示例)", "XXX", "XXX", "XXX0001", "内部", "zhangs3", "张三", "项目经理", "8", ""};

        Map<Integer, String[]> map = new HashMap<>();
        map.put(0, row0values);
        map.put(1, row1values);
        map.put(2, row2values);
        return map;
    }

    /**
     * WBS工时导入模板下载
     *
     * @param response
     */
    public void exportTemplateWbs(HttpServletResponse response) {
        final Map<String, Object> params = new HashMap<>();
        params.put("companyId", SystemContext.getUnitId());
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/orgLaborCostTypeSet/getWbsWorkingHourImportTemplateInfo", params);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Map> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<Map>>() {
        });
        Map resultMap = dataResponse.getData();
        List<WbsWorkingHourTemplateDeptExcelVo> deptExcelVos = JSONObject.parseArray(resultMap.get("deptExcelVos").toString(), WbsWorkingHourTemplateDeptExcelVo.class);
        List<WbsWorkingHourTemplateRoleExcelVo> roleExcelVos = JSONObject.parseArray(resultMap.get("roleExcelVos").toString(), WbsWorkingHourTemplateRoleExcelVo.class);
        List<WbsWorkingHourTemplateWbsExcelVo> wbsExcelVos = JSONObject.parseArray(resultMap.get("wbsExcelVos").toString(), WbsWorkingHourTemplateWbsExcelVo.class);

        //创建新的Excel 工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();

        //设置样式
        HSSFCellStyle cellStyle0 = ExportExcelUtil.getHeaderCellStyle(workbook, HSSFCellStyle.ALIGN_LEFT, (short) 10, (byte) 255, (byte) 0, (byte) 0, true);
        HSSFCellStyle cellStyle2 = ExportExcelUtil.getHeaderCellStyle(workbook, HSSFCellStyle.ALIGN_LEFT, (short) 12, (byte) 0, (byte) 0, (byte) 0, false);

        /** sheet1 **/
        HSSFSheet sheet = workbook.createSheet("工时明细");
        //列数
        int argsSize = 9;
        sheet.setColumnWidth(0, 3000);
        sheet.setColumnWidth(1, 4000);
        sheet.setColumnWidth(2, 4000);
        sheet.setColumnWidth(3, 4000);
        sheet.setColumnWidth(4, 4000);
        sheet.setColumnWidth(5, 12000);
        sheet.setColumnWidth(6, 10000);
        sheet.setColumnWidth(7, 8000);
        sheet.setColumnWidth(8, 3000);

        //固定表头
        sheet.createFreezePane(0, 4, 0, 4);

        //标题值
        Map<Integer, String[]> titleRowValues = getTitleRowValue();

        //最顶端的行：导入说明
        String[] row0values = titleRowValues.get(0);
        HSSFRow row0 = ExportExcelUtil.createCell(sheet, row0values[0], 0, 0, cellStyle0);
        row0.setHeightInPoints(70);//行高
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, argsSize - 1)); // 合并单元格

        //单元格设为文本格式，默认10000行
        HSSFCellStyle textStyle = workbook.createCellStyle();
        textStyle.setDataFormat(workbook.createDataFormat().getFormat("@")); //文本格式
        for (int i = 1; i < maxtextStyle; i++) {
            HSSFRow row = sheet.createRow(i);
            for (int j = 0; j < argsSize; j++) {
                //1-4行是固定标题
                if (i <= 4) {
                    String[] values = titleRowValues.get(i);
                    ExportExcelUtil.createCell(row, values[j], j, cellStyle2);
                } else {
                    HSSFCell cell = row.createCell(j);
                    cell.setCellStyle(textStyle);
                }
            }
        }

        //示例数据（请删除）标红警示
        sheet.getRow(4).getCell(0).setCellStyle(cellStyle0);

        //设置下拉框的值
        if (CollectionUtils.isNotEmpty(deptExcelVos)) { //部门
            String[] explicitListValues = deptExcelVos.stream().map(e -> e.getHrOrgName()).distinct().toArray(String[]::new);
            ExportExcelUtil.createDropDownExpandList(workbook, explicitListValues, 4, maxtextStyle - 1, 5, 5);
        }

        /** sheet2 **/
        ExportExcelUtil.addSheet(workbook, deptExcelVos, WbsWorkingHourTemplateDeptExcelVo.class, null, "部门", true);
        /** sheet3 **/
        ExportExcelUtil.addSheet(workbook, roleExcelVos, WbsWorkingHourTemplateRoleExcelVo.class, null, "部门可以用于填报工时的角色", true);
        /** sheet4 **/
        ExportExcelUtil.addSheet(workbook, wbsExcelVos, WbsWorkingHourTemplateWbsExcelVo.class, null, "部门可以用于填报工时的WBS", true);
        /** sheet5 **/
        HSSFSheet sheet5 = workbook.createSheet("校验逻辑说明");
        String[] instructions = titleRowValues.get(-5);
        HSSFRow row = ExportExcelUtil.createCell(sheet5, instructions[0], 0, 0, cellStyle2);
        row.setHeightInPoints(400);//行高
        sheet5.addMergedRegion(new CellRangeAddress(0, 0, 0, 18)); // 合并单元格

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("WBS工时导入模板_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    /**
     * WBS导入模板信息
     *
     * @return
     */
    private Map<Integer, String[]> getTitleRowValue() {
        ArrayList<String> arrayList = new ArrayList<>();
        arrayList.add("导入说明:");
        arrayList.add("1、重要！该功能主要用于填报人已离职或者已转岗，但存在工时漏填报等特殊场景，如填报人正常在职，请填报人到“我的工时”自行填报！！！");
        arrayList.add("2、模板中的字段名、是否必填、字段说明请勿改动！！！示例数据请删除！！！");
        String content = String.join("\n", arrayList);

        String[] row0values = {content};
        String[] row1values = {"是否必填", "必填", "必填", "必填", "必填", "必填", "必填", "必填", "必填"};
        String[] row2values = {
                "字段说明",
                "请先确认项目状态必须是：项目进行中、预立项转正审批中、项目变更中的，才允许导入工时",
                "格式示例：2021/04/03",
                "姓名必须存在，并且和mip匹配",
                "mip必须存在，并且和姓名匹配",
                "部门必须存在，参考sheet：部门",
                "填报人所在部门，配置了可以选择用来填工时的角色，参考sheet：部门可以用于填报工时的角色，请选用该填报人所在部门的角色",
                "1、填报人所在部门，配置了可以选择用来填工时的WBS，参考sheet：部门可以用于填报工时的WBS，请选用该填报人所在部门的可用WBS\n" +
                        "2、WBS在项目中必须存在\n" +
                        "3、WBS填写项目号后面的即可，参考示例数据",
                "计量单位：小时"
        };
        String[] row3values = {"字段名", "项目编号", "出勤日期", "填报成员姓名", "填报成员mip", "填报人所在部门", "角色名称", "WBS号", "填报工时数"};
        String[] row4values = {"示例数据（请删除）", "F500XX", "2023/01/17", "邢国仕", "xinggs", "柔性系统公司/白车身及电池业务/机械设计部", "BS - Project Management  - Project Management", "01-005-011", "8"};
        String[] sheet5 = {
                "校验逻辑说明：\n" +
                        "1、必填字段不能为空，如果为空，系统报错：必填字段不能为空\n" +
                        "2、项目编号必须在该使用单位下存在，否则，系统报错：项目编号不存在\n" +
                        "3、项目的项目状态必须是：项目进行中、预立项转正审批中、项目变更中，否则，系统报错：项目状态必须是：项目进行中、预立项转正审批中、项目变更中\n" +
                        "4、出勤日期的格式：YYYY/MM/DD，如：2021/04/03，并且是存在的日期，否则，系统报错：出勤日期有误\n" +
                        "5、姓名和mip必须存在，并且相匹配，否则，系统报错：填报成员的姓名和mip有误\n" +
                        "6、填报人所在部门必须存在，否则，系统报错：部门不存在（请下载最新模板，获取最新的部门、角色及WBS的相关信息！！！）\n" +
                        "7、角色名称，必须是：填报人所在部门，配置了可以选择用来填工时的角色，否则，系统报错：用户所在部门：XXX/XX/XXXXXX，没有配置角色：XXX，请联系相关财务进行配置（请下载最新模板，获取最新的部门、角色及WBS的相关信息！！！）\n" +
                        "8、WBS号，必须满足：填报人所在部门，配置了可以选择用来填工时的WBS，否则，系统报错：WBS号和用户所在部门：XXX/XX/XXXXXX，配置的可用WBS不匹配，请联系相关财务进行配置（请下载最新模板，获取最新的部门、角色及WBS的相关信息！！！）\n" +
                        "9、WBS号，必须在项目中存在，否则，系统报错：WBS在项目中不存在\n" +
                        "10、填报工时数，请填写正数，否则，系统报错：填报工时数有误\n" +
                        "11、填报工时数的校验：\n" +
                        "（1）一天最多可填工时校验，按组织参数：工时填报小时数上限，配置的数字，校验一个人一天最多可以填写在多个项目的小时数汇总，超过这个小时数，系统报错：YYYY/MM/DD填报的工时数合计：XX小时，超过了一天的最大可填报工时数XX小时\n" +
                        "（2）使用单位层级的组织参数：工时填报是否受考勤工时的限制，参数值为：1时，校验一个人一天填报的工时汇总不超过考勤工时，超过时，系统报错：YYYY/MM/DD，填报的工时超过了考勤工时，参数值不为1时，不做校验。考勤工时可以通过hr系统获取\n" +
                        "（3）校验填报的工时是否超预算：按项目类型中的“工时预算控制”配置为严控、提醒、还是不控，来控制工时填报超预算校验 \n" +
                        "         a）配置为：严控，项目下该人员填写WBS+角色的工时，校验填写的工时*角色费率/8（角色费率取内部项目成本或内部研发成本，项目属性为研发项目的取内部研发成本，其他项目属性的取内部项目成本）汇总（按WBS+活动事项汇总）是否大于该WBS+活动事项的剩余可用预算，大于，系统报错：WBS：XXX，活动事项：XXX，预算不足\n" +
                        "         b）配置为：提醒，校验填写的工时*角色费率/8（角色费率取内部项目成本或内部研发成本，项目属性为研发项目的取内部研发成本，其他项目属性的取内部项目成本）汇总（按WBS+活动事项汇总）+该WBS+活动事项的“在途成本”+“已发生成本”是否大于该WBS+活动事项的预算*配置的百分比（配置为：提醒后面填写的百分比），大于，流程提交审批时，邮件提醒项目经理\n" +
                        "         c）配置为：不控，填报工时时不做超预算校验\n" +
                        "12、同一个项目、同一天、同一个角色+WBS的工时只允许有一条，超过一条，系统报错：项目：XXX，WBS：XXX+角色：XXX不唯一，请合并填写\n" +
                        "13、活动事项校验：需要校验角色是否配置了活动事项，没有配置，系统报错：角色对应的活动事项缺失，请联系财务维护角色对应的活动事项\n" +
                        "14、人力费率校验：需要校验角色是否配置了人力费率，没有配置，系统报错：角色对应的人力费率缺失，请联系财务维护角色对应的人力费率"
        };

        Map<Integer, String[]> map = new HashMap<>();
        map.put(0, row0values);
        map.put(1, row1values);
        map.put(2, row2values);
        map.put(3, row3values);
        map.put(4, row4values);
        map.put(-5, sheet5);
        return map;
    }

}
