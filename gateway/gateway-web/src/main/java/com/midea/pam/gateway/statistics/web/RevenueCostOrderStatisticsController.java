package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.RevenueCostOrderDto;
import com.midea.pam.common.ctc.vo.RevenueCostOrderExcVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Api("收入成本工单模块")
@RestController
@RequestMapping("statistics/revenueCostOrder")
public class RevenueCostOrderStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "收入成本工单分页查询")
    @GetMapping("page")
    public Response list(RevenueCostOrderDto revenueCostOrderDto,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(revenueCostOrderDto);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/revenueCostOrder/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<RevenueCostOrderDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<RevenueCostOrderDto>>>() {
        });

        return response;
    }

    @ApiOperation(value = "收入成本工单列表导出", response = ResponseMap.class)
    @GetMapping("/export")
    public void listExport(HttpServletResponse response, RevenueCostOrderDto revenueCostOrderDto) {
        final Map<String, Object> param = buildParam(revenueCostOrderDto);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/revenueCostOrder/v1/export", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("收入成本工单_"+ DateUtils.format(new Date(),"yyyyMMddHHmmss"));
        fileName.append(".xls");

        JSONArray revenueArr = (JSONArray) resultMap.get("revenue");

        List<RevenueCostOrderExcVo> revenueCostOrderExcelVOS = JSONObject.parseArray(revenueArr.toJSONString(), RevenueCostOrderExcVo.class);
        for (int i = 0; i < revenueCostOrderExcelVOS.size(); i++) {
            RevenueCostOrderExcVo revenueCostOrderExcelVO = revenueCostOrderExcelVOS.get(i);
            revenueCostOrderExcelVO.setNum(i + 1);
        }

        final Workbook workbook = com.midea.pam.common.util.ExportExcelUtil.buildDefaultSheet(revenueCostOrderExcelVOS, RevenueCostOrderExcVo.class, null, "收入成本工单", true);

        com.midea.pam.common.util.ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    private Map buildParam(RevenueCostOrderDto revenueCostOrderDto) {

        final Map<String, Object> params = new HashMap<>();
        params.put("orderCode", revenueCostOrderDto.getOrderCode());
        params.put("projectCode", revenueCostOrderDto.getProjectCode());
        params.put("projectName", revenueCostOrderDto.getProjectName());
        params.put("currency", revenueCostOrderDto.getCurrency());
        params.put("customerName", revenueCostOrderDto.getCustomerName());
        params.put("crmCode", revenueCostOrderDto.getCrmCode());

        params.put("statusStr", revenueCostOrderDto.getStatusStr());
        params.put("projectTypeStr", revenueCostOrderDto.getProjectTypeStr());
        params.put("ouIdStr", revenueCostOrderDto.getOuIdStr());

        params.put("inputDateStart", revenueCostOrderDto.getInputDateStart());
        params.put("inputDateEnd", revenueCostOrderDto.getInputDateEnd());

        params.put("sourceStr", revenueCostOrderDto.getSourceStr());
        params.put("typeStr", revenueCostOrderDto.getTypeStr());

        return params;
    }
}
