package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.ctc.vo.IncomeCalculateProductTaskExcelVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.statistics.dto.IncomeCalculateProductTaskDTO;
import com.midea.pam.common.statistics.entity.IncomeCalculateProductTask;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.support.utils.BeanConverter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;


/**
 * <AUTHOR>
 * @date 2020/9/17
 * @description
 */
@Api("收入预测产品任务")
@RestController
@RequestMapping("statistics/incomeCalculateProductTask")
public class IncomeCalculateProductTaskController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "列表查询", response = IncomeCalculateProductTask.class)
    @GetMapping("query")
    public Response query(@RequestParam(required = true) @ApiParam(value = "收入预测ID") Long calculateId) {

        final Map<String, Object> param = new HashMap<>();
        param.put("calculateId", calculateId);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "incomeCalculateProductTask/query", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<IncomeCalculateProductTaskDTO>>>() {
        });
    }

    @ApiOperation(value = "分页查询")
    @GetMapping("pageNotCommitted")
    public Response page(@RequestParam(required = true) @ApiParam(value = "收入预测ID") Long calculateId,
                         @RequestParam(required = false) String productOrgName,
                         @RequestParam(required = false) String statusesStr,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put("calculateId", calculateId);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("productOrgName", productOrgName);
        param.put("statusesStr", statusesStr);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "incomeCalculateProductTask/pageNotCommitted", param);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<IncomeCalculateProductTaskDTO>> response =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<IncomeCalculateProductTaskDTO>>>() {
                });
        return response;
    }

    @ApiOperation(value = "保存产品任务")
    @PostMapping("save")
    public Response add(@RequestBody List<IncomeCalculateProductTask> list) {
        final String url = String.format("%sincomeCalculateProductTask/save", ModelsEnum.STATISTICS.getBaseUrl());
        String res = restTemplate.postForEntity(url, list, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation(value = "导出有权限的任务清单")
    @GetMapping("exportAuth")
    public void exportAuth(@RequestParam Long calculateId,
                           HttpServletResponse response) {
        final Map<String, Object> params = new HashMap<>();
        params.put("calculateId", calculateId);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "incomeCalculateProductTask/listAuthTasks", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<IncomeCalculateProductTask>> dataResponse =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<IncomeCalculateProductTask>>>() {
                });
        List<IncomeCalculateProductTaskExcelVO> excelVOS = new ArrayList<>();
        List<IncomeCalculateProductTask> data = dataResponse.getData();
        if (ListUtils.isNotEmpty(data)) {
            excelVOS = BeanConverter.copy(data, IncomeCalculateProductTaskExcelVO.class);
            int i = 1;
            for (IncomeCalculateProductTaskExcelVO task : excelVOS) {
                task.setNumber(i++);
            }
        }
        Workbook workbook = ExportExcelUtil.buildDefaultSheet(excelVOS, IncomeCalculateProductTaskExcelVO.class, null, "产品经理任务明细", true);
        ExportExcelUtil.downLoadExcel("产品经理任务明细_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls", response, workbook);
    }

}
