package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.mcomponent.core.util.BeanUtils;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.StorageDto;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.MaterialTransferDto;
import com.midea.pam.common.ctc.entity.MaterialTransferDetail;
import com.midea.pam.common.ctc.vo.MaterialTransferDetailTempExcelVo;
import com.midea.pam.common.ctc.vo.MaterialTransferDetailsExcelVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.ProcessTemplate;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.utils.FileUtil;
import com.midea.pam.gateway.service.MipWorkflowInnerService;
import com.midea.pam.support.utils.BeanConverter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * @program: pam
 * @description: MaterialTransferController
 * @author: gaojh1
 * @create: 2019-3-12 10:51
 **/
@Api("子库转移")
@RestController
@RequestMapping("materialTransfer")
public class MaterialTransferController extends ControllerHelper {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private MipWorkflowInnerService mipWorkflowInnerService;

    @ApiOperation(value = "转移单分页查询", response = MaterialTransferDto.class)
    @GetMapping("page")
    public Response page(
            @RequestParam(required = false) final Long id,
            @RequestParam(required = false) final Integer status,
            @RequestParam(required = false) final String transferCode,
            @RequestParam(required = false) final String applyUserName,
            @RequestParam(required = false) final String fillUserName,
            @RequestParam(required = false) final Long organizationId,
            @RequestParam(required = false) final String organizationName,
            @RequestParam(required = false) final String sendInventoryCode,
            @RequestParam(required = false) final String receiveInventoryCode,
            @RequestParam(required = false) final String applyDateStart,
            @RequestParam(required = false) final String applyDateEnd,
            @RequestParam(required = true, defaultValue = "1") final Integer pageNum,
            @RequestParam(required = true, defaultValue = "10")final Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        param.put("id", id);
        param.put("status", status);
        param.put("transferCode", transferCode);
        param.put("applyUserName", applyUserName);
        param.put("fillUserName", fillUserName);
        param.put("organizationName", organizationName);
        param.put("sendInventoryCode", sendInventoryCode);
        param.put("receiveInventoryCode", receiveInventoryCode);
        param.put("applyDateStart", applyDateStart);
        param.put("applyDateEnd", applyDateEnd);
        param.put("organizationId", organizationId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/materialTransfer/page", param);
        final String res = restTemplate.getForEntity(url , String.class).getBody();
        DataResponse<PageInfo<MaterialTransferDto>> response = JSON.parseObject(res,
                new TypeReference<DataResponse<PageInfo<MaterialTransferDto>>>() {});
        return response;
    }

    @ApiOperation(value = "转移单详细", response = MaterialTransferDto.class)
    @GetMapping("queryById")
    public Response queryById(
            @RequestParam(required = true) final Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/materialTransfer/queryById", param);
        final String res = restTemplate.getForEntity(url , String.class).getBody();
        DataResponse<MaterialTransferDto> response = JSON.parseObject(res, new TypeReference<DataResponse<MaterialTransferDto>>() {});
        return response;
    }

    @ApiOperation(value = "移动审批转移单详细", response = MaterialTransferDto.class)
    @GetMapping("materialTransferApp")
    public Response materialTransferApp(
            @RequestParam(required = true) final Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/materialTransfer/materialTransferApp", param);
        final String res = restTemplate.getForEntity(url , String.class).getBody();
        DataResponse<ResponseMap> response = JSON.parseObject(res, new TypeReference<DataResponse<ResponseMap>>() {});
        return response;
    }

    @ApiOperation(value = "转移物料明细列表", response = MaterialTransferDetail.class)
    @GetMapping("detailList")
    public Response page(@RequestParam(required = false) final Long headerId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("headerId", headerId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/materialTransfer/detailList", param);
        final String res = restTemplate.getForEntity(url , String.class).getBody();
        DataResponse<List<MaterialTransferDetail>> response = JSON.parseObject(res,
                new TypeReference<DataResponse<List<MaterialTransferDetail>>>() {});
        return response;
    }

    @ApiOperation(value = "保存")
    @PostMapping("save")
    public Response save(@RequestBody MaterialTransferDto dto) {
        final String url = String.format("%smaterialTransfer/save", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<MaterialTransferDto>>(){});
    }

    @ApiOperation(value = "作废")
    @PutMapping("discard")
    public Response discard(@RequestParam(required = false) Long handlerUserId,
                        @RequestParam(required = false) Long formInstanceId){
        DataResponse<Long> response = Response.dataResponse();
        String url = String.format("%smaterialTransfer/discard" +
                        "?handlerUserId=%s" +
                        "&formInstanceId=%s" , ModelsEnum.CTC.getBaseUrl(),
                handlerUserId, formInstanceId);
        ResponseEntity<Long> entity = restTemplate.exchange(url, HttpMethod.PUT, null, Long.class);
        //审批流程作废
        if(entity.getStatusCodeValue() == 200){
            logger.info("流程作废");
            mipWorkflowInnerService.draftAbandon(ProcessTemplate.MATERIAL_TRANSFER_APP.getCode(),formInstanceId);
        }

        return response.setData(entity.getBody());
    }

    @ApiOperation(value = "提交回调")
    @PutMapping("updateStatusSubmit/skipSecurityInterceptor")
    public Response updateStatusSubmit(@RequestParam(required = false) Long formInstanceId,
                                       @RequestParam(required = false) String fdInstanceId,
                                       @RequestParam(required = false) String formUrl,
                                       @RequestParam(required = false) String eventName,
                                       @RequestParam(required = false) String handlerId,
                                       @RequestParam(required = false) Long companyId,
                                       @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialTransfer/updateStatusSubmit/skipSecurityInterceptor" +
               "?formInstanceId=%s" +
                "&fdInstanceId=%s" +
                "&formUrl=%s" +
                "&eventName=%s" +
                "&handlerId=%s" +
                "&companyId=%s" +
                "&createUserId=%s" , ModelsEnum.CTC.getBaseUrl(),
                formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "通过回调")
    @PutMapping("updateStatusPass/skipSecurityInterceptor")
    public Response updateStatusPass(@RequestParam(required = false) Long formInstanceId,
                                     @RequestParam(required = false) String fdInstanceId,
                                     @RequestParam(required = false) String formUrl,
                                     @RequestParam(required = false) String eventName,
                                     @RequestParam(required = false) String handlerId,
                                     @RequestParam(required = false) Long companyId,
                                     @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialTransfer/updateStatusPass/skipSecurityInterceptor" +
                "?formInstanceId=%s" +
                "&fdInstanceId=%s" +
                "&formUrl=%s" +
                "&eventName=%s" +
                "&handlerId=%s" +
                "&companyId=%s" +
                "&createUserId=%s", ModelsEnum.CTC.getBaseUrl(),
                formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "驳回回调")
    @PutMapping("updateStatusReject/skipSecurityInterceptor")
    public Response updateStatusReject(@RequestParam(required = false) Long formInstanceId,
                                       @RequestParam(required = false) String fdInstanceId,
                                       @RequestParam(required = false) String formUrl,
                                       @RequestParam(required = false) String eventName,
                                       @RequestParam(required = false) String handlerId,
                                       @RequestParam(required = false) Long companyId,
                                       @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialTransfer/updateStatusReject/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s", ModelsEnum.CTC.getBaseUrl(),
                formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "审批人作废回调")
    @PutMapping("updateStatusAbandon/skipSecurityInterceptor")
    public Response updateStatusAbandon(@RequestParam(required = false) Long formInstanceId,
                                        @RequestParam(required = false) String fdInstanceId,
                                        @RequestParam(required = false) String formUrl,
                                        @RequestParam(required = false) String eventName,
                                        @RequestParam(required = false) String handlerId,
                                        @RequestParam(required = false) Long companyId,
                                        @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialTransfer/updateStatusAbandon/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s", ModelsEnum.CTC.getBaseUrl(),
                formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "审批人撤回回调")
    @PutMapping("updateStatusDraftReturn/skipSecurityInterceptor")
    public Response updateStatusDraftReturn(@RequestParam(required = false) Long formInstanceId,
                                            @RequestParam(required = false) String fdInstanceId,
                                            @RequestParam(required = false) String formUrl,
                                            @RequestParam(required = false) String eventName,
                                            @RequestParam(required = false) String handlerId,
                                            @RequestParam(required = false) Long companyId,
                                            @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialTransfer/updateStatusDraftReturn/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s", ModelsEnum.CTC.getBaseUrl(),
                formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "项目借料流程作废")
    @PutMapping("updateStatusDeleteForMaterialTransfer/skipSecurityInterceptor")
    public Response updateStatusDeleteForMaterialTransfer(@RequestParam(required = false) Long formInstanceId,
                                                          @RequestParam(required = false) String fdInstanceId,
                                                          @RequestParam(required = false) String formUrl,
                                                          @RequestParam(required = false) String eventName,
                                                          @RequestParam(required = false) String handlerId,
                                                          @RequestParam(required = false) Long companyId,
                                                          @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialTransfer/updateStatusDeleteForMaterialTransfer/skipSecurityInterceptor" +
                       "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s", ModelsEnum.CTC.getBaseUrl(),
                formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "处理人通过")
    @PutMapping("agree/skipSecurityInterceptor")
    public Response agree(@RequestParam(required = false) Long formInstanceId,
                            @RequestParam(required = false) String fdInstanceId,
                            @RequestParam(required = false) String formUrl,
                            @RequestParam(required = false) String eventName,
                            @RequestParam(required = false) String handlerId,
                            @RequestParam(required = false) Long companyId,
                            @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialTransfer/agree/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "处理")
    @PostMapping("process")
    public Response process(@RequestBody MaterialTransferDto[] dtos) {
        final String url = String.format("%smaterialTransfer/process", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dtos, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>(){});
    }

    @ApiOperation(value = "打印")
    @GetMapping("print/{id}")
    public com.midea.pam.gateway.common.base.Response print(@PathVariable Long id) {
        final Map<String, Object> param = new HashMap<>();
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/materialTransfer/print/" + id, param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        com.midea.pam.gateway.common.base.DataResponse<Integer> response =
                JSON.parseObject(res, new TypeReference<com.midea.pam.gateway.common.base.DataResponse<Integer>>() {});
        return response;
    }

    @ApiOperation(value = "处理")
    @GetMapping("pushToErp")
    public Response pushToErp(@RequestParam(required = true) String idList) {
        final Map<String, Object> param = new HashMap<>();
        param.put("idList", idList);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "materialTransfer/pushToErp", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>(){});
    }

    @ApiOperation(value = "上传文件检查数据", notes = "场景：物料转移单批量导入")
    @PostMapping("/importMaterialTransferDetail")
    public com.midea.pam.gateway.common.base.Response importMaterialTransferDetail(@RequestPart("file") MultipartFile file,
                                                                                   @RequestParam(required = true) final Long organizationId,
                                                                                   @RequestParam(required = true) final String subinventoryCode,
                                                                                   @RequestParam(required = false) final String locator,
                                                                                   @RequestParam(required = false) final Integer isKanban) {
        List<MaterialTransferDetailTempExcelVo> excelVos = null;
        try {
            excelVos = FileUtil.importExcel(file, MaterialTransferDetailTempExcelVo.class, 1, 0);
            //祛除空行
            Iterator<MaterialTransferDetailTempExcelVo> iterator = excelVos.iterator();
            while (iterator.hasNext()){
                MaterialTransferDetailTempExcelVo vo = iterator.next();
                if (StringUtils.isBlank(vo.getErpCode()) && StringUtils.isBlank(vo.getApplyNum())){
                    iterator.remove();
                }
            }
        } catch (Exception e) {
            throw new MipException("模板解析异常");
        }
        if (CollectionUtils.isEmpty(excelVos)) {
            throw new MipException("当前导入Excel模板中无法匹配有效的记录，请检查");
        }
        //业务逻辑
        Map<String, Object> resultMap = buildErrMsgInfo(excelVos,organizationId,subinventoryCode,locator,isKanban);
        com.midea.pam.gateway.common.base.DataResponse<Map<String, Object>> response = com.midea.pam.gateway.common.base.Response.dataResponse();
        return response.setData(resultMap);
    }

    /**
     * 转移单导入信息校验
     * @param excelVos
     * @param organizationId
     * @param subinventoryCode
     * @param locator
     * @param isKanban
     * @return
     */
    private Map<String, Object> buildErrMsgInfo(List<MaterialTransferDetailTempExcelVo> excelVos,
                                                Long organizationId,
                                                String subinventoryCode,
                                                String locator,
                                                Integer isKanban){
        List<String> errMsgList = new ArrayList<>();

        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.PAGE_NUM, 1);
        param.put(Constants.Page.PAGE_SIZE, 100000);
        param.put("organizationId", organizationId);
        param.put("subinventoryCode", subinventoryCode);
        param.put("locator", locator);
        param.put("isKanban", isKanban);
        logger.info("查询库存现有量的参数为:{}",JSON.toJSONString(param));
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/storage/queryMaterialsSelected", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<StorageDto>> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<StorageDto>>>() {
        });
        List<StorageDto> storageList = dataResponse.getData().getList();
        logger.info("当前组织子库或货位下物料现有量数量为:{}",storageList.size());
        Map<String, StorageDto> storageMap = storageList.stream()
                .filter(e -> StringUtils.isNotEmpty(e.getSegment1()))
                .peek(s -> s.setId(null))
                .collect(Collectors.toMap(StorageDto::getSegment1, Function.identity(), (a, b) -> a));
        //校验数据
        List<StorageDto> dataList = new ArrayList<>();

        for (int i = 0; i < excelVos.size(); i++) {
            MaterialTransferDetailTempExcelVo excelVo = excelVos.get(i);
            //校验申请转移数量
            if (StringUtils.isNotEmpty(excelVo.getApplyNum())) {
                if (BigDecimalUtils.isBigDecimal(excelVo.getApplyNum())) {
                    BigDecimal applyAmount = new BigDecimal(excelVo.getApplyNum());
                    if (applyAmount.compareTo(BigDecimal.ZERO) < 0) {
                        errMsgList.add(String.format("行：%s，申请转移数量填写有误", i + 3));
                    }
                    excelVo.setApplyAmount(applyAmount);
                } else {
                    errMsgList.add(String.format("行：%s，申请转移数量填写有误", i + 3));
                }
            }
            //校验物料编号
            if(StringUtils.isEmpty(excelVo.getErpCode())){
                errMsgList.add(String.format("行：%s，物料编码缺失", i + 3));
            }else {
                StorageDto item = storageMap.get(excelVo.getErpCode());
                if (item == null) {
                    errMsgList.add(String.format("行：%s，没有可用库存", i + 3));
                } else {
                    StorageDto storageDto = new StorageDto();
                    BeanUtils.copyProperties(item, storageDto);
                    storageDto.setApplyAmount(Optional.ofNullable(excelVo.getApplyAmount()).orElse(BigDecimal.ZERO));
                    storageDto.setRemark(excelVo.getRemark());
                    dataList.add(storageDto);
                }

            }
        }
        Map<String, Object> map = new HashMap();
        map.put("flag", CollectionUtils.isEmpty(errMsgList));
        map.put("errMsg", errMsgList);
        if (CollectionUtils.isEmpty(errMsgList)) {
            map.put("dataList", dataList);
        }
        return map;
    }

    /**
     * 转移单错误信息下载
     * @param file
     * @param errMsg
     * @param response
     */
    @ApiOperation(value = "下载错误数据", notes = "场景：物料转移单批量导入")
    @PostMapping("/detail/downloadErrorMsg")
    public void downloadErrorMsg(@RequestPart("file") MultipartFile file, @RequestParam String errMsg, HttpServletResponse response) {
        List<String> errMsgList = null;
        try {
            errMsgList = JSONObject.parseArray(errMsg, String.class);
        } catch (Exception e) {
            throw new MipException("参数异常");
        }
        Workbook workbook = null;
        try {
            workbook = WorkbookFactory.create(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(1);
            for (int i = 0; i < errMsgList.size(); ++i) {
                Row row = sheet.createRow(i);
                row.createCell(0).setCellValue(errMsgList.get(i));
            }
        } catch (Exception e) {
            throw new MipException("模板解析异常");
        }
        //导出
        ExportExcelUtil.downLoadExcel("报错信息_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xlsx", response, workbook);
    }

    @ApiOperation(value = "库存转移单明细数据导出")
    @GetMapping("exportDetails")
    public void exportDetails(HttpServletResponse response,
                              @RequestParam(required = true) Long id,
                              @RequestParam(required = true) String transferCode){
        final Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "materialTransfer/exportDetails", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<MaterialTransferDetail>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<MaterialTransferDetail>>>() {
        });
        List<MaterialTransferDetail> data = dataResponse.getData();
        logger.info("查询的数据量为:{}",data.size());
        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("库存转移单"+transferCode+"_"+ DateUtils.format(new Date(),"yyyyMMddHHmmss"));
        fileName.append(".xls");
        List<MaterialTransferDetailsExcelVO> excelVOS = BeanConverter.copy(data, MaterialTransferDetailsExcelVO.class);
        for (int i = 0; i < excelVOS.size(); i++) {
            MaterialTransferDetailsExcelVO excelVO = excelVOS.get(i);
            excelVO.setNum(i + 1);
        }
        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(excelVOS, MaterialTransferDetailsExcelVO.class, null, "项目库存转移明细", true);
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }


}
