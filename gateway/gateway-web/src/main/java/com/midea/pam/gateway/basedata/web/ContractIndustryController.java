package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.ContractIndustryDTO;
import com.midea.pam.common.basedata.entity.ContractIndustry;
import com.midea.pam.common.basedata.entity.ContractSigningCenter;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.PageResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.common.utils.PamCurrentUserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 行业controller
 * @date 2023-5-25
 */
@Api("行业")
@RestController
@RequestMapping("contractIndustry")
public class ContractIndustryController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查询行业列表分页", response = ContractIndustryDTO.class)
    @GetMapping("selectPage")
    public Response selectPage(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                               @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize,
                               @RequestParam(required = false) @ApiParam(value = "行业代码") String contractIndustryCode) {

        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("contractIndustryCode", contractIndustryCode);

        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "contractIndustry/selectPage", param);
        String res = cleanStr(restTemplate.getForObject(url, String.class));
        PageInfo<ContractIndustryDTO> data = JSON.parseObject(res, new TypeReference<PageInfo<ContractIndustryDTO>>() {
        });
        PageResponse<ContractIndustryDTO> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation(value = "查询行业层级信息", response = ContractIndustryDTO.class)
    @GetMapping("tree")
    public Response tree(@RequestParam @ApiParam(value = "业务实体") Long ouId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("ouId", ouId);

        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "contractIndustry/tree", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<ContractIndustryDTO>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<ContractIndustryDTO>>>() {
        });
        return response;
    }

    @ApiOperation(value = "新增行业", response = Response.class)
    @PostMapping("add")
    public Response add(@RequestBody @ApiParam(name = "ContractIndustry", value = "行业信息") ContractIndustry contractIndustry) {
        String url = String.format("%scontractIndustry/add", ModelsEnum.BASEDATA.getBaseUrl());

        UserInfo user = CacheDataUtils.findUserByMip(PamCurrentUserUtil.getCurrentUserName());
        contractIndustry.setCreateBy(user.getId());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, contractIndustry, String.class);
        String res = cleanStr(responseEntity.getBody());
        DataResponse<String> response = Response.dataResponse();
        return response.setData(res);
    }

    @ApiOperation(value = "修改行业", response = Response.class)
    @PutMapping("update")
    public Response update(@RequestBody @ApiParam(name = "ContractIndustry", value = "行业信息") ContractIndustry contractIndustry) {
        String url = String.format("%scontractIndustry/update", ModelsEnum.BASEDATA.getBaseUrl());
        UserInfo user = CacheDataUtils.findUserByMip(PamCurrentUserUtil.getCurrentUserName());
        contractIndustry.setUpdateBy(user.getId());
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, new HttpEntity<ContractIndustry>(contractIndustry), String.class);
        String res = cleanStr(responseEntity.getBody());
        DataResponse<String> response = Response.dataResponse();
        return response.setData(res);
    }

    @ApiOperation(value = "删除行业", response = Response.class)
    @DeleteMapping("{id}")
    public Response delete(@PathVariable Long id) {
        String loginUserName = PamCurrentUserUtil.getCurrentUserName();
        UserInfo user = CacheDataUtils.findUserByMip(loginUserName);
        String url = String.format("%scontractIndustry/delete" + id + "/" + user.getId(), ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.DELETE, null, String.class);
        String res = cleanStr(responseEntity.getBody());
        DataResponse<String> response = Response.dataResponse();
        return response.setData(res);
    }

    @ApiOperation(value = "查询行业", response = ContractIndustry.class)
    @GetMapping("view/{id}")
    public Response view(@PathVariable Long id) {
        String url = String.format("%scontractIndustry/view/" + id, ModelsEnum.BASEDATA.getBaseUrl());
        String res = cleanStr(restTemplate.getForObject(url, String.class));
        return Response.dataResponse().setData(JSON.parseObject(res, new TypeReference<ContractIndustry>() {
        }));
    }

    @ApiOperation(value = "查询行业列表", response = ContractIndustry.class)
    @PostMapping("viewList")
    public Response viewList(@RequestBody List<Long> industryIds) {
        String url = String.format("%scontractIndustry/viewList", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, industryIds, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<ContractIndustry>>>() {
        });
    }
}
