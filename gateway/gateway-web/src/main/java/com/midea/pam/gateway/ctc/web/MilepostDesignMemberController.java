package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.entity.MilepostDesignMember;
import com.midea.pam.common.ctc.vo.MilepostDesignMemberVo;
import com.midea.pam.common.ctc.vo.TechnologyLeaderOrMemberVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("详细设计成员")
@RestController
@RequestMapping("milepostDesignMember")
public class MilepostDesignMemberController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "新增/修改详细设计成员")
    @PostMapping("saveMilepostDesignMember")
    public Response saveMilepostDesignMember(@RequestBody List<MilepostDesignMember> milepostDesignMembers) {
        final String url = String.format("%smilepostDesignMember/saveMilepostDesignMember", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, milepostDesignMembers, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "分页查询详细设计成员")
    @GetMapping("page")
    public Response page(@RequestParam final Long milepostId,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put("milepostId", milepostId);
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignMember/page", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<MilepostDesignMemberVo>>>() {
        });
    }

    @ApiOperation(value = "删除详细设计成员")
    @GetMapping("delete")
    public Response delete(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignMember/delete", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "判断当前人是否技术负责人或者设计团队成员")
    @GetMapping("isTechnologyLeaderOrMember")
    public Response isTechnologyLeaderOrMember(@RequestParam Long projectId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignMember/isTechnologyLeaderOrMember", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<TechnologyLeaderOrMemberVo>>() {
        });
    }
}
