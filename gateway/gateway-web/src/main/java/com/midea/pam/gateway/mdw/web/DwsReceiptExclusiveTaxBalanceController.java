package com.midea.pam.gateway.mdw.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @PackageClassName: com.midea.pam.gateway.mdw.web.DwsReceiptExclusiveTaxBalanceController
 * @Description: 统计收入开票金额差额
 * @Author: JerryH
 * @Date: 2022-12-21, 0021 下午 02:00:07
 */
@Api("统计收入开票金额差额")
@RestController
@RequestMapping("/mdw/receiptExclusiveTaxBalance")
public class DwsReceiptExclusiveTaxBalanceController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "手动拉取项目已确认收入&成本数据")
    @GetMapping("statisticsReceiptExclusiveTaxBalance")
    public Response statisticsReceiptExclusiveTaxBalance(@RequestParam(required = false) Long projectId) {
        Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        final String url = buildGetUrl(ModelsEnum.MDW.getBaseUrl(), "/dws/receiptExclusiveTaxBalance/statisticsReceiptExclusiveTaxBalance", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
    }
}
