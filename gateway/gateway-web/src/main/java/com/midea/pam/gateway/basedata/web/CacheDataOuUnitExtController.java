package com.midea.pam.gateway.basedata.web;


import com.midea.pam.common.enums.ModelsEnum;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static com.midea.pam.common.util.StringUtils.buildGetUrl;


@RestController
@RequestMapping("cacheDataOuUnitExt")
@Api("虚拟部门")
public class CacheDataOuUnitExtController {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "将虚拟部门信息存入redis中(因为相关信息在redis中过期，导致合同前缀缺失，现在重新装入)")
    @GetMapping("cache")
    public String cache(@RequestParam(required = false) Map<String, Object> map ) {
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(),  "/cacheDataOuUnitExt/cache",map);
        restTemplate.getForEntity(url, String.class).getBody();
        return null;
    }

    @ApiOperation(value = "直接")
    @GetMapping("getSeqPerfixByOuId")
    public String getSeqPerfixByOuId(@RequestParam(required = true) Long ouId) {
        Map<String, Object> map = new HashMap<>();
        map.put("ouId",ouId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(),  "/cacheDataOuUnitExt/getSeqPerfixByOuId",map);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return res.trim();
    }


}
