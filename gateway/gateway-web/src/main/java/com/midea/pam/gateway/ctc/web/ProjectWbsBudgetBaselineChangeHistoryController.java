package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.cache.WbsTemplateRuleCache;
import com.midea.pam.common.ctc.dto.ProjectHistoryBaseLineDto;
import com.midea.pam.common.ctc.dto.ProjectHistoryHeaderDto;
import com.midea.pam.common.ctc.vo.PreviewWbsBudgetChangeVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExcelUtil;
import com.midea.pam.common.util.ProjectWbsBudgetUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Api("项目wbs预算基线变更")
@RestController
@RequestMapping("projectWbsBudgetBaselineChangeHistory")
public class ProjectWbsBudgetBaselineChangeHistoryController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查看历史基线列表(wbs预算)")
    @GetMapping("v1/findHistoryBaselineList")
    public Response getProjectBaseLineHistory(@RequestParam @ApiParam("项目id") Long projectId) {
        Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectWbsBudgetBaselineChangeHistory/v1/findHistoryBaselineList", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<ProjectHistoryHeaderDto>>>() {
        });
    }

    @ApiOperation(value = "查看历史基线详情(wbs预算)")
    @GetMapping("v1/findHistoryBaselineDetail")
    public Response getProjectBaseLineHistoryDetail(@RequestParam @ApiParam("项目id") Long projectId, @RequestParam @ApiParam("变更头id") Long headerId) {
        Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        param.put("headerId", headerId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectWbsBudgetBaselineChangeHistory/v1/findHistoryBaselineDetail", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectHistoryBaseLineDto>>() {
        });
    }

    @ApiOperation(value = "预立项转正 - 查询WBS预算基线变更明细")
    @PostMapping("findPreviewWbsBudgetBaselineChangeHistory")
    public Response findPreviewWbsBudgetBaselineChangeHistory(@RequestBody Map<String, Object> param) {
        final String url = String.format("%sprojectWbsBudgetBaselineChangeHistory/findPreviewWbsBudgetBaselineChangeHistory", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, param, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<PreviewWbsBudgetChangeVo>>>() {
        });
    }

    @ApiOperation(value = "历史基线详情导出")
    @GetMapping("findHistoryBaselineDetail/export")
    public void findHistoryBaselineDetailExport(HttpServletResponse response, @RequestParam @ApiParam("项目id") Long projectId,
                                                @RequestParam @ApiParam("变更头id") Long headerId,
                                                @RequestParam @ApiParam("wbs模板信息id") Long wbsTemplateInfoId,
                                                HttpServletResponse servletResponse) throws Exception {
        Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        param.put("headerId", headerId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectWbsBudgetBaselineChangeHistory/findHistoryBaselineDetail/export", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();

        DataResponse<List<Map<String, Object>>> dataResponse = JSON.parseObject(res,
                new TypeReference<DataResponse<List<Map<String, Object>>>>() {
                });
        List<Map<String, Object>> dataList = dataResponse.getData();
        if (CollectionUtils.isEmpty(dataList)) {
            throw new Exception("没有详情数据");
        }

        try{
            String fileName = String.format("%s%s%s", "项目wbs预算基线_", DateUtils.format(new Date(), "yyyyMMddHHmmss"), ".xls");
            Workbook workBook = ExcelUtil.createWorkBook(fileName);
            Sheet sheet = ExcelUtil.createSheet(workBook, "项目wbs预算基线");
            CellStyle titleStyle = ExcelUtil.creatTitleStyle(workBook);
            CellStyle style = ExcelUtil.creatCellStyle(workBook);
            // wbs动态列
            List<WbsTemplateRuleCache> wbsCaches = ProjectWbsBudgetUtils.getEligibilityWbsCache(wbsTemplateInfoId);
            JSONArray dataJsonArray = JSONArray.parseArray(JSON.toJSONString(dataList));

            LinkedHashMap<String, String> titleMap = new LinkedHashMap<>();
            titleMap.put("number", "序号");
            titleMap.put("projectCode", "项目");
            if (!org.springframework.util.CollectionUtils.isEmpty(wbsCaches)) {
                for (WbsTemplateRuleCache wbsCacge : wbsCaches) {
                    titleMap.put(wbsCacge.getKey(), wbsCacge.getRuleName());
                }
            }
            titleMap.put("wbsNo", "WBS号");
            titleMap.put("description", "描述");
            titleMap.put("activityCode", "活动事项编码");
            titleMap.put("activityName", "类别名称");
            titleMap.put("activityType", "类别属性");
            titleMap.put("baselineCostString", "预算基线");
            titleMap.put("projectBaselineBatchCode", "预算基线批次");

            ExcelUtil.setExcelData(sheet, dataJsonArray, titleMap, null, null, null, titleStyle, style);
            ExcelUtil.downLoadExcel(fileName, servletResponse, workBook);

        }catch (Exception e) {
            e.printStackTrace();
        }

    }

}
