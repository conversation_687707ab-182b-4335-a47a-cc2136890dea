package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.ctc.dto.ProjectBudgetPushEmsErrorDto;
import com.midea.pam.common.ctc.excelVo.BudgetPushEmsExcelVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

@RestController
@RequestMapping("projectBudgetPushEmsError")
@Api("项目预算推送EMS异常")
public class ProjectBudgetPushEmsErrorController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "预算推送EMS异常查询", response = ProjectBudgetPushEmsErrorDto.class)
    @GetMapping({"pageBudgetPushEmsError"})
    public Response pageBudgetPushEmsError(
            @RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize,
            @RequestParam(required = false) @ApiParam(value = "项目编号") String projectCode,
            @RequestParam(required = false) @ApiParam(value = "项目名称") String projectName,
            @RequestParam(required = false) @ApiParam(value = "项目经理") String projectManager,
            @RequestParam(required = false) @ApiParam(value = "类型") String type,
            @RequestParam(required = false) @ApiParam(value = "状态") String statusStr,
            @RequestParam(required = false) @ApiParam(value = "推送失败原因") String resendErrorReason,
            @RequestParam(required = false) @ApiParam(value = "推送开始时间") String startTime,
            @RequestParam(required = false) @ApiParam(value = "推送结束时间") String endTime) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("projectCode", projectCode);
        param.put("projectName", projectName);
        param.put("projectManager", projectManager);
        param.put("type", type);
        param.put("statusStr", statusStr);
        param.put("resendErrorReason", resendErrorReason);
        param.put("startTime", startTime);
        param.put("endTime", endTime);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "projectBudgetPushEmsError/pageBudgetPushEmsError", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<ProjectBudgetPushEmsErrorDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<ProjectBudgetPushEmsErrorDto>>>() {
        });
        return response;
    }
}
