package com.midea.pam.gateway.ctc.web;

import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

@Api("销售合同开票回款计划变更审批回调")
@RestController
@RequestMapping("contractReceiptPlanChanage")
public class ContractReceiptPlanChanageCallBackController {
    @Resource
    private RestTemplate restTemplate;

    /**
     * 发起审批-流程回调
     *
     * @param formInstanceId 表单实例id
     */
    @ApiOperation(value = "发起审批")
    @PutMapping("approvaling/skipSecurityInterceptor")
    public Response draftSubmitCallback(@RequestParam(required = false) Long formInstanceId,
                                        @RequestParam(required = false) String fdInstanceId,
                                        @RequestParam(required = false) String formUrl,
                                        @RequestParam(required = false) String eventName,
                                        @RequestParam(required = false) String handlerId,
                                        @RequestParam(required = false) Long companyId,
                                        @RequestParam(required = false) Long createUserId) {
        String url = String.format("%scontractReceiptPlanChanage/approvaling/skipSecurityInterceptor" +
                        "?formInstanceId=%d" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%d" +
                        "&createUserId=%d",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    /**
     * 流程审批完成回调方法。
     *
     * @param formInstanceId 表单实例id
     */
    @ApiOperation(value = "通过")
    @PutMapping("approved/skipSecurityInterceptor")
    public Response passCallback(@RequestParam(required = false) Long formInstanceId,
                                 @RequestParam(required = false) String fdInstanceId,
                                 @RequestParam(required = false) String formUrl,
                                 @RequestParam(required = false) String eventName,
                                 @RequestParam(required = false) String handlerId,
                                 @RequestParam(required = false) Long companyId,
                                 @RequestParam(required = false) Long createUserId,
                                 @RequestParam(required = false) String timestamp) {
        String url = String.format("%scontractReceiptPlanChanage/approved/skipSecurityInterceptor" +
                        "?formInstanceId=%d" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%d" +
                        "&createUserId=%d" +
                        "&timestamp=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId, timestamp);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    /**
     * 流程审批驳回回调方法.
     *
     * @param formInstanceId 表单实例id
     */
    @ApiOperation(value = "驳回")
    @PutMapping("refused/skipSecurityInterceptor")
    public Response refuseCallback(@RequestParam(required = false) Long formInstanceId,
                                   @RequestParam(required = false) String fdInstanceId,
                                   @RequestParam(required = false) String formUrl,
                                   @RequestParam(required = false) String eventName,
                                   @RequestParam(required = false) String handlerId,
                                   @RequestParam(required = false) Long companyId,
                                   @RequestParam(required = false) Long createUserId) {
        String url = String.format("%scontractReceiptPlanChanage/refused/skipSecurityInterceptor" +
                        "?formInstanceId=%d" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%d" +
                        "&createUserId=%d",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }


    /**
     * 流程审批撤回回调方法.
     *
     * @param formInstanceId 表单实例id
     */
    @ApiOperation(value = "撤回")
    @PutMapping("return/skipSecurityInterceptor")
    public Response draftReturnCallback(@RequestParam(required = false) Long formInstanceId,
                                        @RequestParam(required = false) String fdInstanceId,
                                        @RequestParam(required = false) String formUrl,
                                        @RequestParam(required = false) String eventName,
                                        @RequestParam(required = false) String handlerId,
                                        @RequestParam(required = false) Long companyId,
                                        @RequestParam(required = false) Long createUserId) {
        String url = String.format("%scontractReceiptPlanChanage/return/skipSecurityInterceptor" +
                        "?formInstanceId=%d" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%d" +
                        "&createUserId=%d",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }


    /**
     * 流程审批废弃回调方法.
     *
     * @param formInstanceId 表单实例id
     */
    @ApiOperation(value = "作废")
    @PutMapping("abandon/skipSecurityInterceptor")
    public Response abandon(@RequestParam(required = false) Long formInstanceId,
                            @RequestParam(required = false) String fdInstanceId,
                            @RequestParam(required = false) String formUrl,
                            @RequestParam(required = false) String eventName,
                            @RequestParam(required = false) String handlerId,
                            @RequestParam(required = false) Long companyId,
                            @RequestParam(required = false) Long createUserId) {
        String url = String.format("%scontractReceiptPlanChanage/abandon/skipSecurityInterceptor" +
                        "?formInstanceId=%d" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%d" +
                        "&createUserId=%d",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }


    /**
     * 流程审批删除回调方法.
     *
     * @param formInstanceId 表单实例id
     */
    @ApiOperation(value = "删除")
    @PutMapping("delete/skipSecurityInterceptor")
    public Response delete(@RequestParam(required = false) Long formInstanceId,
                           @RequestParam(required = false) String fdInstanceId,
                           @RequestParam(required = false) String formUrl,
                           @RequestParam(required = false) String eventName,
                           @RequestParam(required = false) String handlerId,
                           @RequestParam(required = false) Long companyId,
                           @RequestParam(required = false) Long createUserId) {
        String url = String.format("%scontractReceiptPlanChanage/delete/skipSecurityInterceptor" +
                        "?formInstanceId=%d" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%d" +
                        "&createUserId=%d",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "处理人通过")
    @PutMapping("agree/skipSecurityInterceptor")
    public Response agree(@RequestParam(required = false) Long formInstanceId,
                          @RequestParam(required = false) String fdInstanceId,
                          @RequestParam(required = false) String formUrl,
                          @RequestParam(required = false) String eventName,
                          @RequestParam(required = false) String handlerId,
                          @RequestParam(required = false) Long companyId,
                          @RequestParam(required = false) Long createUserId) {
        String url = String.format("%scontractReceiptPlanChanage/agree/skipSecurityInterceptor" +
                        "?formInstanceId=%d" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%d" +
                        "&createUserId=%d",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }
}
