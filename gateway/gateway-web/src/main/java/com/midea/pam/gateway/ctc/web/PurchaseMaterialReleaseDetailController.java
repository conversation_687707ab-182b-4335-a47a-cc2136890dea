package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.ProjectMilepostDto;
import com.midea.pam.common.ctc.dto.PurchaseMaterialReleaseDetailDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Api("物料采购需求发布明细")
@RestController
@RequestMapping("purchaseMaterialReleaseDetail")
public class PurchaseMaterialReleaseDetailController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "采购物料需求发布明细分页", response = PurchaseMaterialReleaseDetailDto.class)
    @GetMapping({"selectPage"})
    public Response selectPage(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                               @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize,
                               @RequestParam @ApiParam(value = "物料需求id") Long purchaseRequirementId,
                               @RequestParam(required = false, defaultValue = "0") @ApiParam(value = "发布日期排序是否正序") Boolean publishTimeIsOrderAsc) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("purchaseRequirementId", purchaseRequirementId);
        param.put("publishTimeIsOrderAsc", publishTimeIsOrderAsc);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseMaterialReleaseDetail/selectPage", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<PurchaseMaterialReleaseDetailDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<PurchaseMaterialReleaseDetailDto>>>() {
        });
        return response;
    }

}
