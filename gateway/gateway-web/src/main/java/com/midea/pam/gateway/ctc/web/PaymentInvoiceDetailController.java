package com.midea.pam.gateway.ctc.web;

import com.midea.pam.common.ctc.dto.PaymentInvoiceDetailDto;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

@Api("采购发票明细")
@RestController
@RequestMapping("ctc/paymentInvoiceDetail")
public class PaymentInvoiceDetailController extends ControllerHelper {

    @ApiOperation(value = "采购发票明细分页", response = PaymentInvoiceDetailDto.class)
    @GetMapping({"selectPageWithDetail"})
    public void selectPage(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                           @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize,
                           @RequestParam(required = false) @ApiParam(value = "模糊:采购发票编号") String fuzzyInvoiceDetailCode,
                           @RequestParam(required = false) @ApiParam(value = "模糊:采购合同名称") String fuzzyPurchaseContractName,
                           @RequestParam(required = false) @ApiParam(value = "模糊:采购合同编号") String fuzzyPurchaseContractCode,
                           @RequestParam(required = false) @ApiParam(value = "模糊:供应商名称") String fuzzyVendorName,
                           @RequestParam(required = false) @ApiParam(value = "发票类型") String invoiceType,
                           @RequestParam(required = false) @ApiParam(value = "发票属性") String attribute,
                           @RequestParam(required = false) @ApiParam(value = "发票日期开始") Date invoiceDateStart,
                           @RequestParam(required = false) @ApiParam(value = "发票日期结束") Date invoiceDateEnd,
                           @RequestParam(required = false) @ApiParam(value = "录入日期开始") Date createAtStart,
                           @RequestParam(required = false) @ApiParam(value = "录入日期结束") Date createAtEnd,
                           @RequestParam(required = false) @ApiParam(value = "发票状态") String invoiceStatus,
                           @RequestParam(required = false) @ApiParam(value = "采购合同id") Long purchaseContractId,
                           @RequestParam(required = false) @ApiParam(value = "采购合同编号") String purchaseContractCode) {
    }

    @ApiOperation(value = "采购发票明细", response = PaymentInvoiceDetailDto.class)
    @GetMapping({"view"})
    public void view(@RequestParam Long id,@RequestParam(required = false)Integer editViewFlag) {
    }

    @ApiOperation(value = "删除采购发票明细", response = Boolean.class)
    @GetMapping({"deleteById"})
    public void deleteById(@RequestParam Long id) {
    }

    @ApiOperation(value = "批量保存", response = PaymentInvoiceDetailDto.class)
    @PostMapping({"batchSave"})
    public void batchSave(@RequestBody List<PaymentInvoiceDetailDto> dtos) {
    }

    @ApiOperation(value = "采购发票明细导出")
    @GetMapping("/export")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false) @ApiParam(value = "模糊:采购发票编号") String fuzzyInvoiceDetailCode,
                       @RequestParam(required = false) @ApiParam(value = "模糊:采购合同名称") String fuzzyPurchaseContractName,
                       @RequestParam(required = false) @ApiParam(value = "模糊:采购合同编号") String fuzzyPurchaseContractCode,
                       @RequestParam(required = false) @ApiParam(value = "模糊:供应商名称") String fuzzyVendorName,
                       @RequestParam(required = false) @ApiParam(value = "发票类型") String invoiceType,
                       @RequestParam(required = false) @ApiParam(value = "发票属性") String attribute,
                       @RequestParam(required = false) @ApiParam(value = "发票日期开始") Date invoiceDateStart,
                       @RequestParam(required = false) @ApiParam(value = "发票日期结束") Date invoiceDateEnd,
                       @RequestParam(required = false) @ApiParam(value = "录入日期开始") Date createAtStart,
                       @RequestParam(required = false) @ApiParam(value = "录入日期结束") Date createAtEnd,
                       @RequestParam(required = false) @ApiParam(value = "发票状态") String invoiceStatus)  {

    }

    @ApiOperation(value = "删除罚扣")
    @GetMapping("/delPunishment")
    public void delPunishment(
                           @RequestParam(required = true) @ApiParam(value = "关联关系表主键") Long id,
                           @RequestParam(required = true) @ApiParam(value = "罚扣ID") Long punishmentId,
                           @RequestParam(required = true) @ApiParam(value = "税票ID") Long invoiceId,
                           @RequestParam(required = false) @ApiParam(value = "税票类型 1.普通税票,2.罚扣税票") Integer invoiceType
    ){

    }
}
