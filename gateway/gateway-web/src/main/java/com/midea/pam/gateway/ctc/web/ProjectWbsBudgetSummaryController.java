package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Api("项目wbs预算汇总")
@RestController
@RequestMapping("projectWbsBudgetSummary")
public class ProjectWbsBudgetSummaryController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "项目详情-wbs预算 查询列表", notes = "查明细列表")
    @PostMapping("findByWbsBudgetList")
    public Response findByWbsBudgetList(@RequestBody Map<String, Object> param) {
        final String url = String.format("%sprojectWbsBudgetSummary/findByWbsBudgetList",
                ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, param, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<Map>>>() {
        });
    }

    @ApiOperation(value = "项目详情-wbs预算 查询树", notes = "查汇总表，再组装树")
    @PostMapping("findByWbsBudgetSummary")
    public Response findByWbsBudgetSummary(@RequestBody Map<String, Object> param) {
        final String url = String.format("%sprojectWbsBudgetSummary/findByWbsBudgetSummary",
                ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, param, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<Map>>>() {
        });
    }

    @ApiOperation(value = "项目详情-actvity预算 查询树", notes = "查汇总表，再组装树")
    @PostMapping("findByActivityBudgetSummary")
    public Response findByActivityBudgetSummary(@RequestBody Map<String, Object> param) {
        final String url = String.format("%sprojectWbsBudgetSummary/findByActivityBudgetSummary",
                ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, param, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<Map>>>() {
        });
    }

}
