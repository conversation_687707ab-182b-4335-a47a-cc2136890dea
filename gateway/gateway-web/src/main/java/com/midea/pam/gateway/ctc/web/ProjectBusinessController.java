package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.constants.AduitAtta;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.AssetDto;
import com.midea.pam.common.ctc.dto.DeliverChangeProject;
import com.midea.pam.common.ctc.dto.ProjectBudgetChangeDTO;
import com.midea.pam.common.ctc.dto.ProjectBudgetMaterialDto;
import com.midea.pam.common.ctc.dto.ProjectBusinessRsDto;
import com.midea.pam.common.ctc.dto.ProjectChangeDto;
import com.midea.pam.common.ctc.dto.ProjectContractRsDto;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.ProjectHistoryHeaderDto;
import com.midea.pam.common.ctc.dto.ProjectManagersOrfinancialsChangeDto;
import com.midea.pam.common.ctc.dto.ProjectMemberDto;
import com.midea.pam.common.ctc.dto.ProjectMilepostDto;
import com.midea.pam.common.ctc.dto.ProjectProfitDto;
import com.midea.pam.common.ctc.dto.ProjectTerminationDTO;
import com.midea.pam.common.ctc.dto.ReceiptPlanDetailDTO;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectBusinessRs;
import com.midea.pam.common.ctc.entity.ProjectMember;
import com.midea.pam.common.ctc.entity.ProjectProfit;
import com.midea.pam.common.ctc.entity.ProjectProgressPredict;
import com.midea.pam.common.ctc.entity.ProjectResourceRel;
import com.midea.pam.common.ctc.vo.BusinessVO;
import com.midea.pam.common.ctc.vo.MaterialStatisticsVO;
import com.midea.pam.common.ctc.vo.ProjectCycleTimeVo;
import com.midea.pam.common.ctc.vo.ProjectIncomeCostPlanVO;
import com.midea.pam.common.ctc.vo.ProjectPackageChangeHistoryVO;
import com.midea.pam.common.ctc.vo.ProjectVO;
import com.midea.pam.common.ctc.vo.ProjectWbsBudgetChangeVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.ProcessTemplate;
import com.midea.pam.common.enums.ProjectReopenEnum;
import com.midea.pam.common.gateway.entity.FileInfo;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.FormInstanceExample;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.mapper.FileInfoMapper;
import com.midea.pam.gateway.remote.ProjectRemoteServer;
import com.midea.pam.gateway.service.FormInstanceService;
import com.midea.pam.gateway.service.FormTemplateService;
import com.midea.pam.gateway.service.MipWorkflowService;
import com.midea.pam.gateway.service.OssService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Api("项目业务")
@RestController
@RequestMapping(value = {"project", "mobile/app/project"})
public class ProjectBusinessController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private MipWorkflowService mipWorkflowService;
    @Resource
    private FileInfoMapper fileInfoMapper;
    @Resource
    private OssService ossService;
    @Resource
    private FormTemplateService formTemplateService;
    @Resource
    private ProjectRemoteServer projectRemoteServer;
    @Resource
    private FormInstanceService formInstanceService;


    @ApiOperation(value = "自动生成里程碑")
    @GetMapping("autoBuildProjectMilepost")
    public Response autoBuildProjectMilepost(@RequestParam String startTime,
                                             @RequestParam String endTime,
                                             @RequestParam Long userId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("startTime", startTime);
        param.put("endTime", endTime);
        param.put("userId", userId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/autoBuildProjectMilepost", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<ProjectMilepostDto>>>() {
        });
    }

    @ApiOperation(value = "自动生成项目快照")
    @GetMapping("autoBuildProjectSnapshot")
    public Response autoBuildProjectSnapshot(@RequestParam String projectIdStr,
                                             @RequestParam(required = false) Integer projectChangeType) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectIdStr", projectIdStr);
        param.put("projectChangeType", projectChangeType);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/autoBuildProjectSnapshot", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation(value = "新增/修改项目")
    @PostMapping("persistence")
    public Response persistence(@RequestBody ProjectDto projectDto) {
        final String url = String.format("%sproject/persistence", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, projectDto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Long>>() {
        });
    }

    @ApiOperation(value = "保存草稿")
    @PostMapping("draft")
    public Response draft(@RequestBody ProjectDto projectDto) {
        ResponseEntity<String> responseEntity;
        /* 避免重复提交，利用redis锁限制保存 */
        String uuid = UUID.randomUUID().toString();
        StringBuilder lockKey = new StringBuilder("PAM:CTC:lock:project:save:" + projectDto.getProjectNameOrCode());
        try {
            // 等待时间 1秒
            long waitTime = 2 * 1000L;
            // 锁过期时间 30秒
            long expireTime = 30 * 1000L;
            if (DistributedCASLock.lock(lockKey.toString(), uuid, waitTime, expireTime)) {
                final String url = String.format("%sproject/draft", ModelsEnum.CTC.getBaseUrl());
                responseEntity = restTemplate.postForEntity(url, projectDto, String.class);
            } else {
                throw new ApplicationBizException("当前项目正在保存中，请稍后再试");
            }
        } finally {
            DistributedCASLock.unLock(lockKey.toString(), uuid);
        }
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Long>>() {
        });
    }

    @ApiOperation(value = "保存")
    @PostMapping("save")
    public Response save(@RequestBody ProjectDto projectDto) {
        ResponseEntity<String> responseEntity;
        /* 避免重复提交，利用redis锁限制保存 */
        String uuid = UUID.randomUUID().toString();
        StringBuilder lockKey = new StringBuilder("PAM:CTC:lock:project:save:" + projectDto.getProjectNameOrCode());
        try {
            // 等待时间 1秒
            long waitTime = 2 * 1000L;
            // 锁过期时间 30秒
            long expireTime = 30 * 1000L;
            if (DistributedCASLock.lock(lockKey.toString(), uuid, waitTime, expireTime)) {
                final String url = String.format("%sproject/save", ModelsEnum.CTC.getBaseUrl());
                responseEntity = restTemplate.postForEntity(url, projectDto, String.class);
            } else {
                throw new ApplicationBizException("当前项目正在保存中，请稍后再试");
            }
        } finally {
            DistributedCASLock.unLock(lockKey.toString(), uuid);
        }
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<ProjectVO>>() {
        });
    }

    @ApiOperation(value = "发起转正")
    @PostMapping("savePreview")
    public Response savePreview(@RequestBody ProjectDto projectDto) {
        final String url = String.format("%sproject/savePreview", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, projectDto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<ProjectVO>>() {
        });
    }

    @ApiOperation(value = "变更项目基础信息")
    @PostMapping("baseInfoChange")
    public Object baseInfoChange(@RequestBody ProjectDto projectDto) {
        final String url = String.format("%sproject/baseInfoChange", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, projectDto, String.class).getBody();
    }

    @ApiOperation(value = "变更项目关联合同")
    @PostMapping("contractRsChange")
    public Response contractRsChange(@RequestBody ProjectDto projectDto) {
        final String url = String.format("%sproject/contractRsChange", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, projectDto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<ProjectVO>>() {
        });
    }

    @ApiOperation(value = "变更项目关联资产")
    @PostMapping("assetRsChange")
    public Response assetRsChange(@RequestBody ProjectDto projectDto) {
        final String url = String.format("%sproject/assetRsChange", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, projectDto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Long>>() {
        });
    }

    @ApiOperation(value = "变更项目目标成本")
    @PostMapping("budgetTargetChange")
    public Object budgetTargetChange(@RequestBody ProjectChangeDto projectDto) {
        final String url = String.format("%sproject/budgetTargetChange", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, projectDto, String.class).getBody();
    }

    @ApiOperation(value = "批量变更项目经理和财务经理")
    @PostMapping("updateManagersOrfinancials")
    public Object updateManagersOrfinancials(@RequestBody ProjectManagersOrfinancialsChangeDto projectDtos) {
        final String url = String.format("%sproject/updateManagersOrfinancials", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, projectDtos, String.class).getBody();
    }

    @ApiOperation(value = "变更项目里程碑信息")
    @PostMapping("milepostChange")
    public Object milepostChange(@RequestBody ProjectDto projectDto) {
        final String url = String.format("%sproject/milepostChange", ModelsEnum.CTC.getBaseUrl());
        return restTemplate.postForEntity(url, projectDto, String.class).getBody();
    }

    @ApiOperation(value = "按id查询项目")
    @GetMapping("findById")
    public Response findById(@RequestParam Long id, @RequestParam(required = false) String flag) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("flag", flag);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/findById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectDto>>() {
        });
    }

    @ApiOperation(value = "按id获取项目转正的变更详情")
    @GetMapping("findPreviewInfoById")
    public Response findPreviewInfoById(@RequestParam Long id, @RequestParam(required = false) String flag) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("flag", flag);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/findPreviewInfoById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectDto>>() {
        });
    }

    @ApiOperation(value = "获取项目关联合同变更详情")
    @GetMapping("findContractRsChangeInfo")
    public Response findContractRsChangeInfo(@RequestParam Long headId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("headId", headId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/findContractRsChangeInfo", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectDto>>() {
        });
    }

    @ApiOperation(value = "按id查询项目基本信息")
    @GetMapping("getBaseInfoById")
    public Response getBaseInfoById(@RequestParam Long id, @RequestParam(required = false) String flag) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("flag", flag);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/getBaseInfoById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectDto>>() {
        });
    }

    @ApiOperation(value = "按id查询项目预算信息")
    @GetMapping("getBudgetInfoById")
    public Response getBudgetInfoById(@RequestParam Long id, @RequestParam(required = false) String flag) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("flag", flag);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/getBudgetInfoById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectDto>>() {
        });
    }

    @ApiOperation(value = "按项目id查询项目wbs预算信息")
    @GetMapping("getWbsBudgetInfoById")
    public Response getWbsBudgetInfoById(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/getWbsBudgetInfoById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectDto>>() {
        });
    }

    @ApiOperation(value = "按id查询项目成员信息")
    @GetMapping("getMemberInfoById")
    public Response getMemberInfoById(@RequestParam Long id, @RequestParam(required = false) String flag) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("flag", flag);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/getMemberInfoById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectDto>>() {
        });
    }

    @ApiOperation(value = "按id查询项目收入成本")
    @GetMapping("getIncomeCostPlansById")
    public Response getIncomeCostPlansById(@RequestParam Long id, @RequestParam(required = false) String flag) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("flag", flag);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/getIncomeCostPlansById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectDto>>() {
        });
    }

    @ApiOperation(value = "按id查询项目里程碑信息")
    @GetMapping("getMilepostInfoById")
    public Response getMilepostInfoById(@RequestParam Long id, @RequestParam(required = false) String flag) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("flag", flag);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/getMilepostInfoById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectDto>>() {
        });
    }

    @ApiOperation(value = "按id查询项目立项/转正数据快照")
    @GetMapping("findProjectSnapshot")
    public Response findProjectSnapshot(@RequestParam Long projectId,
                                        @RequestParam(required = false) Integer snapshotType) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        param.put("snapshotType", snapshotType);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/findProjectSnapshot", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectDto>>() {
        });
    }

    @ApiOperation(value = "分页获取我参与的项目", response = Project.class)
    @GetMapping("pageMyProjects")
    public Response pageMyProjects(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                   @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize,
                                   @RequestParam(required = false, defaultValue = "true") @ApiParam(value = "是否查询全部") Boolean isAll) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("isAll", isAll);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "project/pageMyProjects", param);
        String res = restTemplate.getForEntity(fromHttpUrl(url), String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<ProjectDto>>>() {
        });
    }

    @ApiOperation(value = "获取我参与的项目", response = Project.class)
    @GetMapping("getMyProjects")
    public Response getMyProjects(@RequestParam(required = false) @ApiParam(value = "项目状态列表") String status,
                                  @RequestParam(required = false) @ApiParam(value = "项目经理") String managerName,
                                  @RequestParam(required = false) @ApiParam(value = "工时类型（1项目/2商机）") Integer businessFlag,
                                  @RequestParam(required = false) @ApiParam(value = "单位id") Long unitId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("status", status);
        param.put("businessFlag", businessFlag);
        param.put("managerName", managerName);
        param.put("unitId", unitId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "project/getMyProjects", param);
        String res = restTemplate.getForEntity(fromHttpUrl(url), String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<ProjectDto>>>() {
        });
    }

    @ApiOperation(value = "获取我管理的项目", response = Project.class)
    @GetMapping("getMyChargeProjects")
    public Response getMyChargeProjects(@RequestParam(required = false) @ApiParam(value = "项目状态列表") String status) {
        final Map<String, Object> param = new HashMap<>();
        param.put("status", status);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "project/getMyChargeProjects", param);
        String res = restTemplate.getForEntity(fromHttpUrl(url), String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<ProjectDto>>>() {
        });
    }

    @ApiOperation(value = "根据项目经理/虚拟部门查询我的项目", response = Project.class)
    @GetMapping("getMyProjectsByUnit")
    public Response getMyProjectsByUnit() {
        final Map<String, Object> param = new HashMap<>();
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "project/getMyProjectsByUnit", param);
        String res = restTemplate.getForEntity(fromHttpUrl(url), String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<ProjectDto>>>() {
        });
    }

    @ApiOperation(value = "根据项目ID获取项目成员", response = ProjectMember.class)
    @GetMapping("getMembersByProjectId/{projectId}")
    public Response getMembersByProjectId(@PathVariable Long projectId) {
        final String url = ModelsEnum.CTC.getBaseUrl() + "/project/getMembersByProjectId/" + projectId;
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<ProjectMember>>>() {
        });
    }

    @ApiOperation(value = "获取工时修改的项目成员", response = ProjectMember.class)
    @GetMapping("getMembersForProject/{projectId}")
    public Response getMembersForProject(@PathVariable Long projectId) {
        final String url = ModelsEnum.CTC.getBaseUrl() + "/project/getMembersForProject/" + projectId;
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<ProjectMember>>>() {
        });
    }

    @ApiOperation(value = "根据项目id获取历史更改信息列表", response = ProjectHistoryHeaderDto.class)
    @GetMapping("getChangeHistoryList")
    public Object getChangeHistoryList(@RequestParam Long projectId,
                                       @RequestParam(required = false) String changeType,
                                       @RequestParam(required = false) Date beginTime,
                                       @RequestParam(required = false) Date endTime,
                                       @RequestParam(required = false) Integer status,
                                       @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                       @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = new HashMap<>();
        params.put(Constants.Page.PAGE_NUM, pageNum);
        params.put(Constants.Page.PAGE_SIZE, pageSize);
        params.put("projectId", projectId);
        params.put("changeType", changeType);
        params.put("beginTime", beginTime);
        params.put("endTime", endTime);
        params.put("status", status);

        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/getChangeHistoryList", params);
        return restTemplate.getForEntity(url, String.class).getBody();
    }

    @ApiOperation(value = "获取基础信息变更历史信息详情", response = ProjectPackageChangeHistoryVO.class)
    @GetMapping("getBaseChangeHistory")
    public Object getBaseChangeHistory(@RequestParam Long headId) {
        final Map<String, Object> params = new HashMap<>();
        params.put("headId", headId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/getBaseChangeHistory", params);
        return restTemplate.getForEntity(url, String.class).getBody();
    }

    @ApiOperation(value = "获取关联合同变更历史信息详情", response = ProjectPackageChangeHistoryVO.class)
    @GetMapping("getContractRsChangeHistory")
    public Object getContractRsChangeHistory(@RequestParam Long headId) {
        final Map<String, Object> params = new HashMap<>();
        params.put("headId", headId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/getContractRsChangeHistory", params);
        return restTemplate.getForEntity(url, String.class).getBody();
    }

    @ApiOperation(value = "获取关联商机变更历史信息详情", response = ProjectPackageChangeHistoryVO.class)
    @GetMapping("getBusinessRsChangeHistory")
    public Object getBusinessRsChangeHistory(@RequestParam Long headId) {
        final Map<String, Object> params = new HashMap<>();
        params.put("headId", headId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/getBusinessRsChangeHistory", params);
        return restTemplate.getForEntity(url, String.class).getBody();
    }

    @ApiOperation(value = "获取关联资产变更历史信息详情", response = ProjectPackageChangeHistoryVO.class)
    @GetMapping("getAssetRsChangeHistory")
    public Object getAssetRsChangeHistory(@RequestParam Long headId) {
        final Map<String, Object> params = new HashMap<>();
        params.put("headId", headId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/getAssetRsChangeHistory", params);
        return restTemplate.getForEntity(url, String.class).getBody();
    }

    @ApiOperation(value = "获取目标成本变更历史信息详情", response = ProjectPackageChangeHistoryVO.class)
    @GetMapping("getBudgetTargetChangeHistory")
    public Object getBudgetTargetChangeHistory(@RequestParam Long headId) {
        final Map<String, Object> params = new HashMap<>();
        params.put("headId", headId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/getBudgetTargetChangeHistory", params);
        return restTemplate.getForEntity(url, String.class).getBody();
    }

    @ApiOperation(value = "获取基础信息批量变更历史信息详情", response = ProjectPackageChangeHistoryVO.class)
    @GetMapping("getBaseBatchChangeHistory")
    public Object getBaseBatchChangeHistory(@RequestParam Long batch) {
        final Map<String, Object> params = new HashMap<>();
        params.put("batch", batch);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/getBaseBatchChangeHistory", params);
        return restTemplate.getForEntity(url, String.class).getBody();
    }

    @ApiOperation(value = "获取基础信息变更历史信息详情", response = ProjectPackageChangeHistoryVO.class)
    @GetMapping("getMilepostChangeHistory")
    public Object getMilepostChangeHistory(@RequestParam Long headId) {
        final Map<String, Object> params = new HashMap<>();
        params.put("headId", headId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/getMilepostChangeHistory", params);
        return restTemplate.getForEntity(url, String.class).getBody();
    }

    @Deprecated
    @ApiOperation(value = "分页查询我的项目")
    @GetMapping("selectPage")
    public Response selectPage(@RequestParam(required = false) String name,
                               @RequestParam(required = false) String code,
                               @RequestParam(required = false) Integer status,
                               @RequestParam(required = false) String customerName,
                               @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                               @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = new HashMap<>();
        params.put(Constants.Page.PAGE_NUM, pageNum);
        params.put(Constants.Page.PAGE_SIZE, pageSize);
        params.put(Constants.Page.NAME, name);
        params.put(Constants.Page.CODE, code);
        params.put(Constants.Page.STATUS, status);
        params.put(Constants.Page.CUSTOMER_NAME, customerName);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/selectPage", params);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<ProjectDto>>>() {
        });
    }

    @ApiOperation(value = "分页查询项目列表")
    @GetMapping("list")
    public Response list(ProjectDto projectDto,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = new HashMap<>();
        // 项目编号、项目名称、业务实体、客户名称、客户CRM编码、销售子合同编号、商机编号、“项目经理”模糊查询
        // "项目类型”、“项目属性”、项目状态、是否预立项使用下拉搜索(多选);
        params.put("code", projectDto.getCode());
        params.put("name", projectDto.getName());
        params.put("ouIds", projectDto.getOuIds());
        params.put("customerName", projectDto.getCustomerName());
        params.put("customerCode", projectDto.getCustomerCode());
        params.put("contractCode", projectDto.getContractCode());
        params.put("businessCode", projectDto.getBusinessCode());
        params.put("businessId", projectDto.getBusinessId());
        params.put("managerName", projectDto.getManagerName());
        params.put("types", projectDto.getTypes());
        params.put("priceTypes", projectDto.getPriceTypes());
        params.put("statuses", projectDto.getStatuses());
        params.put("previewFlags", projectDto.getPreviewFlags());

        params.put("status", projectDto.getStatus());
        params.put("priceType", projectDto.getPriceType());
        params.put("type", projectDto.getType());
        params.put("ouId", projectDto.getOuId());
        params.put("previewFlag", projectDto.getPreviewFlag());

        params.put("typesStr", projectDto.getTypesStr());
        params.put("priceTypesStr", projectDto.getPriceTypesStr());
        params.put("statusesStr", projectDto.getStatusesStr());
        params.put("previewFlagsStr", projectDto.getPreviewFlagsStr());
        params.put("ouIdsStr", projectDto.getOuIdsStr());

        params.put("fuzzyLike", projectDto.getFuzzyLike());

        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/list", params);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<ProjectDto>>>() {
        });
    }

    @ApiOperation(value = "查询我创建项目列表")
    @GetMapping("createByMePage")
    public Response createByMePage(ProjectDto projectDto,
                                   @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                   @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = new HashMap<>();
        // 项目编号、项目名称、业务实体、客户名称、客户CRM编码、销售子合同编号、商机编号、“项目经理”模糊查询
        // "项目类型”、“项目属性”、项目状态、是否预立项使用下拉搜索(多选);
        params.put("code", projectDto.getCode());
        params.put("name", projectDto.getName());
        params.put("customerName", projectDto.getCustomerName());
        params.put("customerCode", projectDto.getCustomerCode());
        params.put("contractCode", projectDto.getContractCode());
        params.put("businessCode", projectDto.getBusinessCode());
        params.put("businessId", projectDto.getBusinessId());
        params.put("managerName", projectDto.getManagerName());
        params.put("types", projectDto.getTypes());
        params.put("priceTypes", projectDto.getPriceTypes());
        params.put("statuses", projectDto.getStatuses());
        params.put("previewFlags", projectDto.getPreviewFlags());
        params.put("ouIds", projectDto.getOuIds());

        params.put("status", projectDto.getStatus());
        params.put("priceType", projectDto.getPriceType());
        params.put("type", projectDto.getType());
        params.put("ouId", projectDto.getOuId());
        params.put("previewFlag", projectDto.getPreviewFlag());

        params.put("typesStr", projectDto.getTypesStr());
        params.put("priceTypesStr", projectDto.getPriceTypesStr());
        params.put("statusesStr", projectDto.getStatusesStr());
        params.put("previewFlagsStr", projectDto.getPreviewFlagsStr());
        params.put("ouIdsStr", projectDto.getOuIdsStr());

        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/createByMePage", params);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<ProjectDto>>>() {
        });
    }

    @ApiOperation(value = "财务查询待做收入成本计划项目列表")
    @GetMapping("financialPage")
    public Response financialPage(@RequestParam(required = false) String name,
                                  @RequestParam(required = false) String code,
                                  @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                  @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = new HashMap<>();
        params.put(Constants.Page.PAGE_NUM, pageNum);
        params.put(Constants.Page.PAGE_SIZE, pageSize);
        params.put(Constants.Page.NAME, name);
        params.put(Constants.Page.CODE, code);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/financialPage", params);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<ProjectDto>>>() {
        });
    }

    @ApiOperation(value = "查询收入成本计划")
    @GetMapping("findProjectProfit")
    public Response findProjectProfit(@RequestParam Long projectId) {
        final Map<String, Object> params = new HashMap<>();
        params.put(Constants.Page.PROJECT_ID, projectId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/findProjectProfit", params);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectProfit>>() {
        });
    }

    @ApiOperation(value = "收入成本计划详情")
    @GetMapping("financialDetail")
    public Response financialPage(@RequestParam(required = false) Long projectId) {
        final Map<String, Object> params = new HashMap<>();
        params.put(Constants.Page.PROJECT_ID, projectId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/financialDetail", params);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectProfitDto>>() {
        });
    }

    @ApiOperation(value = "新增/修改收入成本计划")
    @PostMapping("persistenceFinancial")
    public Response persistenceFinancial(@RequestBody ProjectProfitDto profit) {
        final String url = String.format("%sproject/persistenceFinancial", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, profit, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<ProjectProfit>>() {
        });
    }

    @ApiOperation(value = "员工工作情况查询")
    @GetMapping("memberSaturation")
    public Response memberSaturation(@RequestParam Long userId,
                                     @RequestParam Long projectId) {
        DataResponse<List<ProjectMemberDto>> response = Response.dataResponse();
        final Map<String, Object> params = new HashMap<>();
        params.put(Constants.Page.USER_ID, userId);
        params.put(Constants.Page.PROJECT_ID, projectId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/memberSaturation", params);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<ProjectMemberDto>>>() {
        });
    }

    @ApiOperation(value = "新增/修改成员")
    @PostMapping("persistenceMember")
    public Response persistenceMember(@RequestBody ProjectMember projectMember) {
        final String url = String.format("%sproject/persistenceMember", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, projectMember, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });

    }

    @ApiOperation(value = "新增/修改成员")
    @PostMapping("persistenceMembers")
    public Response persistenceMembers(@RequestBody ProjectDto ProjectDto) {
        final String url = String.format("%sproject/persistenceMembers", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, ProjectDto.getMembers(), String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Long>>() {
        });
    }

    @ApiOperation(value = "检查人员一天只能在一个项目")
    @PostMapping("checkMemberDistinct")
    public Response checkMemberDistinct(@RequestBody ProjectDto ProjectDto) {
        final String url = String.format("%sproject/checkMemberDistinct", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, ProjectDto.getMembers(), String.class);
        String res = cleanStr(responseEntity.getBody());
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "查询进行中的项目")
    @GetMapping("approvaled")
    public Response persistenceMembers(@ApiParam("模糊查询条件") @RequestParam(required = false) String fuzzyLike,
                                       @ApiParam("是否启用wbs") @RequestParam(required = false) Boolean wbsEnabled) {
        final Map<String, Object> params = new HashMap<>();
        params.put("fuzzyLike", fuzzyLike);
        params.put("wbsEnabled", wbsEnabled);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/approvaled", params);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<ProjectDto>>>() {
        });
    }

    @ApiOperation(value = "检查项目是否进行中", response = Boolean.class)
    @GetMapping("checkProjectIsApprovaled")
    public Response checkProjectIsApprovaled(@RequestParam @ApiParam("项目id") Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/ctc/project/checkProjectIsApprovaled", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "根据商机ID检查剩余报价成本是否全部带到项目成本", response = Boolean.class)
    @GetMapping("checkBusinessRemainCost")
    public Response checkBusinessRemainCost(@RequestParam @ApiParam("商机Code") String businessCode,
                                            @RequestParam(required = false) @ApiParam("项目关联子合同id") Long contractId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("businessCode", businessCode);
        param.put("contractId", contractId);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/ctc/project/checkBusinessRemainCost", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "判断当前商机报价是否必须用完")
    @PostMapping("isCostCanbeFullUsed")
    public Response isCostCanbeFullUsed(@RequestBody ProjectDto projectDto) {
        final String url = String.format("%sproject/isCostCanbeFullUsed", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, projectDto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<BusinessVO>>>() {
        });
    }

    @ApiOperation(value = "预算变更")
    @PostMapping("budgetChange")
    public Response budgetChange(@RequestBody ProjectBudgetChangeDTO projectBudgetChangeDTO) {
        return null;
    }

    @ApiOperation(value = "查询预算变更影响的审批通过工时")
    @PostMapping("getBudgetChangeWorkingHoursSummary")
    public Response getBudgetChangeWorkingHoursSummary(@RequestBody ProjectBudgetChangeDTO projectBudgetChangeDTO) {
        return null;
    }

    @ApiOperation(value = "移动审批获取预算变更")
    @GetMapping({"getProjectBudgetChangeApp"})
    public com.midea.pam.common.base.Response getProjectBudgetChangeApp(@RequestParam Long id) {
        String url = String.format("%sproject/getProjectBudgetChangeApp?id=%s", ModelsEnum.CTC.getBaseUrl(), id);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<ResponseMap> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<ResponseMap>>() {
        });
        if (dataResponse != null && dataResponse.getData() != null) {
            ResponseMap responseMap = dataResponse.getData();
            List<AduitAtta> fileList = responseMap.getFileList();
            if (CollectionUtils.isNotEmpty(fileList) && fileList.size() > 0) {
                fileList.forEach(aduitAtta -> {
                    FileInfo fileInfo = fileInfoMapper.selectByPrimaryKey(Long.valueOf(aduitAtta.getFdId()));
                    if (!ObjectUtils.isEmpty(fileInfo)) {
                        aduitAtta.setFileSize(String.valueOf(fileInfo.getFileSize()));
                        aduitAtta.setFileName(fileInfo.getFileName());
                    }
                });
            }
        }
        return dataResponse;
    }

    @ApiOperation(value = "移动审批获取WBS预算变更")
    @GetMapping({"getProjectWbsBudgetChangeApp"})
    public com.midea.pam.common.base.Response getProjectWbsBudgetChangeApp(@RequestParam Long id) {
        String url = String.format("%sproject/getProjectWbsBudgetChangeApp?id=%s", ModelsEnum.CTC.getBaseUrl(), id);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<ResponseMap> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<ResponseMap>>() {
        });
        if (dataResponse != null && dataResponse.getData() != null) {
            ResponseMap responseMap = dataResponse.getData();
            List<AduitAtta> fileList = responseMap.getFileList();
            if (CollectionUtils.isNotEmpty(fileList) && fileList.size() > 0) {
                fileList.forEach(aduitAtta -> {
                    FileInfo fileInfo = fileInfoMapper.selectByPrimaryKey(Long.valueOf(aduitAtta.getFdId()));
                    if (!ObjectUtils.isEmpty(fileInfo)) {
                        aduitAtta.setFileSize(String.valueOf(fileInfo.getFileSize()));
                        aduitAtta.setFileName(fileInfo.getFileName());
                    }
                });
            }
        }
        return dataResponse;
    }

    @ApiOperation(value = "预算变更操作校验")
    @PostMapping("budgetChange/check/{projectId}")
    public Response budgetChange(@PathVariable Long projectId) {
        return null;
    }

    @ApiOperation(value = "变更项目收入成本计划信息")
    @PostMapping("profitChange")
    public Response profitChange(@RequestBody ProjectDto projectDto) {
        return null;
    }

    @ApiOperation(value = "获取预算变更记录")
    @PostMapping("budgetChange/{headerId}")
    public Response getBudgetChangeDetail(@PathVariable Long headerId) {
        return null;
    }

    @ApiOperation(value = "获取预算变更记录")
    @PostMapping("wbsBudgetChange/{headerId}")
    public Response getWbsBudgetChangeDetail(@PathVariable Long headerId, @RequestBody ProjectWbsBudgetChangeVO data) {
        String url = String.format("%sproject/wbsBudgetChange/%s", ModelsEnum.CTC.getBaseUrl(), headerId);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, data, String.class);
        return JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<ProjectWbsBudgetChangeVO>>() {
                });
    }

    @ApiOperation(value = "项目成本")
    @PostMapping("cost/{projectId}")
    public Response getProjectCost(@PathVariable Long projectId) {
        return null;
    }

    @ApiOperation(value = "物料统计", response = MaterialStatisticsVO.class)
    @GetMapping("materialStatistics/{materialId}")
    public Response getMaterialStatistics(@PathVariable Long materialId) {
        return null;
    }

    /**
     * @param projectTypeId 项目类型id
     * @param subcontractId 子合同id
     * @return 项目周期时间
     */
    @ApiOperation(value = "获取项目周期时间")
    @GetMapping("findProjectCycleTime")
    public Response findProjectCycleTime(@RequestParam("projectTypeId") Long projectTypeId,
                                         @RequestParam("subcontractId") Long subcontractId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectTypeId", projectTypeId);
        param.put("subcontractId", subcontractId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/findProjectCycleTime", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectCycleTimeVo>>() {
        });
    }

    /**
     * @param projectTypeId 项目类型id
     * @return 服务项目类型，1表示saas服务，2表示非saas服务，月度确认类型，3表示其他服务
     */
    @ApiOperation(value = "获取服务项目类型")
    @GetMapping("findServerProjectType")
    public Response findServerProjectType(
            @RequestParam("projectTypeId") Long projectTypeId, @RequestParam("ouId") Long ouId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectTypeId", projectTypeId);
        param.put("ouId", ouId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/findServerProjectType", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
    }

    /**
     * @param beginTime       项目开始时间（字符串类型, yyyy-MM）
     * @param endTime         项目结束时间（字符串类型, yyyy-MM）
     * @param allIncomeAmount 主-里程碑收入总额
     * @param allCostAmount   主-里程碑预算总额
     * @return 月度成本收入计划集合
     */
    @ApiOperation(value = "计算月度成本收入计划")
    @GetMapping("countIncomeCostPlan")
    public Response countIncomeCostPlan(@RequestParam("beginTime") String beginTime,
                                        @RequestParam("endTime") String endTime,
                                        @RequestParam("allIncomeAmount") String allIncomeAmount,
                                        @RequestParam("allCostAmount") String allCostAmount) {
        final Map<String, Object> param = new HashMap<>();
        param.put("beginTime", beginTime);
        param.put("endTime", endTime);
        param.put("allIncomeAmount", allIncomeAmount);
        param.put("allCostAmount", allCostAmount);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/countIncomeCostPlan", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<ProjectIncomeCostPlanVO>>>() {
        });
    }


    @ApiOperation(value = "根据条件分页查询预立项项目列表")
    @GetMapping("listPre")
    public Response listPre(@RequestParam(required = true) Long managerId,
                            @RequestParam(required = true) Long projectTypeId,
                            @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                            @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = new HashMap<>();
        params.put(Constants.Page.PAGE_NUM, pageNum);
        params.put(Constants.Page.PAGE_SIZE, pageSize);
        params.put(Constants.Page.MANAGER_ID, managerId);
        params.put(Constants.Page.PROJECT_TYPE_ID, projectTypeId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/listPre", params);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<ProjectDto>>>() {
        });
    }

    /**
     * @param projectId 项目ID
     * @return 可用项目预算（不含税）
     */
    @ApiOperation(value = "获取可用项目预算（不含税）")
    @GetMapping("findAvailableProjectBudget")
    public Response findAvailableProjectBudget(@RequestParam("projectId") Long projectId,
                                               @RequestParam(value = "contractId", required = false) Long contractId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        param.put("contractId", contractId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/findAvailableProjectBudget", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<BigDecimal>>() {
        });
    }

    /**
     * @param projectId 项目ID
     * @return 可用项目预算（不含税）
     */
    @ApiOperation(value = "获取可用项目预算（不含税）以及对应明细")
    @GetMapping("findAvailableProjectBudgetDetail")
    public Response findAvailableProjectBudgetDetail(@RequestParam("projectId") Long projectId,
                                                     @RequestParam(value = "contractId", required = false) Long contractId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        param.put("contractId", contractId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/findAvailableProjectBudgetDetail", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<Map<String, Object>>>() {
                });
        return dataResponse;
    }

    /**
     * 流程审批完成回调方法。
     *
     * @param formInstanceId 表单实例id
     */
    @ApiOperation(value = "审批通过demo")
    @PutMapping("demo/approved")
    public Response approved(@RequestParam Long formInstanceId, @RequestParam Long companyId) {
        final String url = String.format("%sdemo/approved?formInstanceId=%s&companyId=%s",
                ModelsEnum.CTC.getBaseUrl(),
                formInstanceId,
                companyId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    /**
     * @param projectId 项目id
     * @return 时间信息
     */
    @ApiOperation(value = "获取收入成本计划总天数")
    @GetMapping("findProjectIncomeCostPlanTotalTime")
    public Response findProjectIncomeCostPlanTotalTime(@RequestParam("projectId") Long projectId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/findProjectIncomeCostPlanTotalTime", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectCycleTimeVo>>() {
        });
    }


    @ApiOperation("根据项目ID判断当前登录用户是否创建人,true是 false否")
    @GetMapping("/isCreater")
    public com.midea.pam.gateway.common.base.Response isCreater(@RequestParam(required = true) Long projectId) {
        String url = String.format("%sproject/isCreater?projectId=%s", ModelsEnum.CTC.getBaseUrl(), projectId);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<com.midea.pam.gateway.common.base.DataResponse<Boolean>>() {
        });
    }

    @ApiOperation("根据项目ID判断当前登录用户是否项目经理,true是 false否")
    @GetMapping("/isManager")
    public com.midea.pam.gateway.common.base.Response isManager(@RequestParam(required = true) Long projectId) {
        String url = String.format("%sproject/isManager?projectId=%s", ModelsEnum.CTC.getBaseUrl(), projectId);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<com.midea.pam.gateway.common.base.DataResponse<Boolean>>() {
        });
    }

    @ApiOperation("根据项目ID判断当前登录用户是项目成员,true是 false否")
    @GetMapping("/isMember")
    public com.midea.pam.gateway.common.base.Response isMember(@RequestParam(required = true) Long projectId) {
        String url = String.format("%sproject/isMember?projectId=%s", ModelsEnum.CTC.getBaseUrl(), projectId);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<com.midea.pam.gateway.common.base.DataResponse<Boolean>>() {
        });
    }

    @ApiOperation("根据项目ID判断当前登录用户是否财务人员,true是 false否")
    @GetMapping("/isFinance")
    public com.midea.pam.gateway.common.base.Response isFinance(@RequestParam(required = true) Long projectId) {
        String url = String.format("%sproject/isFinance?projectId=%s", ModelsEnum.CTC.getBaseUrl(), projectId);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<com.midea.pam.gateway.common.base.DataResponse<Boolean>>() {
        });
    }

    @ApiOperation("根据项目ID判断当前登录用户是否拥有数据权限,true是 false否")
    @GetMapping("/isDataAuthor")
    public com.midea.pam.gateway.common.base.Response isDataAuthor(@RequestParam(required = true) Long projectId) {
        String url = String.format("%sproject/isDataAuthor?projectId=%s", ModelsEnum.CTC.getBaseUrl(), projectId);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<com.midea.pam.gateway.common.base.DataResponse<Boolean>>() {
        });
    }

    @ApiOperation("根据里程碑ID判断当前登录用户是否责任人,true是 false否")
    @GetMapping("/isDeliverAuthor")
    public com.midea.pam.gateway.common.base.Response isDeliverAuthor(@RequestParam(required = true) Long milepostId) {
        String url = String.format("%sproject/isDeliverAuthor?milepostId=%s", ModelsEnum.CTC.getBaseUrl(), milepostId);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<com.midea.pam.gateway.common.base.DataResponse<Boolean>>() {
        });
    }

    @ApiOperation("判断当前登录用户是否有交付按钮权限,true是 false否")
    @GetMapping("/isDeliverPermission")
    public com.midea.pam.gateway.common.base.Response isDeliverPermission(@RequestParam(required = true) Long milepostId,
                                                                          @RequestParam(required = true) Long projectId) {
        String url = String.format("%sproject/isDeliverPermission?milepostId=%s&projectId=%s",
                ModelsEnum.CTC.getBaseUrl(),
                milepostId,
                projectId);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<com.midea.pam.gateway.common.base.DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "收入成本计划变更测试接口")
    @GetMapping("incomeCostPlanChange")
    public Response changeCost(@RequestParam(required = true) Long projectId,
                               @RequestParam(required = true) BigDecimal contractExcludingTaxAmount,
                               @RequestParam(required = true) Date contractStartDate,
                               @RequestParam(required = true) Date contractEndDate,
                               @RequestParam(required = false) BigDecimal mainIncomeAmount,
                               @RequestParam(required = false) BigDecimal helpIncomeAmount) {
        return null;
    }


    @ApiOperation(value = "type organization_custom_dict_20项目名称命名规则提示 organization_custom_dict_22预立项项目提交提示")
    @GetMapping("getProjectInfo")
    public com.midea.pam.gateway.common.base.Response getProjectInfo(@RequestParam String type) {
        String url = String.format("%sproject/getProjectInfo?type=%s", ModelsEnum.CTC.getBaseUrl(), type);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<com.midea.pam.gateway.common.base.DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "里程碑通知")
    @GetMapping("milepostNotice")
    public Response milepostNotice() {
        String url = String.format("%sproject/milepostNotice", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "根据RDM工时生成收入成本计划")
    @PostMapping("incomeCostPlan/create")
    public Response createIncomeCostPlan(@RequestParam String dateStr) {
        String url = String.format("%sproject/incomeCostPlan/create?dateStr=%s", ModelsEnum.CTC.getBaseUrl(), dateStr);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "按ou查询组织参数")
    @GetMapping("findOrganizationRel")
    public Response findOrganizationRel(@RequestParam Long ouId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("ouId", ouId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/findOrganizationRel", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<OrganizationRelDto>>() {
        });
    }

    @ApiOperation(value = "校验项目是否完成收入确认")
    @GetMapping("checkIncomeCompleted")
    public Response checkIncomeCompleted(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/checkIncomeCompleted", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation("作废项目")
    @PostMapping("saveProjectTermination")
    public Response saveProjectTermination(@RequestBody ProjectTerminationDTO dto) {
        // 项目信息
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", dto.getProjectId());
        final String url1 = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "projectReopen/getBaseInfoByProjectId", param);
        String res = restTemplate.getForEntity(url1, String.class).getBody();
        DataResponse<ProjectDto> response1 = JSON.parseObject(res, new TypeReference<DataResponse<ProjectDto>>() {
        });
        ProjectDto projectDto = response1.getData();
        if (projectDto.getReopenStatus() == ProjectReopenEnum.REOPEN.getCode()) {
            this.updateProjectFormInstance(dto.getProjectId());
        }

        String url = String.format("%sproject/saveProjectTermination", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<Boolean> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<Boolean>>() {
                });
        return response;
    }

    private void updateProjectFormInstance(Long projectId) {
        FormInstanceExample condition = new FormInstanceExample();
        condition.createCriteria().andFormInstanceIdEqualTo(projectId)
                .andDeletedFlagEqualTo(Boolean.FALSE)
                .andFormUrlEqualTo(ProcessTemplate.PROJECT_TERMINATE_APP.getCode());
        List<FormInstance> instances = formInstanceService.selectByExample(condition);
        if (instances.size() > 0) {
            instances.sort(Comparator.comparing(FormInstance::getId).reversed());
            FormInstance formInstance = instances.get(0);
            formInstance.setDeletedFlag(Boolean.TRUE);
            formInstanceService.updateByPrimaryKey(formInstance);
        }
    }

    @ApiOperation("删除模组时校验是否有有效采购订单")
    @GetMapping("checkPurchaseOrder")
    public Response checkPurchaseOrder(@RequestParam String ids) {
        final Map<String, Object> param = new HashMap<>();
        param.put("ids", ids);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/checkPurchaseOrder", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }


    @ApiOperation(value = "检验资源计划人员引用时间是否重叠")
    @PutMapping("checkResourceMember")
    public Response checkResourceMember(@RequestBody @ApiParam(name = "ProjectResourceRel", value = "项目成员引用信息") List<ProjectResourceRel> projectResourceRels) {
        String url = String.format("%sproject/checkResourceMember", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.exchange(url,
                HttpMethod.PUT,
                new HttpEntity<List<ProjectResourceRel>>(projectResourceRels),
                String.class);
        String res = cleanStr(responseEntity.getBody());
        return JSON.parseObject(res, new TypeReference<DataResponse<JSONObject>>() {
        });
    }

    @ApiOperation(value = "检查项目编号是否已经存在")
    @GetMapping("checkProjectCode")
    public Response checkProjectCode(@RequestParam String projectCode, @RequestParam Long unitId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectCode", projectCode);
        param.put("unitId", unitId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/checkProjectCode", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "里程碑交付时转质保期内项目前-数据查询")
    @GetMapping("deliverChangeBefore")
    public Response deliverChangeBefore(@RequestParam(required = true) Long projectId) {

        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/deliverChangeBefore", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Project>>() {
        });
    }

    @ApiOperation(value = "暂存里程碑转质保期项目数据")
    @PostMapping("/deliverChangeStage")
    public Response deliverChangeStage(@RequestBody DeliverChangeProject deliverChangeProject) {
        String url = String.format("%sproject/deliverChangeStage", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> stringResponseEntity = restTemplate.postForEntity(url,
                deliverChangeProject,
                String.class);
        return JSON.parseObject(stringResponseEntity.getBody(), new TypeReference<DataResponse<Void>>() {
        });
    }

    @ApiOperation(value = "查询暂存的里程碑转质保期项目数据")
    @GetMapping("/deliverChangeQuery")
    public Response deliverChangeQuery(@RequestParam Long milepostId, @RequestParam Long projectId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        param.put("milepostId", milepostId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/deliverChangeQuery", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<DeliverChangeProject>>() {
        });
    }

    @ApiOperation(value = "关联预算查询")
    @GetMapping("/relevanceBudget")
    public Response relevanceBudget(@RequestParam Long projectId,
                                    @RequestParam Boolean ext,
                                    @RequestParam(required = false) String searchValue) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        param.put("ext", ext);
        param.put("searchValue", searchValue);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/relevanceBudget", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<ProjectBudgetMaterialDto>>>() {
        });
    }

    @ApiOperation(value = "根据条件查询项目")
    @GetMapping("findProject")
    public Response findProject(@RequestParam(required = false) String id,
                                @RequestParam(required = false) String name,
                                @RequestParam(required = false) String code,
                                @RequestParam(required = false) Integer status,
                                @RequestParam(required = false) String customerName) {
        final Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put(Constants.Page.NAME, name);
        params.put(Constants.Page.CODE, code);
        params.put(Constants.Page.STATUS, status);
        params.put(Constants.Page.CUSTOMER_NAME, customerName);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/findProject", params);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<ProjectDto>>>() {
        });
    }

    @ApiOperation(value = "检测项目名字")
    @PostMapping("/checkProjectName")
    public Response checkProjectName(@RequestBody Project project) {
        return projectRemoteServer.checkProjectName(project.getName());
    }

    @ApiOperation(value = "根据里程碑阶段和合同查询是否关联回款计划")
    @GetMapping("checkReceiptPlanDetail")
    public Response checkReceiptPlanDetail(@RequestParam @ApiParam("合同id") Long contractId,
                                           @RequestParam @ApiParam("里程碑阶段id") Long milepostTemplateId,
                                           @RequestParam @ApiParam("里程碑阶段名") String milepostStage) {
        final Map<String, Object> param = new HashMap<>();
        param.put("contractId", contractId);
        param.put("milepostTemplateId", milepostTemplateId);
        param.put("milepostStage", milepostStage);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/checkReceiptPlanDetail", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ReceiptPlanDetailDTO>>() {
        });
    }

    @ApiOperation(value = "资产列表", notes = "场景：关联资产变更")
    @PostMapping("asset/page")
    public Response assetPage(@RequestBody AssetDto query) {
        String url = String.format("%sproject/asset/page", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> stringResponseEntity = restTemplate.postForEntity(url, query, String.class);
        return JSON.parseObject(stringResponseEntity.getBody(), new TypeReference<DataResponse<PageInfo<AssetDto>>>() {
        });
    }

    @ApiOperation(value = "查询项目关联的合同")
    @GetMapping("getContractRsByProjectId")
    public Response getContractRsByProjectId(@RequestParam @ApiParam("项目id") Long projectId) {
        Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/getContractRsByProjectId", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<ProjectContractRsDto>>>() {
        });
    }

    @ApiOperation(value = "查询项目关联的商机")
    @GetMapping("getBusinessRsByProjectId")
    public Response getBusinessRsByProjectId(@RequestParam @ApiParam("项目id") Long projectId) {
        Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/getBusinessRsByProjectId", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<ProjectBusinessRsDto>>>() {
        });
    }

    @ApiOperation(value = "查询预立项转转审批详情")
    @GetMapping("preProject/approve/detail")
    public Response getPreProjectBusinessRsProjectId(@RequestParam @ApiParam("项目id") Long projectId) {
        Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/preProject/approve/detail", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<ProjectBusinessRs>>>() {
        });
    }

    @ApiOperation(value = "判断项目是否存在未结转的收入节点", notes = "场景：关联合同变更")
    @GetMapping("checkCarryStatus")
    public Response checkCarryStatus(@RequestParam Long projectId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/checkCarryStatus", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "检验项目是否存在未冲销状态结转单")
    @GetMapping("checkIfHadCarryoverBill")
    public Response checkIfHadCarryoverBill(@RequestParam Long projectId, @RequestParam String currency) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        param.put("currency", currency);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/checkIfHadCarryoverBill", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "重新计算项目里程碑开始和结束时间", notes = "场景：关联合同变更")
    @PostMapping("recountMilepostTime")
    public Response recountMilepostTime(@RequestBody ProjectMilepostDto projectMilepostDto) {
        String url = String.format("%sproject/recountMilepostTime", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> stringResponseEntity = restTemplate.postForEntity(url, projectMilepostDto, String.class);
        return JSON.parseObject(stringResponseEntity.getBody(), new TypeReference<DataResponse<Void>>() {
        });
    }

    @ApiOperation(value = "判断用户是否能发起变更", notes = "场景：预算基线变更")
    @GetMapping("budgetBaselineChangeCheck")
    public Response budgetBaselineChangeCheck(@RequestParam Long projectId) {
        Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/budgetBaselineChangeCheck", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "项目进度预测新增")
    @PostMapping("projectProgressPredict/save")
    public Response projectProgressPredictSave(@RequestBody ProjectDto projectDto) {
        final String url = String.format("%projectProgressPredict/save", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, projectDto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<ProjectProgressPredict>>>() {
        });
    }

}
