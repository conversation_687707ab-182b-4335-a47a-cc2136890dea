package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.RevenueTargetInfoDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Api("数据字典")
@RequestMapping(value = {"revenueTargetInfo"})
public class RevenueTargetInfoController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation("修改收入目标配置")
    @PostMapping({"batchSave"})
    public Response batchSave(@RequestBody List<RevenueTargetInfoDto> revenueTargetInfoDtoList) {
        String url = String.format("%srevenueTargetInfo/batchSave",ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, revenueTargetInfoDtoList, String.class);
        DataResponse<List<RevenueTargetInfoDto>> dataResponse =  JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<RevenueTargetInfoDto>>>(){});
        return dataResponse;
    }
    
    @ApiOperation("根据类型+编码获取数据字典")
    @GetMapping({"selectList"})
    public Response selectList(@RequestParam Long parentUnitId, @RequestParam Integer targetYear, @RequestParam String orgType) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("parentUnitId", parentUnitId);
        param.put("targetYear",targetYear);
        param.put("orgType",orgType);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "revenueTargetInfo/selectList", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<RevenueTargetInfoDto>>>(){});
    }

    @ApiOperation("根据使用单位+年份查询是否配置内外部合同达成目标: 0-内部,1-外部,2-全部")
    @GetMapping({"selectInnerOrOuter"})
    public Response selectInnerOrOuter(@RequestParam Long parentUnitId, @RequestParam Integer targetYear) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("parentUnitId", parentUnitId);
        param.put("targetYear",targetYear);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "revenueTargetInfo/selectInnerOrOuter", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Integer>>(){});
    }

}
