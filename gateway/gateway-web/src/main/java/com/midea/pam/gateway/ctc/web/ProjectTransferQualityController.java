package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.vo.ProjectVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * Description
 * Created by liuqing
 * Date 2021/12/13 17:39
 */
@Api("转质保项目业务")
@RestController
@RequestMapping("projectTransferQuality")
public class ProjectTransferQualityController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation("按id查询项目基本信息")
    @GetMapping("getBaseInfoByProjectId")
    public Response getBaseInfoByProjectId(@RequestParam Long projectId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "projectTransferQuality/getBaseInfoByProjectId", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectDto>>() {
        });
    }

    @ApiOperation("保存")
    @PostMapping("save")
    public Response save(@RequestBody ProjectDto projectDto) {
        final String url = String.format("%s%s", ModelsEnum.CTC.getBaseUrl(), "projectTransferQuality/save");
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, projectDto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<ProjectVO>>() {
        });
    }
}
