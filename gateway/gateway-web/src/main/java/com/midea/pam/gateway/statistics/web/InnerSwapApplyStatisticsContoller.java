package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.InnerSwapApplyDTO;
import com.midea.pam.common.ctc.vo.InnerSwapApplyExcelVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: common-module
 * @description: 内部调剂申请报表
 * @author:zhongpeng
 * @create:2020-12-16 16:43
 **/
@Api("内部调剂申请")
@RestController
@RequestMapping("statistics/innerSwapApply")
public class InnerSwapApplyStatisticsContoller extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "内部调剂申请列表分页查询")
    @GetMapping("page")
    public Response page(@RequestParam(required = false) @ApiParam("申请单号") final String applyCode,
                         @RequestParam(required = false) @ApiParam("申请年度") final String applyYear,
                         @RequestParam(required = false) @ApiParam("状态") final String statusStr,
                         @RequestParam(required = false) @ApiParam("项目编号") final String projectCode,
                         @RequestParam(required = false) @ApiParam("项目名称") final String projectName,
                         @RequestParam(required = false) @ApiParam("需方产品部门") final String requireProductOrgIdStr,
                         @RequestParam(required = false) @ApiParam("供方产品部门") final String provideProductOrgIdStr,
                         @RequestParam(required = false) @ApiParam("需方对接人") final String requireUserName,
                         @RequestParam(required = false) @ApiParam("供方对接人") final String provideUserName,
                         @RequestParam(required = false) @ApiParam("是否为我的申请单") final Boolean likeMe,
                         @RequestParam(required = false) @ApiParam("排序") final String orderParam,
                         @RequestParam(required = false) @ApiParam("排序类型(1:升序；2：降序)") final Integer orderType,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        Map<String, Object> param = new HashMap<>();
        param.put("applyCode", applyCode);
        param.put("applyYear", applyYear);
        param.put("statusStr", statusStr);
        param.put("projectCode", projectCode);
        param.put("projectName",projectName);
        param.put("requireProductOrgIdStr",requireProductOrgIdStr);
        param.put("provideProductOrgIdStr", provideProductOrgIdStr);
        param.put("requireUserName", requireUserName);
        param.put("provideUserName", provideUserName);
        param.put("likeMe", likeMe);
        param.put("orderParam", orderParam);
        param.put("orderType", orderType);
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "innerSwapApply/page", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<InnerSwapApplyDTO>>>() {
        });
    }

    @ApiOperation(value = "内部调剂申请列表导出Excel", response = ResponseMap.class)
    @GetMapping("export/excel")
    public void excel(HttpServletResponse response, @RequestParam(required = false) @ApiParam("申请单号") final String applyCode,
                     @RequestParam(required = false) @ApiParam("申请年度") final String applyYear,
                     @RequestParam(required = false) @ApiParam("状态") final String statusStr,
                     @RequestParam(required = false) @ApiParam("项目编号") final String projectCode,
                     @RequestParam(required = false) @ApiParam("项目名称") final String projectName,
                     @RequestParam(required = false) @ApiParam("需方产品部门") final String requireProductOrgIdStr,
                     @RequestParam(required = false) @ApiParam("供方产品部门") final String provideProductOrgIdStr,
                     @RequestParam(required = false) @ApiParam("需方对接人") final String requireUserName,
                     @RequestParam(required = false) @ApiParam("供方对接人") final String provideUserName,
                     @RequestParam(required = false) @ApiParam("是否为我的申请单") final Boolean likeMe,
                     @RequestParam(required = false) @ApiParam("排序") final String orderParam,
                     @RequestParam(required = false) @ApiParam("排序类型(1:升序；2：降序)") final Integer orderType) {
        Map<String, Object> param = new HashMap<>();
        param.put("applyCode", applyCode);
        param.put("applyYear", applyYear);
        param.put("statusStr", statusStr);
        param.put("projectCode", projectCode);
        param.put("projectName",projectName);
        param.put("requireProductOrgIdStr",requireProductOrgIdStr);
        param.put("provideProductOrgIdStr", provideProductOrgIdStr);
        param.put("requireUserName", requireUserName);
        param.put("provideUserName", provideUserName);
        param.put("likeMe", likeMe);
        param.put("orderParam", orderParam);
        param.put("orderType", orderType);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "innerSwapApply/export/excel", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();

        com.midea.pam.gateway.common.base.DataResponse<Map<String,Object>> dataResponse = JSON.parseObject(res,new TypeReference<com.midea.pam.gateway.common.base.DataResponse<Map<String,Object>>>(){});
        Map<String, Object> resultMap = dataResponse.getData();
        JSONArray list = (JSONArray) resultMap.get("list");

        if (list!=null){
            StringBuffer fileName = new StringBuffer();
            fileName.append("内部调剂申请_"+ DateUtils.format(new Date(),"yyyyMMddHHmmss"));
            fileName.append(".xls");

            List<InnerSwapApplyExcelVO> innerSwapApplyExcelVO = JSONObject.parseArray(list.toString(), InnerSwapApplyExcelVO.class);
            for (int i=0;i<innerSwapApplyExcelVO.size();i++){
                innerSwapApplyExcelVO.get(i).setNumber(i+1);
                String approvedDate = innerSwapApplyExcelVO.get(i).getApprovedDate();
                if(StringUtils.isNotEmpty(approvedDate)){
                    if(approvedDate.length()>10){
                        innerSwapApplyExcelVO.get(i).setApprovedDate(approvedDate.substring(0,10));
                    }else{
                        innerSwapApplyExcelVO.get(i).setApprovedDate(approvedDate);
                    }

                }

            }
            Workbook workbook = ExportExcelUtil.buildDefaultSheet(innerSwapApplyExcelVO, InnerSwapApplyExcelVO.class, null, "内部调剂申请", Boolean.TRUE);
            ExportExcelUtil.downLoadExcel(fileName.toString(),response,workbook);
        }

    }

}
