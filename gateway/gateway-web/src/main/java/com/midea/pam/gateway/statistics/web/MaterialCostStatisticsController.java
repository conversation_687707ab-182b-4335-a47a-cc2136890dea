package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.MaterialCostDto;
import com.midea.pam.common.basedata.excelVo.MaterialCostExportExcelVo;
import com.midea.pam.common.basedata.excelVo.MaterialCostExportExcelVos;
import com.midea.pam.common.basedata.excelVo.MaterialCostImportExcelVo;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.vo.MaterialCostDetailExportExcelVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.common.utils.ExportExcelUtil;
import com.midea.pam.gateway.common.utils.ListUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

@Api("物料估价")
@RestController
@RequestMapping("statistics/materialCost")
public class MaterialCostStatisticsController extends ControllerHelper {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "导出物料成本", response = MaterialCostDto.class)
    @GetMapping("exportMaterialCostList")
    public void exportMaterialCostList(HttpServletResponse response,
                                       @RequestParam(required = false) @ApiParam("成本类型:1=标准;2=报价;3=估价") Integer materialCostType,
                                       @RequestParam(required = false) @ApiParam("估价状态") Integer itemCostIsNull,
                                       @RequestParam(required = false) @ApiParam("物料小类") String materielType,
                                       @RequestParam(required = false) @ApiParam("业务分类") String businessClassification,
                                       @RequestParam(required = false) @ApiParam("物料大类") String materialClassification,
                                       @RequestParam(required = false) @ApiParam("加工件分类") String machiningPartType,
                                       @RequestParam(required = false) @ApiParam("模糊:pam编码") String fuzzyPamCode,
                                       @RequestParam(required = false) @ApiParam("模糊:erp编码") String fuzzyErpCode,
                                       @RequestParam(required = false) @ApiParam("模糊:材质") String material,
                                       @RequestParam(required = false) @ApiParam("模糊:单位重量") BigDecimal unitWeight,
                                       @RequestParam(required = false) @ApiParam("模糊:木质处理") String materialProcessing,
                                       @RequestParam(required = false) @ApiParam("模糊:物料描述") String fuzzyDescr,
                                       @RequestParam(required = false) @ApiParam("模糊:币种") String currency,
                                       @RequestParam(required = false) @ApiParam("配套人员") String nuclearPriceUserName,
                                       @RequestParam(required = false) @ApiParam("多选：库存组织id") String organizationIds,
                                       @RequestParam(required = false) @ApiParam("是否有批准供应商, true为是，false为否 ") Boolean approvedSupplier,
                                       @RequestParam(required = false) @ApiParam("模糊:项目编号") String projectCode,
                                       @RequestParam(required = false) @ApiParam("同步状态") String erpCode,
                                       @RequestParam(required = false) @ApiParam("来源") String resouce,
                                       @RequestParam(required = false) @ApiParam("核价开始时间") String nuclearPriceStartDate,
                                       @RequestParam(required = false) @ApiParam("核价结束时间") String nuclearPriceEndDate,
                                       @RequestParam(required = false) @ApiParam("ERP编码创建日期开始") String erpCodeCreateAtStart,
                                       @RequestParam(required = false) @ApiParam("ERP编码创建日期结束") String erpCodeCreateAtEnd,
                                       @RequestParam(required = false) @ApiParam("物料创建日期开始") String materialCreateAtStart,
                                       @RequestParam(required = false) @ApiParam("物料创建日期结束") String materialCreateAtEnd,
                                       @RequestParam(required = false) @ApiParam("物料编码") String itemCode) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("materialCostType", materialCostType);
        param.put("itemCostIsNull", itemCostIsNull);
        param.put("materielType", materielType);
        param.put("businessClassification", businessClassification);
        param.put("materialClassification", materialClassification);
        param.put("machiningPartType", machiningPartType);
        param.put("fuzzyPamCode", fuzzyPamCode);
        param.put("fuzzyErpCode", fuzzyErpCode);
        param.put("material", material);
        param.put("unitWeight", unitWeight);
        param.put("materialProcessing", materialProcessing);
        param.put("fuzzyDescr", fuzzyDescr);
        param.put("currency", currency);
        param.put("nuclearPriceUserName", nuclearPriceUserName);
        param.put("organizationIds", organizationIds);
        param.put("resouce", resouce);
        param.put("approvedSupplier", approvedSupplier);
        param.put("projectCode", projectCode);
        param.put("erpCode", erpCode);
        param.put("nuclearPriceStartDate", nuclearPriceStartDate);
        param.put("nuclearPriceEndDate", nuclearPriceEndDate);
        param.put("erpCodeCreateAtStart", erpCodeCreateAtStart);
        param.put("erpCodeCreateAtEnd", erpCodeCreateAtEnd);
        param.put("materialCreateAtStart", materialCreateAtStart);
        param.put("materialCreateAtEnd", materialCreateAtEnd);
        param.put("itemCode", itemCode);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/materialCost/selectList", param);
        String responseEntity = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<MaterialCostDto>> dataResponse = JSON.parseObject(responseEntity, new TypeReference<DataResponse<List<MaterialCostDto>>>() {
        });

        List<MaterialCostDto> dataList = dataResponse.getData();
        if (CollectionUtils.isEmpty(dataList)) {
            throw new Exception("没有数据");
        }

        for (int i = 0; i < dataList.size(); i++) {
            MaterialCostDto materialCostDto = dataList.get(i);
            materialCostDto.setIndex(i + 1);
        }

        List<MaterialCostExportExcelVos> excelVos = ListUtil.map(dataList, new ListUtil.IteratorTask<MaterialCostExportExcelVos, MaterialCostDto>() {
            @Override
            public MaterialCostExportExcelVos getValue(MaterialCostDto item) {
                MaterialCostExportExcelVos wifi = new MaterialCostExportExcelVos();
                BeanUtils.copyProperties(item, wifi);
                if (wifi.getItemCost() != null) {
                    //已估价
                    wifi.setItemCostIsNull(false);
                } else {
                    //未估价
                    wifi.setItemCostIsNull(true);
                }
                return wifi;
            }
        });

        //导出操作
        ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", MaterialCostExportExcelVos.class, "物料估价明细_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls", response);
    }


    @ApiOperation(value = "物料成本分页查询", response = MaterialCostDto.class)
    @GetMapping("MaterialCostList")
    public Response page(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                         @RequestParam(required = false) @ApiParam("成本类型:1=标准;2=报价;3=估价") Integer materialCostType,
                         @RequestParam(required = false) @ApiParam("估价状态") Integer itemCostIsNull,
                         @RequestParam(required = false) @ApiParam("物料小类") String materielType,
                         @RequestParam(required = false) @ApiParam("业务分类") String businessClassification,
                         @RequestParam(required = false) @ApiParam("物料大类") String materialClassification,
                         @RequestParam(required = false) @ApiParam("加工件分类") String machiningPartType,
                         @RequestParam(required = false) @ApiParam("模糊:pam编码") String fuzzyPamCode,
                         @RequestParam(required = false) @ApiParam("模糊:erp编码") String fuzzyErpCode,
                         @RequestParam(required = false) @ApiParam("模糊:材质") String material,
                         @RequestParam(required = false) @ApiParam("模糊:单位重量") BigDecimal unitWeight,
                         @RequestParam(required = false) @ApiParam("模糊:木质处理") String materialProcessing,
                         @RequestParam(required = false) @ApiParam("模糊:物料描述") String fuzzyDescr,
                         @RequestParam(required = false) @ApiParam("模糊:币种") String currency,
                         @RequestParam(required = false) @ApiParam("配套人员") String nuclearPriceUserName,
                         @RequestParam(required = false) @ApiParam("多选：库存组织id") String organizationIds,
                         @RequestParam(required = false) @ApiParam("是否有批准供应商, true为是，false为否 ") Boolean approvedSupplier,
                         @RequestParam(required = false) @ApiParam("模糊:项目编号") String projectCode,
                         @RequestParam(required = false) @ApiParam("同步状态") String erpCode,
                         @RequestParam(required = false) @ApiParam("核价开始时间") String nuclearPriceStartDate,
                         @RequestParam(required = false) @ApiParam("核价结束时间") String nuclearPriceEndDate,
                         @RequestParam(required = false) @ApiParam("来源") String resouce,
                         @RequestParam(required = false) @ApiParam("物料编码") String itemCode,
                         @RequestParam(required = false) @ApiParam("最后定价区间开始") String pricingDateStart,
                         @RequestParam(required = false) @ApiParam("最后定价区间结束") String pricingDateEnd,
                         @RequestParam(required = false) @ApiParam("最后更新日期开始") String lastUpdateDateStart,
                         @RequestParam(required = false) @ApiParam("最后更新日期结束") String lastUpdateDateEnd,
                         @RequestParam(required = false) @ApiParam("ERP编码创建日期开始") String erpCodeCreateAtStart,
                         @RequestParam(required = false) @ApiParam("ERP编码创建日期结束") String erpCodeCreateAtEnd,
                         @RequestParam(required = false) @ApiParam("物料创建日期开始") String materialCreateAtStart,
                         @RequestParam(required = false) @ApiParam("物料创建日期结束") String materialCreateAtEnd,
                         @RequestParam(required = false) @ApiParam("是否来源估价") Integer itemCostOrNot) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("materialCostType", materialCostType);
        param.put("itemCostIsNull", itemCostIsNull);
        param.put("materielType", materielType);
        param.put("businessClassification", businessClassification);
        param.put("materialClassification", materialClassification);
        param.put("machiningPartType", machiningPartType);
        param.put("fuzzyPamCode", fuzzyPamCode);
        param.put("fuzzyErpCode", fuzzyErpCode);
        param.put("material", material);
        param.put("unitWeight", unitWeight);
        param.put("materialProcessing", materialProcessing);
        param.put("fuzzyDescr", fuzzyDescr);
        param.put("currency", currency);
        param.put("nuclearPriceUserName", nuclearPriceUserName);
        param.put("organizationIds", organizationIds);
        param.put("approvedSupplier", approvedSupplier);
        param.put("projectCode", projectCode);
        param.put("erpCode", erpCode);
        param.put("nuclearPriceStartDate", nuclearPriceStartDate);
        param.put("nuclearPriceEndDate", nuclearPriceEndDate);
        param.put("itemCode", itemCode);
        param.put("resouce", resouce);
        param.put("pricingDateStart", pricingDateStart);
        param.put("pricingDateEnd", pricingDateEnd);
        param.put("lastUpdateDateStart", lastUpdateDateStart);
        param.put("lastUpdateDateEnd", lastUpdateDateEnd);
        param.put("erpCodeCreateAtStart", erpCodeCreateAtStart);
        param.put("erpCodeCreateAtEnd", erpCodeCreateAtEnd);
        param.put("materialCreateAtStart", materialCreateAtStart);
        param.put("materialCreateAtEnd", materialCreateAtEnd);
        param.put("itemCostOrNot", itemCostOrNot);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/materialCost/page", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<MaterialCostDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<MaterialCostDto>>>() {
        });
        return response;
    }

/*    @ApiOperation(value = "批量导入物料")
    @PostMapping("importMaTerialCost")
    public Response importMaTerialCost(@RequestParam(value = "file") MultipartFile file) {
        DataResponse<List<MaterialCostImportExcelVo>> response = Response.dataResponse();
        try {

            List<MaterialCostImportExcelVo> importExcelVos = FileUtil.importExcel(file, MaterialCostImportExcelVo.class, 1, 0);

            if (importExcelVos.size() > 0) {
                final String url = String.format("%statistics/materialCost/importMaTerialCost", ModelsEnum.STATISTICS.getBaseUrl());

                String res = restTemplate.postForEntity(url, importExcelVos, String.class).getBody();
                try {
                    response = JSON.parseObject(res, new TypeReference<DataResponse<List<MaterialCostImportExcelVo>>>() {
                    });
                    return response;
                } catch (Exception e) {
                    DataResponse<List<MaterialCostImportExcelVo>> responseImportDtos = JSON.parseObject(res, new TypeReference<DataResponse<List<MaterialCostImportExcelVo>>>() {
                    });
                    return responseImportDtos;
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            Asserts.success(ErrorCode.SYSTEM_FILE_ERROR);

            response = Response.dataResponse();
            response.setCode((com.midea.pam.gateway.common.base.Code) Code.ERROR);
            response.setMsg(e.getMessage());
        }

        return response;
    }*/

    @ApiOperation(value = "批量导入退料检查错误导出", response = ResponseMap.class)
    @PostMapping("exportMaTerialCostErrorDetail")
    public void exportMaTerialCostErrorDetail(HttpServletResponse response, @RequestBody MaterialCostImportExcelVo materialCostImportExcelVo) {

        final String url = String.format("%sstatistics/materialCost/exportMaTerialCostErrorDetail", ModelsEnum.STATISTICS.getBaseUrl());

        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, materialCostImportExcelVo, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("物料估价明细检查单_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");

        JSONArray materialReturnDetailArr = (JSONArray) resultMap.get("materialCostDetailList");

        List<MaterialCostDetailExportExcelVo> materialCostDetailExportExcelVos = new ArrayList<>();
        if (materialReturnDetailArr != null) {
            materialCostDetailExportExcelVos = JSONObject.parseArray(materialReturnDetailArr.toJSONString(), MaterialCostDetailExportExcelVo.class);
            for (int i = 0; i < materialCostDetailExportExcelVos.size(); i++) {
                MaterialCostDetailExportExcelVo materialCostDetailExportExcelVo = materialCostDetailExportExcelVos.get(i);
                materialCostDetailExportExcelVo.setNum(i + 1);
            }
        }

        final Workbook workbook = com.midea.pam.common.util.ExportExcelUtil.buildDefaultSheet(materialCostDetailExportExcelVos, MaterialCostDetailExportExcelVo.class, null, "物料估价明细检查", true);

        com.midea.pam.common.util.ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }
}
