package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.excelVo.MilepostDesignPlanDetailPurchaseExportVo;
import com.midea.pam.common.ctc.vo.MilepostDesignPlanDetailApprovedVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.gateway.common.utils.BeanConverter;
import com.midea.pam.gateway.service.OssService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.http.entity.ContentType;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.midea.pam.common.util.StringUtils.buildGetUrl;

/**
 * Description
 * Created by liuqing
 * Date 2021/12/15 11:11
 */
@Api("转质保项目审批回调")
@RestController
@RequestMapping("projectTransferQualityCallBack")
public class ProjectTransferQualityCallBackController {

    private static final Logger logger = LoggerFactory.getLogger(ProjectTransferQualityCallBackController.class);

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private OssService ossService;


    @ApiOperation(value = "发起审批")
    @PutMapping("approvaling/skipSecurityInterceptor")
    public Response approvaling(@RequestParam(required = false) Long formInstanceId,
                                @RequestParam(required = false) String fdInstanceId,
                                @RequestParam(required = false) String formUrl,
                                @RequestParam(required = false) String eventName,
                                @RequestParam(required = false) String handlerId,
                                @RequestParam(required = false) Long companyId,
                                @RequestParam(required = false) Long createUserId) {
        // 取详细设计方案发布(非采购)信息
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", formInstanceId);
        String url1 = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan/getDetailByProjectId", param);
        ResponseEntity<String> responseEntity1 = restTemplate.getForEntity(url1, String.class);
        // 暂用MilepostDesignPlanDetailApprovedVO这个属性
        DataResponse<MilepostDesignPlanDetailApprovedVO> response1 = JSON.parseObject(responseEntity1.getBody(), new TypeReference<DataResponse<MilepostDesignPlanDetailApprovedVO>>() {
        });
        JSONObject jsonObject = new JSONObject();
        String fileId = "";
        String fileName = "";
        String fileSize = "";
        if (response1 != null && response1.getData() != null) {
            //生成附件
            MultipartFile multipartFile = createAnnex(response1.getData());
            try {
                if (multipartFile != null) {
                    JSONArray result = ossService.upload(multipartFile);
                    if (!ObjectUtils.isEmpty(result)) {
                        jsonObject = result.getJSONObject(0);
                        //提交审批
                        fileId = jsonObject.getString("fileId");
                        fileName = jsonObject.getString("fileName");
                        fileSize = jsonObject.getString("fileSize");
                    }
                }
            } catch (Exception e) {
                logger.info("详细设计生成文件，上传失败:", e);
            }
        }
        String url = String.format("%sprojectTransferQualityCallBack/approvaling/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s" +
                        "&fileId=%s" +
                        "&fileName=%s" +
                        "&fileSize=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId, fileId, fileName, fileSize);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    //根据数据产生Excel
    public MultipartFile createAnnex(MilepostDesignPlanDetailApprovedVO milepostDesignPlanDetailApprovedVO) {
        // 附件名称为：项目编号+“详设发布明细”+“_”+YYYYMMDD MM:HH:SS;举例：IA19108详设发布明细_20191127 14:09:01.xlsx
        MultipartFile multipartFile = null;
        String filePath = milepostDesignPlanDetailApprovedVO.getProjectCode() + "详设发布明细_" + DateUtils.format(new Date(), "yyyyMMdd HH:mm:ss") + ".xls";
        try {
            ExportParams exportParams = new ExportParams(filePath, "Sheet1");
            List<MilepostDesignPlanDetailDto> designPlanDetailDtos = milepostDesignPlanDetailApprovedVO.getDesignPlanDetailDtos();
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(designPlanDetailDtos)) {
//                List<MilepostDesignPlanDetailApprovedExportVo> excelVos = BeanConverter.copy(designPlanDetailDtos, MilepostDesignPlanDetailApprovedExportVo.class);
                List<MilepostDesignPlanDetailPurchaseExportVo> excelVos = this.buildExcelVos(designPlanDetailDtos);
                Workbook workbook = ExcelExportUtil.exportExcel(exportParams, MilepostDesignPlanDetailPurchaseExportVo.class, excelVos);
                File pdfFile = new File("/apps/pam/gateway/file/" + filePath);
                try (
                        OutputStream out = new FileOutputStream("/apps/pam/gateway/file/" + filePath);
                        FileInputStream fileInputStream = new FileInputStream(pdfFile);
                ) {
                    workbook.write(out);
                    multipartFile = new MockMultipartFile(pdfFile.getName(), pdfFile.getName(),
                            ContentType.APPLICATION_OCTET_STREAM.toString(), fileInputStream);
                } catch (IOException e) {
                    logger.info("详细设计变更生成文件，上传失败:", e);
                }

            }
        } catch (Exception e) {
            logger.info("详细设计生成文件，上传失败:", e);
        }
        return multipartFile;
    }


    // 最多做三层数据封装
    private List<MilepostDesignPlanDetailPurchaseExportVo> buildExcelVos(List<MilepostDesignPlanDetailDto> designPlanDetailDtos) {
        List<MilepostDesignPlanDetailPurchaseExportVo> milepostDesignPlanDetailPurchaseExportVos = new ArrayList<>();
        int i = 0;
        for (MilepostDesignPlanDetailDto designPlanDetailDto : designPlanDetailDtos) {
            i = i + 1;
            MilepostDesignPlanDetailPurchaseExportVo milepostDesignPlanDetailPurchaseExportVo = new MilepostDesignPlanDetailPurchaseExportVo();
            BeanConverter.copy(designPlanDetailDto, milepostDesignPlanDetailPurchaseExportVo);
            milepostDesignPlanDetailPurchaseExportVo.setSerialNumber(String.valueOf(i));
            //去掉小数点后面的0
            milepostDesignPlanDetailPurchaseExportVo.setNumber(designPlanDetailDto.getNumber() == null ? BigDecimal.ZERO : designPlanDetailDto.getNumber().stripTrailingZeros());
            milepostDesignPlanDetailPurchaseExportVos.add(milepostDesignPlanDetailPurchaseExportVo);
            List<MilepostDesignPlanDetailDto> firstSonDtos = designPlanDetailDto.getSonDtos();  //子模组数据
            int j = 0;
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(firstSonDtos)) {
                for (MilepostDesignPlanDetailDto firstSonDto : firstSonDtos) {
                    j = j + 1;
                    MilepostDesignPlanDetailPurchaseExportVo firstSonExportVo = new MilepostDesignPlanDetailPurchaseExportVo();
                    BeanConverter.copy(firstSonDto, firstSonExportVo);
                    firstSonExportVo.setSerialNumber(String.valueOf(i) + "." + String.valueOf(j));
                    firstSonExportVo.setNumber(firstSonDto.getNumber() == null ? BigDecimal.ZERO : firstSonDto.getNumber().stripTrailingZeros());
                    milepostDesignPlanDetailPurchaseExportVos.add(firstSonExportVo);
                    List<MilepostDesignPlanDetailDto> SecSonDtos = firstSonDto.getSonDtos();  //二级子模组数据
                    int k = 0;
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(SecSonDtos)) {
                        for (MilepostDesignPlanDetailDto secSonDto : SecSonDtos) {
                            k = k + 1;
                            MilepostDesignPlanDetailPurchaseExportVo SecSonExportVo = new MilepostDesignPlanDetailPurchaseExportVo();
                            BeanConverter.copy(secSonDto, SecSonExportVo);
                            SecSonExportVo.setSerialNumber(String.valueOf(i) + "." + String.valueOf(j) + "." + String.valueOf(k));
                            SecSonExportVo.setNumber(secSonDto.getNumber() == null ? BigDecimal.ZERO : secSonDto.getNumber().stripTrailingZeros());
                            milepostDesignPlanDetailPurchaseExportVos.add(SecSonExportVo);
                        }
                    }
                }
            }
        }
        return milepostDesignPlanDetailPurchaseExportVos;
    }

    @ApiOperation(value = "审批通过")
    @PutMapping("approved/skipSecurityInterceptor")
    public Response approved(@RequestParam(required = false) Long formInstanceId,
                             @RequestParam(required = false) String fdInstanceId,
                             @RequestParam(required = false) String formUrl,
                             @RequestParam(required = false) String eventName,
                             @RequestParam(required = false) String handlerId,
                             @RequestParam(required = false) Long companyId,
                             @RequestParam(required = false) Long createUserId,
                             @RequestParam(required = false) Long handlerUserId) {
        String url = String.format("%sprojectTransferQualityCallBack/approved/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s" +
                        "&handlerUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId, handlerUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "驳回")
    @PutMapping("refused/skipSecurityInterceptor")
    public Response refused(@RequestParam(required = false) Long formInstanceId,
                            @RequestParam(required = false) String fdInstanceId,
                            @RequestParam(required = false) String formUrl,
                            @RequestParam(required = false) String eventName,
                            @RequestParam(required = false) String handlerId,
                            @RequestParam(required = false) Long companyId,
                            @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sprojectTransferQualityCallBack/refused/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "撤回")
    @PutMapping("return/skipSecurityInterceptor")
    public Response returned(@RequestParam(required = false) Long formInstanceId,
                             @RequestParam(required = false) String fdInstanceId,
                             @RequestParam(required = false) String formUrl,
                             @RequestParam(required = false) String eventName,
                             @RequestParam(required = false) String handlerId,
                             @RequestParam(required = false) Long companyId,
                             @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sprojectTransferQualityCallBack/return/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "废弃")
    @PutMapping("abandon/skipSecurityInterceptor")
    public Response abandon(@RequestParam(required = false) Long formInstanceId,
                            @RequestParam(required = false) String fdInstanceId,
                            @RequestParam(required = false) String formUrl,
                            @RequestParam(required = false) String eventName,
                            @RequestParam(required = false) String handlerId,
                            @RequestParam(required = false) Long companyId,
                            @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sprojectTransferQualityCallBack/abandon/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "删除")
    @PutMapping("delete/skipSecurityInterceptor")
    public Response delete(@RequestParam(required = false) Long formInstanceId,
                           @RequestParam(required = false) String fdInstanceId,
                           @RequestParam(required = false) String formUrl,
                           @RequestParam(required = false) String eventName,
                           @RequestParam(required = false) String handlerId,
                           @RequestParam(required = false) Long companyId,
                           @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sprojectTransferQualityCallBack/delete/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "通过")
    @PutMapping("agree/skipSecurityInterceptor")
    public Response agree(@RequestParam(required = false) Long formInstanceId,
                                                            @RequestParam(required = false) String fdInstanceId,
                                                            @RequestParam(required = false) String formUrl,
                                                            @RequestParam(required = false) String eventName,
                                                            @RequestParam(required = false) String handlerId,
                                                            @RequestParam(required = false) Long companyId,
                                                            @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sprojectTransferQualityCallBack/agree/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }
}
