package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.crm.dto.RdmResourcePlanDto;
import com.midea.pam.common.ctc.entity.RdmResourcePlan;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;

@Api("rdm-资源计划")
@RestController
@RequestMapping("rdmResourcePlan")
public class RdmResourcePlanController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "同步RDM数据")
    @GetMapping("getRdmResourcePlan")
    public Response getRdmResourcePlan(@RequestParam(required = true)String startDate,
                                        @RequestParam(required = false)String endDate){
        final Map<String, Object> param = new HashMap<>();
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "rdmResourcePlan/getRdmResourcePlan",param);
        restTemplate.getForObject(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        response.setData("0");
        return response;
    }

    @ApiOperation(value = "手工同步RDM资源计划")
    @GetMapping("handleRdmResourcePlan")
    public Response handleRdmResourcePlan(){
        final Map<String, Object> param = new HashMap<>();
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "rdmResourcePlan/handleRdmResourcePlan",param);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<String> response = JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
        return response;
    }

    @ApiOperation(value = "查询人力职级单价信息")
    @GetMapping("queryRdmResourcePlan")
    public Response queryRdmResourcePlan(@RequestParam final String ids,
                                         @RequestParam(required = false) final String priceType) {
        final Map<String, Object> params = new HashMap<>();
        params.put("ids", ids);
        params.put("priceType", priceType);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "rdmResourcePlan/queryRdmResourcePlan", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<RdmResourcePlanDto>>>() {
        });
    }

    @ApiOperation(value = "查询资源计划信息")
    @GetMapping("selectRdmResourcePlan")
    public Response selectRdmResourcePlan(final RdmResourcePlanDto rdmResourcePlanDto,
                                          @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                          @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = new HashMap<>();
        params.put("projectManager", rdmResourcePlanDto.getProjectManager());
        params.put("projectName", rdmResourcePlanDto.getProjectName());
        params.put("username", rdmResourcePlanDto.getUsername());
        params.put("sTime", rdmResourcePlanDto.getsTime());
        params.put("eTime", rdmResourcePlanDto.geteTime());
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "rdmResourcePlan/selectRdmResourcePlan", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);

        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<RdmResourcePlan>>>() {
        });
    }

    @ApiOperation(value = "根据资源计划号查询资源计划信息")
    @GetMapping("queryRdmResourcePlanByResourceCode")
    public Response queryRdmResourcePlanByResourceCode(final RdmResourcePlanDto rdmResourcePlanDto,
                                                       @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                                       @RequestParam(required = false, defaultValue = "1000") final Integer pageSize) {
        final Map<String, Object> params = new HashMap<>();
        params.put("resourceCode", rdmResourcePlanDto.getResourceCode());
        params.put("username", rdmResourcePlanDto.getUsername());
        params.put("mipname", rdmResourcePlanDto.getMipname());
        params.put("priceType", rdmResourcePlanDto.getPriceType());
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "rdmResourcePlan/queryRdmResourcePlanByResourceCode", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<RdmResourcePlanDto>>>() {
        });
    }

    @ApiOperation(value = "根据资源计划号查询资源计划信息1")
    @GetMapping("queryRdmResourcePlanByResourceCode1")
    public Response queryRdmResourcePlanByResourceCode1(final RdmResourcePlanDto rdmResourcePlanDto,
                                                       @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                                       @RequestParam(required = false, defaultValue = "1000") final Integer pageSize) {
        final Map<String, Object> params = new HashMap<>();
        params.put("resourceCode", rdmResourcePlanDto.getResourceCode());
        params.put("username", rdmResourcePlanDto.getUsername());
        params.put("mipname", rdmResourcePlanDto.getMipname());
        params.put("priceType", rdmResourcePlanDto.getPriceType());
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "rdmResourcePlan/queryRdmResourcePlanByResourceCode1", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<RdmResourcePlanDto>>>() {
        });
    }

}
