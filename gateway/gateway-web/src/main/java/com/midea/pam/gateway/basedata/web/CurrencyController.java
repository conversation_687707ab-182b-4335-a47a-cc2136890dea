package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.mcomponent.core.util.StringUtil;
import com.midea.mcomponent.security.common.util.UserUtils;
import com.midea.pam.common.basedata.dto.CurrencyDto;
import com.midea.pam.common.basedata.dto.UserInfoDto;
import com.midea.pam.common.basedata.entity.Currency;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.PageResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.common.utils.PamCurrentUserUtil;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 币种controller
 * @date 2019-3-19
 */
@Api("币种")
@RestController
@RequestMapping("currency")
public class CurrencyController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查询币种列表分页", response = CurrencyDto.class)
    @GetMapping("selectPage")
    public Response selectPage(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                               @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize,
                               @RequestParam(required = false) @ApiParam(value = "币种代码") String currencyCode) {

        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("currencyCode", currencyCode);

        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "currency/selectPage", param);
        String res = cleanStr(restTemplate.getForObject(url, String.class));
        PageInfo<CurrencyDto> data = JSON.parseObject(res, new TypeReference<PageInfo<CurrencyDto>>() {
        });
        PageResponse<CurrencyDto> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation(value = "新增币种", response = Response.class)
    @PostMapping("")
    public Response add(@RequestBody @ApiParam(name = "Currency", value = "币种信息") Currency currency) {
        String url = String.format("%scurrency", ModelsEnum.BASEDATA.getBaseUrl());

        UserInfo user = CacheDataUtils.findUserByMip(PamCurrentUserUtil.getCurrentUserName());
        currency.setCreateBy(user.getId());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, currency, String.class);
        String res = cleanStr(responseEntity.getBody());
        DataResponse<String> response = Response.dataResponse();
        return response.setData(res);
    }

    @ApiOperation(value = "修改币种", response = Response.class)
    @PutMapping("")
    public Response update(@RequestBody @ApiParam(name = "Currency", value = "币种信息") Currency currency) {
        String url = String.format("%scurrency", ModelsEnum.BASEDATA.getBaseUrl());
        UserInfo user = CacheDataUtils.findUserByMip(PamCurrentUserUtil.getCurrentUserName());
        currency.setUpdateBy(user.getId());
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, new HttpEntity<Currency>(currency), String.class);
        String res = cleanStr(responseEntity.getBody());
        DataResponse<String> response = Response.dataResponse();
        return response.setData(res);
    }

    @ApiOperation(value = "删除币种", response = Response.class)
    @DeleteMapping("{id}")
    public Response delete(@PathVariable Long id) {
        String loginUserName = PamCurrentUserUtil.getCurrentUserName();
        UserInfo user = CacheDataUtils.findUserByMip(loginUserName);
        String url = String.format("%scurrency/" + id + "/" + user.getId(), ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.DELETE, null, String.class);
        String res = cleanStr(responseEntity.getBody());
        DataResponse<String> response = Response.dataResponse();
        return response.setData(res);
    }
}
