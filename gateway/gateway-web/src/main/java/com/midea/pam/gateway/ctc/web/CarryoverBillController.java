package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.CarryoverBillDto;
import com.midea.pam.common.ctc.dto.CarryoverBillIncomeCollectionDto;
import com.midea.pam.common.ctc.dto.CarryoverBillProcessScheduleDto;
import com.midea.pam.common.ctc.dto.SelectionRangeDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Api("财务结转")
@RestController
@RequestMapping("ctc/carryoverBill")
public class CarryoverBillController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "结转单分页", response = CarryoverBillDto.class)
    @GetMapping({"selectPage"})
    public Response selectPage(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                               @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize,
                               @RequestParam(required = false) @ApiParam(value = "项目编号") String fuzzyProjectNum,
                               @RequestParam(required = false) @ApiParam(value = "项目名称") String fuzzyProjectName,
                               @RequestParam(required = false) @ApiParam(value = "会计期间") String periodName,
                               @RequestParam(required = false) @ApiParam(value = "项目类型id") Long projectType,
                               @RequestParam(required = false) @ApiParam(value = "结转单号") String fuzzyBillNum,
                               @RequestParam(required = false) @ApiParam(value = "业务实体id") Long ouId,
                               @RequestParam(required = false) @ApiParam(value = "执行批次") String carryoverBatchNum,
                               @RequestParam(required = false, defaultValue = "1") @ApiParam(value = "0：草稿， 1：正式") Integer status) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("fuzzyProjectNum", fuzzyProjectNum);
        param.put("fuzzyProjectName", fuzzyProjectName);
        param.put("periodName", periodName);
        param.put("projectType", projectType);
        param.put("fuzzyBillNum", fuzzyBillNum);
        param.put("ouId", ouId);
        param.put("carryoverBatchNum", carryoverBatchNum);
        param.put("status", status);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/ctc/carryoverBill/selectPage", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<CarryoverBillDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<CarryoverBillDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "结转确认计划分页", response = CarryoverBillDto.class)
    @GetMapping({"findWaitFroCarryoverByBatchNum"})
    public Response findWaitFroCarryoverByBatchNum(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                                   @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize,
                                                   @RequestParam(required = false) @ApiParam(value = "执行批次") String carryoverBatchNum) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("carryoverBatchNum", carryoverBatchNum);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/ctc/carryoverBill/findWaitFroCarryoverByBatchNum", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<CarryoverBillDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<CarryoverBillDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "结转单详情", response = CarryoverBillDto.class)
    @GetMapping({"view"})
    public Response view(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/ctc/carryoverBill/view", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<CarryoverBillDto> response = JSON.parseObject(res, new TypeReference<DataResponse<CarryoverBillDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "选择范围", response = SelectionRangeDto.class)
    @GetMapping({"selectionRangePage"})
    public Response selectionRangePage(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                       @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize,
                                       @RequestParam(required = false) @ApiParam(value = "项目名称") String fuzzyProjectName,
                                       @RequestParam(required = false) @ApiParam(value = "项目编号") String fuzzyProjectNum,
                                       @RequestParam(required = false) @ApiParam(value = "节点实际结束日期开始") Date actualEndTimeBegin,
                                       @RequestParam(required = false) @ApiParam(value = "节点实际结束日期结束") Date actualEndTimeEndEnd,
                                       @RequestParam(required = false) @ApiParam(value = "成本方法") String costMethod,
                                       @RequestParam(required = false) @ApiParam(value = "来源类型 1 正常 2 变更 3 追溯分摊") Integer sourceType,
                                       @RequestParam(required = false) @ApiParam(value = "客户名称") String fuzzyCustomerName,
                                       @RequestParam(required = false) @ApiParam(value = "业务实体id") Long projectOuId,
                                       @RequestParam(required = false) @ApiParam(value = "业务分类ID，多个用逗号分隔") String unitIdStr,
                                       @RequestParam(required = false) @ApiParam(value = "项目名称或编号") String projectNameOrCode) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("fuzzyProjectNum", fuzzyProjectNum);
        param.put("fuzzyProjectName", fuzzyProjectName);
        param.put("actualEndTimeBegin", actualEndTimeBegin == null ? "" : actualEndTimeBegin.getTime());
        param.put("actualEndTimeEndEnd", actualEndTimeEndEnd == null ? "" : actualEndTimeEndEnd.getTime());
        param.put("costMethod", costMethod);
        param.put("sourceType", sourceType);
        param.put("fuzzyCustomerName", fuzzyCustomerName);
        param.put("projectOuId", projectOuId);
        param.put("unitIdStr", unitIdStr);
        param.put("projectNameOrCode", projectNameOrCode);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/ctc/carryoverBill/selectionRangePage", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<SelectionRangeDto>>>() {
        });
    }

    @ApiOperation(value = "全部移入", response = Boolean.class)
    @PostMapping({"moveAllIn"})
    public Response moveAllIn(@RequestBody SelectionRangeDto query) {
        String url = String.format("%sctc/carryoverBill/moveAllIn", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, query, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "全部移出", response = Boolean.class)
    @PostMapping({"removeAll"})
    public Response removeAll(@RequestBody SelectionRangeDto query) {
        String url = String.format("%sctc/carryoverBill/removeAll", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, query, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "批量移入", response = Boolean.class)
    @PostMapping({"batchMoveIn"})
    public Response batchMoveIn(@RequestBody SelectionRangeDto query) {
        String url = String.format("%sctc/carryoverBill/batchMoveIn", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, query, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "批量移出", response = Boolean.class)
    @PostMapping({"batchRemove"})
    public Response batchRemove(@RequestBody SelectionRangeDto query) {
        String url = String.format("%sctc/carryoverBill/batchRemove", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, query, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "批量移出清除批次号", response = Boolean.class)
    @PostMapping({"batchRemoveAndClearBatchNum"})
    public Response batchRemoveAndClearBatchNum(@RequestBody SelectionRangeDto query) {
        String url = String.format("%sctc/carryoverBill/batchRemoveAndClearBatchNum", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, query, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "获取结转单执行进度", response = CarryoverBillProcessScheduleDto.class)
    @GetMapping({"getByBatchNumAndUserBy"})
    public Response getByBatchNumAndUserBy(@RequestParam @ApiParam(value = "执行批次") String carryoverBatchNum) {
        final Map<String, Object> param = new HashMap<>();
        param.put("carryoverBatchNum", carryoverBatchNum);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/ctc/carryoverBill/getByBatchNumAndUserBy", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<CarryoverBillProcessScheduleDto> response = JSON.parseObject(res, new TypeReference<DataResponse<CarryoverBillProcessScheduleDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "归集汇总")
    @GetMapping({"collectionSummary"})
    public Response collectionSummary(@RequestParam @ApiParam(value = "执行批次") String carryoverBatchNum) {
        final Map<String, Object> param = new HashMap<>();
        param.put("carryoverBatchNum", carryoverBatchNum);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/ctc/carryoverBill/collectionSummary", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "校验")
    @GetMapping({"check"})
    public Response check(@RequestParam @ApiParam(value = "执行批次") String carryoverBatchNum) {
        final Map<String, Object> param = new HashMap<>();
        param.put("carryoverBatchNum", carryoverBatchNum);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/ctc/carryoverBill/check", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "收入计算")
    @GetMapping({"incomeCalculation"})
    public Response incomeCalculation(@RequestParam @ApiParam(value = "执行批次") String carryoverBatchNum) {
        final Map<String, Object> param = new HashMap<>();
        param.put("carryoverBatchNum", carryoverBatchNum);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/ctc/carryoverBill/incomeCalculation", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "当前结转批次的收入总额计算", response = CarryoverBillProcessScheduleDto.class)
    @GetMapping({"totalIncomeCalculation"})
    public Response totalIncomeCalculation(@RequestParam @ApiParam(value = "执行批次") String carryoverBatchNum) {
        final Map<String, Object> param = new HashMap<>();
        param.put("carryoverBatchNum", carryoverBatchNum);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/ctc/carryoverBill/totalIncomeCalculation", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<CarryoverBillProcessScheduleDto> response = JSON.parseObject(res, new TypeReference<DataResponse<CarryoverBillProcessScheduleDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "收入计算分页", response = CarryoverBillDto.class)
    @GetMapping({"incomeCollectionPage"})
    public Response incomeCollectionPage(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                         @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize,
                                         @RequestParam(required = false) @ApiParam(value = "项目编号") String fuzzyProjectNum,
                                         @RequestParam(required = false) @ApiParam(value = "项目名称") String fuzzyProjectName,
                                         @RequestParam(required = false) @ApiParam(value = "成本方法") String costMethod,
                                         @RequestParam(required = false) @ApiParam(value = "执行批次") String carryoverBatchNum,
                                         @RequestParam(required = false) @ApiParam(value = "业务分类ID") Long unitId,
                                         @RequestParam(required = false) @ApiParam(value = "项目名称或编号") String projectNameOrCode) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("fuzzyProjectNum", fuzzyProjectNum);
        param.put("fuzzyProjectName", fuzzyProjectName);
        param.put("costMethod", costMethod);
        param.put("carryoverBatchNum", carryoverBatchNum);
        param.put("unitId", unitId);
        param.put("projectNameOrCode", projectNameOrCode);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/ctc/carryoverBill/incomeCollectionPage", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<CarryoverBillIncomeCollectionDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<CarryoverBillIncomeCollectionDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "确认结转")
    @GetMapping({"confirmCarryover"})
    public Response confirmCarryover(@RequestParam @ApiParam(value = "执行批次") String carryoverBatchNum,
                                     @RequestParam @ApiParam(value = "业务实体id") Long ouId,
                                     @RequestParam @ApiParam(value = "会计期间(YYYY-MM)") String glPeriodName) {
        return null;
    }

    @ApiOperation(value = "成本预览导出")
    @GetMapping("/exportCost")
    public void exportCost(HttpServletResponse response,
                           @RequestParam(required = false) @ApiParam(value = "项目编号") String fuzzyProjectNum,
                           @RequestParam(required = false) @ApiParam(value = "项目名称") String fuzzyProjectName,
                           @RequestParam(required = false) @ApiParam(value = "会计期间") String periodName,
                           @RequestParam(required = false) @ApiParam(value = "项目类型id") Long projectType,
                           @RequestParam(required = false) @ApiParam(value = "结转单号") String fuzzyBillNum,
                           @RequestParam(required = false) @ApiParam(value = "业务实体id") Long ouId,
                           @RequestParam(required = false) @ApiParam(value = "执行批次") String carryoverBatchNum,
                           @RequestParam(required = false, defaultValue = "1") @ApiParam(value = "0：草稿， 1：正式") Integer status) {
    }

    @ApiOperation(value = "结转单导出")
    @GetMapping("/exportCarryover")
    public void exportCarryover(HttpServletResponse response,
                                @RequestParam(required = false) @ApiParam(value = "项目编号") String fuzzyProjectNum,
                                @RequestParam(required = false) @ApiParam(value = "项目名称") String fuzzyProjectName,
                                @RequestParam(required = false) @ApiParam(value = "会计期间") String periodName,
                                @RequestParam(required = false) @ApiParam(value = "项目类型id") Long projectType,
                                @RequestParam(required = false) @ApiParam(value = "结转单号") String fuzzyBillNum,
                                @RequestParam(required = false) @ApiParam(value = "业务实体id") Long ouId,
                                @RequestParam(required = false) @ApiParam(value = "执行批次") String carryoverBatchNum,
                                @RequestParam(required = false, defaultValue = "1") @ApiParam(value = "0：草稿， 1：正式") Integer status) {
    }

    @ApiOperation(value = "收入计算导出")
    @GetMapping("/exportCarryoverIncomeCollection")
    public void exportCarryoverIncomeCollection(HttpServletResponse response,
                                                @RequestParam(required = false) @ApiParam(value = "项目编号") String fuzzyProjectNum,
                                                @RequestParam(required = false) @ApiParam(value = "项目名称") String fuzzyProjectName,
                                                @RequestParam(required = false) @ApiParam(value = "成本方法") String costMethod,
                                                @RequestParam(required = false) @ApiParam(value = "执行批次") String carryoverBatchNum) {
    }

    @ApiOperation(value = "结转确认计划导出")
    @GetMapping({"exportWaitForCarryoverByBatchNum"})
    public void exportWaitForCarryoverByBatchNum(@RequestParam(required = false) @ApiParam(value = "执行批次") String carryoverBatchNum) {
    }

    @ApiOperation(value = "符合结转条件项目")
    @GetMapping("pageValidCarryoverBill")
    public Response pageValidCarryoverByBatchNum(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                                 @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize,
                                                 @RequestParam(required = false) @ApiParam(value = "项目编号") String fuzzyProjectNum,
                                                 @RequestParam(required = false) @ApiParam(value = "项目名称") String fuzzyProjectName,
                                                 @RequestParam(required = false) @ApiParam(value = "会计期间") String periodName,
                                                 @RequestParam(required = false) @ApiParam(value = "项目类型id") Long projectType,
                                                 @RequestParam(required = false) @ApiParam(value = "结转单号") String fuzzyBillNum,
                                                 @RequestParam(required = false) @ApiParam(value = "业务实体id") Long ouId,
                                                 @RequestParam(required = false) @ApiParam(value = "执行批次") String carryoverBatchNum,
                                                 @RequestParam(required = false, defaultValue = "1") @ApiParam(value = "0：草稿， 1：正式") Integer status) {
        return null;
    }

    @ApiOperation(value = "不符合结转条件项目")
    @GetMapping("pageInValidCarryoverBill")
    public Response pageInValidCarryoverBill(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                             @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize,
                                             @RequestParam(required = false) @ApiParam(value = "项目编号") String fuzzyProjectNum,
                                             @RequestParam(required = false) @ApiParam(value = "项目名称") String fuzzyProjectName,
                                             @RequestParam(required = false) @ApiParam(value = "会计期间") String periodName,
                                             @RequestParam(required = false) @ApiParam(value = "项目类型id") Long projectType,
                                             @RequestParam(required = false) @ApiParam(value = "结转单号") String fuzzyBillNum,
                                             @RequestParam(required = false) @ApiParam(value = "业务实体id") Long ouId,
                                             @RequestParam(required = false) @ApiParam(value = "执行批次") String carryoverBatchNum,
                                             @RequestParam(required = false, defaultValue = "1") @ApiParam(value = "0：草稿， 1：正式") Integer status) {
        return null;
    }

    @ApiOperation(value = "释放结转")
    @PutMapping({"cancel/{batchNum}"})
    public Response batchRemove(@PathVariable("batchNum") @ApiParam("执行批次") String batchNum) {
        return null;
    }

    @ApiOperation(value = "结转单冲销")
    @PostMapping("reverse")
    public Response confirmCarryover(@RequestBody CarryoverBillDto carryoverBillDto) {
        String url = String.format("%sctc/carryoverBill/reverse", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, carryoverBillDto, String.class).getBody();
        DataResponse<Integer> response = JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
        return response;
    }

    @ApiOperation(value = "结转单释放")
    @GetMapping("releaseCarryoverBill")
    public Response releaseCarryoverBill(@RequestParam String carryoverBatchNum,
                                         @RequestParam String projectIdStr) {
        final Map<String, Object> param = new HashMap<>();
        param.put("carryoverBatchNum", carryoverBatchNum);
        param.put("projectIdStr", projectIdStr);
        String url = String.format("%sctc/carryoverBill/releaseCarryoverBill", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.getForEntity(url, String.class, param).getBody();
        DataResponse<Integer> response = JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
        return response;
    }

}
