package com.midea.pam.gateway.ctc.web;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.ProjectHistoryBaseLineDto;
import com.midea.pam.common.ctc.dto.ProjectHistoryHeaderDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("项目里程碑变更历史")
@RestController
@RequestMapping("projectMilepostChangeHistory")
public class ProjectMilepostChangeHistoryController extends ControllerHelper {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查看历史基线列表(里程碑)")
    @GetMapping("v1/findHistoryBaselineList")
    public Response findHistoryBaselineList(@RequestParam @ApiParam("项目id") Long projectId) {
        Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectMilepostChangeHistory/v1/findHistoryBaselineList", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<ProjectHistoryHeaderDto>>>() {
        });
    }

    @ApiOperation(value = "查看历史基线详情(里程碑)")
    @GetMapping("v1/findHistoryBaselineDetail")
    public Response findHistoryBaselineDetail(@RequestParam @ApiParam("项目id") Long projectId, @RequestParam @ApiParam("变更头id") Long headerId) {
        Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        param.put("headerId", headerId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectMilepostChangeHistory/v1/findHistoryBaselineDetail", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectHistoryBaseLineDto>>() {
        });
    }
}
