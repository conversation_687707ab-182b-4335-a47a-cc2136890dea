package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.entity.StorageInventory;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("storageInventory")
@Api("子库管理")
public class StorageInventoryController extends ControllerHelper{
    @Resource
    private RestTemplate restTemplate;

    @ApiOperation("查询库存组织下可用子库列表")
    @GetMapping("listInventory")
    public Response listInventory(@RequestParam(required = false) Long organizationId,
                                   @RequestParam(required = false) String organizationCode,
                                   @RequestParam(required = false) String inventoryType,
                                   @RequestParam(required = false) String secondaryInventoryName
                                  ){
        final Map<String, Object> param = new HashMap<>();
        param.put("organizationId", organizationId);
        param.put("organizationCode", organizationCode);
        param.put("inventoryType", inventoryType);
        param.put("secondaryInventoryName", secondaryInventoryName);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/storageInventory/listInventory", param);
        final String res = restTemplate.getForEntity(url , String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<StorageInventory>>>(){});
    }

    @ApiOperation("更新子库")
    @PutMapping("persistence")
    public Response persistence(@RequestBody StorageInventory storageInventory){
        String url = String.format("%sstorageInventory/persistence", ModelsEnum.BASEDATA.getBaseUrl());
        String responseEntity = restTemplate.postForEntity(url, storageInventory, String.class).getBody();
        DataResponse<Integer> response = JSON.parseObject(responseEntity, new TypeReference<DataResponse<Integer>>() {
        });
        return response;
    }


    @ApiOperation("分页查询库存组织下可用子库列表")
    @GetMapping("pageInventory")
    public Response pageInventory(@RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                  @RequestParam(required = false, defaultValue = "10") final Integer pageSize,
                                  @RequestParam(required = false) String organizationCode,
                                  @RequestParam(required = false) String secondaryInventoryName,
                                  @RequestParam(required = false) String description,
                                  @RequestParam(required = false) String type){
        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        param.put("organizationCode", organizationCode);
        param.put("secondaryInventoryName", secondaryInventoryName);
        param.put("description",description);
        param.put("type",type);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/storageInventory/pageInventory", param);
        final String res = restTemplate.getForEntity(url , String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<StorageInventory>>>(){});

    }
}
