package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.entity.ProjectProductMaintenance;
import com.midea.pam.common.crm.dto.LeadDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.PageResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: 项目产品维护控制层
 * @author: ex_xuwj4
 * @create: 2021-08-24
 **/
@RestController
@RequestMapping("projectProductMaintenance")
@Api("项目产品维护")
public class ProjectProductMaintenanceController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查询项目产品维护列表分页")
    @GetMapping("selectPage")
    public Response selectPage(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                               @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize,
                               @RequestParam(required = false) @ApiParam(value = "项目产品名称") String projectProductName,
                               @RequestParam @ApiParam(value = "使用单位") Long unitId) {

        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("projectProductName", projectProductName);
        param.put("unitId", unitId);

        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "projectProductMaintenance/selectPage", param);
        String res = cleanStr(restTemplate.getForObject(url, String.class));
        PageInfo<ProjectProductMaintenance> data = JSON.parseObject(res, new TypeReference<PageInfo<ProjectProductMaintenance>>() {
        });
        PageResponse<ProjectProductMaintenance> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation(value = "新增产品")
    @PostMapping("add")
    public Response add(@RequestBody @ApiParam(name = "ProjectProductMaintenance", value = "项目产品维护信息") ProjectProductMaintenance projectProductMaintenance) {
        String url = String.format("%sprojectProductMaintenance/add", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, projectProductMaintenance, String.class);
        String res = responseEntity.getBody();
        DataResponse<String> response = Response.dataResponse();
        return response.setData(res);
    }

    @ApiOperation(value = "修改产品")
    @PostMapping("update")
    public com.midea.pam.common.base.Response update(@RequestBody ProjectProductMaintenance projectProductMaintenance) {
        String url = String.format("%sprojectProductMaintenance/update", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, projectProductMaintenance, String.class);
        com.midea.pam.common.base.DataResponse<ProjectProductMaintenance> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<com.midea.pam.common.base.DataResponse<ProjectProductMaintenance>>() {
        });
        return response;
    }

    @ApiOperation(value = "删除产品")
    @GetMapping({"delete"})
    public com.midea.pam.common.base.Response delete(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/projectProductMaintenance/delete", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        com.midea.pam.common.base.DataResponse<Boolean> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<com.midea.pam.common.base.DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "查询项目产品维护列表")
    @GetMapping("selectList")
    public Response selectList(@RequestParam @ApiParam("使用单位") Long unitId) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("unitId", unitId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/projectProductMaintenance/selectList", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<ProjectProductMaintenance>> response= JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<List<ProjectProductMaintenance>>>(){});
        return response;
    }
}
