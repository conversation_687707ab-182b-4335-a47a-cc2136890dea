package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.BankAccountDto;
import com.midea.pam.common.basedata.query.BankAccountQuery;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.PageResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Api("银行账号")
@RestController
@RequestMapping("bankAccount")
public class BankAccountController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "（带参）查询银行账号列表")
    @GetMapping("getBankList")
    public Response getBankList(@RequestParam(required = false) @ApiParam("银行账号") String bankAccountNum,
                                @RequestParam(required = false) @ApiParam("银行账号名称") String bankAccountName,
                                @RequestParam(required = false) @ApiParam("业务实体ID集合【,拼接】") String orgIdStr,
                                @RequestParam(required = false) String ouName,
                                @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        final Map<String, Object> param = new HashMap<>(6);
        param.put("bankAccountNum", bankAccountNum);
        param.put("bankAccountName", bankAccountName);
        param.put("orgIdStr", orgIdStr);
        param.put("ouName", ouName);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);

        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "bankAccount/getBankList", param);
        String res = cleanStr(restTemplate.getForObject(url, String.class));
        PageInfo<BankAccountDto> data = JSON.parseObject(res, new TypeReference<PageInfo<BankAccountDto>>() {
        });
        PageResponse<BankAccountDto> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation(value = "根据条件查询银行账号")
    @GetMapping("list")
    public Response list(BankAccountQuery query) {
        final Map<String, Object> param = new HashMap<>();
        param.put("bankAccountNum", query.getBankAccountNum());
        param.put("orgId", query.getOrgId());
        param.put("ouName", query.getOuName());
        param.put("pageNum", query.getPageNum());
        param.put("pageSize", query.getPageSize());

        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "bankAccount/list", param);
        String res = cleanStr(restTemplate.getForObject(url, String.class, query));
        PageInfo<BankAccountDto> data = JSON.parseObject(res, new TypeReference<PageInfo<BankAccountDto>>() {
        });
        PageResponse<BankAccountDto> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation(value = "银行账号erp同步")
    @GetMapping("getBankAccountFromErp")
    public Response getBankAccountFromErp(@RequestParam(required = false) String lastUpdateDate) {
        final Map<String, Object> param = new HashMap<>();
        param.put("lastUpdateDate", lastUpdateDate);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "bankAccount/getBankAccountFromErp", param);
        restTemplate.getForObject(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        response.setData("0");
        return response;
    }

    @ApiOperation(value = "根据bankAccountId查询银行账户信息")
    @GetMapping("getBankAccountByBankAccountId")
    public Response getBankAccountByBankAccountId(@RequestParam(required = false) Long bankAccountId) {
        Map<String, Object> param = new HashMap<>();
        param.put("bankAccountId", bankAccountId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "bankAccount/getBankAccountByBankAccountId", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<BankAccountDto>>() {
        });
    }
}
