package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.ctc.dto.ProjectSecurityIncidentDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @PackageClassName: com.midea.pam.gateway.ctc.web.ProjectSecurityIncidentController
 * @Description: TODO
 * @Author: JerryH
 * @Date: 2023-02-02, 0002 下午 05:21:09
 */
@Api("项目安全事件")
@RestController
@RequestMapping("statistics/projectSecurityIncidentStatistics")
public class ProjectSecurityIncidentStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查询项目问题列表")
    @GetMapping("page")
    public Response list(ProjectSecurityIncidentDto projectSecurityIncidentDto,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(projectSecurityIncidentDto);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectSecurityIncidentStatistics/page", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<ProjectSecurityIncidentDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ProjectSecurityIncidentDto>>>() {
        });

        return response;
    }

    @ApiOperation(value = "获取项目安全事件严重性类型")
    @GetMapping("getSecurityIncidentSeverityLevelType")
    public Response getSecurityIncidentSeverityLevelType() {
        Map<String, Object> params = new HashMap<>();
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectSecurityIncidentStatistics/getSecurityIncidentSeverityLevelType", params);
        return restTemplate.getForEntity(url, DataResponse.class).getBody();
    }

    private Map<String, Object> buildParam(ProjectSecurityIncidentDto projectSecurityIncidentDto) {
        final Map<String, Object> params = new HashMap<>();
        params.put("projectId",projectSecurityIncidentDto.getProjectId());
        params.put("title",projectSecurityIncidentDto.getTitle());
        params.put("code",projectSecurityIncidentDto.getCode());
        params.put("createByUserName",projectSecurityIncidentDto.getCreateByUserName());
        params.put("severityLevelStr",projectSecurityIncidentDto.getSeverityLevelStr());
        params.put("statusStr",projectSecurityIncidentDto.getStatusStr());
        params.put("happenStartDate",projectSecurityIncidentDto.getHappenStartDate());
        params.put("happenEndDate",projectSecurityIncidentDto.getHappenEndDate());
        return params;
    }
}
