package com.midea.pam.gateway;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.common.utils.PamCurrentUserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/4/15
 * @description 系统设置
 */
@Api("系统设置")
@RestController
@RequestMapping(value = {"systemSet"})
public class SystemSetController extends ControllerHelper {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "切换ERP通用查询接口")
    @GetMapping("changeMassQueryInterface/{strategyNumber}")
    public Response changeMassQueryInterface(@PathVariable("strategyNumber") Integer strategyNumber) {
        logger.info("切换ERP通用查询接口");

        checkAuth();

        String basedataUrl = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "basedata/system/changeMassQueryInterface/" + strategyNumber, null);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(basedataUrl, String.class);

        DataResponse<Boolean> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
        int code = response.getCode();
        if (Code.SUCCESS.getCode() != code) {
            logger.info("切换ERP通用查询接口设置basedata失败");
            return response;
        }

        String ctcUrl = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "ctc/system/changeMassQueryInterface/" + strategyNumber, null);
        responseEntity = restTemplate.getForEntity(ctcUrl, String.class);
        response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    private void checkAuth() {
        String username = PamCurrentUserUtil.getCurrentUserName();
        // 限定部分登陆用户才能操作
        if (!Objects.equals(username, "wangqn1") && !Objects.equals(username, "hongfang")) {
            throw new BizException(Code.ERROR, "该用户无权限");
        }
    }


    @ApiOperation(value = "待办事项中新增同步操作")
    @GetMapping("syndataToErp")
    public com.midea.pam.common.base.Response syndataToErp(@RequestParam String businessType, @RequestParam(required = false) Long applyNo) {
        final Map<String, Object> param = new HashMap<>();
        param.put("businessType", businessType);
        param.put("applyNo",applyNo);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "ctc/system/syndataToErp", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<com.midea.pam.common.base.DataResponse<Boolean>>() {
        });
    }

}
