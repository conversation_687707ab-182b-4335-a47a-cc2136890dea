package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.ProjectCustomerSatisfactionDto;
import com.midea.pam.common.ctc.vo.ProjectCustomerSatisfactionExcelVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @PackageClassName: com.midea.pam.gateway.ctc.web.ProjectCustomerSatisfactionController
 * @Description: 客户满意度
 * @Author: JerryH
 * @Date: 2023-02-02, 0002 下午 02:27:16
 */
@Api("客户满意度")
@RestController
@RequestMapping("statistics/projectCustomerSatisfaction")
public class ProjectCustomerSatisfactionStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "客户满意度列表")
    @GetMapping("page")
    public Response page(ProjectCustomerSatisfactionDto projectCustomerSatisfactionDto,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(projectCustomerSatisfactionDto);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectCustomerSatisfaction/page", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<ProjectCustomerSatisfactionDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ProjectCustomerSatisfactionDto>>>() {
        });

        return response;
    }

    @ApiOperation(value = "项目客户满意度列表导出", response = ResponseMap.class)
    @GetMapping("export")
    public void listExport(HttpServletResponse response, ProjectCustomerSatisfactionDto projectCustomerSatisfactionDto) {
        final Map<String, Object> params = buildParam(projectCustomerSatisfactionDto);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectCustomerSatisfaction/export", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("项目客户满意度_"+ DateUtils.format(new Date(),"yyyyMMddHHmmss"));
        fileName.append(".xls");

        JSONArray materialGetArr = (JSONArray) resultMap.get("projectCustomerSatisfactionList");

        List<ProjectCustomerSatisfactionExcelVO> satisfactionExcelVOS = new ArrayList<>();
        if(materialGetArr != null){
            satisfactionExcelVOS = JSONObject.parseArray(materialGetArr.toJSONString(), ProjectCustomerSatisfactionExcelVO.class);
            for (int i = 0; i < satisfactionExcelVOS.size(); i++) {
                ProjectCustomerSatisfactionExcelVO satisfactionExcelVO = satisfactionExcelVOS.get(i);
                satisfactionExcelVO.setNum(i + 1);
            }
        }

        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(satisfactionExcelVOS, ProjectCustomerSatisfactionExcelVO.class, null, "项目客户满意度", true);

        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    private Map<String, Object> buildParam(ProjectCustomerSatisfactionDto projectCustomerSatisfactionDto) {
        final Map<String, Object> params = new HashMap<>();
        params.put("projectId",projectCustomerSatisfactionDto.getProjectId());
        params.put("satisfactionStatusStr",projectCustomerSatisfactionDto.getSatisfactionStatusStr());
        params.put("title",projectCustomerSatisfactionDto.getTitle());
        params.put("code",projectCustomerSatisfactionDto.getCode());
        params.put("createByUserName",projectCustomerSatisfactionDto.getCreateByUserName());
        return params;
    }

}
