package com.midea.pam.gateway;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.DeliveryAddressDto;
import com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDeliveryAddressHistoryDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

@Api("收货地址历史管理")
@RestController
@RequestMapping("deliveryAddressHistory")
public class PurchaseMaterialRequirementDeliveryAddressHistoryController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @PostMapping("pageList")
    public Response page(@RequestBody PurchaseMaterialRequirementDeliveryAddressHistoryDto dto) {
        final String url = String.format("%sdeliveryAddressHistory/pageList", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<PurchaseMaterialRequirementDeliveryAddressHistoryDto>>>() {
        });
    }

}
