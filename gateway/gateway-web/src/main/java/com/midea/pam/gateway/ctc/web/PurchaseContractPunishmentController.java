package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.CtcAttachmentDto;
import com.midea.pam.common.ctc.dto.PurchaseContractPunishmentConfigDto;
import com.midea.pam.common.ctc.dto.PurchaseContractPunishmentDetailDto;
import com.midea.pam.common.ctc.dto.PurchaseContractPunishmentDto;
import com.midea.pam.common.ctc.vo.PurchaseContractPunishmentConfigExcelVO;
import com.midea.pam.common.ctc.vo.PurchaseContractPunishmentDetailExcelVO;
import com.midea.pam.common.ctc.vo.PurchaseContractPunishmentExcelVO;
import com.midea.pam.common.ctc.vo.PurchaseContractPunishmentVo;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.PurchaseContractPunishmentAccountStatusEnum;
import com.midea.pam.common.enums.PurchaseContractPunishmentApproveEnums;
import com.midea.pam.common.enums.PurchaseContractPunishmentInvoiceStatusEnum;
import com.midea.pam.common.enums.PurchaseContractPunishmentSupplierStatusEnums;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.gateway.entity.FileInfo;
import com.midea.pam.common.gateway.entity.FileInfoExample;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.mapper.FileInfoMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.parameters.P;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 采购合同罚扣控制层
 */
@Api("采购合同罚扣")
@RestController
@RequestMapping("purchaseContractPunishment")
public class PurchaseContractPunishmentController extends ControllerHelper {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private FileInfoMapper fileInfoMapper;

    @ApiOperation("合同罚扣保存")
    @RequestMapping("save")
    public Response save(@RequestBody PurchaseContractPunishmentDto dto) {
        final String url = String.format("%spurchaseContractPunishment/save", ModelsEnum.CTC.getBaseUrl());
        String protocolFile = dto.getProtocolFile();
        List<CtcAttachmentDto> ctcAttachmentDtos = findFileInfoList(protocolFile);
        dto.setAttachmentList(ctcAttachmentDtos);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
    }

    private List<CtcAttachmentDto> findFileInfoList(String fileIdsStr) {
        if(StringUtils.isEmpty(fileIdsStr)){
            return Collections.emptyList();
        }
        List<Long> fileIdList = Arrays.stream(fileIdsStr.split(",")).map(Long::parseLong).collect(Collectors.toList());
        FileInfoExample fileInfoExample = new FileInfoExample();
        fileInfoExample.createCriteria().andIdIn(fileIdList);
        List<FileInfo> fileInfoList = fileInfoMapper.selectByExample(fileInfoExample);
        ArrayList<CtcAttachmentDto> ctcAttachmentDtos = new ArrayList<>();
        if(ListUtils.isNotEmpty(fileInfoList)){
            for (FileInfo fileInfo : fileInfoList) {
                CtcAttachmentDto ctcAttachmentDto = new CtcAttachmentDto();
                ctcAttachmentDto.setAttachId(fileInfo.getId());
                ctcAttachmentDto.setAttachName(fileInfo.getFileName());
                ctcAttachmentDtos.add(ctcAttachmentDto);
            }
        }
       return ctcAttachmentDtos;
    }

    @ApiOperation("合同罚扣修改")
    @RequestMapping("update")
    public Response update(@RequestBody PurchaseContractPunishmentDto dto) {
        final String url = String.format("%spurchaseContractPunishment/update", ModelsEnum.CTC.getBaseUrl());
        List<CtcAttachmentDto> ctcAttachmentDtos = findFileInfoList(dto.getProtocolFile());
        dto.setAttachmentList(ctcAttachmentDtos);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation("合同罚扣列表")
    @RequestMapping("page")
    public Response page(@RequestBody PurchaseContractPunishmentDto dto) {
        final String url = String.format("%spurchaseContractPunishment/page", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<PurchaseContractPunishmentVo>>>() {
        });
    }

    @ApiOperation("合同罚扣明细")
    @RequestMapping("detail")
    public Response detail(@RequestParam("id") Long id) {
        final String url = String.format("%spurchaseContractPunishment/detail?id=%s", ModelsEnum.CTC.getBaseUrl(), id);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PurchaseContractPunishmentVo>>() {
        });
    }

    @ApiOperation(value = "合同罚扣列表导出")
    @PostMapping("export")
    public void export(HttpServletResponse response, @RequestBody PurchaseContractPunishmentDto dto) {
        dto.setPageNum(1);
        dto.setPageSize(Integer.MAX_VALUE); // 导出全部数据，不分页
        dto.setIsExport(true);
        final String url = String.format("%spurchaseContractPunishment/page", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<PageInfo<PurchaseContractPunishmentVo>> punishmentListData = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<PageInfo<PurchaseContractPunishmentVo>>>() {
                });
        List<PurchaseContractPunishmentVo> data = punishmentListData.getData().getList();
        logger.info("合同罚扣列表导出-查询的数据量为:{}", data.size());
        //导出操作
        StringBuffer fileName = new StringBuffer();
        // 获取当前时间
        Date now = new Date();
        // 定义日期格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        // 格式化当前时间
        String formattedDate = sdf.format(now);
        fileName.append("采购合同罚扣_");
        fileName.append(formattedDate);
        fileName.append(".xls");
        int num = 0;
        Integer detailNum = 0;
        List<PurchaseContractPunishmentExcelVO> excelVOS = new ArrayList<>();
        ArrayList<PurchaseContractPunishmentDetailExcelVO> detailExcelVOS = new ArrayList<>();
        HashMap<String, List<PurchaseContractPunishmentDetailDto>> detailMap = new HashMap<>();
        for (PurchaseContractPunishmentVo vo : data) {
            num++;
            PurchaseContractPunishmentExcelVO excelVO = new PurchaseContractPunishmentExcelVO();
            excelVO.setNum(num);
            excelVO.setCode(vo.getCode());
            excelVO.setStatus(PurchaseContractPunishmentApproveEnums.getMsgByCode(vo.getStatus()));
            excelVO.setSupplierConfirmStatus(PurchaseContractPunishmentSupplierStatusEnums.getMsgByCode(vo.getSupplierConfirmStatus()));
            excelVO.setPunishmentType(vo.getPunishmentType());
            excelVO.setVendorCode(vo.getVendorCode());
            excelVO.setVendorName(vo.getVendorName());
            excelVO.setCurrency(vo.getCurrency());
            excelVO.setConversionRate(vo.getConversionRate());
            excelVO.setAmount(vo.getAmount());
            excelVO.setTaxRate(vo.getTaxRate()+"%");;
            excelVO.setProjectCode(vo.getProjectCode());
            excelVO.setContractCode(vo.getContractCode());
            excelVO.setOuName(vo.getOuName());
            excelVO.setTitle(vo.getTitle());
            excelVO.setPunishmentDate(DateUtils.format(vo.getPunishmentDate(), "yyyy-MM-dd"));
            excelVO.setCreateBy(CacheDataUtils.findUserNameById(vo.getCreateBy()));
            excelVO.setCreateTime(DateUtils.format(vo.getCreateAt(), "yyyy-MM-dd HH:mm:ss"));
            excelVO.setAccountStatus(PurchaseContractPunishmentAccountStatusEnum.getMsgByCode(vo.getAccountStatus()));
            excelVO.setInvoiceStatus(PurchaseContractPunishmentInvoiceStatusEnum.getMsgByCode(vo.getInvoiceStatus()));
            excelVO.setCancelCause(vo.getCancelCause());
            excelVOS.add(excelVO);
            for (PurchaseContractPunishmentDetailDto punishmentDetail : vo.getPunishmentDetails()) {
                detailNum++;
                PurchaseContractPunishmentDetailExcelVO detailExcelVO = new PurchaseContractPunishmentDetailExcelVO();
                detailExcelVO.setNum(detailNum);
                detailExcelVO.setCode(vo.getCode());
                detailExcelVO.setRemark(punishmentDetail.getRemark());
                detailExcelVO.setAmount(punishmentDetail.getAmount());
                detailExcelVO.setItem(punishmentDetail.getItem());
                detailExcelVOS.add(detailExcelVO);
            }

        }
        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(excelVOS, PurchaseContractPunishmentExcelVO.class, null, "基本信息", true);
        ExportExcelUtil.addSheet(workbook, detailExcelVOS, PurchaseContractPunishmentDetailExcelVO.class, null, "罚扣内容", true);
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    @ApiOperation("合同罚扣作废")
    @GetMapping("cancel")
    public Response canal(@RequestParam("id") Long id,@RequestParam("cancelCause")String cancelCause) {
        final String url = String.format("%spurchaseContractPunishment/cancel?id=%s&cancelCause=%s", ModelsEnum.CTC.getBaseUrl(), id,cancelCause);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
    }


    @ApiOperation("合同罚扣供应商状态修改")
    @GetMapping("supplierStatusEdit")
    public Response supplierStatusEdit(@RequestParam("id") Long id, @RequestParam("supplierConfirmStatus") Integer supplierConfirmStatus) {
        final String url = String.format("%spurchaseContractPunishment/supplierStatusEdit?id=%s&supplierConfirmStatus=%s", ModelsEnum.CTC.getBaseUrl(), id, supplierConfirmStatus);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation("合同罚扣证明文件上传")
    @PostMapping("proveFileUpload")
    public Response proveFileUpload(@RequestBody Map<String,Object> params) {
        Long id = Long.parseLong(params.get("id").toString());
        String proveFile = (String) params.get("proveFile");
        String proveRemark = (String) params.get("proveRemark");
        final String url = String.format("%spurchaseContractPunishment/proveFileUpload?id=%s&proveFile=%s&proveRemark=%s", ModelsEnum.CTC.getBaseUrl(), id, proveFile,proveRemark);
        List<CtcAttachmentDto> ctcAttachmentDtos = findFileInfoList(proveFile);
        PurchaseContractPunishmentDto purchaseContractPunishmentDto = new PurchaseContractPunishmentDto();
        purchaseContractPunishmentDto.setProveAttachmentList(ctcAttachmentDtos);
        purchaseContractPunishmentDto.setId(id);
        purchaseContractPunishmentDto.setProveRemark(proveRemark);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url,purchaseContractPunishmentDto,String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation("合同罚扣撤回")
    @GetMapping("withdraw")
    public Response withdraw(@RequestParam("id") Long id) {
        final String url = String.format("%spurchaseContractPunishment/withdraw?id=%s", ModelsEnum.CTC.getBaseUrl(), id);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation("查询合同税率集合")
    @GetMapping("taxRateList")
    public Response selectPurchaseContractTaxRateList(@RequestParam("id") Long purchaseContractId) {
        final String url = String.format("%spurchaseContractPunishment/taxRateList?id=%s", ModelsEnum.CTC.getBaseUrl(), purchaseContractId);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<Integer>>>() {
        });
    }

    @ApiOperation("查询合同税率集合")
    @GetMapping("selectCurrencyList")
    public Response selectCurrencyList() {
        final String url = String.format("%spurchaseContractPunishment/selectCurrencyList", ModelsEnum.CTC.getBaseUrl(),null);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<String>>>() {
        });
    }

    @ApiOperation("根据状态查询合同罚扣列表")
    @PostMapping("selectListByStatus")
    public Response selectListByStatus(@RequestBody PurchaseContractPunishmentDto dto){
        final String url = String.format("%spurchaseContractPunishment/selectListByStatus", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<PurchaseContractPunishmentVo>>>() {
        });
    }

    @ApiOperation("我的合同罚扣列表")
    @RequestMapping("createByMePage")
    public Response createByMePage(@RequestBody PurchaseContractPunishmentDto dto) {
        final String url = String.format("%spurchaseContractPunishment/createByMePage", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<PurchaseContractPunishmentVo>>>() {
        });
    }

}
