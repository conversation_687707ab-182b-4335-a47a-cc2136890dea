package com.midea.pam.gateway.ctc.web;

import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @program: pam
 * @description: esb日志测试接口 网关控制层
 * @author: xiedl
 * @create: 2021-8-4
 **/
@RestController
@RequestMapping("esbResultReturn")
@Api("esb日志")
public class EsbResultReturnController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "测试新增")
    @GetMapping("testInsert")
    public Response testInsert(){
        final Map<String, Object> param = new HashMap<>();
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "esbResultReturn/testInsert",param);
        restTemplate.getForObject(url, String.class);
        DataResponse<Integer> response = Response.dataResponse();
        response.setData(0);
        return response;
    }
}
