package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.basedata.dto.MaterialCheckDto;
import com.midea.pam.common.basedata.dto.MaterialDto;
import com.midea.pam.common.basedata.entity.Material;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.PageResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("物料基础信息")
@RestController
@RequestMapping("material")
public class MaterialController extends ControllerHelper {

    @Autowired
    private RestTemplate restTemplate;


    @ApiOperation(value = "查询物料分页", response = MaterialDto.class)
    @GetMapping("getMaterialList")
    public Response selectPage(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                               @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                               @RequestParam(required = false) String organizationName,
                               @RequestParam(required = false) String itemCodeFrom,
                               @RequestParam(required = false) String itemCodeTo,
                               @RequestParam(required = false) String itemInfo,
                               @RequestParam(required = false) String itemType,
                               @RequestParam(required = false) String itemStatus,
                               @RequestParam(required = false) @ApiParam("库存组织") String organizationCode,
                               @RequestParam(required = false) @ApiParam("库存组织id") Long organizationId,
                               @RequestParam(required = false) @ApiParam("名称模糊匹配") String fuzzyName,
                               @RequestParam(required = false) @ApiParam("型号模糊匹配") String fuzzyModel) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("organizationName", organizationName);
        param.put("itemCodeFrom", itemCodeFrom);
        param.put("itemCodeTo", itemCodeTo);
        param.put("itemInfo", itemInfo);
        param.put("itemType", itemType);
        param.put("itemStatus", itemStatus);
        param.put("organizationCode", organizationCode);
        param.put("organizationId", organizationId);
        param.put("fuzzyName", fuzzyName);
        param.put("fuzzyModel", fuzzyModel);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "material/selectPage", param);
        String res = restTemplate.getForObject(url, String.class);
        res = cleanStr(res);
        PageInfo<Map<String, Object>> data = JSON.parseObject(res, new TypeReference<PageInfo<Map<String, Object>>>() {
        });
        PageResponse<Map<String, Object>> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation(value = "查询物料以及设计成本分页", response = MaterialDto.class)
    @GetMapping({"materialWithCostPage"})
    public Response materialWithCostPage(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                         @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                                         @RequestParam(required = false) @ApiParam("库存组织") String organizationCode,
                                         @RequestParam(required = false) @ApiParam("库存组织id") Long organizationId,
                                         @RequestParam(required = false) @ApiParam("名称模糊匹配") String fuzzyName,
                                         @RequestParam(required = false) @ApiParam("型号模糊匹配") String fuzzyModel,
                                         @RequestParam(required = false) @ApiParam("名称") String name,
                                         @RequestParam(required = false) @ApiParam("型号") String model,
                                         @RequestParam(required = false) @ApiParam("品牌") String brand,
                                         @RequestParam(required = false) @ApiParam("二级模糊匹配(名称或型号)") String secFuzzyMatch,
                                         @RequestParam(required = false) @ApiParam("物料小类") String materialType,
                                         @RequestParam(required = false) @ApiParam("查询类型 （1：导入查询类型）") String searchType,
                                         @RequestParam(required = false) @ApiParam("物料中类") String materialMiddleClass,
                                         @RequestParam(required = false) @ApiParam("图号") String figureNumber,
                                         @RequestParam(required = false) @ApiParam("图号") String brandMaterialCode,
                                         @RequestParam(required = false) @ApiParam("物料描述") String itemInfo) throws UnsupportedEncodingException {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("organizationCode", organizationCode);
        param.put("organizationId", organizationId);
        param.put("fuzzyName", StringUtils.isEmpty(fuzzyName) ? fuzzyName : URLEncoder.encode(fuzzyName, "UTF-8"));
        param.put("fuzzyModel", StringUtils.isEmpty(fuzzyModel) ? fuzzyModel : URLEncoder.encode(fuzzyModel, "UTF-8"));
        param.put("name", StringUtils.isEmpty(name) ? name : URLEncoder.encode(name, "UTF-8"));
        param.put("model", StringUtils.isEmpty(model) ? model : URLEncoder.encode(model, "UTF-8"));
        param.put("brand", StringUtils.isEmpty(brand) ? brand : URLEncoder.encode(brand, "UTF-8"));
        param.put("secFuzzyMatch", StringUtils.isEmpty(secFuzzyMatch) ? secFuzzyMatch : URLEncoder.encode(secFuzzyMatch, "UTF-8"));
        param.put("materialType", StringUtils.isEmpty(materialType) ? materialType : URLEncoder.encode(materialType, "UTF-8"));
        param.put("searchType", searchType);
        param.put("materialMiddleClass", materialMiddleClass);
        param.put("figureNumber", figureNumber);
        param.put("brandMaterialCode", brandMaterialCode);
        param.put("itemInfo", StringUtils.isEmpty(itemInfo) ? itemInfo : URLEncoder.encode(itemInfo, "UTF-8"));
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/material/materialWithCostPage", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<MaterialDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<MaterialDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "查询物料列表", response = MaterialDto.class)
    @GetMapping("selectList")
    public Response selectList(@RequestParam(required = false) @ApiParam("库存组织") String organizationCode,
                               @RequestParam(required = false) @ApiParam("库存组织id") Long organizationId,
                               @RequestParam(required = false) @ApiParam("名称模糊匹配") String fuzzyName,
                               @RequestParam(required = false) @ApiParam("型号模糊匹配") String fuzzyModel,
                               @RequestParam(required = false) @ApiParam("名称") String name,
                               @RequestParam(required = false) @ApiParam("型号") String model,
                               @RequestParam(required = false) @ApiParam("品牌") String brand,
                               @RequestParam(required = false) @ApiParam("二级模糊匹配(名称或型号)") String secFuzzyMatch,
                               @RequestParam(required = false) @ApiParam("物料小类") String materialType) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("organizationCode", organizationCode);
        param.put("organizationId", organizationId);
        param.put("fuzzyName", fuzzyName);
        param.put("fuzzyModel", fuzzyModel);
        param.put("name", name);
        param.put("model", model);
        param.put("brand", brand);
        param.put("secFuzzyMatch", secFuzzyMatch);
        param.put("materialType", materialType);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "material/selectList", param);
        String res = restTemplate.getForObject(url, String.class);
        res = cleanStr(res);
        PageInfo<Map<String, Object>> data = JSON.parseObject(res, new TypeReference<PageInfo<Map<String, Object>>>() {
        });
        PageResponse<Map<String, Object>> response = Response.pageResponse();
        return response.convert(data);
    }


    @ApiOperation(value = "新增物料")
    @PostMapping("add")
    public Response add(@RequestBody Material material) {
        String url = String.format("%smaterial/add", ModelsEnum.BASEDATA.getBaseUrl());

        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, material, String.class);
        String res = cleanStr(responseEntity.getBody());
        DataResponse<String> response = Response.dataResponse();
        return response.setData(res);
    }

    @ApiOperation(value = "修改物料")
    @PutMapping("update")
    public Response update(@RequestBody Material material) {
        String url = String.format("%smaterial/update", ModelsEnum.BASEDATA.getBaseUrl());
        String res = restTemplate.exchange(url, HttpMethod.PUT, new HttpEntity<Material>(material), String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation(value = "删除物料")
    @DeleteMapping("{id}")
    public Response delete(@PathVariable Long id) {
        String url = String.format("%smaterial/" + id, ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.DELETE, null, String.class);
        String res = cleanStr(responseEntity.getBody());
        DataResponse<String> response = Response.dataResponse();
        return response.setData(res);
    }

    @ApiOperation(value = "手工同步物料数据")
    @GetMapping("getMaterialFromErp")
    public void getMaterialFromErp(@RequestParam(required = false) String lastUpdateDate,
                                   @RequestParam(required = false) String lastUpdateDateEnd) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("lastUpdateDate", lastUpdateDate);
        param.put("lastUpdateDateEnd", lastUpdateDateEnd);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "material/getMaterialFromErp", param);
        String res = restTemplate.getForObject(url, String.class);
    }

    @ApiOperation(value = "推送物料到erp")
    @PostMapping("callErpItemImport")
    public void callErpItemImport(@RequestBody List<Long> ids) throws Exception {
        String url = String.format("%smaterial/callErpItemImport", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, ids, String.class);
        EsbResponse response = JSON.parseObject(responseEntity.getBody(), new TypeReference<EsbResponse>() {
        });
    }

    @ApiOperation(value = "通过物料描述模糊查询物料以及设计成本分页")
    @GetMapping("materialByItemInfoPage")
    public Response materialByItemInfoPage(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                           @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                                           @RequestParam(required = false) @ApiParam("物料描述") String itemInfo) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("itemInfo", itemInfo);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/material/materialByItemInfoPage", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<MaterialDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<MaterialDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "根据库存组织查询物料列表")
    @GetMapping("selectListByOrg")
    public Response selectListByOrg(@RequestParam(required = false) @ApiParam("模糊查询") String fuzzyLike,
                                    @RequestParam(required = false) @ApiParam("退料类型") Integer materialGetType,
                                    @RequestParam(required = false) @ApiParam("工单任务id") Long ticketTasksId,
                                    @RequestParam(required = true) @ApiParam("库存组织ID") Long organizationId,
                                    @RequestParam(required = false) @ApiParam("退市模糊查询") String fuzzyLikeDelist) {
        final Map<String, Object> param = new HashMap<>();
        param.put("organizationId", organizationId);
        param.put("fuzzyLike", fuzzyLike);
        param.put("materialGetType", materialGetType);
        param.put("ticketTasksId", ticketTasksId);
        param.put("fuzzyLikeDelist", fuzzyLikeDelist);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/material/selectListByOrg", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<MaterialDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<MaterialDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "根据库存组织查询物料列表")
    @PostMapping("selectListByOrgNew")
    public Response selectListByOrgNew(@RequestBody MaterialDto dto) {
        Asserts.notEmpty(dto.getOrganizationId(), ErrorCode.BASEDATA_ORGANIZATION_ID_NOT_NULL);

        String url = String.format("%smaterial/selectListByOrgNew", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<MaterialDto>>>() {
        });
    }

    @ApiOperation(value = "物料详情")
    @GetMapping("view")
    public Response view(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/material/view", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<MaterialDto> response = JSON.parseObject(res, new TypeReference<DataResponse<MaterialDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "通过pam编码获取物料信息")
    @GetMapping("getByPamCode")
    public Response getByPamCode(@RequestParam String pamCode,
                                 @RequestParam(required = false) Long organizationId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pamCode", pamCode);
        param.put("organizationId", organizationId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/material/getByPamCode", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<MaterialDto> response = JSON.parseObject(res, new TypeReference<DataResponse<MaterialDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "通过erp编码获取物料信息")
    @GetMapping("getByErpCode")
    public Response getByErpCode(@RequestParam String erpCode,
                                 @RequestParam(required = false) Long organizationId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("erpCode", erpCode);
        param.put("organizationId", organizationId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/material/getByErpCode", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<MaterialDto> response = JSON.parseObject(res, new TypeReference<DataResponse<MaterialDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "通过erp编码获取物料信息")
    @GetMapping("getByErpCodes")
    public Response getByErpCodes(@RequestParam String erpCode,
                                  @RequestParam(required = false) Long organizationId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("erpCode", erpCode);
        param.put("organizationId", organizationId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/material/getByErpCodes", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<MaterialDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<MaterialDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "根据库存组织查询物料详情", response = MaterialDto.class)
    @GetMapping({"materialPageByOrg"})
    public Response materialPageByOrg(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                      @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                                      @RequestParam(required = false) @ApiParam("PAM物料编码模糊匹配") String pamCode,
                                      @RequestParam(required = false) @ApiParam("ERP物料编码模糊匹配") String erpCode,
                                      @RequestParam(required = false) @ApiParam("库存组织id") Long organizationId
    ) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("pamCode", pamCode);
        param.put("erpCode", erpCode);
        param.put("organizationId", organizationId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/material/materialPageByOrg", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<MaterialDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<MaterialDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "根据OU与erp或pam编码查询物料详情", response = MaterialDto.class)
    @GetMapping({"materialPageByCodeAndOu"})
    public Response materialPageByCodeAndOu(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                            @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                                            @RequestParam(required = false) @ApiParam("PAM物料编码模糊匹配") String pamCode,
                                            @RequestParam(required = false) @ApiParam("ERP物料编码模糊匹配") String erpCode,
                                            @RequestParam(required = false) @ApiParam("业务实体id") Long ouId
    ) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("pamCode", pamCode);
        param.put("erpCode", erpCode);
        param.put("ouId", ouId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/material/materialPageByCodeAndOu", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<MaterialDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<MaterialDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "推送物料到erp")
    @PostMapping("materialPushErpItemImport")
    public void materialPushErpItemImport(@RequestBody List<Long> ids) throws Exception {
        String url = String.format("%smaterial/materialPushErpItemImport", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, ids, String.class);
        EsbResponse response = JSON.parseObject(responseEntity.getBody(), new TypeReference<EsbResponse>() {
        });
    }

    @ApiOperation(value = "根据库存组织和物料编码查询物料对应活动事项编码")
    @PostMapping("getActivityCodeByOrganizationIdAndItemCode/{organizationId}")
    public Response getActivityCodeByOrganizationIdAndItemCode(@PathVariable Long organizationId, @RequestBody List<String> itemCodes) {
        String url = String.format("%smaterial/getActivityCodeByOrganizationIdAndItemCode/" + organizationId, ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, itemCodes, String.class);
        String res = responseEntity.getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Map<String, Object>>>() {
        });
    }

    @ApiOperation(value = "校验物料是否同步erp成功")
    @PostMapping("checkMaterialSyncErp")
    public Response checkMaterialSyncErp(@RequestBody MaterialCheckDto materialCheckDto) {
        String url = String.format("%smaterial/checkMaterialSyncErp", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, materialCheckDto, String.class);
        String res = responseEntity.getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

}
