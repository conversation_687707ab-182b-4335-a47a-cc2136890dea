package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.MaterialTransferDto;
import com.midea.pam.common.ctc.vo.MaterialTransferExcelVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/3/1
 * @description
 */
@Api("统计-转移单")
@RestController
@RequestMapping("statistics/materialTransfer")
public class MaterialTransferStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "查询转移单列表")
    @GetMapping("page")
    public Response list(MaterialTransferDto materialTransferDto,
                         @RequestParam(required = false) final String applyDateStart,
                         @RequestParam(required = false) final String applyDateEnd,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(materialTransferDto);
        params.put("list", Boolean.TRUE);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        params.put("applyDateStart", applyDateStart);
        params.put("applyDateEnd", applyDateEnd);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/materialTransfer/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<MaterialTransferDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<MaterialTransferDto>>>() {
        });

        return response;
    }

    private Map buildParam(MaterialTransferDto materialTransferDto) {
        final Map<String, Object> params = new HashMap<>();
        //转移单编号 单据状态 申请人 制单人 申请日期 发料项目号 发料子库 发料货位 收料项目号 收料子库 收料货位 同步状态
        params.put("transferCode", materialTransferDto.getTransferCode());
        params.put("statusStr", materialTransferDto.getStatusStr());
        params.put("applyUserId", materialTransferDto.getApplyUserId());
        params.put("applyUserName", materialTransferDto.getApplyUserName());
        params.put("fillUserId", materialTransferDto.getFillUserId());
        params.put("fillUserName", materialTransferDto.getFillUserName());
        params.put("sendProjectCode", materialTransferDto.getSendProjectCode());
        params.put("sendInventoryCode", materialTransferDto.getSendInventoryCode());
        params.put("sendLocation", materialTransferDto.getSendLocation());
        params.put("sendManagerName", materialTransferDto.getSendManagerName());
        params.put("receiveProjectCode", materialTransferDto.getReceiveProjectCode());
        params.put("receiveInventoryCode", materialTransferDto.getReceiveInventoryCode());
        params.put("receiveLocation", materialTransferDto.getReceiveLocation());
        params.put("receiveManagerName", materialTransferDto.getReceiveManagerName());
        params.put("erpCodeStr", materialTransferDto.getErpCodeStr());
        params.put("resouce", materialTransferDto.getResouce());
        params.put("operatingUnitIdsStr", materialTransferDto.getOperatingUnitIdsStr());
        params.put("organizationCodesStr", materialTransferDto.getOrganizationCodesStr());
        return params;
    }


    @ApiOperation(value = "转移单列表导出", response = ResponseMap.class)
    @GetMapping("export")
    public void listExport(HttpServletResponse response,
                           MaterialTransferDto materialTransferDto,
                           @RequestParam(required = false) final String applyDateStart,
                           @RequestParam(required = false) final String applyDateEnd) {
        final Map<String, Object> params = buildParam(materialTransferDto);
        params.put("applyDateStart", applyDateStart);
        params.put("applyDateEnd", applyDateEnd);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/materialTransfer/v1/export", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("转移单_"+ DateUtils.format(new Date(),"yyyyMMddHHmmss"));
        fileName.append(".xls");

        JSONArray materialTransferArr = (JSONArray) resultMap.get("materialTransferList");

        List<MaterialTransferExcelVO> materialTransferExcelVOS = new ArrayList<>();
        if(materialTransferArr != null){
            materialTransferExcelVOS = JSONObject.parseArray(materialTransferArr.toJSONString(), MaterialTransferExcelVO.class);
            for (int i = 0; i < materialTransferExcelVOS.size(); i++) {
                MaterialTransferExcelVO materialTransferExcelVO = materialTransferExcelVOS.get(i);
                materialTransferExcelVO.setNum(i + 1);
            }
        }

        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(materialTransferExcelVOS, MaterialTransferExcelVO.class, null, "转移单", true);

        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

}
