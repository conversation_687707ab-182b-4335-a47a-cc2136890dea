package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.VendorSiteBankDto;
import com.midea.pam.common.basedata.entity.VendorSiteBank;
import com.midea.pam.common.basedata.entity.VendorSiteBankForDisplay;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.PageResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 供应商controller
 * @date 2019-3-19
 */
@RestController
@RequestMapping("vendor")
@Api("供应商")
public class VendorController extends ControllerHelper {

    @Autowired
    private RestTemplate restTemplate;


    @ApiOperation(value = "查询供应商列表分页", response = VendorSiteBankForDisplay.class)
    @GetMapping("selectPage")
    public Response selectPage(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                               @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize,
                               @RequestParam(required = false) @ApiParam(value = "供应商地点ID") Long erpVendorSiteId,
                               @RequestParam(required = false) @ApiParam(value = "业务实体") String operatingUnitName,
                               @RequestParam(required = false) @ApiParam(value = "供应商名称") String vendorName,
                               @RequestParam(required = false) @ApiParam(value = "OU") Long ouId,
                               @RequestParam(required = false) @ApiParam(value = "供应商编码") String vendorCode,
                               @RequestParam(required = false) @ApiParam(value = "统一社会信用代码") String uniteCreditCode,
                               @RequestParam(required = false) @ApiParam(value = "组织机构代码") String vendorOrgCode,
                               @RequestParam(required = false) @ApiParam(value = "税务登记号") String taxRegisterCode,
                               @RequestParam(required = false) @ApiParam(value = "银行账号") String bankAccount,
                               @RequestParam(required = false) @ApiParam(value = "付款方式") String paymentMethodName,
                               @RequestParam(required = false) @ApiParam(value = "付款条件") String termName,
                               @RequestParam(required = false) @ApiParam(value = "是否可采购") String purchasingSiteFlag,
                               @RequestParam(required = false) @ApiParam(value = "供应商类型名称，多个逗号分隔") String vendorTypeNameStr) {

        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("erpVendorSiteId", erpVendorSiteId);
        param.put("operatingUnitName", operatingUnitName);
        param.put("vendorName", vendorName);
        param.put("ouId", ouId);
        param.put("vendorCode", vendorCode);
        param.put("uniteCreditCode", uniteCreditCode);
        param.put("vendorOrgCode", vendorOrgCode);
        param.put("taxRegisterCode", taxRegisterCode);
        param.put("bankAccount", bankAccount);
        param.put("paymentMethodName", paymentMethodName);
        param.put("termName", termName);
        param.put("purchasingSiteFlag", purchasingSiteFlag);
        param.put("vendorTypeNameStr", vendorTypeNameStr);

        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "vendor/selectPage", param);
        String res = cleanStr(restTemplate.getForObject(url, String.class));
        PageInfo<VendorSiteBankForDisplay> data = JSON.parseObject(res, new TypeReference<PageInfo<VendorSiteBankForDisplay>>() {
        });
        PageResponse<VendorSiteBankForDisplay> response = Response.pageResponse();
        return response.convert(data);

    }

    @ApiOperation(value = "查询供应商列表分页(供应商+地点去重)", response = VendorSiteBankForDisplay.class)
    @GetMapping("selectPageNew")
    public Response selectPageNew(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                  @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize,
                                  @RequestParam(required = false) @ApiParam(value = "供应商地点ID") Long erpVendorSiteId,
                                  @RequestParam(required = false) @ApiParam(value = "业务实体") String operatingUnitName,
                                  @RequestParam(required = false) @ApiParam(value = "供应商名称") String vendorName,
                                  @RequestParam(required = false) @ApiParam(value = "供应商编码或名称") String vendorCodeOrName,
                                  @RequestParam(required = false) @ApiParam(value = "OU") Long ouId,
                                  @RequestParam(required = false) @ApiParam(value = "OU（多选）") String ouIdsStr,
                                  @RequestParam(required = false) @ApiParam(value = "供应商编码") String vendorCode,
                                  @RequestParam(required = false) @ApiParam(value = "统一社会信用代码") String uniteCreditCode,
                                  @RequestParam(required = false) @ApiParam(value = "组织机构代码") String vendorOrgCode,
                                  @RequestParam(required = false) @ApiParam(value = "税务登记号") String taxRegisterCode,
                                  @RequestParam(required = false) @ApiParam(value = "银行账号") String bankAccount,
                                  @RequestParam(required = false) @ApiParam(value = "付款方式") String paymentMethodName,
                                  @RequestParam(required = false) @ApiParam(value = "付款条件") String termName,
                                  @RequestParam(required = false) @ApiParam(value = "展示不可采购的供应商标识") Integer showPurchasingSiteFlagIsN,
                                  @RequestParam(required = false) @ApiParam(value = "是否可采购") String purchasingSiteFlag) {

        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("erpVendorSiteId", erpVendorSiteId);
        param.put("operatingUnitName", operatingUnitName);
        param.put("vendorName", vendorName);
        param.put("vendorCodeOrName", vendorCodeOrName);
        param.put("ouId", ouId);
        param.put("ouIdsStr", ouIdsStr);
        param.put("vendorCode", vendorCode);
        param.put("uniteCreditCode", uniteCreditCode);
        param.put("vendorOrgCode", vendorOrgCode);
        param.put("taxRegisterCode", taxRegisterCode);
        param.put("bankAccount", bankAccount);
        param.put("paymentMethodName", paymentMethodName);
        param.put("termName", termName);
        param.put("purchasingSiteFlag", purchasingSiteFlag);
        param.put("showPurchasingSiteFlagIsN", showPurchasingSiteFlagIsN);

        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "vendor/selectPageNew", param);
        String res = cleanStr(restTemplate.getForObject(url, String.class));
        PageInfo<VendorSiteBankForDisplay> data = JSON.parseObject(res, new TypeReference<PageInfo<VendorSiteBankForDisplay>>() {
        });
        PageResponse<VendorSiteBankForDisplay> response = Response.pageResponse();
        return response.convert(data);

    }

    @ApiOperation(value = "查询供应商列表", response = VendorSiteBankDto.class)
    @GetMapping("selectList")
    public Response selectList(@RequestParam(required = true) @ApiParam(value = "供应商编码") String vendorCode,
                               @RequestParam(required = false) @ApiParam(value = "供应商名称") String vendorName,
                               @RequestParam(required = false) @ApiParam(value = "供应商地点ID") String erpVendorSiteId,
                               @RequestParam(required = false) @ApiParam(value = "OU") Long ouId,
                               @RequestParam(required = false) @ApiParam(value = "付款方式编码") String paymentMethodCode,
                               @RequestParam(required = false) @ApiParam(value = "业务实体") String operatingUnitName) {
        final Map<String, Object> param = new HashMap<>();
        param.put("operatingUnitName", operatingUnitName);
        param.put("vendorName", vendorName);
        param.put("erpVendorSiteId", erpVendorSiteId);
        param.put("ouId", ouId);
        param.put("vendorCode", vendorCode);
        param.put("paymentMethodCode", paymentMethodCode);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "vendor/selectList", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<VendorSiteBankDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<VendorSiteBankDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "获取地点币种", response = Response.class)
    @GetMapping("getCurrencyCode")
    public Response getCurrencyCode(@RequestParam(required = false) @ApiParam(value = "ouId") Long ouId,
                                    @RequestParam(required = false) @ApiParam(value = "供应商地点名称") String vendorSiteCode,
                                    @RequestParam(required = false) @ApiParam(value = "供应商名称") String vendorName) {
        final Map<String, Object> map = new HashMap<>();
        map.put("ouId", ouId);
        map.put("vendorSiteCode", vendorSiteCode);
        map.put("vendorName", vendorName);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "vendor/getCurrencyCode", map);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<VendorSiteBank> response = JSON.parseObject(res, new TypeReference<DataResponse<VendorSiteBank>>() {
        });
        return response;
    }

    @ApiOperation(value = "根据指定时间或前一天0点拉取数据")
    @GetMapping("getVendorFromErp")
    public Response getVendorFromErp(@RequestParam(required = false) @ApiParam(value = "格式：yyyy-MM-dd hh:mm:ss") String lastUpdateDate,
                                     @RequestParam(required = false) @ApiParam(value = "格式：yyyy-MM-dd hh:mm:ss") String lastUpdateDateEnd) {

        final Map<String, Object> param = new HashMap<>();
        param.put("lastUpdateDate", lastUpdateDate);
        param.put("lastUpdateDateEnd", lastUpdateDateEnd);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "vendor/getVendorFromErp", param);
        String res = cleanStr(restTemplate.getForObject(url, String.class));

        DataResponse<String> response = Response.dataResponse();
        return response.setData(res);
    }

    @ApiOperation(value = "详情", response = VendorSiteBankDto.class)
    @GetMapping("view")
    public Response view(@RequestParam(required = true) Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "vendor/view", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<VendorSiteBankDto> response = JSON.parseObject(res, new TypeReference<DataResponse<VendorSiteBankDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "获取供应商类型", response = Response.class)
    @GetMapping("getVendorTypeNames")
    public Response getVendorTypeNames() {
        final Map<String, Object> param = new HashMap<>();
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "vendor/getVendorTypeNames", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<String> response = JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
        return response;
    }

    @ApiOperation(value = "根据供应商编号和ouId查询供应商信息", response = Response.class)
    @GetMapping("getVendorSiteBankListByCodeAndOuId")
    public Response getVendorSiteBankListByCodeAndOuId(@RequestParam("ouId") Long ouId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("ouId", ouId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "vendor/getVendorSiteBankListByCodeAndOuId", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<VendorSiteBankDto>>>() {
        });
    }
}
