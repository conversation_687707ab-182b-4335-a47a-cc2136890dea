package com.midea.pam.gateway;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.DeliveryAddressDto;
import com.midea.pam.common.ctc.vo.DeliveryAddressExcelVO;
import com.midea.pam.common.ctc.vo.PurchaseContractPunishmentDetailExcelVO;
import com.midea.pam.common.ctc.vo.PurchaseContractPunishmentExcelVO;
import com.midea.pam.common.ctc.vo.PurchaseContractPunishmentVo;
import com.midea.pam.common.enums.DeliveryAddressAttributeEnum;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtil;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Api("收货地址管理")
@RestController
@RequestMapping("deliveryAddress")
public class DeliveryAddressController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @PostMapping("page")
    public Response page(@RequestBody DeliveryAddressDto deliveryAddressDto) {
        final String url = String.format("%sdeliveryAddress/page", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, deliveryAddressDto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<DeliveryAddressDto>>>() {
        });
    }

    @ApiOperation("保存或更新")
    @PostMapping("saveOrUpdate")
    public Response saveOrUpdate(@RequestBody List<DeliveryAddressDto> deliveryAddressDtoList) {
        final String url = String.format("%sdeliveryAddress/saveOrUpdate", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, deliveryAddressDtoList, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation("逻辑删除")
    @GetMapping("logicDelete")
    public Response LogicDelete(@RequestParam Long id) {
        final String url = String.format("%sdeliveryAddress/logicDelete?id="+id, ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation("导出")
    @PostMapping("export")
    public void export(HttpServletResponse response, @RequestBody DeliveryAddressDto deliveryAddressDto) {
        final String url = String.format("%sdeliveryAddress/export", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, deliveryAddressDto, String.class);
        DataResponse<List<DeliveryAddressDto>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<DeliveryAddressDto>>>() {
        });
        //导出操作
        StringBuffer fileName = new StringBuffer();
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        String formattedTime = now.format(formatter);
        fileName.append("收货地址_");
        fileName.append(formattedTime);
        fileName.append(".xls");
        ArrayList<DeliveryAddressExcelVO> excelVOS = new ArrayList<>();
        List<DeliveryAddressDto> deliveryAddressDtoList = dataResponse.getData();
        int num = 0;
        if (ListUtils.isNotEmpty(deliveryAddressDtoList)) {
            for (DeliveryAddressDto resultDto : deliveryAddressDtoList) {
                num++;
                DeliveryAddressExcelVO excelVO = new DeliveryAddressExcelVO();
                excelVO.setDeliveryAddress(resultDto.getDeliveryAddress());
                excelVO.setConsignee(resultDto.getConsignee());
                excelVO.setContactPhone(resultDto.getContactPhone());
                excelVO.setRemark(resultDto.getRemark());
                excelVO.setAttributeName(DeliveryAddressAttributeEnum.getNameByCode(resultDto.getAttribute().intValue()));
                excelVO.setProjectCode(resultDto.getProjectCode());
                excelVO.setUpdateByName(resultDto.getUpdateByName());
                excelVO.setUpdateTime(DateUtil.format(resultDto.getUpdateAt(), "yyyy-MM-dd"));
                excelVO.setNum(num);
                excelVOS.add(excelVO);
            }
        }
        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(excelVOS, DeliveryAddressExcelVO.class, null, "收货地址", true);
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }
}
