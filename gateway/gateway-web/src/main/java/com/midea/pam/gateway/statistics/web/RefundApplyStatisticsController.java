package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.RefundApplyDTO;
import com.midea.pam.common.ctc.excelVo.FinanceEntryExcelVO;
import com.midea.pam.common.ctc.excelVo.RefundApplyDetailExcelVO;
import com.midea.pam.common.ctc.excelVo.RefundApplyExcelVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * @program: common-module
 * @description: 退款申请报表
 * @author:zhongpeng
 * @create:2020-08-11 13:45
 **/
@Api("退款申请报表")
@RestController
@RequestMapping("statistics/refundApply")
public class RefundApplyStatisticsController extends ControllerHelper {
    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "退款申请导出", response = RefundApplyDTO.class)
    @GetMapping({"exprot"})
    public void exportRefundApply(HttpServletResponse response,
                                  @RequestParam(required = false) @ApiParam("退款申请号") final String refundApplyCode,
                                  @RequestParam(required = false) @ApiParam("退款状态") final String refundApplyStatusStr,
                                  @RequestParam(required = false) @ApiParam("退款入账开始时间") final String refundEntryStartDate,
                                  @RequestParam(required = false) @ApiParam("退款入账结束时间") final String refundEntryEndDate,
                                  @RequestParam(required = false) @ApiParam("客户编码") final String customerCode,
                                  @RequestParam(required = false) @ApiParam("客户名称") final String customerName,
                                  @RequestParam(required = false) @ApiParam("银行开户行") final String payeeBankName,
                                  @RequestParam(required = false) @ApiParam("银行账户") final String payeeBankAccount,
                                  @RequestParam(required = false) @ApiParam("付款方式") final String paymentTypeStr,
                                  @RequestParam(required = false) @ApiParam("子合同编号") final String contractCode,
                                  @RequestParam(required = false) @ApiParam("子合同名称") final String contractName,
                                  @RequestParam(required = false) @ApiParam("申请人") final String applyName,
                                  @RequestParam(required = false) @ApiParam("申请开始日期") final String applyStartDate,
                                  @RequestParam(required = false) @ApiParam("申请结束日期") final String applyEndDate,
                                  @RequestParam(required = false) @ApiParam("同步状态") final String outerStatusStr,
                                  @RequestParam(required = false) @ApiParam("业务实体") final String ouIdStr,
                                  @RequestParam(required = false) @ApiParam("来源类型") final Integer source) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("refundApplyCode",refundApplyCode );
        param.put("refundApplyStatusStr",refundApplyStatusStr );
        param.put("refundEntryStartDate",refundEntryStartDate );
        param.put("refundEntryEndDate",refundEntryEndDate );
        param.put("customerCode",customerCode );
        param.put("customerName",customerName );
        param.put("payeeBankName",payeeBankName );
        param.put("payeeBankAccount",payeeBankAccount );
        param.put("paymentTypeStr",paymentTypeStr );
        param.put("contractCode",contractCode );
        param.put("contractName",contractName );
        param.put("applyName",applyName );
        param.put("applyStartDate",applyStartDate );
        param.put("applyEndDate",applyEndDate );
        param.put("outerStatusStr",outerStatusStr );
        param.put("ouIdStr",ouIdStr );
        param.put("source",source );
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/refundApply/export", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        final DataResponse<Map<String,Object>> dataResponse =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String,Object>>>() {
                });
        Map<String,Object>resultMap = dataResponse.getData();
        JSONArray refundApplyDTOArr = (JSONArray) resultMap.get("refundApplyDTOList");
        JSONArray detailDtoArr = (JSONArray) resultMap.get("detailDtoList");
        JSONArray financeEntryDTOArr = (JSONArray) resultMap.get("financeEntryDTOList");

        List<RefundApplyExcelVO> refundApplyExcelVOList = new ArrayList<>();
        List<RefundApplyDetailExcelVO> refundApplyDetailExcelVOList = new ArrayList<>();
        List<FinanceEntryExcelVO> financeEntryExcelVOList = new ArrayList<>();

        if(refundApplyDTOArr != null) {
            refundApplyExcelVOList = JSONObject.parseArray(refundApplyDTOArr.toJSONString(), RefundApplyExcelVO.class);
        }

        if (detailDtoArr != null) {
            refundApplyDetailExcelVOList = JSONObject.parseArray(detailDtoArr.toJSONString(), RefundApplyDetailExcelVO.class);
        }

        if (financeEntryDTOArr!=null) {
            financeEntryExcelVOList = JSONObject.parseArray(financeEntryDTOArr.toJSONString(), FinanceEntryExcelVO.class);
        }

        Workbook workbook = ExportExcelUtil.buildDefaultSheet(refundApplyExcelVOList, RefundApplyExcelVO.class, null, "列表", true);
        ExportExcelUtil.addSheet(workbook,refundApplyDetailExcelVOList,RefundApplyDetailExcelVO.class,null,"合同明细",true);
        ExportExcelUtil.addSheet(workbook,financeEntryExcelVOList,FinanceEntryExcelVO.class,null,"入账明细",true);

        ExportExcelUtil.downLoadExcel("退款申请列表_"+ DateUtils.format(new Date(),"yyyyMMddHHmmss")+".xls", response, workbook);
    }

    @ApiOperation(value = "退款申请分页查询")
    @GetMapping("queryRefundApplyPage")
    public Response queryRefundApplyPage(
            @RequestParam(required = false) @ApiParam("退款申请号") final String refundApplyCode,
            @RequestParam(required = false) @ApiParam("退款状态") final String refundApplyStatusStr,
            @RequestParam(required = false) @ApiParam("退款入账开始时间") final String refundEntryStartDate,
            @RequestParam(required = false) @ApiParam("退款入账结束时间") final String refundEntryEndDate,
            @RequestParam(required = false) @ApiParam("客户编码") final String customerCode,
            @RequestParam(required = false) @ApiParam("客户名称") final String customerName,
            @RequestParam(required = false) @ApiParam("银行开户行") final String payeeBankName,
            @RequestParam(required = false) @ApiParam("银行账户") final String payeeBankAccount,
            @RequestParam(required = false) @ApiParam("付款方式") final String paymentTypeStr,
            @RequestParam(required = false) @ApiParam("子合同编号") final String contractCode,
            @RequestParam(required = false) @ApiParam("子合同名称") final String contractName,
            @RequestParam(required = false) @ApiParam("申请人") final String applyName,
            @RequestParam(required = false) @ApiParam("申请开始日期") final String applyStartDate,
            @RequestParam(required = false) @ApiParam("申请结束日期") final String applyEndDate,
            @RequestParam(required = false) @ApiParam("同步状态") final String outerStatusStr,
            @RequestParam(required = false) @ApiParam("业务实体") final String ouIdStr,
            @RequestParam(required = false) @ApiParam("来源类型") final Integer source,
            @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        param.put("refundApplyCode",refundApplyCode );
        param.put("refundApplyStatusStr",refundApplyStatusStr );
        param.put("refundEntryStartDate",refundEntryStartDate );
        param.put("refundEntryEndDate",refundEntryEndDate );
        param.put("customerCode",customerCode );
        param.put("customerName",customerName );
        param.put("payeeBankName",payeeBankName );
        param.put("payeeBankAccount",payeeBankAccount );
        param.put("paymentTypeStr",paymentTypeStr );
        param.put("contractCode",contractCode );
        param.put("contractName",contractName );
        param.put("applyName",applyName );
        param.put("applyStartDate",applyStartDate );
        param.put("applyEndDate",applyEndDate );
        param.put("outerStatusStr",outerStatusStr );
        param.put("ouIdStr",ouIdStr );
        param.put("source",source );
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/statistics/refundApply/queryRefundApplyPage", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<RefundApplyDTO>>>() {
        });
    }
}
