package com.midea.pam.gateway.ctc.web;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.SavePurchaseOrderChangeRecordDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Api("采购订单变更")
@RestController
@RequestMapping("purchaseOrderChange")
public class PurchaseOrderChangeController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "保存采购订单变更记录")
    @PostMapping("saveRecord")
    public Response saveRecord(@RequestBody SavePurchaseOrderChangeRecordDto purchaseOrderRecordDto) {
        String url = String.format("%spurchaseOrderChange/saveRecord", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, purchaseOrderRecordDto, String.class).getBody();
        DataResponse<Long> response = JSON.parseObject(res, new TypeReference<DataResponse<Long>>() {
        });
        return response;
    }

    @ApiOperation(value = "采购订单变更详情")
    @GetMapping("changeDetails")
    public Response changeDetails(@RequestParam Long id, Integer editFlag) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("editFlag", editFlag); //是否重新编辑
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseOrderChange/changeDetails", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<SavePurchaseOrderChangeRecordDto> response = JSON.parseObject(res, new TypeReference<DataResponse<SavePurchaseOrderChangeRecordDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "移动审批采购订单变更详情")
    @GetMapping("getMobileApprovalChangeDetail")
    public Response getMobileApprovalChangeDetail(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseOrderChange/getMobileApprovalChangeDetail", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<ResponseMap> response = JSON.parseObject(res, new TypeReference<DataResponse<ResponseMap>>() {
        });
        return response;
    }

}
