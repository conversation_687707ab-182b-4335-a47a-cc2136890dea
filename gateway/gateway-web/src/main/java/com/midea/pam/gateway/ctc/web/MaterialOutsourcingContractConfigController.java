package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.MaterialOutsourcingContractConfigDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Api("物料外包合同配置")
@RestController
@RequestMapping("ctc/materialOutsourcingContractConfig")
public class MaterialOutsourcingContractConfigController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "物料外包合同配置分页", response = MaterialOutsourcingContractConfigDto.class)
    @GetMapping({"selectPageWithDetail"})
    public Response selectPageWithDetail(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                         @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize,
                                         @RequestParam(required = false) @ApiParam(value = "使用单位id") Long unitId,
                                         @RequestParam(required = false) @ApiParam(value = "模糊:分包类型") String fuzzyOutsourcingContractTypeName) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("unitId", unitId);
        param.put("fuzzyOutsourcingContractTypeName", fuzzyOutsourcingContractTypeName);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/ctc/materialOutsourcingContractConfig/selectPageWithDetail", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<MaterialOutsourcingContractConfigDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<MaterialOutsourcingContractConfigDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "物料外包合同配置详情", response = MaterialOutsourcingContractConfigDto.class)
    @GetMapping({"getDetailById"})
    public Response getDetailById(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/ctc/materialOutsourcingContractConfig/getDetailById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<MaterialOutsourcingContractConfigDto> response = JSON.parseObject(res, new TypeReference<DataResponse<MaterialOutsourcingContractConfigDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "物料外包合同配置保存", response = MaterialOutsourcingContractConfigDto.class)
    @PostMapping({"saveWithDetail"})
    public Response saveWithDetail(@RequestBody MaterialOutsourcingContractConfigDto dto) {
        String url = String.format("%sctc/materialOutsourcingContractConfig/saveWithDetail", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, dto, String.class).getBody();
        DataResponse<MaterialOutsourcingContractConfigDto> response = JSON.parseObject(res, new TypeReference<DataResponse<MaterialOutsourcingContractConfigDto>>() {
        });
        return response;
    }
}
