package com.midea.pam.gateway;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.FormInstanceExample;
import com.midea.pam.common.gateway.entity.WorkFlowDraftSubmitTemporaryRecord;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.common.utils.PamCurrentUserUtil;
import com.midea.pam.gateway.service.FormInstanceService;
import com.midea.pam.gateway.service.WorkFlowDraftSubmitTemporaryRecordService;
import com.midea.pam.gateway.vo.WorkflowDraftForm;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description 工作流提交临时记录
 * Created by liuqing85
 * Date 2022/9/26 10:08
 */
@Api("工作流提交临时记录")
@RestController
@RequestMapping("workFlowDraftSubmitTemporaryRecord")
public class WorkFlowDraftSubmitTemporaryRecordController {

    private static Logger logger = LoggerFactory.getLogger(WorkFlowDraftSubmitTemporaryRecordController.class);

    @Resource
    private FormInstanceService formInstanceService;

    @Resource
    private WorkFlowDraftSubmitTemporaryRecordService workFlowDraftSubmitTemporaryRecordService;

    @ApiOperation("起草人提交")
    @PutMapping("draftSubmit/{formTemplateId}/{formInstanceId}")
    public Response add(@PathVariable String formTemplateId, @PathVariable Long formInstanceId,
                        @RequestBody @ApiParam(name = "WorkflowDraftForm", value = "起草form") WorkflowDraftForm form) {
        Guard.notNullOrEmpty(formTemplateId, "表单url为空");
        Guard.notNull(formInstanceId, "表单实例id为空");
        Guard.notNull(form, "起草人提交的参数为空");

        FormInstanceExample formInstanceExample = new FormInstanceExample();
        formInstanceExample.createCriteria().andFormUrlEqualTo(formTemplateId).andFormInstanceIdEqualTo(formInstanceId)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<FormInstance> formInstanceList = formInstanceService.selectByExample(formInstanceExample);
        Guard.notNullOrEmpty(formInstanceList, "流程实例不存在，请确认前面的提交操作是否有误");
        String fdInstanceId = formInstanceList.get(0).getFdInstanceId();

        String fdUrl = form.getFdUrl();
        fdUrl = String.format("%s&fdInstanceId=%s", fdUrl, fdInstanceId);
        form.setFdUrl(fdUrl);

        WorkFlowDraftSubmitTemporaryRecord record = new WorkFlowDraftSubmitTemporaryRecord();
        record.setFormUrl(formTemplateId);
        record.setFormInstanceId(formInstanceId);
        record.setSubmitParam(JSONObject.toJSONString(form));
        record.setUserName(PamCurrentUserUtil.getCurrentUserName());
        logger.info("工作流提交临时记录add的record：{}", JSONObject.toJSONString(record));

        workFlowDraftSubmitTemporaryRecordService.draftSubmit(record);
        return Response.dataResponse();
    }

    @ApiOperation("获取提交状态")
    @GetMapping("getStatus")
    public Response getStatus(@ApiParam("模版类型，多个用,隔开") @RequestParam String formTemplateIds,
                              @ApiParam("业务ID，多个用,隔开") @RequestParam String formInstanceIds) {
        Guard.notNullOrEmpty(formTemplateIds, "表单url列表为空");
        Guard.notNullOrEmpty(formInstanceIds, "表单实例id列表为空");

        List<String> formTemplateIdList = StringUtils.splitToList(formTemplateIds, ",");
        List<Long> formInstanceIdList = StringUtils.splitToList(formInstanceIds, ",").stream().map(Long::valueOf).collect(Collectors.toList());
        List<Map<String, Object>> resultList = workFlowDraftSubmitTemporaryRecordService.getStatus(formTemplateIdList, formInstanceIdList);

        DataResponse<List<Map<String, Object>>> response = Response.dataResponse();
        return response.setData(resultList);
    }

    @ApiOperation("删除提交记录")
    @PostMapping("delete/{id}")
    public Response delete(@PathVariable Long id) {
        workFlowDraftSubmitTemporaryRecordService.deleteByIdList(Lists.newArrayList(id));
        return Response.dataResponse();
    }
}
