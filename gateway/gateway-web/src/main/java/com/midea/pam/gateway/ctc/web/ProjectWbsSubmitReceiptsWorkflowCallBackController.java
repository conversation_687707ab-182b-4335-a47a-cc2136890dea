package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.ctc.excelVo.MilepostDesignPlanDetailWbsExportVo;
import com.midea.pam.common.ctc.excelVo.ProjectWbsReceiptsDetailExcelVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.service.OssService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.http.entity.ContentType;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @description 详细设计发布单据流程回调
 */
@Api("详细设计发布单据流程回调")
@RestController
@RequestMapping({"projectWbsSubmitReceipts/workflow/callback", "projectWbsSubmitReceipts"})
public class ProjectWbsSubmitReceiptsWorkflowCallBackController extends ControllerHelper {

    private static final Logger logger = LoggerFactory.getLogger(ProjectWbsSubmitReceiptsWorkflowCallBackController.class);

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private OssService ossService;

    @ApiOperation(value = "审批中")
    @PutMapping("draftSubmit/skipSecurityInterceptor")
    public Response draftSubmitCallback(@RequestParam(required = false) String formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                        @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                        @RequestParam(required = false) String handlerId,
                                        @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {

        //生成附件
        MultipartFile multipartFile = createAnnex(formInstanceId);
        String fileId = "";
        String fileName = "";
        String fileSize = "";
        try {
            if (multipartFile != null) {
                JSONArray result = ossService.upload(multipartFile);
                if (!ObjectUtils.isEmpty(result)) {
                    JSONObject jsonObject = result.getJSONObject(0);
                    fileId = jsonObject.getString("fileId");
                    fileName = jsonObject.getString("fileName");
                    fileSize = jsonObject.getString("fileSize");
                }
            }
        } catch (Exception e) {
            logger.error("详细设计发布审批移动端附件，上传失败", e);
        }

        final String url = String.format("%sprojectWbsSubmitReceipts/workflow/callback/draftSubmit/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s" +
                        "&fileId=%s" +
                        "&fileName=%s" +
                        "&fileSize=%s",
                ModelsEnum.CTC.getBaseUrl(),
                formInstanceId,
                fdInstanceId,
                formUrl,
                eventName,
                handlerId,
                companyId,
                createUserId,
                fileId,
                fileName,
                fileSize);

        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    public MultipartFile createAnnex(String formInstanceId) {
        // 1.生成Excel
        String url = String.format("%sprojectWbsReceipts/detail/export?id=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<Map<String, Object>>>() {
                });

        Map<String, Object> resultMap = dataResponse.getData();
        JSONArray receiptsDetailArr = (JSONArray) resultMap.get("receiptsDetailList");
        JSONArray designPlanDetailArr = (JSONArray) resultMap.get("designPlanDetailList");

        List<ProjectWbsReceiptsDetailExcelVo> receiptsDetailExcelList = new ArrayList<>();
        List<MilepostDesignPlanDetailWbsExportVo> designPlanDetailExcelList = new ArrayList<>();
        String code = formInstanceId;
        if (receiptsDetailArr != null) {
            receiptsDetailExcelList = JSONObject.parseArray(receiptsDetailArr.toJSONString(),
                    ProjectWbsReceiptsDetailExcelVo.class);
            if (!receiptsDetailExcelList.isEmpty()) {
                code = receiptsDetailExcelList.get(0).getRequirementCode();
            }
        }
        if (designPlanDetailArr != null) {
            designPlanDetailExcelList = JSONObject.parseArray(designPlanDetailArr.toJSONString(),
                    MilepostDesignPlanDetailWbsExportVo.class);
        }


        Workbook workbook = ExportExcelUtil.buildDefaultSheet(receiptsDetailExcelList,
                ProjectWbsReceiptsDetailExcelVo.class, null, "基本信息", true);
        ExportExcelUtil.addSheet(workbook, designPlanDetailExcelList,
                MilepostDesignPlanDetailWbsExportVo.class, null, "明细内容", true);

        // 2，转换成MultipartFile
        // 附件名称为：详细设计单据编号+“详细设计发布”+“_”+YYYYMMDD MM:HH:SS;举例：REL2023022300007详细设计发布_20230223 17:05:01.xls
        MultipartFile multipartFile = null;
        String fileName = code + "详细设计发布_" + DateUtils.format(new Date(), "yyyyMMdd HH:mm:ss") + ".xls";
        File pdfFile = new File("/apps/pam/gateway/file/" + fileName);

        try (OutputStream out = Files.newOutputStream(Paths.get("/apps/pam/gateway/file/" + fileName));
             FileInputStream fileInputStream = new FileInputStream(pdfFile)) {
            workbook.write(out);
            multipartFile = new MockMultipartFile(pdfFile.getName(), pdfFile.getName(), ContentType.APPLICATION_OCTET_STREAM.toString(),
                    fileInputStream);
        } catch (IOException e) {
            logger.error("详细设计发布审批移动端附件，生成失败", e);
        }
        return multipartFile;
    }

    @ApiOperation(value = "驳回")
    @PutMapping("refuse/skipSecurityInterceptor")
    public Response refuseCallback(@RequestParam(required = false) String formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                   @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                   @RequestParam(required = false) String handlerId,
                                   @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%sprojectWbsSubmitReceipts/workflow/callback/refuse/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "通过")
    @PutMapping("pass/skipSecurityInterceptor")
    public Response passCallback(@RequestParam(required = false) String formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                 @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                 @RequestParam(required = false) String handlerId,
                                 @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%sprojectWbsSubmitReceipts/workflow/callback/pass/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(600 * 1000);
        httpRequestFactory.setConnectTimeout(600 * 1000);
        httpRequestFactory.setReadTimeout(600 * 1000);
        restTemplate.setRequestFactory(httpRequestFactory);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "撤回")
    @PutMapping("draftReturn/skipSecurityInterceptor")
    public Response draftReturnCallback(@RequestParam(required = false) String formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                        @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                        @RequestParam(required = false) String handlerId,
                                        @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%sprojectWbsSubmitReceipts/workflow/callback/draftReturn/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "作废")
    @PutMapping("abandon/skipSecurityInterceptor")
    public Response abandonCallback(@RequestParam(required = false) String formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                    @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                    @RequestParam(required = false) String handlerId,
                                    @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%sprojectWbsSubmitReceipts/workflow/callback/abandon/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "删除")
    @PutMapping("delete/skipSecurityInterceptor")
    public Response deleteCallback(@RequestParam(required = false) String formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                   @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                   @RequestParam(required = false) String handlerId,
                                   @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%sprojectWbsSubmitReceipts/workflow/callback/delete/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "处理人通过")
    @PutMapping("agree/skipSecurityInterceptor")
    public Response agree(@RequestParam(required = false) String formInstanceId, @RequestParam(required = false) String fdInstanceId,
                          @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                          @RequestParam(required = false) String handlerId,
                          @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%sprojectWbsSubmitReceipts/workflow/callback/agree/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "页面按钮作废")
    @PutMapping("button/abandon")
    public Response buttonAbandon(@RequestParam(required = false) String formInstanceId) {
        final String url = String.format("%sprojectWbsSubmitReceipts/workflow/callback/button/abandon?formInstanceId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "页面按钮撤回")
    @PutMapping("button/draftReturn")
    public Response buttonDraftReturn(@RequestParam(required = false) String formInstanceId, @RequestParam(required = false) Long handlerUserId) {
        final String url = String.format("%sprojectWbsSubmitReceipts/workflow/callback/button/draftReturn?formInstanceId=%s&handlerUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, handlerUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }
}
