package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.CostCollectionDto;
import com.midea.pam.common.ctc.dto.LaborCostDetailDto;
import com.midea.pam.common.ctc.dto.WorkingHourAccountingDto;
import com.midea.pam.common.ctc.dto.WorkingHourAccountingSummaryDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.statistics.excelVo.CostCollectionExcelVO;
import com.midea.pam.common.statistics.excelVo.LaborCostDetailExcelVO;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: common-module
 * @description: 工时成本归集统计
 * @author:zhongpeng
 * @create:2020-03-20 10:40
 **/
@Api("工时成本归集统计")
@RestController
@RequestMapping("statistics/workingHourAccounting")
public class WorkingHourAccountingStaController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "工时成本统计分页查询", response = CostCollectionDto.class)
    @GetMapping("/collectionSummaryPage")
    public Response collectionSummaryPage(@RequestParam(required = false) String ouId,
                                          @RequestParam(required = false) final Long projectId,
                                          @RequestParam(required = false) String projectCode,
                                          @RequestParam(required = false) String projectName,
                                          @RequestParam(required = false) String projectFuzzyLike,
                                          @RequestParam(required = false) String applyMonth,
                                          @RequestParam(required = false) String approveMonth,
                                          @RequestParam(required = false) String projectType,
                                          @RequestParam(required = false) String currency,
                                          @RequestParam(required = false) String statusStr,
                                          @RequestParam(required = true, defaultValue = "1") final Integer pageNum,
                                          @RequestParam(required = true, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put("ouId", ouId);
        param.put("projectId", projectId);
        param.put("projectCode", projectCode);
        param.put("projectName", projectName);
        param.put("projectFuzzyLike", projectFuzzyLike);
        param.put("applyMonth", applyMonth);
        param.put("approveMonth", approveMonth);
        param.put("projectType", projectType);
        param.put("currency", currency);
        param.put("statusStr", statusStr);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/workingHourAccounting/collectionSummaryPage", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<JSONObject> data = Response.dataResponse();
        data.setData(JSON.parseObject(res));
        return data;
    }

    @ApiOperation(value = "批量入账统计")
    @PostMapping("/batchSummary")
    public Response batchSummary(@RequestBody WorkingHourAccountingSummaryDto dto) {
        String url = String.format("%sstatistics/workingHourAccounting/batchSummary", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        String res = cleanStr(responseEntity.getBody());
        return JSON.parseObject(res, new TypeReference<DataResponse<WorkingHourAccountingDto>>() {
        });
    }

    @ApiOperation(value = "工时成本统计明细分页查询", response = LaborCostDetailDto.class)
    @GetMapping("/laborCostSummaryPage")
    public Response laborCostSummaryPage(@RequestParam(required = true) final Long projectId,
                                         @RequestParam(required = false, defaultValue = "1") final String applyMonth,
                                         @RequestParam(required = false, defaultValue = "1") final String approveMonth,
                                         @RequestParam(required = true, defaultValue = "1") final Integer pageNum,
                                         @RequestParam(required = true, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        param.put("applyMonth", applyMonth);
        param.put("approveMonth", approveMonth);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/workingHourAccounting/laborCostSummaryPage", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<JSONObject> data = Response.dataResponse();
        data.setData(JSON.parseObject(res));
        return data;
    }

    @ApiOperation(value = "工时成本统计列表导出", response = ResponseMap.class)
    @GetMapping("/export")
    public void listExport(HttpServletResponse response, @RequestParam(required = false) String ouId,
                           @RequestParam(required = false) final Long projectId,
                           @RequestParam(required = false) String projectCode,
                           @RequestParam(required = false) String projectName,
                           @RequestParam(required = false) String projectFuzzyLike,
                           @RequestParam(required = false) String applyMonth,
                           @RequestParam(required = false) String approveMonth,
                           @RequestParam(required = false) String projectType,
                           @RequestParam(required = false) String currency,
                           @RequestParam(required = false) String statusStr) {
        final Map<String, Object> param = new HashMap<>();
        param.put("ouId", ouId);
        param.put("projectId", projectId);
        param.put("projectCode", projectCode);
        param.put("projectName", projectName);
        param.put("projectFuzzyLike", projectFuzzyLike);
        param.put("applyMonth", applyMonth);
        param.put("approveMonth", approveMonth);
        param.put("projectType", projectType);
        param.put("currency", currency);
        param.put("statusStr", statusStr);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/workingHourAccounting/v1/export", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });
        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("工时成本统计_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");
        JSONArray costCollectionDtoListArr = (JSONArray) resultMap.get("costCollectionDtoList");
        JSONArray laborCostDetailDtoListArr = (JSONArray) resultMap.get("laborCostDetailDtoList");
        List<CostCollectionExcelVO> costCollectionExcelVOS = JSONObject.parseArray(costCollectionDtoListArr.toJSONString(), CostCollectionExcelVO.class);
        List<LaborCostDetailExcelVO> laborCostDetailExcelVOS = JSONObject.parseArray(laborCostDetailDtoListArr.toJSONString(), LaborCostDetailExcelVO.class);
        if (ListUtils.isNotEmpty(costCollectionExcelVOS)) {
            for (int i = 0; i < costCollectionExcelVOS.size(); i++) {
                CostCollectionExcelVO costCollectionExcelVO = costCollectionExcelVOS.get(i);
                BigDecimal innerWorkingHour = costCollectionExcelVO.getInnerWorkingHour() == null ? BigDecimal.ZERO : costCollectionExcelVO.getInnerWorkingHour();
                BigDecimal outerWorkingHour = costCollectionExcelVO.getOuterWorkingHour() == null ? BigDecimal.ZERO : costCollectionExcelVO.getOuterWorkingHour();
                BigDecimal innerLaborCost = costCollectionExcelVO.getInnerLaborCost() == null ? BigDecimal.ZERO : costCollectionExcelVO.getInnerLaborCost();
                BigDecimal outerLaborCost = costCollectionExcelVO.getOuterLaborCost() == null ? BigDecimal.ZERO : costCollectionExcelVO.getOuterLaborCost();
                BigDecimal totalWorkingHour = innerWorkingHour.add(outerWorkingHour);
                BigDecimal totalLaborCost = innerLaborCost.add(outerLaborCost);
                costCollectionExcelVO.setNum(i + 1);
                costCollectionExcelVO.setOuterWorkingHour(outerWorkingHour.setScale(2, BigDecimal.ROUND_HALF_UP));
                costCollectionExcelVO.setOuterLaborCost(outerLaborCost.setScale(2, BigDecimal.ROUND_HALF_UP));
                costCollectionExcelVO.setTotalWorkingHour(totalWorkingHour.setScale(2, BigDecimal.ROUND_HALF_UP));
                costCollectionExcelVO.setTotalLaborCost(totalLaborCost.setScale(2, BigDecimal.ROUND_HALF_UP));
            }

            if (ListUtils.isNotEmpty(laborCostDetailExcelVOS)) {
                for (int i = 0; i < laborCostDetailExcelVOS.size(); i++) {
                    LaborCostDetailExcelVO laborCostDetailExcelVO = laborCostDetailExcelVOS.get(i);
                    final Unit unit = CacheDataUtils.findUnitById(Long.valueOf(laborCostDetailExcelVO.getTypeName()));
                    laborCostDetailExcelVO.setTypeName(null != unit ? unit.getUnitName() : null);
                    laborCostDetailExcelVO.setNum(i + 1);
                    laborCostDetailExcelVO.setIsCrossUnitLaborCostStr(laborCostDetailExcelVO.getIsCrossUnitLaborCost() ? "是" : "");
                }
            }

            Workbook workbook = ExportExcelUtil.buildDefaultSheet(costCollectionExcelVOS, CostCollectionExcelVO.class, null, "汇总", Boolean.TRUE);
            ExportExcelUtil.addSheet(workbook, laborCostDetailExcelVOS, LaborCostDetailExcelVO.class, null, "明细", Boolean.TRUE);
            ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
        }
    }

}
