package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.mcomponent.core.util.Assert;
import com.midea.pam.common.basedata.dto.MaterialPriceReceiptDetailPageDto;
import com.midea.pam.common.basedata.dto.MaterialPriceReceiptDetailPageExcelVo;
import com.midea.pam.common.basedata.dto.MaterialPriceReceiptDto;
import com.midea.pam.common.basedata.dto.MaterialPriceReceiptHeaderDto;
import com.midea.pam.common.basedata.entity.MaterialPriceReceiptDetail;
import com.midea.pam.common.basedata.entity.MaterialPriceReceiptHeader;
import com.midea.pam.common.basedata.excelVo.MaterialPriceReceiptExcelVO;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.gateway.common.base.Code;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.common.utils.FileUtil;
import com.midea.pam.gateway.service.MipWorkflowInnerService;
import com.midea.pam.gateway.service.OssService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.http.entity.ContentType;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @PackageClassName: com.midea.pam.gateway.basedata.web.MaterialPriceReceiptController
 * @Description: 物料价格手工单据
 * @Author: JerryH
 * @Date: 2023-05-18, 0018 下午 05:18
 */
@RestController
@Api("物料价格手工单据")
@RequestMapping({"materialPriceReceipt"})
public class MaterialPriceReceiptController extends ControllerHelper {

    private static final Logger logger = LoggerFactory.getLogger(MaterialPriceReceiptController.class);

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private OssService ossService;
    @Resource
    private MipWorkflowInnerService mipWorkflowInnerService;

    @ApiOperation("新增物料价格手工单据&重新编辑-暂存")
    @PostMapping({"draft"})
    public Response draft(@RequestBody MaterialPriceReceiptHeaderDto dto) {
        String url = String.format("%smaterialPriceReceipt/draft", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<MaterialPriceReceiptHeader>>() {
        });
    }

    @ApiOperation(value = "物料价格手工单据-详情")
    @GetMapping("view/{id}")
    public Response view(@PathVariable Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "materialPriceReceipt/view", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<MaterialPriceReceiptHeaderDto>>() {
        });
    }

    @ApiOperation(value = "分页获取收工单据行详情")
    @GetMapping("pageMaterial")
    public Response page(
            @RequestParam() Long headerId,
            @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put("headerId", headerId);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "materialPriceReceipt/pageMaterial", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<MaterialPriceReceiptDetailPageDto>>>() {
        });
    }

    @ApiOperation(value = "发起审批回调")
    @PutMapping("draftSubmit/skipSecurityInterceptor")
    public Response draftSubmit(@RequestParam(required = false) Long formInstanceId,
                                @RequestParam(required = false) String fdInstanceId,
                                @RequestParam(required = false) String formUrl,
                                @RequestParam(required = false) String eventName,
                                @RequestParam(required = false) String handlerId,
                                @RequestParam(required = false) Long companyId,
                                @RequestParam(required = false) Long createUserId) {
        DataResponse<String> response = Response.dataResponse();
        Map<String, Object> param = createMaterialAnnexList(formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/materialPriceReceipt/draftSubmit/skipSecurityInterceptor", param);
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        return response.setData(responseEntity.getBody());
    }

    @ApiOperation(value = "驳回回调")
    @PutMapping("updateStatusReject/skipSecurityInterceptor")
    public Response updateStatusReject(@RequestParam(required = false) Long formInstanceId,
                                       @RequestParam(required = false) String fdInstanceId,
                                       @RequestParam(required = false) String formUrl,
                                       @RequestParam(required = false) String eventName,
                                       @RequestParam(required = false) String handlerId,
                                       @RequestParam(required = false) Long companyId,
                                       @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialPriceReceipt/updateStatusReject/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.BASEDATA.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response.setData(responseEntity.getBody());
    }

    @ApiOperation(value = "撤回回调")
    @PutMapping("updateStatusReturn/skipSecurityInterceptor")
    public Response updateStatusReturn(@RequestParam(required = false) Long formInstanceId,
                                       @RequestParam(required = false) String fdInstanceId,
                                       @RequestParam(required = false) String formUrl,
                                       @RequestParam(required = false) String eventName,
                                       @RequestParam(required = false) String handlerId,
                                       @RequestParam(required = false) Long companyId,
                                       @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialPriceReceipt/updateStatusReturn/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.BASEDATA.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response.setData(responseEntity.getBody());
    }

    @ApiOperation(value = "通过回调")
    @PutMapping("updateStatusPass/skipSecurityInterceptor")
    public Response updateStatusPass(@RequestParam(required = false) Long formInstanceId,
                                     @RequestParam(required = false) String fdInstanceId,
                                     @RequestParam(required = false) String formUrl,
                                     @RequestParam(required = false) String eventName,
                                     @RequestParam(required = false) String handlerId,
                                     @RequestParam(required = false) Long companyId,
                                     @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialPriceReceipt/updateStatusPass/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.BASEDATA.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response.setData(responseEntity.getBody());
    }

    @ApiOperation(value = "废弃回调")
    @PutMapping("abandon/skipSecurityInterceptor")
    public Response abandon(@RequestParam(required = false) Long formInstanceId,
                            @RequestParam(required = false) String fdInstanceId,
                            @RequestParam(required = false) String formUrl,
                            @RequestParam(required = false) String eventName,
                            @RequestParam(required = false) String handlerId,
                            @RequestParam(required = false) Long companyId,
                            @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialPriceReceipt/abandon/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.BASEDATA.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response.setData(responseEntity.getBody());
    }

    @ApiOperation(value = "删除回调")
    @PutMapping("delete/skipSecurityInterceptor")
    public Response delete(@RequestParam(required = false) Long formInstanceId,
                           @RequestParam(required = false) String fdInstanceId,
                           @RequestParam(required = false) String formUrl,
                           @RequestParam(required = false) String eventName,
                           @RequestParam(required = false) String handlerId,
                           @RequestParam(required = false) Long companyId,
                           @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialPriceReceipt/delete/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.BASEDATA.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response.setData(responseEntity.getBody());
    }

    @ApiOperation(value = "处理人通过")
    @PutMapping("agree/skipSecurityInterceptor")
    public Response agree(@RequestParam(required = false) Long formInstanceId,
                          @RequestParam(required = false) String fdInstanceId,
                          @RequestParam(required = false) String formUrl,
                          @RequestParam(required = false) String eventName,
                          @RequestParam(required = false) String handlerId,
                          @RequestParam(required = false) Long companyId,
                          @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smaterialPriceReceipt/agree/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.BASEDATA.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response.setData(responseEntity.getBody());
    }

    @ApiOperation(value = "单据作废")
    @GetMapping("deleteDraft")
    public Response deleteDraft(@RequestParam Long id) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "/materialPriceReceipt/deleteDraft?id=" + id;
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<Integer> response = JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
        //同时作废工作流
        if (response.getCode() == 0) {
            mipWorkflowInnerService.draftAbandon("materialPriceReceiptApp", id);
        }
        return response;
    }

    @ApiOperation(value = "根据库存组织、物料编码、价格类型进行查询物料是否锁定状态")
    @PostMapping("checkPriceIsLock")
    public Response checkPriceIsLock(@RequestBody MaterialPriceReceiptDetail materialPriceReceiptDetail) {
        String url = String.format("%smaterialPriceReceipt/checkPriceIsLock", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, materialPriceReceiptDetail, String.class);
        return JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    private Map<String, Object> createMaterialAnnexList(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId,
                                                        Long companyId, Long createUserId) {
        //查询物料明细
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/materialPriceReceipt/detail/" + formInstanceId, null);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<MaterialPriceReceiptDetailPageDto>> dataResponse =
                JSON.parseObject(res, new TypeReference<DataResponse<List<MaterialPriceReceiptDetailPageDto>>>() {
                });
        Guard.isTrue(Objects.equals(0, dataResponse.getCode()), dataResponse.getMsg());
        List<MaterialPriceReceiptDetailPageDto> receiptDetailPageDtoList = dataResponse.getData();
        Guard.notNullOrEmpty(receiptDetailPageDtoList, "查询物料价格手工单据-单据详情失败!");
        //提交审批
        final Map<String, Object> param = new HashMap<>();
        param.put("formInstanceId", formInstanceId);
        param.put("fdInstanceId", fdInstanceId);
        param.put("formUrl", formUrl);
        param.put("eventName", eventName);
        param.put("handlerId", handlerId);
        param.put("companyId", companyId);
        param.put("createUserId", createUserId);

        JSONObject jsonObject = new JSONObject();
        try {
            if (CollectionUtils.isNotEmpty(receiptDetailPageDtoList)) {
                //生成附件
                MultipartFile multipartFile = createAnnex(receiptDetailPageDtoList);
                //上传附件
                JSONArray result = ossService.upload(multipartFile);
                if (!ObjectUtils.isEmpty(result)) {
                    jsonObject = result.getJSONObject(0);
                }
                param.put("fileId", jsonObject.get("fileId"));
                param.put("fileName", jsonObject.get("fileName"));
                param.put("fileSize", jsonObject.get("fileSize"));
            }
        } catch (IOException e) {
            logger.error("oss upload error", e);
            throw new BizException(Code.ERROR.getCode(), "文件上传失败");
        }
        return param;
    }

    public MultipartFile createAnnex(List<MaterialPriceReceiptDetailPageDto> receiptDetailPageDtoList) throws IOException {
        if (CollectionUtils.isEmpty(receiptDetailPageDtoList)) {
            return null;
        }
        String fileName = receiptDetailPageDtoList.get(0).getReceiptCode() + "物料手工单据明细_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls";
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        // 创建工作表并设置数据
        List<MaterialPriceReceiptDetailPageExcelVo> detailVoList = new ArrayList<>();
        for (MaterialPriceReceiptDetailPageDto getDetail : receiptDetailPageDtoList) {
            MaterialPriceReceiptDetailPageExcelVo excelVo = new MaterialPriceReceiptDetailPageExcelVo();
            BeanUtils.copyProperties(getDetail, excelVo);
            excelVo.setAmount(getDetail.getAmount().setScale(2, RoundingMode.HALF_UP));
            detailVoList.add(excelVo);
        }
        Workbook workbook = ExportExcelUtil.buildDefaultSheet(detailVoList, MaterialPriceReceiptDetailPageExcelVo.class, null, "Sheet1", true);
        workbook.write(outputStream);
        byte[] bytes = outputStream.toByteArray();
        return new MockMultipartFile(fileName, fileName, ContentType.APPLICATION_OCTET_STREAM.toString(), new ByteArrayInputStream(bytes));
    }

    @ApiOperation(value = "上传文件检查数据", notes = "场景：手工维护物料价格导入")
    @PostMapping("/importBatchFromExcel")
    public Response checkTemplate(@RequestParam(value = "file") MultipartFile file,
                                  @RequestParam(required = false) @ApiParam(value = "业务实体id") Long ouId,
                                  @RequestParam(required = false) @ApiParam(value = "库存组织id") Long organizationId,
                                  HttpServletResponse response) {
        MaterialPriceReceiptDto dto = new MaterialPriceReceiptDto();
        dto.setOuId(ouId);
        dto.setOrganizationId(organizationId);
        int offsetLine = 3;
        dto.setOffsetLine(offsetLine);
        List<MaterialPriceReceiptExcelVO> importExcelVos = null;
        try {
            importExcelVos = FileUtil.importExcel(file, MaterialPriceReceiptExcelVO.class, offsetLine, 0);
        } catch (Exception e) {
            if (e instanceof BizException && e.getMessage().toLowerCase().startsWith("error converting")) {
                Asserts.success(ErrorCode.SYSTEM_FORMAT_ERROR);
            }
            logger.error(e.getMessage(), e);
            Asserts.success(ErrorCode.SYSTEM_FILE_ERROR);
        }
        if (CollectionUtils.isEmpty(importExcelVos)) {
            throw new MipException("当前导入Excel模板中无法匹配有效的记录，请检查");
        }
        dto.setImportExcelVos(importExcelVos);//要导入的数据
        final String url = String.format("%smaterialPriceReceipt/importBatchFromExcel", ModelsEnum.BASEDATA.getBaseUrl());
        String res = restTemplate.postForEntity(url, dto, String.class).getBody();
        DataResponse<MaterialPriceReceiptDto> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<MaterialPriceReceiptDto>>() {
        });
        Assert.isTrue(Objects.equals(0, dataResponse.getCode()), dataResponse.getMsg());
        Assert.notNull(dataResponse.getData(), "批量导入数据解析失败!");
        return dataResponse;
    }

    @ApiOperation(value = "下载错误数据", notes = "场景：手工维护物料价格导入")
    @PostMapping("/downloadErrorMsg")
    public void downloadErrorMsg(@RequestPart("file") MultipartFile file, @RequestParam String errMsg, HttpServletResponse response) {
        List<MaterialPriceReceiptExcelVO> errorMsgList = null;
        try {
            String s = StringEscapeUtils.unescapeJava(errMsg);
            errorMsgList = JSON.parseArray(s, MaterialPriceReceiptExcelVO.class);
        } catch (Exception e) {
            throw new MipException("参数异常");
        }
        Workbook workbook = null;
        try {
            workbook = WorkbookFactory.create(file.getInputStream());
            ExportExcelUtil.addCommonSheet(workbook, errorMsgList, MaterialPriceReceiptExcelVO.class, "错误信息");
            Sheet sheet = workbook.getSheet("错误信息");
            CellStyle highlightStyle = workbook.createCellStyle();
            Font font = workbook.createFont();
            font.setColor(IndexedColors.RED.getIndex());
            highlightStyle.setFont(font);
            for (Row row : sheet) {
                Cell cell = row.getCell(5);
                if (cell != null) {
                    cell.setCellStyle(highlightStyle);
                }
            }
        } catch (Exception e) {
            throw new MipException("模板解析异常");
        }
        //导出
        String fileName = "错误日志_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls";
        if (workbook instanceof XSSFWorkbook) {
            fileName += "x";
        }
        ExportExcelUtil.downLoadExcel(fileName, response, workbook);
    }

}
