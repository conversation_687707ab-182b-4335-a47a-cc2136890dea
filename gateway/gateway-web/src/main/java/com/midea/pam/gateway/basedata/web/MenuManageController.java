
package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.basedata.dto.RoleMenuFuncDto;
import com.midea.pam.common.basedata.dto.TreeNode;
import com.midea.pam.common.basedata.entity.Menu;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;

@RestController
@Api("菜单管理")
@RequestMapping({"menuManage"})
public class MenuManageController extends ControllerHelper {
    @Resource
    private RestTemplate restTemplate;

    @ApiOperation("通过ID获取菜单信息")
    @GetMapping({"{id}"})
    public Response getById(@PathVariable Long id) {
        String url = String.format("%smenuManage/%s",ModelsEnum.BASEDATA.getBaseUrl(),id);
        String res = cleanStr(restTemplate.getForObject(url , String.class));
        Menu data = JSON.parseObject(res, new TypeReference<Menu>(){});
        DataResponse<Menu> response = Response.dataResponse();
        return response.setData(data);
    }

    @ApiOperation("获取所有菜单列表")
    @GetMapping
    public Response listAll() throws Exception {
        if(null == CacheDataUtils.findUserByMip(getUserMip())){
            DataResponse<List<Map<String, Object>> > response = Response.dataResponse();
            return response.setData(new ArrayList<>());
        }

        String url = String.format("%smenuManage",ModelsEnum.BASEDATA.getBaseUrl());
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<Menu>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<Menu>>>() {
        });
        return response;
    }

    @ApiOperation("获取可见菜单树")
    @GetMapping({"tree"})
    public Response menuTree() throws Exception {
        String url = String.format("%smenuManage/tree",ModelsEnum.BASEDATA.getBaseUrl());
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<TreeNode>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<TreeNode>>>() {
        });
        return response;
    }

    @ApiOperation("获取所有菜单树")
    @GetMapping({"tree/all"})
    public Response allMenuTree(@RequestParam(required = false) String fuzzyLike) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("fuzzyLike", fuzzyLike);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "menuManage/tree/all", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<TreeNode>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<TreeNode>>>() {
        });
        return response;
    }

    @ApiOperation("新增菜单")
    @PostMapping({"/addMenu"})
    public Response addMenu(@RequestBody Menu menu) {
        String url = String.format("%smenuManage/addMenu",ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, menu, String.class);
        String res = cleanStr(responseEntity.getBody());
        Menu data = JSON.parseObject(res, new TypeReference<Menu>(){});
        DataResponse<Menu> response = Response.dataResponse();
        return response.setData(data);
    }

    @ApiOperation("修改菜单")
    @PutMapping({"/updateMenu"})
    public Response updateMenu(@RequestBody Menu menu) {
        String url = String.format("%smenuManage/updateMenu",ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, menu, String.class);
        String res = cleanStr(responseEntity.getBody());
        Menu data = JSON.parseObject(res, new TypeReference<Menu>(){});
        DataResponse<Menu> response = Response.dataResponse();
        return response.setData(data);
    }


    @ApiOperation(value = "获取用户角色菜单列表")
    @GetMapping("/getMenuByUserRole")
    public Response getMenuByUserRole() {
        String url = String.format("%smenuManage/getMenuByUserRole",ModelsEnum.BASEDATA.getBaseUrl());
        String res = cleanStr(restTemplate.getForObject(url , String.class));
        DataResponse<List<Menu>> response =  JSON.parseObject(res, new TypeReference<DataResponse<List<Menu>>>(){});
        return response;
    }


  /*  @ApiOperation(value = "获取用户角色菜单列表")
    @GetMapping("/getmenuFeaturesByUserRole")
    public Response getmenuFeaturesByUserRole(@RequestParam Long roleId,@RequestParam Long menuId) {

        return null;
    }*/

    @ApiOperation(value = "获取用户角色菜单功能权限" , response = RoleMenuFuncDto.class)
    @GetMapping("/getMenuFeaturesByUserRole")
    public Response getMenuFeaturesByUserRole(@RequestParam Long roleId, @RequestParam Long menuId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("roleId", roleId);
        param.put("menuId", menuId);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/menuManage/getMenuFeaturesByUserRole", param);
        final String res = restTemplate.getForEntity(url , String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<RoleMenuFuncDto>>>(){});
    }

    @ApiOperation(value = "用户登入获取所有的按钮权限")
    @GetMapping("/getMenuFeatureAll")
    public Response getMenuFeatureAll() {

        String url = String.format("%smenuManage/getMenuFeatureAll",ModelsEnum.BASEDATA.getBaseUrl());
        String res = cleanStr(restTemplate.getForObject(url , String.class));
        DataResponse<List<String>> response =  JSON.parseObject(res, new TypeReference<DataResponse<List<String>>>(){});
        return response;

    }
}
