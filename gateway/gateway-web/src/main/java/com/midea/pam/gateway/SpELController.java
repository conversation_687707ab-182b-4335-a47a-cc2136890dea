package com.midea.pam.gateway;

import com.midea.mcomponent.core.exception.MipException;
import com.midea.pam.common.basedata.dto.SpELDTO;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.service.SpELService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api("spel表达式")
@RestController
@RequestMapping("spel")
public class SpELController extends ControllerHelper {

	private static final Logger logger = LoggerFactory.getLogger(SpELController.class);

	@Autowired
	private SpELService spELService;


	@ApiOperation(value = "根据入参和表达式获取值")
	@PostMapping("getValue")
	public Response getValue(@RequestBody SpELDTO spELDTO) {
		Guard.notNull(spELDTO.getType(), "请选择输入参数类型");
		Guard.notNull(spELDTO.getTarget(), "输入参数不能为空");
		DataResponse<Object> response = Response.dataResponse();
		spELDTO.setIfTest(1);
		Object value = null;
		try {
			value = spELService.getValue(spELDTO);
		} catch (Exception e) {
			//审批配置调试接口直接把错误信息抛出，方便IT寻找问题
			throw new MipException(spELDTO.getSpELLog().getErrorMsg());
		}
		return response.setData(value);
	}
}