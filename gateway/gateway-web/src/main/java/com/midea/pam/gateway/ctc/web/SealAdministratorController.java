package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.SealAdministratorDTO;
import com.midea.pam.common.ctc.entity.SealAdministrator;
import com.midea.pam.common.ctc.vo.ContractVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: common-module
 * @description: 获取印章管理员信息
 * @author:zhongpeng
 * @create:2020-09-28 15:54
 **/
@Api("印章管理员信息")
@RestController
@RequestMapping("sealAdministrator")
public class SealAdministratorController extends ControllerHelper {
    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "获取印章管理员信息")
    @GetMapping("getSealAdministratorDTOs")
    public Response getSealAdministratorDTOs(@RequestParam(required = false) Long id,
                                             @RequestParam(required = false) String idStr,
                                             @RequestParam(required = false) String sealAdminAccount,
                                             @RequestParam(required = false) String sealAdminName,
                                             @RequestParam(required = false) String contractUnitCode,
                                             @RequestParam(required = false) String sealDepartmentNumber,
                                             @RequestParam(required = false) String sealName) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("idStr", idStr);
        param.put("sealAdminAccount", sealAdminAccount);
        param.put("sealAdminName", sealAdminName);
        param.put("contractUnitCode", contractUnitCode);
        param.put("sealDepartmentNumber", sealDepartmentNumber);
        param.put("sealName", sealName);

        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "sealAdministrator/getSealAdministratorDTOs", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<SealAdministratorDTO>>>() {
        });
    }
}
