package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.CarryoverBillDto;
import com.midea.pam.common.ctc.vo.CarryoverBillExcelVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Api("收入成本结转单模块")
@RestController
@RequestMapping("statistics/carryoverBill")
public class CarryoverBillStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "收入成本结转单分页查询")
    @GetMapping("page")
    public Response list(CarryoverBillDto carryoverBillDto,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(carryoverBillDto);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/carryoverBill/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<CarryoverBillDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<CarryoverBillDto>>>() {
        });

        return response;
    }

    @ApiOperation(value = "收入成本结转单列表导出", response = ResponseMap.class)
    @GetMapping("/export")
    public void listExport(HttpServletResponse response, CarryoverBillDto carryoverBillDto) {
        final Map<String, Object> param = buildParam(carryoverBillDto);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/carryoverBill/v1/export", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("收入成本结转单_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");

        JSONArray carryoverBillArr = (JSONArray) resultMap.get("carryoverBill");

        List<CarryoverBillExcelVO> carryoverBillExcelVOS = JSONObject.parseArray(carryoverBillArr.toJSONString(), CarryoverBillExcelVO.class);
        for (int i = 0; i < carryoverBillExcelVOS.size(); i++) {
            CarryoverBillExcelVO carryoverBillExcelVO = carryoverBillExcelVOS.get(i);
            carryoverBillExcelVO.setNum(i + 1);
        }

        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(carryoverBillExcelVOS, CarryoverBillExcelVO.class, null, "收入成本结转单", true);

        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    private Map buildParam(CarryoverBillDto carryoverBillDto) {

        final Map<String, Object> params = new HashMap<>();
        params.put("billNum", carryoverBillDto.getBillNum());
        params.put("periodName", carryoverBillDto.getPeriodName());
        params.put("projectNum", carryoverBillDto.getProjectNum());
        params.put("projectName", carryoverBillDto.getProjectName());
        params.put("localCurrency", carryoverBillDto.getLocalCurrency());

        params.put("projectTypeStr", carryoverBillDto.getProjectTypeStr());
        params.put("erpSyncStatusStr", carryoverBillDto.getErpSyncStatusStr());
        params.put("ouIdStr", carryoverBillDto.getOuIdStr());
        params.put("reverseStatusStr", carryoverBillDto.getReverseStatusStr());
        params.put("resource", carryoverBillDto.getResource());
        return params;
    }
}
