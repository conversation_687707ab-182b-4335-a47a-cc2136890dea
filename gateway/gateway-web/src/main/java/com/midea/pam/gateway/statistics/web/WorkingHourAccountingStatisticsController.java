package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.PurchaseOrderDto;
import com.midea.pam.common.ctc.dto.WorkingHourAccountingDto;
import com.midea.pam.common.ctc.excelVo.WorkingHourAccountingDetailExcelVo;
import com.midea.pam.common.ctc.excelVo.WorkingHourAccountingDetailInfoExcelVo;
import com.midea.pam.common.ctc.excelVo.WorkingHourAccountingExcelVo;
import com.midea.pam.common.ctc.vo.ContractExcelVO;
import com.midea.pam.common.ctc.vo.InvoicePlanExcelVo;
import com.midea.pam.common.ctc.vo.ReceiptPlanExcelVo;
import com.midea.pam.common.ctc.vo.SubContractExcelVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

@Api("工时成本入账")
@RestController
@RequestMapping("statistics/workingHourAccount")
public class WorkingHourAccountingStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "工时成本入账导出", response = WorkingHourAccountingDto.class)
    @GetMapping({"exprot"})
    public void exportWorkingHourAccount(
            HttpServletResponse response,
            @RequestParam(required = false) @ApiParam(value = "入账单号") String code,
            @RequestParam(required = false) @ApiParam(value = "出勤月份") String applyMonth,
            @RequestParam(required = false) @ApiParam(value = "审批月份") String approveMonth,
            @RequestParam(required = false) @ApiParam(value = "会计期间") String glPeriod,
            @RequestParam(required = false) @ApiParam(value = "入账日期开始") String glDateBegin,
            @RequestParam(required = false) @ApiParam(value = "入账日期结束") String glDateEnd,
            @RequestParam(required = false) @ApiParam(value = "币种") String currency,
            @RequestParam(required = false) @ApiParam(value = "同步状态") String manyErpStatus,
            @RequestParam(required = false) @ApiParam(value = "业务实体") String ouName,
            @RequestParam(required = false) @ApiParam(value = "作废状态") String manyStatus,
            @RequestParam(required = false) @ApiParam(value = "工时成本入账id") String workingHourAccountingIds,
            @RequestParam(required = false) @ApiParam(value = "资源类型") String resource,
            @RequestParam(required = false) @ApiParam(value = "冲销状态") String writeOffStatusStr) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("code", code);
        param.put("applyMonth", applyMonth);
        param.put("approveMonth", approveMonth);
        param.put("glPeriod", glPeriod);
        param.put("glDateBegin", glDateBegin);
        param.put("glDateEnd",glDateEnd);
        param.put("currency",currency);
        param.put("manyErpStatus", manyErpStatus);
        param.put("ouName", ouName);
        param.put("manyStatus", manyStatus);
        param.put("workingHourAccountingIds", workingHourAccountingIds);
        param.put("resource", resource);
        param.put("writeOffStatusStr",writeOffStatusStr);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/workingHourAccounting/export", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        final DataResponse<Map<String,Object>> dataResponse =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String,Object>>>() {
                });
        Map<String,Object>resultMap = dataResponse.getData();
        JSONArray accountingArr = (JSONArray) resultMap.get("accountingList");
        JSONArray accountingDetailArr = (JSONArray) resultMap.get("accountingDetailList");
        JSONArray detailDtoArr = (JSONArray) resultMap.get("detailDtoList");

        List<WorkingHourAccountingExcelVo> workingHourAccountingExcelVoList = new ArrayList<>();
        List<WorkingHourAccountingDetailInfoExcelVo> workingHourAccountingDetailInfoExcelVoList = new ArrayList<>();
        List<WorkingHourAccountingDetailExcelVo> workingHourAccountingDetailExcelVoList = new ArrayList<>();

        if(accountingArr!=null) {
            workingHourAccountingExcelVoList = JSONObject.parseArray(accountingArr.toJSONString(), WorkingHourAccountingExcelVo.class);
        }

        if(accountingDetailArr!=null) {
            workingHourAccountingDetailInfoExcelVoList = JSONObject.parseArray(accountingDetailArr.toJSONString(), WorkingHourAccountingDetailInfoExcelVo.class);
        }

        if (detailDtoArr!=null) {
            workingHourAccountingDetailExcelVoList = JSONObject.parseArray(detailDtoArr.toJSONString(), WorkingHourAccountingDetailExcelVo.class);
        }

        Workbook workbook = ExportExcelUtil.buildDefaultSheet(workingHourAccountingExcelVoList, WorkingHourAccountingExcelVo.class, null, "工时入账单", true);
        ExportExcelUtil.addSheet(workbook,workingHourAccountingDetailInfoExcelVoList, WorkingHourAccountingDetailInfoExcelVo.class, null, "按项目汇总工时",true);
        ExportExcelUtil.addSheet(workbook,workingHourAccountingDetailExcelVoList,WorkingHourAccountingDetailExcelVo.class,null,"工时明细",true);

        ExportExcelUtil.downLoadExcel("工时成本入账单_"+DateUtils.format(new Date(),"yyyyMMddHHmmss")+".xls", response, workbook);
    }

    @ApiOperation(value = "工时成本入账分页查询", response = WorkingHourAccountingDto.class)
    @GetMapping({"selectPage"})
    public Response selectPage(
            @RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize,
            @RequestParam(required = false) @ApiParam(value = "入账单号") String code,
            @RequestParam(required = false) @ApiParam(value = "出勤月份") String applyMonth,
            @RequestParam(required = false) @ApiParam(value = "审批月份") String approveMonth,
            @RequestParam(required = false) @ApiParam(value = "会计期间") String glPeriod,
            @RequestParam(required = false) @ApiParam(value = "入账日期开始") String glDateBegin,
            @RequestParam(required = false) @ApiParam(value = "入账日期结束") String glDateEnd,
            @RequestParam(required = false) @ApiParam(value = "币种") String currency,
            @RequestParam(required = false) @ApiParam(value = "同步状态") String manyErpStatus,
            @RequestParam(required = false) @ApiParam(value = "业务实体") String ouName,
            @RequestParam(required = false) @ApiParam(value = "作废状态") String manyStatus,
            @RequestParam(required = false) @ApiParam(value = "工时成本入账id") String workingHourAccountingIds,
            @RequestParam(required = false) @ApiParam(value = "资源类型") String resource,
            @RequestParam(required = false) @ApiParam(value = "冲销状态") String writeOffStatusStr) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum",pageNum);
        param.put("pageSize",pageSize);
        param.put("code", code);
        param.put("applyMonth", applyMonth);
        param.put("approveMonth", approveMonth);
        param.put("glPeriod", glPeriod);
        param.put("glDateBegin", glDateBegin);
        param.put("glDateEnd",glDateEnd);
        param.put("currency",currency);
        param.put("manyErpStatus", manyErpStatus);
        param.put("ouName", ouName);
        param.put("manyStatus", manyStatus);
        param.put("workingHourAccountingIds", workingHourAccountingIds);
        param.put("resource", resource);
        param.put("writeOffStatusStr",writeOffStatusStr);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/workingHourAccounting/page", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<WorkingHourAccountingDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<WorkingHourAccountingDto>>>() {
        });
        return response;
    }

}
