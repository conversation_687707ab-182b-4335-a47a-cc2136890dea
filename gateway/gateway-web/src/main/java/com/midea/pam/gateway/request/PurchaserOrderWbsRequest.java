package com.midea.pam.gateway.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "WbsPageRequest", description = "我的采购订单(wbs)")
public class PurchaserOrderWbsRequest {
    @ApiModelProperty("页数")
    Integer pageNum;

    @ApiModelProperty("分页大小")
    Integer pageSize;

    @ApiModelProperty("详细设计单据编号")
    private String requirementCode;

    @ApiModelProperty("设计发布批次号")
    private String designReleaseLotNumber;

    @ApiModelProperty("erp订单状态(1-未发布 2-已发布 3-已回签)")
    private String erpOrderStatusStr;

    @ApiModelProperty("发布结束日期")
    String publishEndTime;

    @ApiModelProperty("发布开始日期")
    String publishStartTime;

    @ApiModelProperty("图纸版本号")
    String chartVersion;

    @ApiModelProperty("品牌")
    String brand;

    @ApiModelProperty("图号/型号")
    String model;

    @ApiModelProperty("wbs")
    String wbsSummaryCode;

    @ApiModelProperty("采购订单号")
    String fuzzyOrderNum;

    @ApiModelProperty("供应商名称")
    String fuzzyVendorName;

    @ApiModelProperty("供应商编码")
    String fuzzyVendorNum;

    @ApiModelProperty("供应商地点")
    String fuzzyVendorSiteCode;

    @ApiModelProperty("采购员")
    String buyerName;

    @ApiModelProperty("物料描述")
    String materielDescr;

    @ApiModelProperty("pam物料编码")
    String pamCode;

    @ApiModelProperty("ERP物料编码")
    String erpCode;

    @ApiModelProperty("项目名称")
    String fuzzyProjectName;

    @ApiModelProperty("项目编号")
    String fuzzyProjectNum;

    @ApiModelProperty("业务实体")
    String manyProjectOuName;

    @ApiModelProperty("创建日期开始")
    String orderCreateAtBegin;

    @ApiModelProperty("创建日期结束")
    String orderCreateAtEnd;

    @ApiModelProperty("计划交货日期开始")
    String deliveryTimeBegin;

    @ApiModelProperty("计划交货日期结束")
    String deliveryTimeEnd;

    @ApiModelProperty("类型：0=已创建；3=创建中")
    String manyType;

    @ApiModelProperty("订单状态")
    String manyStatus;

    @ApiModelProperty("订单行状态")
    String statusStr;

    @ApiModelProperty("是否合并行")
    String isMerge;

    @ApiModelProperty("定价类型:1一单一价2一揽子")
    Integer pricingType;

    @ApiModelProperty(value = "审批通过日期开始")
    private Date approvalStartTime;

    @ApiModelProperty(value = "审批通过日期结束")
    private Date approvalEndTime;

    @ApiModelProperty(value = "供方承诺交期开始")
    private Date promisedDateStart;

    @ApiModelProperty(value = "供方承诺交期开始")
    private Date promisedDateEnd;

    @ApiModelProperty("跟踪日期开始时间")
    private Date trackDateStart;

    @ApiModelProperty("跟踪日期结束时间")
    private Date trackDateEnd;
}