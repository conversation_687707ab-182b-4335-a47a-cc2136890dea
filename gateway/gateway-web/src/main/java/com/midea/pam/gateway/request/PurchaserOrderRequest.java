package com.midea.pam.gateway.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "PurchaserOrderRequest", description = "采购订单查询请求对象")
public class PurchaserOrderRequest {
    @ApiModelProperty("页数")
    Integer pageNum;

    @ApiModelProperty("分页大小")
    Integer pageSize;

    @ApiModelProperty("详细设计单据编号")
    String requirementCode;

    @ApiModelProperty("设计发布批次号")
    String designReleaseLotNumber;

    @ApiModelProperty("发布结束日期")
    String publishEndTime;

    @ApiModelProperty("发布开始日期")
    String publishStartTime;

    @ApiModelProperty("图纸版本号")
    String chartVersion;

    @ApiModelProperty("品牌")
    String brand;

    @ApiModelProperty("图号/型号")
    String model;

    @ApiModelProperty("wbs")
    String wbsSummaryCode;

    @ApiModelProperty("采购订单号")
    String fuzzyOrderNum;

    @ApiModelProperty("供应商名称")
    String fuzzyVendorName;

    @ApiModelProperty("供应商编码")
    String fuzzyVendorNum;

    @ApiModelProperty("采购员")
    String buyerName;

    @ApiModelProperty("物料描述")
    String materielDescr;

    @ApiModelProperty("pam物料编码")
    String pamCode;

    @ApiModelProperty("ERP物料编码")
    String erpCode;

    @ApiModelProperty("项目名称")
    String fuzzyProjectName;

    @ApiModelProperty("项目编号")
    String fuzzyProjectNum;

    @ApiModelProperty("业务实体")
    String manyProjectOuName;

    @ApiModelProperty("订单行创建人")
    String orderDetailCreateByName;

    @ApiModelProperty("订单行创建日期开始")
    String orderDetailCreateAtBegin;

    @ApiModelProperty("订单行创建日期结束")
    String orderDetailCreateAtEnd;

    @ApiModelProperty("创建日期开始")
    String orderCreateAtBegin;

    @ApiModelProperty("创建日期结束")
    String orderCreateAtEnd;

    @ApiModelProperty("计划交货日期开始")
    String deliveryTimeBegin;

    @ApiModelProperty("计划交货日期结束")
    String deliveryTimeEnd;

    @ApiModelProperty("类型：0=已创建；1=待下达；2=待变更；3=创建中")
    String manyType;

    @ApiModelProperty("订单状态")
    String manyStatus;

    @ApiModelProperty("订单行状态")
    String statusStr;

    @ApiModelProperty("是否合并行")
    String isMerge;

    @ApiModelProperty("定价类型:1一单一价;2一揽子")
    Integer pricingType;
}