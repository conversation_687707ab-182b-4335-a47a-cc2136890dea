package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


@Api("项目wbs预算变更汇总")
@RestController
@RequestMapping("projectWbsBudgetSummaryChangeHistory")
public class ProjectWbsBudgetSummaryChangeHistoryController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "预立项转正-审批页面-wbs预算汇总")
    @PostMapping("findByWbsBudgetSummary")
    public Response findByWbsBudgetSummary(@RequestBody Map<String, Object> param) {
        final String url = String.format("%sprojectWbsBudgetSummaryChangeHistory/findByWbsBudgetChangeHistorySummary",
                ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, param, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<Map>>>() {
        });
    }

    @ApiOperation(value = "预立项转正-审批页面-activity预算汇总")
    @PostMapping("findByActivityBudgetSummary")
    public Response findByActivityBudgetSummary(@RequestBody Map<String, Object> param) {
        final String url = String.format("%sprojectWbsBudgetSummaryChangeHistory/findByActivityBudgetChangeHistorySummary",
                ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, param, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<Map>>>() {
        });
    }
}
