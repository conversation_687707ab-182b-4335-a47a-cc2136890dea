package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.ProjectContractBudgetMaterialDto;
import com.midea.pam.common.ctc.dto.PurchaseContractBudgetMaterialDto;
import com.midea.pam.common.ctc.vo.ProjectContractBudgetMaterialVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

@Api(value = "项目采购合同关联")
@RestController
@RequestMapping("/purchaseContractBudget")
public class PurchaseContractBudgetController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "保存价格")
    @PostMapping("savePrice")
    public Response update(@RequestBody PurchaseContractBudgetMaterialDto materialDto) {
        final String url = String.format("%spurchaseContractBudget/savePrice", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, materialDto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation(value = "列表查询")
    @GetMapping("list")
    public Response list(@RequestParam Long projectId, @RequestParam String code) {
        final HashMap<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        param.put("code", code);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseContractBudget/list", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PurchaseContractBudgetMaterialDto>>() {
        });
    }

    @Deprecated
    @ApiOperation(value = "查询采购合同已使用的材料模组ID及已使用余额")
    @GetMapping("getMaterialIdsAndUseBalance")
    public Response getMaterialIdsAndUseBalance(@RequestParam(required = false) @ApiParam(value = "项目ID") Long projectId,
                                                @RequestParam @ApiParam(value = "合同编号") String code,
                                                @RequestParam(required = false) @ApiParam(value = "模组ID,多个用逗号隔开") String materialIds,
                                                @RequestParam(required = false) @ApiParam(value = "合同ID") Long purchaseContractId,
                                                @RequestParam(required = false) @ApiParam(value = "状态") Boolean status,
                                                @RequestParam(required = false) @ApiParam(value = "变更头ID") Long headerId) {
        final HashMap<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        param.put("code", code);
        param.put("materialIds", materialIds);
        param.put("purchaseContractId", purchaseContractId);
        param.put("status", status);
        param.put("headerId", headerId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseContractBudget/getMaterialIdsAndUseBalance", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<ProjectContractBudgetMaterialVo>>>() {
        });
    }

    @ApiOperation(value = "根据材料模组ID查询采购合同已使用余额数据")
    @GetMapping("getContractBudgetByMaterialId")
    public Response getContractBudgetByMaterialId(@RequestParam @ApiParam(value = "模组ID") Long materialId,
                                                  @RequestParam(required = false) @ApiParam(value = "是否过滤当前合同") Long purchaseContractId) {
        final HashMap<String, Object> param = new HashMap<>();
        param.put("materialId", materialId);
        param.put("purchaseContractId", purchaseContractId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/purchaseContractBudget/getContractBudgetByMaterialId", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<ProjectContractBudgetMaterialDto>>() {
        });
    }

}

































