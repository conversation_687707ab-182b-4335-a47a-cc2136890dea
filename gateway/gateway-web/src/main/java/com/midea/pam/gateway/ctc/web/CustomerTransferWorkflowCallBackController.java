package com.midea.pam.gateway.ctc.web;

import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

@Api("客户间回款新建流程回调")
@RestController
@RequestMapping("customerTransfer/workflow/callback")
public class CustomerTransferWorkflowCallBackController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "审批中")
    @PutMapping("draftSubmit/skipSecurityInterceptor")
    public String draftSubmitCallback(@RequestParam(required = false) String formInstanceId, @RequestParam(required = false) String handlerId) {
        final String url = String.format("%scustomerTransfer/workflow/callback/draftSubmit/skipSecurityInterceptor?formInstanceId=%s&handlerId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId, handlerId);
        restTemplate.put(url,String.class);
        return null;
    }

    @ApiOperation(value = "驳回")
    @PutMapping("refuse/skipSecurityInterceptor")
    public String refuseCallback(@RequestParam(required = false) String formInstanceId, @RequestParam(required = false) String handlerId) {
        final String url = String.format("%scustomerTransfer/workflow/callback/refuse/skipSecurityInterceptor?formInstanceId=%s&handlerId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId, handlerId);
        restTemplate.put(url,String.class);
        return null;
    }

    @ApiOperation(value = "通过")
    @PutMapping("pass/skipSecurityInterceptor")
    public String passCallback(@RequestParam(required = false) String formInstanceId, @RequestParam(required = false) String handlerId, @RequestParam(required = false) Long companyId) {
        final String url = String.format("%scustomerTransfer/workflow/callback/pass/skipSecurityInterceptor?formInstanceId=%s&handlerId=%s&companyId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId, handlerId, companyId);
        restTemplate.put(url,String.class);
        return null;
    }

    @ApiOperation(value = "撤回")
    @PutMapping("draftReturn/skipSecurityInterceptor")
    public String draftReturnCallback(@RequestParam(required = false) String formInstanceId, @RequestParam(required = false) String handlerId) {
        final String url = String.format("%scustomerTransfer/workflow/callback/draftReturn/skipSecurityInterceptor?formInstanceId=%s&handlerId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId, handlerId);
        restTemplate.put(url,String.class);
        return null;
    }

    @ApiOperation(value = "作废")
    @PutMapping("abandon/skipSecurityInterceptor")
    public String abandonCallback(@RequestParam(required = false) String formInstanceId, @RequestParam(required = false) String handlerId) {
        final String url = String.format("%scustomerTransfer/workflow/callback/abandon/skipSecurityInterceptor?formInstanceId=%s&handlerId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId, handlerId);
        restTemplate.put(url,String.class);
        return null;
    }

    @ApiOperation(value = "删除")
    @PutMapping("delete/skipSecurityInterceptor")
    public String deleteCallback(@RequestParam(required = false) String formInstanceId, @RequestParam(required = false) String handlerId) {
        final String url = String.format("%scustomerTransfer/workflow/callback/delete/skipSecurityInterceptor?formInstanceId=%s&handlerId=%s", ModelsEnum.CTC.getBaseUrl(), formInstanceId, handlerId);
        restTemplate.put(url,String.class);
        return null;
    }
}
