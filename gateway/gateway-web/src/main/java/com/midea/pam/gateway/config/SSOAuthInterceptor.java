package com.midea.pam.gateway.config;

import com.alibaba.fastjson.JSONArray;
import com.midea.pam.gateway.common.utils.PamCurrentUserUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019-11-12
 * @description
 */
public class SSOAuthInterceptor implements Filter {

    private Logger logger = LoggerFactory.getLogger(this.getClass());


    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        final HttpServletRequest request = (HttpServletRequest) servletRequest;
        final HttpServletResponse response = (HttpServletResponse) servletResponse;

        String currentUserName = getCurrentUserName(request);
        if (StringUtils.isNotBlank(currentUserName)) {
//            UserUtils.setUserTL(null, currentUserName, null, null);
            PamCurrentUserUtil.setUserTL(currentUserName);
        }

        filterChain.doFilter(request, response);
    }

    /**
     * 获取当前登录用户
     *
     * @param request
     * @return
     */
    private String getCurrentUserName(HttpServletRequest request) {
        String userName = null;

        // apmUser=xupan4
        final Cookie[] cookies = request.getCookies();
        if (cookies != null && cookies.length > 0) {
            logger.info("cookies:{}", JSONArray.toJSONString(cookies));
            for (int i = 0; i < cookies.length; i++) {
                final Cookie cookie = cookies[i];
                final String name = cookie.getName();
                final String value = cookie.getValue();
                if (Objects.equals("apmUser", name)) {
                    userName = value;
                    break;
                }
            }
        }
        return userName;
    }

    @Override
    public void destroy() {

    }
}
