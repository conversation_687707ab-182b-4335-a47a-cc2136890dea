package com.midea.pam.gateway.basedata.web;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.midea.pam.common.basedata.dto.BrandMaintenanceDto;
import com.midea.pam.common.basedata.dto.BrandMaintenanceIntermediateHeadDto;
import com.midea.pam.common.basedata.entity.BrandMaintenance;
import com.midea.pam.common.basedata.entity.BrandMaintenanceIntermediateHead;
import com.midea.pam.common.basedata.excelVo.BrandMaintenanceExcelVo;
import com.midea.pam.common.basedata.excelVo.BrandMaintenanceExportExcelVo;
import com.midea.pam.common.basedata.excelVo.BrandMaintenanceValidResultExcelVo;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.enums.BrandEnum;
import com.midea.pam.common.enums.CodePrefix;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.common.utils.ExportExcelUtil;
import com.midea.pam.gateway.common.utils.FileUtil;
import com.midea.pam.gateway.service.MipWorkflowInnerService;
import com.midea.pam.support.utils.BeanConverter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @description: 品牌维护控制层
 * @author: ex_xuwj4
 * @create: 2021-09-07
 **/
@RestController
@RequestMapping({"brandMaintenance", "mobile/app/brandMaintenance"})
@Api("品牌查询")
public class BrandMaintenanceController extends ControllerHelper {

    private static final Logger logger = LoggerFactory.getLogger(BrandMaintenanceController.class);

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private MipWorkflowInnerService mipWorkflowInnerService;


    @ApiOperation(value = "品牌查询列表分页")
    @GetMapping("selectPage")
    public Response selectPage(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                               @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize,
                               @RequestParam(required = false) @ApiParam("品牌名称") String brandName,
                               @RequestParam(required = false) @ApiParam("品牌名称(中文)") String brandNameCn,
                               @RequestParam(required = false) @ApiParam("品牌名称(英文)") String brandNameEn,
                               @RequestParam(required = false) @ApiParam("品牌编码") String brandCode,
                               @RequestParam(required = false) @ApiParam("别名") String anotherName,
                               @RequestParam(required = false) @ApiParam("品牌状态") String brandStatuses
    ) {

        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("brandName", brandName);
        param.put("brandNameCn", brandNameCn);
        param.put("brandNameEn", brandNameEn);
        param.put("brandCode", brandCode);
        param.put("anotherName", anotherName);
        param.put("brandStatuses", brandStatuses);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/brandMaintenance/selectPage", param);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<BrandMaintenanceDto>>>() {
        });
    }

    @ApiOperation(value = "Excel导出", response = BrandMaintenanceDto.class)
    @GetMapping("/export")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false) @ApiParam("品牌名称") String brandName,
                       @RequestParam(required = false) @ApiParam("品牌编码") String brandCode,
                       @RequestParam(required = false) @ApiParam("品牌名称(中文)") String brandNameCn,
                       @RequestParam(required = false) @ApiParam("品牌名称(英文)") String brandNameEn,
                       @RequestParam(required = false) @ApiParam("别名") String anotherName,
                       @RequestParam(required = false) @ApiParam("品牌状态") String brandStatuses) {
        HashMap<String, Object> param = Maps.newHashMap();
        param.put("pageNum", 1);
        param.put("pageSize", Integer.MAX_VALUE);
        param.put("brandName", brandName);
        param.put("brandNameCn", brandNameCn);
        param.put("brandNameEn", brandNameEn);
        param.put("brandCode", brandCode);
        param.put("anotherName", anotherName);
        param.put("brandStatuses", brandStatuses);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/brandMaintenance/selectPage", param);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<PageInfo<BrandMaintenanceDto>> dataResponse = JSON.parseObject(res,
                new TypeReference<DataResponse<PageInfo<BrandMaintenanceDto>>>() {
                });
        List<BrandMaintenanceDto> list = dataResponse.getData().getList();
        List<BrandMaintenanceExportExcelVo> excelList = BeanConverter.copy(list, BrandMaintenanceExportExcelVo.class);
        for (int i = 0; i < excelList.size(); i++) {
            excelList.get(i).setIndex(i + 1);
        }
        Workbook workbook = com.midea.pam.common.util.ExportExcelUtil.buildDefaultSheet(excelList, BrandMaintenanceExportExcelVo.class, null,
                "品牌清单", true);
        com.midea.pam.common.util.ExportExcelUtil.downLoadExcel("品牌清单_" + DateUtils.format(new Date(), "yyyyMMdd") +
                CacheDataUtils.generateSequence(Constants.PPQD, CodePrefix.PPQD.code()) + ".xls", response, workbook);
    }

    @ApiOperation(value = "新增", response = BrandMaintenanceIntermediateHeadDto.class)
    @PostMapping("/addOrUpdate")
    public Response saveWithDetail(@RequestBody BrandMaintenanceIntermediateHeadDto dto) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "/brandMaintenance/addOrUpdate";
        String res = restTemplate.postForObject(url, dto, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<BrandMaintenanceIntermediateHeadDto>>() {
        });
    }

    @ApiOperation(value = "品牌变更")
    @PostMapping("update")
    public com.midea.pam.common.base.Response update(@RequestBody BrandMaintenanceIntermediateHeadDto dto) {
        String url = String.format("%sbrandMaintenance/update", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        com.midea.pam.common.base.DataResponse<BrandMaintenanceIntermediateHeadDto> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<com.midea.pam.common.base.DataResponse<BrandMaintenanceIntermediateHeadDto>>() {
                });
        return response;
    }

    @ApiOperation(value = "编辑")
    @PostMapping("updateBrand")
    public com.midea.pam.common.base.Response updateBrand(@RequestBody BrandMaintenance brandMaintenance) {
        String url = String.format("%sbrandMaintenance/updateBrand", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, brandMaintenance, String.class);
        com.midea.pam.common.base.DataResponse<BrandMaintenance> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<com.midea.pam.common.base.DataResponse<BrandMaintenance>>() {
                });
        return response;
    }

    @ApiOperation(value = "作废")
    @GetMapping("deleteDraft")
    public Response deleteDraft(@RequestParam Long id) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "/brandMaintenance/deleteDraft?id=" + id;
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<BrandMaintenanceIntermediateHead> response = JSON.parseObject(res,
                new TypeReference<DataResponse<BrandMaintenanceIntermediateHead>>() {
                });
        //同时作废工作流
        if (response.getCode() == 0) {
            BrandMaintenanceIntermediateHead head = response.getData();
            if (Objects.equals(head.getType(), BrandEnum.BRAND_ADDED.code().toString())) {
                mipWorkflowInnerService.draftAbandon("brandAddApp", id);
            } else if (Objects.equals(head.getType(), BrandEnum.BRAND_CHANGE.code().toString())) {
                mipWorkflowInnerService.draftAbandon("brandChangeApp", id);
            }
        }
        return response;
    }

    @ApiOperation(value = "品牌新增-导入")
    @PostMapping("importBrandMaintenance")
    public Response importBrandMaintenance(@RequestParam(value = "file") MultipartFile file) {
        List<BrandMaintenanceExcelVo> detailImportExcelVoList;
        try {
            detailImportExcelVoList = FileUtil.importExcel(file, BrandMaintenanceExcelVo.class, 1, 0);
        } catch (Exception e) {
            try {
                detailImportExcelVoList = FileUtil.importExcel(file, BrandMaintenanceExcelVo.class, 0, 0);
            } catch (Exception exception) {
                logger.error("品牌新增-导入，上传失败:", e);
                throw new BizException(100, "导入文件有误或数据类型未按要求填写");
            }
        }
        // 移除空行的数据
        Iterator<BrandMaintenanceExcelVo> iterator = detailImportExcelVoList.iterator();
        while (iterator.hasNext()) {
            BrandMaintenanceExcelVo next = iterator.next();
            if (StringUtils.isBlank(next.getBrandNameCn())
                    && StringUtils.isBlank(next.getBrandNameEn())
                    && StringUtils.isBlank(next.getAnotherName())
                    && StringUtils.isBlank(next.getDes())) {
                iterator.remove();
            }
        }
        Asserts.notEmpty(detailImportExcelVoList, ErrorCode.SYSTEM_FILE_EMPTY);

        final String url = String.format("%sbrandMaintenance/validImportDetail", ModelsEnum.BASEDATA.getBaseUrl());
        String res = restTemplate.postForEntity(url, detailImportExcelVoList, String.class).getBody();
        DataResponse<List<BrandMaintenanceDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<BrandMaintenanceDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "品牌新增和变更详情", response = BrandMaintenanceIntermediateHeadDto.class)
    @GetMapping("/getDetailById")
    public Response getDetailById(@RequestParam @ApiParam("ID") Long id) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "/brandMaintenance/getDetailById?id=" + id;
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<BrandMaintenanceIntermediateHeadDto>>() {
        });
    }

    @ApiOperation(value = "品牌新增-导出检查结果")
    @PostMapping("exportDetailValidResult")
    public void exportDetailValidResult(HttpServletResponse response, @RequestBody List<BrandMaintenanceDto> detailDTOList) {
        List<BrandMaintenanceValidResultExcelVo> excelList = BeanConverter.copy(detailDTOList, BrandMaintenanceValidResultExcelVo.class);
        ExportExcelUtil.exportExcel(excelList, null, "品牌新增检查结果", BrandMaintenanceValidResultExcelVo.class, "品牌新增检查结果.xls", response);
    }

    @ApiOperation(value = "发起审批")
    @PutMapping("updateStatusChecking/skipSecurityInterceptor")
    public Response updateStatusChecking(@RequestParam(required = false) Long formInstanceId,
                                         @RequestParam(required = false) String fdInstanceId,
                                         @RequestParam(required = false) String formUrl,
                                         @RequestParam(required = false) String eventName,
                                         @RequestParam(required = false) String handlerId,
                                         @RequestParam(required = false) Long companyId,
                                         @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sbrandMaintenance/updateStatusChecking/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.BASEDATA.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "驳回")
    @PutMapping("updateStatusReject/skipSecurityInterceptor")
    public Response updateStatusReject(@RequestParam(required = false) Long formInstanceId,
                                       @RequestParam(required = false) String fdInstanceId,
                                       @RequestParam(required = false) String formUrl,
                                       @RequestParam(required = false) String eventName,
                                       @RequestParam(required = false) String handlerId,
                                       @RequestParam(required = false) Long companyId,
                                       @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sbrandMaintenance/updateStatusReject/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.BASEDATA.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "通过")
    @PutMapping("updateStatusPass/skipSecurityInterceptor")
    public Response updateStatusPass(@RequestParam(required = false) Long formInstanceId,
                                     @RequestParam(required = false) String fdInstanceId,
                                     @RequestParam(required = false) String formUrl,
                                     @RequestParam(required = false) String eventName,
                                     @RequestParam(required = false) String handlerId,
                                     @RequestParam(required = false) Long companyId,
                                     @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sbrandMaintenance/updateStatusPass/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.BASEDATA.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "撤回")
    @PutMapping("updateStatusReturn/skipSecurityInterceptor")
    public Response updateStatusReturn(@RequestParam(required = false) Long formInstanceId,
                                       @RequestParam(required = false) String fdInstanceId,
                                       @RequestParam(required = false) String formUrl,
                                       @RequestParam(required = false) String eventName,
                                       @RequestParam(required = false) String handlerId,
                                       @RequestParam(required = false) Long companyId,
                                       @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sbrandMaintenance/updateStatusReturn/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.BASEDATA.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "作废")
    @PutMapping("abandon/skipSecurityInterceptor")
    public Response abandon(@RequestParam(required = false) Long formInstanceId,
                            @RequestParam(required = false) String fdInstanceId,
                            @RequestParam(required = false) String formUrl,
                            @RequestParam(required = false) String eventName,
                            @RequestParam(required = false) String handlerId,
                            @RequestParam(required = false) Long companyId,
                            @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sbrandMaintenance/abandon/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.BASEDATA.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "删除")
    @PutMapping("delete/skipSecurityInterceptor")
    public Response delete(@RequestParam(required = false) Long formInstanceId,
                           @RequestParam(required = false) String fdInstanceId,
                           @RequestParam(required = false) String formUrl,
                           @RequestParam(required = false) String eventName,
                           @RequestParam(required = false) String handlerId,
                           @RequestParam(required = false) Long companyId,
                           @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sbrandMaintenance/delete/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.BASEDATA.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "处理人通过")
    @PutMapping("agree/skipSecurityInterceptor")
    public Response agree(@RequestParam(required = false) Long formInstanceId,
                          @RequestParam(required = false) String fdInstanceId,
                          @RequestParam(required = false) String formUrl,
                          @RequestParam(required = false) String eventName,
                          @RequestParam(required = false) String handlerId,
                          @RequestParam(required = false) Long companyId,
                          @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sbrandMaintenance/agree/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.BASEDATA.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "查询品牌维护列表")
    @GetMapping("selectList")
    public Response selectList() {
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/brandMaintenance/selectList", null);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<List<BrandMaintenance>>>() {
        });
    }

}
