package com.midea.pam.gateway.config;

import com.midea.mcomponent.core.exception.MipException;
import com.midea.pam.common.base.BaseResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.client.RestClientException;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import javax.servlet.http.HttpServletRequest;

@Order(-2)
@ControllerAdvice
@ResponseBody
public class CommonExceptionHandler extends ResponseEntityExceptionHandler {

    /**
     * 所有通用的业务异常，需要提示给用户
     *
     * @param e 异常
     */
    @ExceptionHandler(ApplicationBizException.class)
    public Response applicationBizException(ApplicationBizException e) {
        BaseResponse response = Response.response();
        response.setCode(ErrorCode.WARNING.getCode());
        response.setMsg(e.getMessage());
        return response;
    }

    /**
     * 业务异常，需要提示给用户
     *
     * @param e 异常
     */
    @ExceptionHandler(MipException.class)
    public Response mipException(MipException e) {
        BaseResponse response = Response.response();
        response.setCode(ErrorCode.WARNING.getCode());
        // 现框架抛出异常为key值，后续国际化之后取message，现取key值
        response.setMsg(e.getMessage());
        return response;
    }

    /**
     * 业务异常
     *
     * @param e 异常
     */
    @ExceptionHandler(BizException.class)
    public Response bizException(BizException e) {
        BaseResponse response = Response.response();
        response.setCode(e.getCode());
        response.setMsg(e.getMessage());
        return response;
    }

    /**
     * 超时异常
     *
     * @param e 异常
     */
    @ExceptionHandler(RestClientException.class)
    public Response restClientException(RestClientException e) {
        BaseResponse response = Response.response();
        response.setCode(ErrorCode.TIME_OUT.getCode());
        response.setMsg(ErrorCode.TIME_OUT.getMsg());
        logger.error("【网关异常收集:】" + e.getMessage(), e);
        return response;
    }

    /**
     * 业务异常
     *
     * @param e 异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public Response illegalArgumentException(IllegalArgumentException e) {
        BaseResponse response = Response.response();
        response.setCode(ErrorCode.WARNING.getCode());
        response.setMsg(e.getMessage());
        return response;
    }

    /**
     * 未捕获异常统一处理
     *
     * @param e 异常
     */
    @ExceptionHandler(Exception.class)
    public Response commonException(HttpServletRequest request, Exception e) {
        logger.error(request.getRequestURL() + "：请求异常", e);
        BaseResponse response = Response.response();
        response.setCode(ErrorCode.ERROR.getCode());
        response.setMsg("系统出错,请重试或联系系统管理员");
        return response;
    }
}
