package com.midea.pam.gateway.mdw.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @PackageClassName: com.midea.pam.gateway.statistics.web.CarryoverBillIncomeCostDetailStatisticsController
 * @Description: 项目已确认收入&成本详表数据
 * @Author: JerryH
 * @Date: 2022-12-12, 0012 下午 04:47:58
 */
@Api("项目已确认收入&成本详表数据")
@RestController
@RequestMapping("mdw/carryoverBillIncomeCostDetailStatistics")
public class DwdCarryoverBillIncomeCostDetailStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "手动拉取项目已确认收入&成本数据")
    @GetMapping("saveOrUpdateCarryoverBillIncomeCostDetail")
    public Response saveOrUpdateCarryoverBillIncomeCostDetail(@RequestParam(required = false) Long projectId) {
        Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        final String url = buildGetUrl(ModelsEnum.MDW.getBaseUrl(), "mdw/carryoverBillIncomeCostDetailStatistics/saveOrUpdateCarryoverBillIncomeCostDetail", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "手动统计项目已确认收入数据")
    @GetMapping("statisticsCarryoverBillIncomeCostDetail")
    public Response statisticsCarryoverBillIncomeCostDetail(@RequestParam(required = false) Long projectId) {
        Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        final String url = buildGetUrl(ModelsEnum.MDW.getBaseUrl(), "mdw/carryoverBillIncomeCostDetailStatistics/statisticsCarryoverBillIncomeCostDetail", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
    }
}
