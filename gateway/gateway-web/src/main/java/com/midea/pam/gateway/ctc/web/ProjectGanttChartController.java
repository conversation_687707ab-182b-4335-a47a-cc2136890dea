package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.ProjectGanttChartDTO;
import com.midea.pam.common.ctc.dto.ProjectHelpAndMainDto;
import com.midea.pam.common.ctc.dto.ProjectHelpAndMainMilepostsDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * @program: common-module
 * @description: 项目下的甘特图信息展示接口
 * @author:zhongpeng
 * @create:2020-11-18 11:41
 **/
@Api("项目甘特图信息展示")
@RestController
@RequestMapping(value = {"projectGanttChartInfo"})
public class ProjectGanttChartController extends ControllerHelper {
    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "项目里程碑交付甘特图信息")
    @GetMapping("getMilepostAndIncomeCost")
    public Response getMilepostAndIncomeCost(@RequestParam(required = true) final Long id) {
        String url = String.format("%sprojectGanttChartInfo/getMilepostAndIncomeCost?id=%s", ModelsEnum.CTC.getBaseUrl(), id);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<ProjectGanttChartDTO> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<ProjectGanttChartDTO>>() {
        });
        return dataResponse;
    }

    @ApiOperation(value = "项目收入成本展示视图信息")
    @GetMapping("getProjectHelpAndMainMileposts")
    public Response getProjectHelpAndMainMileposts(@RequestParam(required = true) final Long id) {
        String url = String.format("%sprojectGanttChartInfo/getProjectHelpAndMainMileposts?id=%s", ModelsEnum.CTC.getBaseUrl(), id);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<ProjectHelpAndMainDto> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<ProjectHelpAndMainDto>>() {
        });
        return dataResponse;
    }
}
