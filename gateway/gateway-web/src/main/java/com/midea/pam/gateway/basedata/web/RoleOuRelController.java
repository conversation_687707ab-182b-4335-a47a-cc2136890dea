package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.*;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: pam
 * @description: 用户虚拟部门 网关控制层
 * @author: fangyl
 * @create: 2019-4-11
 **/
@RestController
@RequestMapping("roleUnit")
@Api("角色虚拟部门")
public class RoleOuRelController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "分页查询角色与虚拟部门关系")
    @GetMapping("userUnitPage")
    public Response userUnitPage(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                 @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                                 @RequestParam(required = false) Long roleId) {
        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        param.put("roleId", roleId);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "roleUnit/roleUnitPage", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<RoleUnitRelDto>>>(){});
    }

    @ApiOperation(value = "分页查询角色与业务实体关系")
    @GetMapping("roleOuPage")
    public Response roleOuPage(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                               @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                               @RequestParam(required = false) Long roleId,
                               @RequestParam(required = false) Long unitId,
                               @RequestParam(required = false) boolean isNew) {
        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        param.put("roleId", roleId);
        param.put("unitId", unitId);
        param.put("isNew", isNew);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "roleUnit/roleOuPage", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<UserOuRelDto>>>(){});
    }
    @ApiOperation(value = "保存用户数据权限（虚拟部门和业务实体）")
    @PostMapping("save")
    public Response save(@RequestBody RoleUnitRelBatchSaveDto roleUnitRelBatchSaveDto) {
        final String url = String.format("%sroleUnit/save", ModelsEnum.BASEDATA.getBaseUrl());
        String res = restTemplate.postForEntity(url, roleUnitRelBatchSaveDto, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>(){});
    }

    @ApiOperation(value = "删除部门更新部门与实体表的deleteflag")
    @GetMapping("updateDeleteFlag")
    public Response updateDeleteFlag(
            @RequestParam(required = true) Long roleId,
            @RequestParam(required = true) Long unitId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("roleId", roleId);
        param.put("unitId", unitId);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "roleUnit/updateDeleteFlag", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>(){});
    }


}
