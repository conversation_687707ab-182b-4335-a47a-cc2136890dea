package com.midea.pam.gateway.mdw.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @PackageClassName: com.midea.pam.gateway.mdw.web.DwdProjectProblemController
 * @Description: 项目工时
 * @Author: JerryH
 * @Date: 2023-02-10, 0010 下午 03:14:42
 */
@Api("项目工时宽表数据")
@RestController
@RequestMapping("mdw/dwdWorkingHour")
public class DwdWorkingHourController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "手动拉取并统计项目工时宽表数据")
    @GetMapping("statisticsWorkingHour")
    public Response statisticsWorkingHour(@RequestParam(required = false) Boolean isPullAll,
                                          @RequestParam(required = false) Date pullDate,
                                          @RequestParam(required = false) Integer pullDay,
                                          @RequestParam(required = false) Long parentUnitId,
                                          @RequestParam(required = false) Long projectId) {
        Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        param.put("parentUnitId", parentUnitId);
        param.put("pullDay", pullDay);
        param.put("pullDate", pullDate);
        param.put("isPullAll", isPullAll);
        final String url = buildGetUrl(ModelsEnum.MDW.getBaseUrl(), "dwd/workingHour/statisticsWorkingHour", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
    }
}
