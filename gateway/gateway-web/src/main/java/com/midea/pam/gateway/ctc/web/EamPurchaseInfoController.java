package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.entity.OrganizationRel;
import com.midea.pam.common.ctc.dto.EamPaymentApplyInfoDto;
import com.midea.pam.common.ctc.dto.EamPurchaseInfoDto;
import com.midea.pam.common.ctc.entity.BusinessApplyRel;
import com.midea.pam.common.ctc.entity.EamPurchaseInfo;
import com.midea.pam.common.ctc.entity.EamPurchasePayInfo;
import com.midea.pam.common.ctc.excelVo.EamPaymentApplyExcelVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("eam-采购合同")
@RestController
@RequestMapping("eamPurchaseInfo")
public class EamPurchaseInfoController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "同步EAM数据")
    @GetMapping("getEamPurchaseInfo")
    public Response getEamPurchaseInfo(@RequestParam(required = true) String startDate,
                                       @RequestParam(required = false) String endDate,
                                       @RequestParam(required = true) String modelKey) {
        final Map<String, Object> param = new HashMap<>();
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        param.put("modelKey", modelKey);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "eamPurchaseInfo/getEamPurchaseInfo", param);
        restTemplate.getForObject(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        response.setData("0");
        return response;
    }

    @ApiOperation(value = "同步开票信息到EAM")
    @GetMapping("geamPamSyncInvoice")
    public Response geamPamSyncInvoice(@RequestParam Long id,
                                       @RequestParam(required = false) String modelKey) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("modelKey", modelKey);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "eamPurchaseInfo/geamPamSyncInvoice", param);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "PAM开票校验接口")
    @GetMapping("checkInvoiceServer")
    public Response checkInvoiceServer(@RequestParam String geamContractCode,
                                       @RequestParam String payStage,
                                       @RequestParam String modelKey,
                                       @RequestParam BigDecimal totalTaxIncludedPrice) {
        final Map<String, Object> param = new HashMap<>();
        param.put("geamContractCode", geamContractCode);
        param.put("modelKey", modelKey);
        param.put("payStage", payStage);
        param.put("totalTaxIncludedPrice", totalTaxIncludedPrice);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "eamPurchaseInfo/checkInvoiceServer", param);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<EsbResponse> response = JSON.parseObject(res, new TypeReference<DataResponse<EsbResponse>>() {
        });
        return response;
    }

    @ApiOperation(value = "PAM开票状态同步接口")
    @GetMapping("callSyncInvoiceStatur")
    public Response callSyncInvoiceStatur(@RequestParam Long id,
                                          @RequestParam String modelKey) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("modelKey", modelKey);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "eamPurchaseInfo/callSyncInvoiceStatur", param);
        restTemplate.getForObject(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        response.setData("0");
        return response;
    }

    @ApiOperation(value = "EAM付款申请状态同步接口")
    @GetMapping("callGEAMPmsPayStatus")
    public Response callGEAMPmsPayStatus(@RequestParam String startDate,
                                         @RequestParam String endDate,
                                         @RequestParam(required = false) String pamInvoiceCode,
                                         @RequestParam(required = false) String modelKey) {
        final Map<String, Object> param = new HashMap<>();
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        param.put("pamInvoiceCode", pamInvoiceCode);
        param.put("modelKey", modelKey);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "eamPurchaseInfo/callGEAMPmsPayStatus", param);
        restTemplate.getForObject(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        response.setData("0");
        return response;
    }

    @ApiOperation(value = "手工同步EAM数据")
    @GetMapping("handleEamPurchaseInfo")
    public Response handleEamPurchaseInfo() {
        final Map<String, Object> param = new HashMap<>();
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "eamPurchaseInfo/handleEamPurchaseInfo", param);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<String> response = JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
        return response;
    }

    @ApiOperation(value = "采购申请列表查询")
    @GetMapping("query")
    public Response query(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                          @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                          @RequestParam(required = false) String projectName,
                          @RequestParam(required = false) String managerName,
                          @RequestParam(required = false) String contractCode,
                          @RequestParam(required = false) String contractName,
                          @RequestParam(required = false) String crmCode,
                          @RequestParam(required = false) String ouName,
                          @RequestParam(required = false) String datacontractNumber,
                          @RequestParam(required = false) String orMain,
                          @RequestParam(required = false) String operatingUnitName,
                          @RequestParam(required = false) String eamPurchasePayIds,
                          @RequestParam(required = false) String modelKey) {

        Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("projectName", projectName);
        param.put("managerName", managerName);
        param.put("contractCode", contractCode);
        param.put("contractName", contractName);
        param.put("crmCode", crmCode);
        param.put("ouName", ouName);
        param.put("datacontractNumber", datacontractNumber);
        param.put("orMain", orMain);
        param.put("operatingUnitName", operatingUnitName);
        param.put("eamPurchasePayIds", eamPurchasePayIds);
        param.put("modelkey", modelKey);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "eamPurchaseInfo/query", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<EamPurchaseInfoDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<EamPurchaseInfoDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "获取pam客户信息和ou信息")
    @GetMapping("getPamCustomer")
    public Response getPamCustomerAndOu(@RequestParam String rdmProjectCode, @RequestParam String type, @RequestParam String supplierCode) {
        Map<String, Object> param = new HashMap<>();
        param.put("rdmProjectCode", rdmProjectCode);
        param.put("type", type);
        param.put("supplierCode", supplierCode);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "eamPurchaseInfo/getPamOuAndCus", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<OrganizationRel>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<OrganizationRel>>>() {
        });
        return response;
    }

    @ApiOperation(value = "通过采购申请获取付款期数")
    @GetMapping("payInfo")
    public Response payInfo(@RequestParam Long eamPurchaseId) {

        Map<String, Object> param = new HashMap<>();
        param.put("eamPurchaseId", eamPurchaseId);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "eamPurchaseInfo/payInfo", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<EamPurchasePayInfo>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<EamPurchasePayInfo>>>() {
        });
        return response;
    }

    @ApiOperation(value = "通过PAM合同查找EAM采购合同")
    @GetMapping("findPurchasebyContract")
    public Response findPurchasebyContract(@RequestParam Long contractId) {

        Map<String, Object> param = new HashMap<>();
        param.put("contractId", contractId);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "eamPurchaseInfo/findPurchasebyContract", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<EamPurchaseInfo> response = JSON.parseObject(res, new TypeReference<DataResponse<EamPurchaseInfo>>() {
        });
        return response;
    }

    @ApiOperation(value = "获取关联交易供需关系信息")
    @GetMapping("getBusinessApplyRel")
    public Response getBusinessApplyRel(@RequestParam String ouId, @RequestParam String ouName, @RequestParam String crmCode) {
        Map<String, Object> param = new HashMap<>();
        param.put("ouId", ouId);
        param.put("ouName", ouName);
        param.put("crmCode", crmCode);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "eamPurchaseInfo/getBusinessApplyRel", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<BusinessApplyRel>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<BusinessApplyRel>>>() {
        });
        return response;
    }

    @ApiOperation(value = "关联交易单据状态信息查询", response = EamPaymentApplyExcelVo.class)
    @GetMapping({"pageEamPaymentApplyInfo"})
    public Response pageEamPaymentApplyInfo(
            @RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize,
            @RequestParam(required = false) @ApiParam(value = "关联交易号") String seqId,
            @RequestParam(required = false) @ApiParam(value = "开票申请编号") String pamInvoiceCode,
            @RequestParam(required = false) @ApiParam(value = "开票申请状态") String invoiceApplyStatus,
            @RequestParam(required = false) @ApiParam(value = "付款申请单") String applyCode,
            @RequestParam(required = false) @ApiParam(value = "付款申请状态") String paymentApplyStatus,
            @RequestParam(required = false) @ApiParam(value = "付款申请人") String paymentApplyName,
            @RequestParam(required = false) @ApiParam(value = "EMS同步状态") String emsSynchStatus,
            @RequestParam(required = false) @ApiParam(value = "EMS同步时间") String emsSyncDate,
            @RequestParam(required = false) @ApiParam(value = "EMS付款单号") String emsPayCode,
            @RequestParam(required = false) @ApiParam(value = "资金支付状态") String emsMoneyStatus,
            @RequestParam(required = false) @ApiParam(value = "AP发票号") String apInvoiceCode,
            @RequestParam(required = false) @ApiParam(value = "错误消息") String errorReason,
            @RequestParam(required = false) @ApiParam(value = "EAM项目经理") String eamProjectManager,
            @RequestParam(required = false) @ApiParam(value = "PAM销售合同名称") String pamContractName,
            @RequestParam(required = false) @ApiParam(value = "PAM项目经理") String pamProjectManager,
            @RequestParam(required = false) @ApiParam(value = "开票时间") String invoiceDate) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("seqId", seqId);
        param.put("pamInvoiceCode", pamInvoiceCode);
        param.put("invoiceApplyStatus", invoiceApplyStatus);
        param.put("applyCode", applyCode);
        param.put("paymentApplyStatus", paymentApplyStatus);
        param.put("paymentApplyName", paymentApplyName);
        param.put("emsSynchStatus", emsSynchStatus);
        param.put("emsSyncDate", emsSyncDate);
        param.put("emsPayCode", emsPayCode);
        param.put("emsMoneyStatus", emsMoneyStatus);
        param.put("apInvoiceCode", apInvoiceCode);
        param.put("errorReason", errorReason);
        param.put("eamProjectManager", eamProjectManager);
        param.put("pamContractName", pamContractName);
        param.put("pamProjectManager", pamProjectManager);
        param.put("invoiceDate", invoiceDate);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "eamPurchaseInfo/pageEamPaymentApplyInfo", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<EamPaymentApplyInfoDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<EamPaymentApplyInfoDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "关联交易单据状态信息导出", response = EamPaymentApplyExcelVo.class)
    @GetMapping("exportEamPaymentApplyInfo")
    public void exportEamPaymentApplyInfo(HttpServletResponse response,
                                          @RequestParam(required = false) @ApiParam(value = "关联交易号") String seqId,
                                          @RequestParam(required = false) @ApiParam(value = "开票申请编号") String pamInvoiceCode,
                                          @RequestParam(required = false) @ApiParam(value = "开票申请状态") String invoiceApplyStatus,
                                          @RequestParam(required = false) @ApiParam(value = "付款申请单") String applyCode,
                                          @RequestParam(required = false) @ApiParam(value = "付款申请状态") String paymentApplyStatus,
                                          @RequestParam(required = false) @ApiParam(value = "付款申请人") String paymentApplyName,
                                          @RequestParam(required = false) @ApiParam(value = "EMS同步状态") String emsSynchStatus,
                                          @RequestParam(required = false) @ApiParam(value = "EMS同步时间") String emsSyncDate,
                                          @RequestParam(required = false) @ApiParam(value = "EMS付款单号") String emsPayCode,
                                          @RequestParam(required = false) @ApiParam(value = "资金支付状态") String emsMoneyStatus,
                                          @RequestParam(required = false) @ApiParam(value = "AP发票号") String apInvoiceCode,
                                          @RequestParam(required = false) @ApiParam(value = "错误消息") String errorReason,
                                          @RequestParam(required = false) @ApiParam(value = "EAM项目经理") String eamProjectManager,
                                          @RequestParam(required = false) @ApiParam(value = "PAM销售合同名称") String pamContractName,
                                          @RequestParam(required = false) @ApiParam(value = "PAM项目经理") String pamProjectManager,
                                          @RequestParam(required = false) @ApiParam(value = "开票时间") String invoiceDate) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("seqId", seqId);
        param.put("pamInvoiceCode", pamInvoiceCode);
        param.put("invoiceApplyStatus", invoiceApplyStatus);
        param.put("applyCode", applyCode);
        param.put("paymentApplyStatus", paymentApplyStatus);
        param.put("paymentApplyName", paymentApplyName);
        param.put("emsSynchStatus", emsSynchStatus);
        param.put("emsSyncDate", emsSyncDate);
        param.put("emsPayCode", emsPayCode);
        param.put("emsMoneyStatus", emsMoneyStatus);
        param.put("apInvoiceCode", apInvoiceCode);
        param.put("errorReason", errorReason);
        param.put("eamProjectManager", eamProjectManager);
        param.put("pamContractName", pamContractName);
        param.put("pamProjectManager", pamProjectManager);
        param.put("invoiceDate", invoiceDate);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "eamPurchaseInfo/exportEamPaymentApplyInfo", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        final DataResponse<Map<String, Object>> dataResponse =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
                });
        Map<String, Object> resultMap = dataResponse.getData();
        JSONArray accountingArr = (JSONArray) resultMap.get("eamPaymentApplyInfoList");

        List<EamPaymentApplyExcelVo> eamPaymentApplyInfoList = new ArrayList<>();

        if (accountingArr != null) {
            eamPaymentApplyInfoList = JSONObject.parseArray(accountingArr.toJSONString(), EamPaymentApplyExcelVo.class);
        }

        Workbook workbook = ExportExcelUtil.buildDefaultSheet(eamPaymentApplyInfoList, EamPaymentApplyExcelVo.class, null, "sheet1", true);
        ExportExcelUtil.downLoadExcel("关联交易单据状态_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls", response, workbook);
    }


    @ApiOperation(value = "通过合同id查找注销的EAM采购合同")
    @GetMapping("findEamPurchaseCancel")
    public Response findEamPurchaseCancel(@RequestParam Long contractId) {
        Map<String, Object> param = new HashMap<>();
        param.put("contractId", contractId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "eamPurchaseInfo/findEamPurchaseCancel", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<EamPurchaseInfo>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<EamPurchaseInfo>>>() {
        });
        return response;
    }

}
