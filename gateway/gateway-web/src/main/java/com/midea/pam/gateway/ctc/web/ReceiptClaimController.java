package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.ReceiptClaimContractRelDto;
import com.midea.pam.common.ctc.dto.ReceiptClaimDetailDTO;
import com.midea.pam.common.ctc.dto.ReceiptClaimDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("回款认领")
@RestController
@RequestMapping("receiptClaim")
public class ReceiptClaimController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "销售/非销售回款分页查询")
    @GetMapping("queryReceiptClaimDetailPage")
    public Response queryReceiptClaimDetailPage(
            @RequestParam(required = false) final Integer claimStatus,
            @RequestParam(required = false) final Integer businessType,
            @RequestParam(required = false) final Long ouId,
            @RequestParam(required = false) final String ouName,
            @RequestParam(required = false) final String cashReceiptCode,
            @RequestParam(required = false) final String receiptCode,
            @RequestParam(required = false) final String payDate,
            @RequestParam(required = false) final String accountingDate,
            @RequestParam(required = false) final Integer erpStatus,
            @RequestParam(required = false) final Integer contractStatus,
            @RequestParam(required = false) final String contractStatusList,
            @RequestParam(required = false) final String customerName,
            @RequestParam(required = false) final String customerCode,
            @RequestParam(required = false) final BigDecimal claimAmount,
            @RequestParam(required = false) final String currencyCode,
            @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        param.put("claimStatus", claimStatus);
        param.put("businessType", businessType);
        param.put("ouId", ouId);
        param.put("ouName", ouName);
        param.put("cashReceiptCode", cashReceiptCode);
        param.put("receiptCode", receiptCode);
        param.put("payDate", payDate);
        param.put("accountingDate", accountingDate);
        param.put("erpStatus", erpStatus);
        param.put("contractStatus", contractStatus);
        param.put("contractStatusList", contractStatusList);
        param.put("customerName", customerName);
        param.put("customerCode", customerCode);
        param.put("claimAmount", claimAmount);
        param.put("currencyCode", currencyCode);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/receiptClaim/queryReceiptClaimDetailPage", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<ReceiptClaimDto>>>() {
        });
    }

    @ApiOperation(value = "收款认领详细")
    @GetMapping("getDetailById")
    public Response page(
            @RequestParam(required = true) Long receiptClaimId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("receiptClaimId", receiptClaimId);

        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/receiptClaim/getDetailById", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ReceiptClaimDto>>() {
        });
    }

    @ApiOperation(value = "非销售回款详细")
    @GetMapping("findDetailById")
    public Response findDetailById(@RequestParam(required = true) Long receiptClaimDetailId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("receiptClaimDetailId", receiptClaimDetailId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/receiptClaim/findDetailById", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ReceiptClaimDto>>() {
        });
    }

    @ApiOperation(value = "拆款信息保存")
    @PostMapping("saveDivideDetail")
    public Response saveDivideDetail(@RequestBody ReceiptClaimDto receiptClaimDto) {
        final String url = String.format("%sreceiptClaim/saveDivideDetail", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, receiptClaimDto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "认款保存")
    @PostMapping("saveClaimBatch")
    public Response saveClaimBatch(@RequestBody List<ReceiptClaimDto> dtoList) {
        final String url = String.format("%sreceiptClaim/saveClaimBatch", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dtoList, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "退款重付认款")
    @PostMapping("saveRefundClaimBatch")
    public Response saveRefundClaimBatch(@RequestBody List<ReceiptClaimDto> dtoList) {
        final String url = String.format("%sreceiptClaim/saveRefundClaimBatch", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dtoList, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "非销售业务场景配置")
    @GetMapping("getNonSaleSceneDetail")
    public Response getNonSaleSceneDetail(
            @RequestParam(required = true) Long busiSceneNonSaleId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("busiSceneNonSaleId", busiSceneNonSaleId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/receiptClaim/getNonSaleSceneDetail", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ReceiptClaimDto>>() {
        });
    }

    @ApiOperation(value = "推送ERP")
    @PostMapping("pushToERP")
    public Response pushToERP(@RequestBody(required = false) final List<Long> idList) {
        final String url = String.format("%sreceiptClaim/pushToERP", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, idList, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "ERP冲销")
    @GetMapping("reverseFromERP")
    public Response reverseFromERP(@RequestParam(required = true) final Long id,
                                   @RequestParam(required = true) final String reason,
                                   @RequestParam final String erpReversalDate) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("reason", reason);
        param.put("erpReversalDate", erpReversalDate);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/receiptClaim/reverseFromERP", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "收款撤回至资金")
    @GetMapping("drawbackToGceb")
    public Response drawbackToGceb(@RequestParam(required = true) final Long receiptClaimId,
                                   @RequestParam(required = true) final String reason,
                                   @RequestParam(required = true) final String erpReversalDate) {
        final Map<String, Object> param = new HashMap<>();
        param.put("receiptClaimId", receiptClaimId);
        param.put("reason", reason);
        param.put("erpReversalDate", erpReversalDate);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/receiptClaim/drawbackToGceb", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "收款撤回至资金")
    @GetMapping("drawbackToGcebTest")
    public Response drawbackToGcebTest(@RequestParam(required = false) final Long receiptClaimId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("receiptClaimId", receiptClaimId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/receiptClaim/drawbackToGcebTest", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "退款重付认领撤回")
    @GetMapping("refundUndo")
    public Response refundUndo(@RequestParam(required = true) final Long receiptClaimId,
                               @RequestParam(required = true) final String reason,
                               @RequestParam(required = true) final String erpReversalDate) {
        final Map<String, Object> param = new HashMap<>();
        param.put("receiptClaimId", receiptClaimId);
        param.put("reason", reason);
        param.put("erpReversalDate", erpReversalDate);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/receiptClaim/refundUndo", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "退款申请获取原资金回款信息")
    @GetMapping("refundGetCashReceiptCode")
    public Response refundGetCashReceiptCode(@RequestParam(required = false) final String receiptCode,
                                             @RequestParam(required = false) final String cashReceiptCode,
                                             @RequestParam(required = false) final String payDate,
                                             @RequestParam(required = false) final String customerName,
                                             @RequestParam(required = false) final String customerCode,
                                             @RequestParam(required = false) final String contractCode,
                                             @RequestParam(required = false) final String contractName,
                                             @RequestParam(required = false) final String projectCode,
                                             @RequestParam(required = false) final String projectName,
                                             @RequestParam(required = true, defaultValue = "1") final Integer pageNum,
                                             @RequestParam(required = true, defaultValue = "10") final Integer pageSize
    ) {
        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        param.put("receiptCode", receiptCode);
        param.put("cashReceiptCode", cashReceiptCode);
        param.put("payDate", payDate);
        param.put("customerName", customerName);
        param.put("customerCode", customerCode);
        param.put("contractCode", contractCode);
        param.put("contractName", contractName);
        param.put("projectCode", projectCode);
        param.put("projectName", projectName);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/receiptClaim/refundGetCashReceiptCode", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<ReceiptClaimDto>>>() {
        });
    }

    @ApiOperation(value = "退款申请获取原合同回款信息")
    @GetMapping("refundGetContractInfo")
    public Response refundGetContractInfo(@RequestParam(required = false) final String contractCode,
                                          @RequestParam(required = false) final String contractName,
                                          @RequestParam(required = false) final String projectCode,
                                          @RequestParam(required = false) final String projectName,
                                          @RequestParam(required = false) final Long customerId,
                                          @RequestParam(required = false) final String customerName,
                                          @RequestParam(required = false) final String invoicePlanDetailCode,
                                          @RequestParam(required = false) final String receiptCode,
                                          @RequestParam(required = true, defaultValue = "1") final Integer pageNum,
                                          @RequestParam(required = true, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        param.put("contractCode", contractCode);
        param.put("contractName", contractName);
        param.put("projectCode", projectCode);
        param.put("projectName", projectName);
        param.put("customerId", customerId);
        param.put("customerName", customerName);
        param.put("invoicePlanDetailCode", invoicePlanDetailCode);
        param.put("receiptCode", receiptCode);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/receiptClaim/refundGetContractInfo", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<ReceiptClaimContractRelDto>>>() {
        });
    }

    @ApiOperation(value = "检验收款单据是否存在")
    @GetMapping("checkExist")
    public Response checkExist(@RequestParam String receiptCode, @RequestParam Long ouId, @RequestParam Long customerId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("receiptCode", receiptCode);
        param.put("ouId", ouId);
        param.put("customerId", customerId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/receiptClaim/checkExist", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ReceiptClaimDetailDTO>>() {
        });
    }
}
