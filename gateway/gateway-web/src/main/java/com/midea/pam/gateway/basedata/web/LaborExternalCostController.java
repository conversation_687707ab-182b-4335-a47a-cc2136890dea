package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.LaborCostTypeDto;
import com.midea.pam.common.basedata.dto.LaborExternalCostDto;
import com.midea.pam.common.basedata.dto.VendorSiteBankDto;
import com.midea.pam.common.basedata.entity.LaborExternalCost;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Api("人力点工分类")
@RequestMapping({"laborExternalCost"})
public class LaborExternalCostController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation("批量编辑人力点工分类角色名称")
    @PostMapping({"saveBatchExternalLaborCostRole"})
    public Response saveBatchLaborCostType(@RequestBody List<LaborCostTypeDto> dtos) {
        String url = String.format("%slaborExternalCost/saveBatchExternalLaborCostRole", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dtos, String.class);
        DataResponse<Boolean> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation("新增人力点工分类")
    @PostMapping({"addExternalLaborCost"})
    public com.midea.pam.common.base.Response addExternalLaborCost(@RequestBody List<LaborExternalCost> laborExternalCostList) {
        String url = String.format("%slaborExternalCost/addExternalLaborCost", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, laborExternalCostList, String.class);
        com.midea.pam.common.base.DataResponse<Boolean> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<com.midea.pam.common.base.DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "人力点工分类角色名称列表查询")
    @GetMapping("LaborExternalCostRoleList")
    public Response laborCostTypeList(@RequestParam(required = false) @ApiParam(value = "顶层使用单位ID") Long unitId) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("unitId", unitId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborExternalCost/LaborExternalCostRoleList", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<LaborCostTypeDto>> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<List<LaborCostTypeDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "人力点工分类分页查询")
    @GetMapping("selectLaborExternalCostPage")
    public Response selectLaborExternalCostPage(
            @RequestParam(required = false) @ApiParam("角色名称") String name,
            @RequestParam(required = false) @ApiParam("供应商") String vendorName,
            @RequestParam(required = false) @ApiParam("使用单位") Long bizUnitId,
            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("name", name);
        param.put("vendorName", vendorName);
        param.put("bizUnitId", bizUnitId);

        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "laborExternalCost/selectLaborExternalCostPage", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<LaborExternalCostDto>>>() {
        });
    }

    @ApiOperation("编辑人力点工分类")
    @PostMapping({"updateLaborExternalCost"})
    public Response updateLaborExternalCost(@RequestBody LaborExternalCostDto dto) {
        String url = String.format("%slaborExternalCost/updateLaborExternalCost", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<LaborExternalCostDto> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<LaborExternalCostDto>>() {
        });
        return response;
    }

    @ApiOperation("删除人力点工分类")
    @GetMapping({"deleteLaborExternalCost"})
    public Response deleteLaborExternalCost(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "laborExternalCost/deleteLaborExternalCost", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(cleanStr(res), new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "查询当前使用单位下的供应商信息")
    @GetMapping("selectByOuId")
    public Response selectByOuId(@RequestParam Long bizUnitId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("bizUnitId", bizUnitId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "laborExternalCost/selectByOuId", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<VendorSiteBankDto>> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<List<VendorSiteBankDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "人力点工分类列表查询")
    @GetMapping("selectList")
    public Response selectList(@RequestParam(required = false) @ApiParam("类型,1=角色;") Integer type,
                               @RequestParam(required = false) @ApiParam("所属分类id") Long costTypeId,
                               @RequestParam(required = false) @ApiParam("使用单位") Long bizUnitId) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("type", type);
        param.put("costTypeId", costTypeId);
        param.put("bizUnitId", bizUnitId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborExternalCost/selectList", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<LaborExternalCostDto>> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<List<LaborExternalCostDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "根据角色id和供应商查询外部人力信息")
    @GetMapping("getLaborExternalCost")
    public Response getLaborExternalCost(@RequestParam Long costTypeId, @RequestParam String vendorName) {
        Map<String, Object> param = new HashMap<>();
        param.put("costTypeId", costTypeId);
        param.put("vendorName", vendorName);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "laborExternalCost/getLaborExternalCost", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<LaborExternalCost>>() {
        });
    }
}
