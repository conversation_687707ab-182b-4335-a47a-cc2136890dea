package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.StandardTermsDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.service.MipWorkflowInnerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Api("标准条款")
@RestController
@RequestMapping("standardTerms")
public class StandardTermsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private MipWorkflowInnerService mipWorkflowInnerService;

    @ApiOperation(value = "标准条款保存")
    @PostMapping("save")
    public Response save(@RequestBody StandardTermsDto dto) {
        String url = String.format("%sstandardTerms/save", ModelsEnum.CTC.getBaseUrl());
        String res = restTemplate.postForEntity(url, dto, String.class).getBody();
        DataResponse<Long> response = JSON.parseObject(res, new TypeReference<DataResponse<Long>>() {
        });
        return response;
    }

    @ApiOperation(value = "根据ID查询标准条款信息")
    @GetMapping("findById")
    public Response findInfoRuleById(@RequestParam @ApiParam("标准条款ID") Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "standardTerms/findById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<StandardTermsDto> response = JSON.parseObject(res,
                new TypeReference<DataResponse<StandardTermsDto>>() {
                });

        return response;
    }

    @ApiOperation(value = "查询标准条款")
    @PostMapping("selectPage")
    public Response createByMePage(@RequestBody StandardTermsDto dto) {
        final String url = String.format("%sstatistics/standardTerms/v1/list", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<StandardTermsDto>>>() {
        });
    }

    @ApiOperation(value = "废弃")
    @PostMapping("discard")
    public Object discard(@RequestBody StandardTermsDto dto) {
        String url = String.format("%sstandardTerms/discard", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> stringResponseEntity = restTemplate.postForEntity(url, dto, String.class);
        if (stringResponseEntity.getStatusCodeValue() == 200) {
            //废弃工作流
            mipWorkflowInnerService.draftAbandon("standardTermsApp", dto.getId());
        }

        return stringResponseEntity.getBody();
    }


}
