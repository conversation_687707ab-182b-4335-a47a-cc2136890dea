package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.BudgetItemDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/7/21
 * @description
 */

@RestController
@RequestMapping("statistics/budgetItem")
@Api("预算项目号")
public class BudgetItemStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "分页查询预算项目号")
    @GetMapping("page")
    public Response page(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") String pageNum,
                         @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") String pageSize,
                         @RequestParam(required = false) @ApiParam(value = "id") String id,
                         @RequestParam(required = false) @ApiParam(value = "预算项目编号") String budgetItemCode,
                         @RequestParam(required = false) @ApiParam(value = "预算项目名称") String budgetItemName,
                         @RequestParam(required = false) @ApiParam(value = "预算项目类型") String budgetItemType,
                         @RequestParam(required = false) @ApiParam(value = "所属类别") String category,
                         @RequestParam(required = false) @ApiParam(value = "拆款类型(多选)") String businesstypeStr,
                         @RequestParam(required = false) @ApiParam(value = "状态(多选)") String statusStr,
                         @RequestParam(required = false) @ApiParam(value = "业务实体id（多选）") String ouIdStr,
                         @RequestParam(required = false) @ApiParam(value = "创建人") String createByName,
                         @RequestParam(required = false) @ApiParam(value = "创建日期") String createAt,
                         @RequestParam(required = false) @ApiParam(value = "更新人") String updateByName,
                         @RequestParam(required = false) @ApiParam(value = "更新日期")String updateAt,
                         @RequestParam(required = false) @ApiParam(value = "预算项目号或者名称模糊查询") String codeOrName) {
        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        param.put("id", id);
        param.put("budgetItemCode", budgetItemCode);
        param.put("budgetItemName", budgetItemName);
        param.put("budgetItemType", budgetItemType);
        param.put("category", category);
        param.put("statusStr", statusStr);
        param.put("businesstypeStr",businesstypeStr);
        param.put("ouIdStr",ouIdStr);
        param.put("createByName",createByName);
        param.put("createAt",createAt);
        param.put("updateByName",updateByName);
        param.put("updateAt",updateAt);
        param.put("codeOrName",codeOrName);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/budgetItem/page", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<BudgetItemDto>>>(){});
    }


    @ApiOperation(value = "查询所有的预算项目号")
    @GetMapping("findAll")
    public Response findAll(
                         @RequestParam(required = false) @ApiParam(value = "id") String id,
                         @RequestParam(required = false) @ApiParam(value = "预算项目编号") String budgetItemCode,
                         @RequestParam(required = false) @ApiParam(value = "预算项目名称") String budgetItemName,
                         @RequestParam(required = false) @ApiParam(value = "预算项目类型") String budgetItemType,
                         @RequestParam(required = false) @ApiParam(value = "所属类别") String category,
                         @RequestParam(required = false) @ApiParam(value = "拆款类型(多选)") String businesstypeStr,
                         @RequestParam(required = false) @ApiParam(value = "状态(多选)") String statusStr,
                         @RequestParam(required = false) @ApiParam(value = "业务实体id（多选）") String ouIdStr,
                         @RequestParam(required = false) @ApiParam(value = "创建人") String createByName,
                         @RequestParam(required = false) @ApiParam(value = "创建日期") String createAt,
                         @RequestParam(required = false) @ApiParam(value = "更新人") String updateByName,
                         @RequestParam(required = false) @ApiParam(value = "更新日期")String updateAt,
                         @RequestParam(required = false) @ApiParam(value = "预算项目号或者名称模糊查询") String codeOrName) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("budgetItemCode", budgetItemCode);
        param.put("budgetItemName", budgetItemName);
        param.put("budgetItemType", budgetItemType);
        param.put("category", category);
        param.put("statusStr", statusStr);
        param.put("businesstypeStr",businesstypeStr);
        param.put("ouIdStr",ouIdStr);
        param.put("createByName",createByName);
        param.put("createAt",createAt);
        param.put("updateByName",updateByName);
        param.put("updateAt",updateAt);
        param.put("codeOrName",codeOrName);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/budgetItem/findAll", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<BudgetItemDto>>>(){});
    }


    @ApiOperation(value = "复制预算项目号")
    @GetMapping("copyBudgetItem")
    public Response copyBudgetItem(@RequestParam(required = true)  Long oldOuId,
                                   @RequestParam(required = true)  Long newOuId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("oldOuId", oldOuId);
        param.put("newOuId", newOuId);

        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/budgetItem/copyBudgetItem", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Long>>(){});
    }
}
