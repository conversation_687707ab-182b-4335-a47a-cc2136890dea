package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.statistics.dto.IncomeCalculateDTO;
import com.midea.pam.common.statistics.entity.IncomeCalculateProductTask;
import com.midea.pam.common.statistics.entity.IncomeCalculateProject;
import com.midea.pam.common.statistics.entity.IncomeCalculateProjectTask;
import com.midea.pam.common.statistics.excelVo.IncomeCalculateAutoCalculateExcelVO;
import com.midea.pam.common.statistics.vo.IncomeCalculateAutoCalculateVO;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.support.utils.BeanConverter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/9/17
 * @description 收入预测
 */
@Api("收入预测")
@RestController
@RequestMapping("statistics/incomeCalculate")
public class IncomeCalculateController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "保存", response = IncomeCalculateDTO.class)
    @PostMapping("save")
    public Response save(@RequestBody IncomeCalculateDTO incomeCalculateDTO) {
        final String url = String.format("%sstatistics/incomeCalculate/save", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, incomeCalculateDTO, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<IncomeCalculateDTO>>() {
        });
    }

    @ApiOperation(value = "分页查询")
    @GetMapping("page")
    public Response page(@RequestParam(required = false) final Long id,
                         @RequestParam(required = false) final String month,
                         @RequestParam(required = false) final String name,
                         @RequestParam(required = false) final String statusesStr,
                         @RequestParam(required = false) final String deadlineDate,
                         @RequestParam(required = false) final String startMonth,
                         @RequestParam(required = false) final String endMonth,
                         @RequestParam(required = false) final String createByName,
                         @RequestParam(required = false) final String projectNameOrCodeFuzzyLike,
                         @RequestParam(required = false) final String deadlineDateStart,
                         @RequestParam(required = false) final String deadlineDateEnd,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = new HashMap<>();

        params.put("id", id);
        params.put("month", month);
        params.put("name", name);
        params.put("statusesStr", statusesStr);
        params.put("deadlineDate", deadlineDate);
        params.put("startMonth", startMonth);
        params.put("endMonth", endMonth);
        params.put("createByName", createByName);
        params.put("deadlineDateStart", deadlineDateStart);
        params.put("deadlineDateEnd", deadlineDateEnd);
        params.put("projectNameOrCodeFuzzyLike", projectNameOrCodeFuzzyLike);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/incomeCalculate/page", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<IncomeCalculateDTO>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<IncomeCalculateDTO>>>() {
        });
        return response;
    }

    @ApiOperation(value = "根据收入预测ID查询项目经理收入预测任务")
    @GetMapping("pageIncomeCalculateProjectTask")
    public Response pageIncomeCalculateProjectTask(@RequestParam @ApiParam(value = "收入预测ID") final Long calculateId,
                                                   @RequestParam(required = false) @ApiParam(value = "项目编号") final String projectCode,
                                                   @RequestParam(required = false) @ApiParam(value = "项目名称") final String projectName,
                                                   @RequestParam(required = false) @ApiParam(value = "项目类型") final String projectTypeName,
                                                   @RequestParam(required = false) @ApiParam(value = "项目经理") final String projectManagerName,
                                                   @RequestParam(required = false) @ApiParam(value = "状态") final Integer status,
                                                   @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                                   @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = new HashMap<>();

        params.put("calculateId", calculateId);
        params.put("projectCode", projectCode);
        params.put("projectName", projectName);
        params.put("projectTypeName", projectTypeName);
        params.put("projectManagerName", projectManagerName);
        params.put("status", status);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/incomeCalculate/pageIncomeCalculateProjectTask", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<IncomeCalculateProjectTask>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<IncomeCalculateProjectTask>>>() {
        });
        return response;
    }

    @ApiOperation(value = "根据收入预测ID查询产品总监收入预测任务")
    @GetMapping("pageIncomeCalculateProductTask")
    public Response pageIncomeCalculateProductTask(@RequestParam @ApiParam(value = "收入预测ID") final Long calculateId,
                         @RequestParam(required = false) @ApiParam(value = "产品部门") final String productOrgName,
                         @RequestParam(required = false) @ApiParam(value = "产品总监") final String productManagerName,
                         @RequestParam(required = false) @ApiParam(value = "状态") final Integer status,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = new HashMap<>();

        params.put("calculateId", calculateId);
        params.put("productOrgName", productOrgName);
        params.put("productManagerName", productManagerName);
        params.put("status", status);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/incomeCalculate/pageIncomeCalculateProductTask", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<IncomeCalculateProductTask>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<IncomeCalculateProductTask>>>() {
        });
        return response;
    }

    private Map<String, Object> buildParam(IncomeCalculateDTO incomeCalculateDTO) {
        final Map<String, Object> params = new HashMap<>();

        params.put("id", incomeCalculateDTO.getId());
        params.put("month", incomeCalculateDTO.getMonth());
        params.put("name", incomeCalculateDTO.getName());
        params.put("statusesStr", incomeCalculateDTO.getStatusesStr());
        params.put("deadlineDate", incomeCalculateDTO.getDeadlineDate());
        params.put("startMonth", incomeCalculateDTO.getStartMonth());
        params.put("endMonth", incomeCalculateDTO.getEndMonth());
        params.put("createByName", incomeCalculateDTO.getCreateByName());

        if (incomeCalculateDTO.getDeadlineDateStart() != null) {
            params.put("deadlineDateStart", incomeCalculateDTO.getDeadlineDateStart());
        }

        if (incomeCalculateDTO.getDeadlineDateEnd() != null) {
            params.put("deadlineDateEnd", incomeCalculateDTO.getDeadlineDateEnd());
        }

        params.put("projectNameOrCodeFuzzyLike", incomeCalculateDTO.getProjectNameOrCodeFuzzyLike());

        return params;
    }

    @ApiOperation(value = "查询预测周期内的项目清单")
    @GetMapping("listNeedIncomeCalculateProjects")
    public Response listNeedIncomeCalculateProjects(IncomeCalculateDTO incomeCalculateDTO) {
        final Map<String, Object> params = buildParam(incomeCalculateDTO);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/incomeCalculate/listNeedIncomeCalculateProjects", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<IncomeCalculateProject>> response =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<IncomeCalculateProject>>>() {
                });
        return response;
    }

    @ApiOperation(value = "详情查询")
    @GetMapping("detail/{id}")
    public Response getDetail(@PathVariable Long id) {
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/incomeCalculate/detail/" + id, null);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<IncomeCalculateDTO> response =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<IncomeCalculateDTO>>() {
                });
        return response;
    }

    @ApiOperation(value = "详情查询")
    @GetMapping("summaryDetail/{id}")
    public Response getSummaryDetail(@PathVariable Long id) {
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/incomeCalculate/summaryDetail/" + id, null);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<IncomeCalculateDTO> response =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<IncomeCalculateDTO>>() {
                });
        return response;
    }

    @ApiOperation(value = "自动计算类查询")
    @GetMapping("listIncomeCalculateAutoCalculates/{calculateId}")
    public Response listIncomeCalculateAutoCalculates(@PathVariable Long calculateId) {
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/incomeCalculate/listIncomeCalculateAutoCalculates/" + calculateId, null);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<IncomeCalculateAutoCalculateVO>> response =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<IncomeCalculateAutoCalculateVO>>>() {
                });
        return response;
    }

    @ApiOperation(value = "一键生成报表")
    @GetMapping("generateReport/{id}")
    public Response generateReport(@PathVariable Long id) {
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/incomeCalculate/generateReport/" + id, null);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Boolean> response =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
                });
        int code = response.getCode();
        if (code == Code.ERROR.getCode() && Objects.equals(response.getMsg(), "报表正在执行，不能重复提交")) {
            response.setCode(Code.SUCCESS.getCode());
            response.setData(Boolean.FALSE);
        }
        return response;
    }

    @ApiOperation(value = "查询所有产品")
    @GetMapping("listAllProductUnit")
    public Response listAllProductUnit() {
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/incomeCalculate/listAllProductUnit", null);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<IncomeCalculateProductTask>> response =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<IncomeCalculateProductTask>>>() {
                });
        return response;
    }

    @ApiOperation(value = "自动计算类导出")
    @GetMapping("exportIncomeCalculateAutoCalculates/{calculateId}")
    public void exportIncomeCalculateAutoCalculates(@PathVariable Long calculateId,
                                                    HttpServletResponse response) {
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/incomeCalculate/listIncomeCalculateAutoCalculates/" + calculateId, null);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<IncomeCalculateAutoCalculateVO>> dataResponse =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<IncomeCalculateAutoCalculateVO>>>() {
                });
        List<IncomeCalculateAutoCalculateExcelVO> excelVOList = new ArrayList<>();
        List<IncomeCalculateAutoCalculateVO> data = dataResponse.getData();
        if (ListUtils.isNotEmpty(data)) {
            excelVOList = BeanConverter.copy(data, IncomeCalculateAutoCalculateExcelVO.class);
            int i = 1;
            for (IncomeCalculateAutoCalculateExcelVO task : excelVOList) {
                task.setNum(i++);
            }
        }

        Workbook workbook = ExportExcelUtil.buildDefaultSheet(excelVOList, IncomeCalculateAutoCalculateExcelVO.class, null, "自动计算类", true);
        ExportExcelUtil.downLoadExcel("自动计算类_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls", response, workbook);
    }

}
