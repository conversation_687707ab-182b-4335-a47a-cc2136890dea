package com.midea.pam.gateway.ctc.web;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.ProductTaxSettingDto;
import com.midea.pam.common.ctc.vo.ProjectTaxSettingExportVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Description: 产品税收设置网关控制层
 * @Date: 2021-1-18 17:51
 * @Version: 1.0
 */
@Api("项目奖金设置")
@RestController
@RequestMapping({"productTaxSetting"})
public class ProductTaxSettingController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查询产品税收名称列表")
    @GetMapping("list")
    public Response getProjectFundList(@RequestParam(required = false) @ApiParam("产品税收编码") String taxCode,
                                       @RequestParam(required = false) @ApiParam("产品税收名称") String taxName,
                                       @RequestParam(required = false) @ApiParam("业务实体,【多个用,拼接】") String ouId,
                                       @RequestParam(required = false) @ApiParam("状态（false失效，true生效）") Boolean status,
                                       @RequestParam(required = false) @ApiParam("创建人【根据用户名模糊查询】") String createUserName,
                                       @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                       @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        Map<String, Object> param = new HashMap<>(7);
        param.put("taxCode", taxCode);
        param.put("taxName", taxName);
        param.put("ouId", ouId);
        param.put("status", status);
        param.put("createUserName", createUserName);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "productTaxSetting/list", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ProductTaxSettingDto>>>() {
        });
    }

    @ApiOperation(value = "新增/修改产品税收名称")
    @PostMapping("save")
    public Response save(@RequestBody ProductTaxSettingDto dto) {
        final String url = String.format("%sproductTaxSetting/save", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Long>>() {
        });
    }

    @ApiOperation(value = "根据id查询产品税收名称详情")
    @GetMapping("getById")
    public Response getById(@ApiParam("产品税收名称id") @RequestParam Long id) {
        Map<String, Object> param = new HashMap<>(1);
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "productTaxSetting/getById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ProductTaxSettingDto>>() {
        });
    }

    @ApiOperation(value = "导出产品税收名称列表")
    @GetMapping("export")
    public void exportProjectFundList(@RequestParam(required = false) @ApiParam("产品税收编码") String taxCode,
                                      @RequestParam(required = false) @ApiParam("产品税收名称") String taxName,
                                      @RequestParam(required = false) @ApiParam("业务实体") String ouId,
                                      @RequestParam(required = false) @ApiParam("状态（false失效，true生效）") Boolean status,
                                      @RequestParam(required = false) @ApiParam("创建人") Long createBy,
                                      HttpServletResponse response) {
        Map<String, Object> param = new HashMap<>(5);
        param.put("taxCode", taxCode);
        param.put("taxName", taxName);
        param.put("ouId", ouId);
        param.put("status", status);
        param.put("createBy", createBy);
        param.put("pageNum", 1);
        param.put("pageSize", 10000);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "productTaxSetting/list", param);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<ProductTaxSettingDto>> pageInfoDataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ProductTaxSettingDto>>>() {
        });
        List<ProductTaxSettingDto> list = pageInfoDataResponse.getData().getList();

        List<ProjectTaxSettingExportVo> excelVOList = parseExportVo(list);
        ExportExcelUtil.exportExcel(excelVOList, null, "Sheet1", ProjectTaxSettingExportVo.class, "产品税收名称设置导出.xls", response);
    }


    @ApiOperation(value = "数据初始化，执行一次即可")
    @GetMapping("dataInit")
    public Response dataInit() {
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "productTaxSetting/dataInit", null);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Long>>() {
        });
    }

    /**
     * 转化为导出对象
     *
     * @param list
     * @return
     */
    private List<ProjectTaxSettingExportVo> parseExportVo(List<ProductTaxSettingDto> list) {
        List<ProjectTaxSettingExportVo> result = new ArrayList<>();
        for (ProductTaxSettingDto dto : list) {
            ProjectTaxSettingExportVo vo = new ProjectTaxSettingExportVo();
            BeanUtil.copyProperties(dto, vo);
            result.add(vo);
        }
        return result;
    }

}
