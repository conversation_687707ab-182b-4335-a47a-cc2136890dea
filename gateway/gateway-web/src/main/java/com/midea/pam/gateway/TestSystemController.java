package com.midea.pam.gateway;

import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.service.TestSystemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Description 测试接口
 * Created by liuqing85
 * Date 2023/2/14 10:08
 */
@Api("工作流提交临时记录")
@RestController
@RequestMapping("testSystem")
public class TestSystemController {

    @Resource
    private TestSystemService testSystemService;

    @ApiOperation("获取 pam_system.mip_callback_log 表中回调结果超时的数据")
    @GetMapping("getFailToOverTimeMipCallbackLog")
    public Response getFailToOverTimeMipCallbackLog(@RequestParam(required = false) Date startDate, @RequestParam(required = false) Date endDate) {
        List<Map<String, String>> resultList = testSystemService.getFailToOverTimeMipCallbackLog(startDate, endDate);
        DataResponse<List<Map<String, String>>> response = Response.dataResponse();
        return response.setData(resultList);
    }
}
