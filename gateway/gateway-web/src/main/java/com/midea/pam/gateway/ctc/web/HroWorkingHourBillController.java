package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.annotation.CheckSubmit;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.ctc.dto.HroWorkingHourBillDto;
import com.midea.pam.common.ctc.excelVo.HroWorkingHourBillExcelVo;
import com.midea.pam.common.ctc.excelVo.HroWorkingHourItemExcelVo;
import com.midea.pam.common.enums.HroWorkingHourBillStatus;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.gateway.entity.FileInfo;
import com.midea.pam.common.gateway.entity.FileInfoExample;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.mapper.FileInfoMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Api("点工工时对账单")
@RestController
@RequestMapping("hroWorkingHourBill")
public class HroWorkingHourBillController {

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private FileInfoMapper fileInfoMapper;


    @ApiOperation("获取未对账过的工时")
    @GetMapping("getNotBillWorkingHour")
    public Response getNotBillWorkingHour(@RequestParam Long contractId,
                                          @RequestParam Date startDate,
                                          @RequestParam Date endDate,
                                          @RequestParam(required = false) Long billId) {
        String url = ModelsEnum.CTC.getBaseUrl() + "hroWorkingHourBill/getNotBillWorkingHour";
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        url += "?contractId=" + contractId;
        url += "&startDate=" + dateFormat.format(startDate);
        url += "&endDate=" + dateFormat.format(endDate);
        if (billId != null) {
            url += "&billId=" + billId;
        }
        return restTemplate.getForObject(url, DataResponse.class);
    }

    @ApiOperation("保存草稿对账单")
    @PostMapping("saveHroWorkingHourBill")
    public Response saveHroWorkingHourBill(@RequestBody HroWorkingHourBillDto hroWorkingHourBillDto) {
        String url = ModelsEnum.CTC.getBaseUrl() + "hroWorkingHourBill/saveHroWorkingHourBill";
        return restTemplate.postForObject(url, hroWorkingHourBillDto, DataResponse.class);
    }

    @ApiOperation("对账单提交审批")
    @PostMapping("submitApprove")
    public Response submitApprove(@RequestBody HroWorkingHourBillDto hroWorkingHourBillDto) {
        String url = ModelsEnum.CTC.getBaseUrl() + "hroWorkingHourBill/submitApprove";
        return restTemplate.postForObject(url, hroWorkingHourBillDto, DataResponse.class);
    }

    @ApiOperation("分页查询对账单")
    @GetMapping("pageHroWorkingHourBill")
    public Response pageHroWorkingHourBill(HroWorkingHourBillDto hroWorkingHourBillDto) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String url = ModelsEnum.CTC.getBaseUrl() + "hroWorkingHourBill/pageHroWorkingHourBill";
        Map<String, Object> params = new HashMap<>(10);
        params.put("pageNum", hroWorkingHourBillDto.getPageNum());
        params.put("pageSize", hroWorkingHourBillDto.getPageSize());
        params.put("code", hroWorkingHourBillDto.getCode());
        params.put("statusStr", hroWorkingHourBillDto.getStatusStr());
        params.put("erpSyncStatusStr", hroWorkingHourBillDto.getErpSyncStatusStr());
        params.put("creator", hroWorkingHourBillDto.getCreator());
        params.put("handler", hroWorkingHourBillDto.getHandler());
        params.put("projectCode", hroWorkingHourBillDto.getProjectCode());
        params.put("projectName", hroWorkingHourBillDto.getProjectName());
        params.put("contractCode", hroWorkingHourBillDto.getContractCode());
        params.put("contractName", hroWorkingHourBillDto.getContractName());
        if (hroWorkingHourBillDto.getStartDate() != null) {
            params.put("startDate", dateFormat.format(hroWorkingHourBillDto.getStartDate()));
        }
        if (hroWorkingHourBillDto.getEndDate() != null) {
            params.put("endDate", dateFormat.format(hroWorkingHourBillDto.getEndDate()));
        }
        if (hroWorkingHourBillDto.getStartTime() != null) {
            params.put("startTime", dateFormat.format(hroWorkingHourBillDto.getStartTime()));
        }
        if (hroWorkingHourBillDto.getEndTime() != null) {
            params.put("endTime", dateFormat.format(hroWorkingHourBillDto.getEndTime()));
        }
        if (hroWorkingHourBillDto.getGlDateStart() != null) {
            params.put("glDateStart", dateFormat.format(hroWorkingHourBillDto.getGlDateStart()));
        }
        if (hroWorkingHourBillDto.getGlDateEnd() != null) {
            params.put("glDateEnd", dateFormat.format(hroWorkingHourBillDto.getGlDateEnd()));
        }
        url = buildUrl(url, params);
        return restTemplate.getForObject(url, DataResponse.class);
    }

    @ApiOperation(value = "更新总账日期")
    @PostMapping("updateGlDate")
    public Response updateGlDate(@RequestBody HroWorkingHourBillDto hroWorkingHourBillDto) {
        final String url = String.format("%shroWorkingHourBill/updateGlDate", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, hroWorkingHourBillDto, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation("对账单详情")
    @GetMapping("detail")
    public Response detail(@RequestParam Long id) {
        String url = ModelsEnum.CTC.getBaseUrl() + "hroWorkingHourBill/detail?id=" + id;
        String resStr = restTemplate.getForObject(url, String.class);
        DataResponse<HroWorkingHourBillDto> response = JSON.parseObject(resStr, new TypeReference<DataResponse<HroWorkingHourBillDto>>() {
        });
        if (response != null && response.getCode() == 0) {
            HroWorkingHourBillDto billDto = response.getData();
            if (billDto != null) {
                if (StringUtils.isNotEmpty(billDto.getAttachmentIds())) {
                    List<Long> ids = Arrays.stream(billDto.getAttachmentIds().split(","))
                            .map(Long::valueOf).collect(Collectors.toList());
                    billDto.setAttachments(getFileByIds(ids));
                } else {
                    billDto.setAttachments(new ArrayList<>(0));
                }
                if (StringUtils.isNotEmpty(billDto.getBillAttachmentIds())) {
                    List<Long> ids = Arrays.stream(billDto.getBillAttachmentIds().split(","))
                            .map(Long::valueOf).collect(Collectors.toList());
                    billDto.setBillAttachments(getFileByIds(ids));
                } else {
                    billDto.setBillAttachments(new ArrayList<>(0));
                }
            }
        }
        return response;
    }

    @ApiOperation("单据回退")
    @GetMapping("fallback")
    public Response fallback(@RequestParam Long id) {
        String url = ModelsEnum.CTC.getBaseUrl() + "hroWorkingHourBill/fallback?id=" + id;
        return restTemplate.getForObject(url, DataResponse.class);
    }

    @ApiOperation(value = "废弃单据")
    @GetMapping("abandon")
    public Response abandon(@RequestParam Long id) {
        String url = ModelsEnum.CTC.getBaseUrl() + "hroWorkingHourBill/abandon?id=" + id;
        return restTemplate.getForObject(url, DataResponse.class);
    }

    @ApiOperation(value = "审批提交回调")
    @PutMapping("workflow/callback/draftSubmit/skipSecurityInterceptor")
    public Response draftSubmitCallback(@RequestParam(required = false) String formInstanceId) {
        String url = ModelsEnum.CTC.getBaseUrl() +
                "hroWorkingHourBill/workflow/callback/draftSubmit/skipSecurityInterceptor";
        url += "?formInstanceId=" + (formInstanceId != null ? formInstanceId : "");
        return restTemplate.getForObject(url, DataResponse.class);
    }

    @ApiOperation(value = "审批驳回回调")
    @PutMapping("workflow/callback/refuse/skipSecurityInterceptor")
    public Response refuseCallback(@RequestParam(required = false) String formInstanceId) {
        String url = ModelsEnum.CTC.getBaseUrl() +
                "hroWorkingHourBill/workflow/callback/refuse/skipSecurityInterceptor";
        url += "?formInstanceId=" + (formInstanceId != null ? formInstanceId : "");
        return restTemplate.getForObject(url, DataResponse.class);
    }

    @ApiOperation(value = "审批通过回调")
    @PutMapping("workflow/callback/pass/skipSecurityInterceptor")
    public Response passCallback(@RequestParam(required = false) String formInstanceId,
                                 @RequestParam(required = false) Long companyId) {
        String url = ModelsEnum.CTC.getBaseUrl() +
                "hroWorkingHourBill/workflow/callback/pass/skipSecurityInterceptor";
        url += "?formInstanceId=" + (formInstanceId != null ? formInstanceId : "");
        url += "&companyId=" + (companyId != null ? companyId : "");
        return restTemplate.getForObject(url, DataResponse.class);
    }

    @ApiOperation(value = "撤回审批回调")
    @PutMapping("workflow/callback/draftReturn/skipSecurityInterceptor")
    public Response draftReturnCallback(@RequestParam(required = false) String formInstanceId) {
        String url = ModelsEnum.CTC.getBaseUrl() +
                "hroWorkingHourBill/workflow/callback/draftReturn/skipSecurityInterceptor";
        url += "?formInstanceId=" + (formInstanceId != null ? formInstanceId : "");
        return restTemplate.getForObject(url, DataResponse.class);
    }

    @ApiOperation(value = "作废审批回调")
    @PutMapping("workflow/callback/abandon/skipSecurityInterceptor")
    public Response abandonCallback(@RequestParam(required = false) String formInstanceId) {
        String url = ModelsEnum.CTC.getBaseUrl() +
                "hroWorkingHourBill/workflow/callback/abandon/skipSecurityInterceptor";
        url += "?formInstanceId=" + (formInstanceId != null ? formInstanceId : "");
        return restTemplate.getForObject(url, DataResponse.class);
    }

    @ApiOperation(value = "导出对账单工时明细")
    @GetMapping("exportBillWorkingHour")
    public void exportBillWorkingHour(@RequestParam Long billId,
                                      HttpServletResponse servletResponse) {
        String url = ModelsEnum.CTC.getBaseUrl() + "hroWorkingHourBill/getBillWorkingHour";
        String res = restTemplate.postForObject(url, Collections.singleton(billId), String.class);
        DataResponse<List<HroWorkingHourItemExcelVo>> dataResponse = JSON.parseObject(res,
                new TypeReference<DataResponse<List<HroWorkingHourItemExcelVo>>>() {
                });
        List<HroWorkingHourItemExcelVo> excelVos = Collections.emptyList();
        if (dataResponse != null && dataResponse.getCode() == 0) {
            excelVos = dataResponse.getData();
        }
        ExportExcelUtil.exportExcel(excelVos, null, "Sheet1",
                HroWorkingHourItemExcelVo.class, "导出对账单工时明细.xls", servletResponse);
    }

    @ApiOperation(value = "导出对账单")
    @GetMapping("exportWorkingHourBill")
    public void exportWorkingHourBill(HroWorkingHourBillDto hroWorkingHourBillDto,
                                      HttpServletResponse servletResponse) throws IOException {
        hroWorkingHourBillDto.setPageNum(1);
        hroWorkingHourBillDto.setPageSize(1000000);
        Response response = pageHroWorkingHourBill(hroWorkingHourBillDto);
        String res = JSON.toJSONString(response);
        DataResponse<PageInfo<HroWorkingHourBillExcelVo>> billResponse = JSON.parseObject(res,
                new TypeReference<DataResponse<PageInfo<HroWorkingHourBillExcelVo>>>() {
                });
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd ");
        boolean isSuccess = false;
        if (billResponse != null && billResponse.getCode() == 0) {
            List<HroWorkingHourBillExcelVo> billExcelVos = billResponse.getData().getList();
            billExcelVos.forEach(e -> {
                e.setStatusDesc(HroWorkingHourBillStatus.getValue(e.getStatus()));
                if (e.getStartDate() != null && e.getEndDate() != null) {
                    e.setBillDate(dateFormat.format(e.getStartDate()) + " ~ " + dateFormat.format(e.getEndDate()));
                }
            });
            List<Long> billIds = billExcelVos.stream().map(HroWorkingHourBillExcelVo::getId).collect(Collectors.toList());

            Workbook workbook = ExportExcelUtil.buildDefaultSheet(billExcelVos, HroWorkingHourBillExcelVo.class, null, "人力点工对账单据", true);

            String url = ModelsEnum.CTC.getBaseUrl() + "hroWorkingHourBill/getBillWorkingHour";
            res = restTemplate.postForObject(url, billIds, String.class);
            DataResponse<List<HroWorkingHourItemExcelVo>> billItemResponse = JSON.parseObject(res,
                    new TypeReference<DataResponse<List<HroWorkingHourItemExcelVo>>>() {
                    });
            if (billItemResponse != null && billItemResponse.getCode() == 0) {
                List<HroWorkingHourItemExcelVo> billItemExcelVo = billItemResponse.getData();
                ExportExcelUtil.addSheet(workbook, billItemExcelVo, HroWorkingHourItemExcelVo.class, null, "对账单据工时明细", true);
                ExportExcelUtil.downLoadExcel("人力点工工时对账单据列表导出.xls", servletResponse, workbook);
                isSuccess = true;
            }
        }
        if (!isSuccess) {
            servletResponse.setContentType(MediaType.APPLICATION_JSON_UTF8_VALUE);
            servletResponse.getWriter().print(res);
        }
    }

    @ApiOperation(value = "查询对账单开票信息")
    @GetMapping("paymentInvoiceQueryBill")
    public Response paymentInvoiceQueryBill(@RequestParam Long contractId,
                                            @RequestParam(required = false) String billCode) {
        String url = ModelsEnum.CTC.getBaseUrl() + "hroWorkingHourBill/paymentInvoiceQueryBill?contractId=" + contractId;
        url += "&billCode=" + (billCode == null ? "" : billCode);
        return restTemplate.getForObject(url, DataResponse.class);
    }

    @ApiOperation(value = "查询入账信息")
    @GetMapping("getEntryAccountInfo")
    public Response getEntryAccountInfo(@RequestParam Long id) {
        String url = ModelsEnum.CTC.getBaseUrl() + "hroWorkingHourBill/getEntryAccountInfo?id=" + id;
        return restTemplate.getForObject(url, DataResponse.class);
    }

    @ApiOperation(value = "同步入账信息")
    @GetMapping("syncEntryAccountInfo")
    public Response syncEntryAccountInfo(@RequestParam Long id) {
        String url = ModelsEnum.CTC.getBaseUrl() + "hroWorkingHourBill/syncEntryAccountInfo?id=" + id;
        return restTemplate.getForObject(url, DataResponse.class);
    }

    private List<FileInfo> getFileByIds(List<Long> ids) {
        FileInfoExample fileInfoExample = new FileInfoExample();
        fileInfoExample.createCriteria().andIdIn(ids);
        List<FileInfo> fileInfoList = fileInfoMapper.selectByExample(fileInfoExample);
        Map<Long, UserInfo> userMap = new HashMap<>(fileInfoList.size());
        fileInfoList.forEach(f -> {
            UserInfo userInfo = userMap.computeIfAbsent(f.getCreateBy(), CacheDataUtils::findUserById);
            if (userInfo != null) {
                f.setCreateName(userInfo.getName());
            }
        });
        return fileInfoList;
    }

    private String buildUrl(String url, Map<String, Object> params) {
        String queryParams = params.entrySet().stream()
                .filter(e -> e.getValue() != null)
                .map(e -> e.getKey() + "=" + e.getValue())
                .collect(Collectors.joining("&"));
        if (!queryParams.isEmpty()) {
            url += "?" + queryParams;
        }
        return url;
    }

    @ApiOperation(value = "手动同步入账信息（修复同步了含税金额的bug）")
    @GetMapping("manualSyncEntryAccountInfo")
    @CheckSubmit(delaySeconds = 30)
    public Response manualSyncEntryAccountInfo(@RequestParam Long id,
                                               @RequestParam BigDecimal negTaxAmount,
                                               @RequestParam Date accountingDate) {
        String url = ModelsEnum.CTC.getBaseUrl() + "hroWorkingHourBill/manualSyncEntryAccountInfo";
        url += "?id=" + id;
        url += "&negTaxAmount=" + negTaxAmount;
        url += "&accountingDate=" + DateUtils.format(accountingDate);
        return restTemplate.getForObject(url, DataResponse.class);
    }

}
