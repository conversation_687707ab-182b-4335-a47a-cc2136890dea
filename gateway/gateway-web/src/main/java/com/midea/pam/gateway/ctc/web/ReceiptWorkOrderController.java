package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.BankAccountDto;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.ReceiptWorkOrderAccountingDto;
import com.midea.pam.common.ctc.dto.ReceiptWorkOrderDto;
import com.midea.pam.common.ctc.excelVo.ReceiptWorkOrderExportExcelVo;
import com.midea.pam.common.ctc.query.ReceiptWorkOrderQuery;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.BeanConverter;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.service.MipWorkflowInnerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("回款工单")
@RestController
@RequestMapping("receiptWorkOrder")
public class ReceiptWorkOrderController extends ControllerHelper {
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private MipWorkflowInnerService mipWorkflowInnerService;

    @ApiOperation("新增回款工单&重新编辑-提交")
    @PostMapping({"submit"})
    public Response submit(@RequestBody ReceiptWorkOrderDto dto) {
        String url = String.format("%sreceiptWorkOrder/submit", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<ReceiptWorkOrderDto>>() {
        });
    }

    @ApiOperation(value = "回款工单-列表")
    @PostMapping("page")
    public Response page(@RequestBody ReceiptWorkOrderQuery queryDto) {
        String url = String.format("%sreceiptWorkOrder/page", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, queryDto, String.class);
        return JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<PageInfo<ReceiptWorkOrderDto>>>() {
        });
    }

    @ApiOperation(value = "回款工单列表导出")
    @PostMapping("export")
    public void export(HttpServletResponse response, @RequestBody ReceiptWorkOrderQuery queryDto) {
        String url = String.format("%sreceiptWorkOrder/exportList", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, queryDto, String.class);
        DataResponse<List<ReceiptWorkOrderDto>> dataResponse = JSON.parseObject(cleanStr(responseEntity.getBody()),
                new TypeReference<DataResponse<List<ReceiptWorkOrderDto>>>() {
                });
        List<ReceiptWorkOrderDto> list = dataResponse.getData();
        //导出操作
        StringBuilder fileName = new StringBuilder();
        fileName.append("回款工单_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");

        List<ReceiptWorkOrderExportExcelVo> exportExcelVos = new ArrayList<>();
        if (ListUtils.isNotEmpty(list)) {
            exportExcelVos = BeanConverter.copy(list, ReceiptWorkOrderExportExcelVo.class);
            int number = 1;
            for (ReceiptWorkOrderExportExcelVo exportExcelVo : exportExcelVos) {
                exportExcelVo.setNum(number++);
            }
        }
        Workbook workbook = ExportExcelUtil.buildDefaultSheet(exportExcelVos, ReceiptWorkOrderExportExcelVo.class, null,
                "回款工单", true);
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    @ApiOperation(value = "回款工单-详情")
    @GetMapping("view/{id}")
    public Response view(@PathVariable Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "receiptWorkOrder/view", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ReceiptWorkOrderDto>>() {
        });
    }

    @ApiOperation(value = "移动审批获取回款工单")
    @GetMapping({"getReceiptWorkOrderApp"})
    public Response getReceiptWorkOrderApp(@RequestParam Long id) {
        String url = String.format("%sreceiptWorkOrder/getReceiptWorkOrderApp?id=%s", ModelsEnum.CTC.getBaseUrl(), id);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<ResponseMap>>() {
        });
    }


    @ApiOperation(value = "根据回款工单类型查询银行账户信息")
    @GetMapping("getBankAccountByBankAccountId")
    public Response getBankAccountByBankAccountId(@RequestParam(required = false) Long busiSceneId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("busiSceneId", busiSceneId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "receiptWorkOrder/getBankAccountByBankAccountId", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<BankAccountDto>>() {
        });
    }

    @ApiOperation(value = "发起审批回调")
    @PutMapping("draftSubmit/skipSecurityInterceptor")
    public Response draftSubmitCallback(@RequestParam(required = false) Long formInstanceId,
                                        @RequestParam(required = false) String fdInstanceId,
                                        @RequestParam(required = false) String formUrl,
                                        @RequestParam(required = false) String eventName,
                                        @RequestParam(required = false) String handlerId,
                                        @RequestParam(required = false) Long companyId,
                                        @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sreceiptWorkOrder/draftSubmit/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response.setData(responseEntity.getBody());
    }

    @ApiOperation(value = "驳回回调")
    @PutMapping("updateStatusReject/skipSecurityInterceptor")
    public Response updateStatusReject(@RequestParam(required = false) Long formInstanceId,
                                       @RequestParam(required = false) String fdInstanceId,
                                       @RequestParam(required = false) String formUrl,
                                       @RequestParam(required = false) String eventName,
                                       @RequestParam(required = false) String handlerId,
                                       @RequestParam(required = false) Long companyId,
                                       @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sreceiptWorkOrder/updateStatusReject/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response.setData(responseEntity.getBody());
    }

    @ApiOperation(value = "撤回回调")
    @PutMapping("updateStatusReturn/skipSecurityInterceptor")
    public Response updateStatusReturn(@RequestParam(required = false) Long formInstanceId,
                                       @RequestParam(required = false) String fdInstanceId,
                                       @RequestParam(required = false) String formUrl,
                                       @RequestParam(required = false) String eventName,
                                       @RequestParam(required = false) String handlerId,
                                       @RequestParam(required = false) Long companyId,
                                       @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sreceiptWorkOrder/updateStatusReturn/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response.setData(responseEntity.getBody());
    }

    @ApiOperation(value = "通过回调")
    @PutMapping("updateStatusPass/skipSecurityInterceptor")
    public Response updateStatusPass(@RequestParam(required = false) Long formInstanceId,
                                     @RequestParam(required = false) String fdInstanceId,
                                     @RequestParam(required = false) String formUrl,
                                     @RequestParam(required = false) String eventName,
                                     @RequestParam(required = false) String handlerId,
                                     @RequestParam(required = false) Long companyId,
                                     @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sreceiptWorkOrder/updateStatusPass/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response.setData(responseEntity.getBody());
    }

    @ApiOperation(value = "废弃回调")
    @PutMapping("abandon/skipSecurityInterceptor")
    public Response abandon(@RequestParam(required = false) Long formInstanceId,
                            @RequestParam(required = false) String fdInstanceId,
                            @RequestParam(required = false) String formUrl,
                            @RequestParam(required = false) String eventName,
                            @RequestParam(required = false) String handlerId,
                            @RequestParam(required = false) Long companyId,
                            @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sreceiptWorkOrder/abandon/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response.setData(responseEntity.getBody());
    }

    @ApiOperation(value = "删除")
    @PutMapping("delete/skipSecurityInterceptor")
    public Response delete(@RequestParam(required = false) Long formInstanceId,
                           @RequestParam(required = false) String fdInstanceId,
                           @RequestParam(required = false) String formUrl,
                           @RequestParam(required = false) String eventName,
                           @RequestParam(required = false) String handlerId,
                           @RequestParam(required = false) Long companyId,
                           @RequestParam(required = false) Long createUserId) {
        String url = String.format("%sreceiptWorkOrder/delete/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response.setData(responseEntity.getBody());
    }

    @ApiOperation(value = "处理人通过")
    @PutMapping("agree/skipSecurityInterceptor")
    public Response agree(@RequestParam(required = false) Long formInstanceId,
                          @RequestParam(required = false) String fdInstanceId,
                          @RequestParam(required = false) String formUrl,
                          @RequestParam(required = false) String eventName,
                          @RequestParam(required = false) String handlerId,
                          @RequestParam(required = false) Long companyId,
                          @RequestParam(required = false) Long createUserId) {
        final String url = String.format("%sreceiptWorkOrder/agree/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, null, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response.setData(responseEntity.getBody());
    }

    @ApiOperation(value = "单据作废")
    @GetMapping("deleteDraft")
    public Response deleteDraft(@RequestParam(required = false) Long id, @RequestParam(required = false) String reason) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("reason", reason);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "receiptWorkOrder/deleteDraft", param);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<Integer> response = JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
        //同时作废工作流
        if (response.getCode() == 0) {
            mipWorkflowInnerService.draftAbandon("receiptWorkOrderApp", id);
        }
        return response;
    }

    @ApiOperation(value = "查询日记账详情")
    @GetMapping("getAccounting")
    public Response getAccounting(@RequestParam(required = false) String receiptCode) {
        final Map<String, Object> param = new HashMap<>();
        param.put("receiptCode", receiptCode);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "receiptWorkOrder/getAccounting", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ReceiptWorkOrderAccountingDto>>() {
        });
    }

}
