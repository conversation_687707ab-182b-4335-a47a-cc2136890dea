package com.midea.pam.gateway.ctc.web;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.WbsBudgetFieldConstant;
import com.midea.pam.common.ctc.dto.ProjectWbsBudgetObjectDto;
import com.midea.pam.common.ctc.vo.PreviewWbsBudgetChangeVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("项目wbs预算变更")
@RestController
@RequestMapping("projectWbsBudgetChangeHistory")
public class ProjectWbsBudgetChangeHistoryController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "预立项转正 - 查询WBS预算变更明细")
    @PostMapping("findPreviewWbsBudgetChangeHistory")
    public Response findPreviewWbsBudgetChangeHistory(@RequestBody Map<String, Object> param) {
        final String url = String.format(
                "%sprojectWbsBudgetChangeHistory/findPreviewWbsBudgetChangeHistory", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, param, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<PreviewWbsBudgetChangeVo>>>() {
        });
    }

    @ApiOperation(value = "预立项转正 - 获取前端格式的wbs预算关联信息", notes = "预立项转正审批页面")
    @GetMapping("v1/findPreWbsBudgetChangeInfoByWebfront")
    public Response findPreWbsBudgetChangeInfoByWebfront(@ApiParam("项目id") @RequestParam Long projectId, @ApiParam("变更头id") @RequestParam Long headerId) {
        Map<String, Object> map = new HashMap();
        map.put(WbsBudgetFieldConstant.PROJECT_ID, projectId);
        map.put(WbsBudgetFieldConstant.HEADER_ID, headerId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "projectWbsBudgetChangeHistory/v1/findPreWbsBudgetChangeInfoByWebfront", map);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<ProjectWbsBudgetObjectDto>>() {
        });
    }
}
