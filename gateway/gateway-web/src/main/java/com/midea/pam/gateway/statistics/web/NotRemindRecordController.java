package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.UnitDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.statistics.dto.PortalWarningDTO;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

@Api("记录不再提醒的数据")
@RestController
@RequestMapping("statistics/notRemindRecord")
public class NotRemindRecordController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "新增")
    @PostMapping("add")
    public Response add(@RequestBody PortalWarningDTO portalWarningDTO) {
        final String url = String.format("%sstatistics/notRemindRecord/add", ModelsEnum.STATISTICS.getBaseUrl());
        String res = restTemplate.postForEntity(url, portalWarningDTO, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
    }
}
