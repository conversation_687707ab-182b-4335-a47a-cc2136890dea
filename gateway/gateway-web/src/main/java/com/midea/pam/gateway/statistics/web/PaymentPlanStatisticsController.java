package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.PaymentPlanDTO;
import com.midea.pam.common.ctc.vo.PaymentPlanExcelVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/3/1
 * @description
 */
@Api("统计-付款计划")
@RestController
@RequestMapping("statistics/paymentPlan")
public class PaymentPlanStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查询付款计划列表")
    @GetMapping("page")
    public Response list(PaymentPlanDTO paymentPlanDto,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false) final String startDate,
                         @RequestParam(required = false) final String endDate,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(paymentPlanDto);
        params.put("list", Boolean.TRUE);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        params.put("startDate", startDate);//计划付款日期
        params.put("endDate", endDate);//计划付款日期

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/paymentPlan/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<PaymentPlanDTO>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<PaymentPlanDTO>>>() {
        });

        return response;
    }

    private Map buildParam(PaymentPlanDTO paymentPlanDto) {
        final Map<String, Object> params = new HashMap<>();
        params.put("code", paymentPlanDto.getCode());//付款计划编号
        params.put("purchaseContractName", paymentPlanDto.getPurchaseContractName());//采购合同名称
        params.put("purchaseContractCode", paymentPlanDto.getPurchaseContractCode());//采购合同编号
        params.put("contractStatusStr", paymentPlanDto.getContractStatusStr());//采购合同状态
        params.put("vendorName", paymentPlanDto.getVendorName());//供应商名称
        params.put("vendorCode", paymentPlanDto.getVendorCode());//供应商编码
        params.put("paymentMethodName", paymentPlanDto.getPaymentMethodName());//付款方式名称
        params.put("purchasingFollowerName", paymentPlanDto.getPurchasingFollowerName());//采购跟进人
        params.put("projectCode", paymentPlanDto.getProjectCode());//项目编码
        params.put("projectName", paymentPlanDto.getProjectName());//项目名称
        params.put("milestoneName", paymentPlanDto.getMilestoneName());//关联里程碑
        params.put("milestoneStatusStr", paymentPlanDto.getMilestoneStatusStr());//里程碑状态
        params.put("statusStr", paymentPlanDto.getStatusStr());//付款计划状态
        params.put("currency", paymentPlanDto.getCurrency());//付款计划状态
        return params;
    }


    @ApiOperation(value = "付款计划列表导出", response = ResponseMap.class)
    @GetMapping("export")
    public void listExport(HttpServletResponse response, PaymentPlanDTO paymentPlanDto,
                           @RequestParam(required = false) final String startDate,
                           @RequestParam(required = false) final String endDate) {
        final Map<String, Object> params = buildParam(paymentPlanDto);
        params.put("startDate", startDate);//计划付款日期
        params.put("endDate", endDate);//计划付款日期
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/paymentPlan/v1/export", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("付款计划_"+ DateUtils.format(new Date(),"yyyyMMddHHmmss"));
        fileName.append(".xls");

        JSONArray paymentPlanArr = (JSONArray) resultMap.get("paymentPlanList");

        List<PaymentPlanExcelVO> paymentPlanExcelVOS = new ArrayList<>();
        if(paymentPlanArr != null){
            paymentPlanExcelVOS = JSONObject.parseArray(paymentPlanArr.toJSONString(), PaymentPlanExcelVO.class);
            for (int i = 0; i < paymentPlanExcelVOS.size(); i++) {
                PaymentPlanExcelVO paymentPlanExcelVO = paymentPlanExcelVOS.get(i);
                paymentPlanExcelVO.setNumb(i + 1);
            }
        }

        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(paymentPlanExcelVOS, PaymentPlanExcelVO.class, null, "付款计划", true);
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

}
