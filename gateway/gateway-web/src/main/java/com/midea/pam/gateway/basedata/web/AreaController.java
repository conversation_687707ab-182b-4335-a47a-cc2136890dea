package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.basedata.dto.AreaDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.Utils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Api("行政区域")
@RequestMapping({"area"})
public class AreaController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "行政区域树")
    @GetMapping("getProvincesWithCities")
    public Response getProvincesWithCities(@RequestParam(required = false) Long provinceId, @RequestParam(required = false) String areaName) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", provinceId);
        param.put("areaName", areaName);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/area/getProvincesWithCities", param);
        String res = cleanStr(restTemplate.getForObject(url, String.class));
        List<Map<String, Object>> dataList = Utils.jsonStr2List(res);
        DataResponse<List<Map<String, Object>>> response = Response.dataResponse();
        return response.setData(dataList);
    }

    @ApiOperation("新增或者编辑")
    @PostMapping("submit")
    public Response submit(@RequestBody AreaDto dto) {
        String url = String.format("%sarea/submit", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "删除")
    @GetMapping("delete/{id}")
    public Response delete(@PathVariable Long id) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "area/delete", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "行政区域树哈希值")
    @GetMapping("getHashCodeFromProvincesWithCities")
    public com.midea.pam.common.base.Response getHashCodeFromProvincesWithCities(@RequestParam(required = false) Long provinceId) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("provinceId", provinceId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/area/getHashCodeFromProvincesWithCities", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        com.midea.pam.common.base.DataResponse<Integer> response = JSON.parseObject(res, new TypeReference<com.midea.pam.common.base.DataResponse<Integer>>() {
        });
        return response;
    }

}
