package com.midea.pam.gateway.mdw.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @PackageClassName: com.midea.pam.gateway.statistics.web.InvoiceSaleContractDetailStatisticsController
 * @Description: 发票-销售合同开票详表数据
 * @Author: JerryH
 * @Date: 2022-12-10, 0010 下午 03:32:14
 */
@Api("发票-销售合同开票详表数据")
@RestController
@RequestMapping("mdw/dwdInvoiceSaleContractDetail")
public class DwdInvoiceSaleContractDetailStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "手动拉取合同收款详表数据")
    @GetMapping("saveOrUpdateDwdInvoiceSaleContractDetail")
    public Response myPageChildrenContracts(@RequestParam(required = false) Long projectId) {
        Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        final String url = buildGetUrl(ModelsEnum.MDW.getBaseUrl(), "mdw/dwdInvoiceSaleContractDetail/saveOrUpdateDwdInvoiceSaleContractDetail", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "统计发票-销售合同开票原子表数据")
    @GetMapping("statisticsDwdInvoiceSaleContractDetail")
    public Response statisticsDwdInvoiceSaleContractDetail(@RequestParam(required = false) Long projectId) {
        Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        final String url = buildGetUrl(ModelsEnum.MDW.getBaseUrl(), "mdw/dwdInvoiceSaleContractDetail/statisticsDwdInvoiceSaleContractDetail", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "统计发票-销售合同开票原子表数据（逾期）")
    @GetMapping("statisticsDwdInvoiceSaleContractOverdueDetail")
    public Response statisticsDwdInvoiceSaleContractOverdueDetail(@RequestParam(required = false) Long projectId) {
        Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        final String url = buildGetUrl(ModelsEnum.MDW.getBaseUrl(), "mdw/dwdInvoiceSaleContractDetail/statisticsDwdInvoiceSaleContractOverdueDetail", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "统计发票-销售合同开票原子表数据（未逾期）")
    @GetMapping("statisticsDwdInvoiceSaleContractNotOverdueDetail")
    public Response statisticsDwdInvoiceSaleContractNotOverdueDetail(@RequestParam(required = false) Long projectId) {
        Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        final String url = buildGetUrl(ModelsEnum.MDW.getBaseUrl(), "mdw/dwdInvoiceSaleContractDetail/statisticsDwdInvoiceSaleContractNotOverdueDetail", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
    }
}
