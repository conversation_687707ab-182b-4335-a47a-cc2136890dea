package com.midea.pam.gateway.ctc.web;

import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Api("rdm-工时")
@RestController
@RequestMapping("rdmWorkingHour")
public class RdmWorkingHourController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "同步RDM数据")
    @GetMapping("getRdmWorkingHour")
    public Response getRdmWorkingHour(@RequestParam(required = true)String startDate,
                                        @RequestParam(required = false)String endDate){
        final Map<String, Object> param = new HashMap<>();
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "rdmWorkingHour/getRdmWorkingHour",param);
        restTemplate.getForObject(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        response.setData("0");
        return response;
    }

    @ApiOperation(value = "更新RDM数据到正式数据")
    @GetMapping("rdmToWorkingHour")
    public Response rdmToWorkingHour(){
        final Map<String, Object> param = new HashMap<>();
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "rdmWorkingHour/rdmToWorkingHour",param);
        restTemplate.getForObject(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        response.setData("0");
        return response;
    }

}
