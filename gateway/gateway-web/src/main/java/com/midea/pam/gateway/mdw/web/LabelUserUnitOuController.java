package com.midea.pam.gateway.mdw.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @PackageClassName: com.midea.pam.gateway.mdw.web.LabelUserUnitOuController
 * @Description: 用户与unit，ou的关系表数据
 * @Author: JerryH
 * @Date: 2023-03-13, 0013 下午 02:13
 */
@Api("label")
@RestController
@RequestMapping("mdw/userUnitOu")
public class LabelUserUnitOuController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "用户与unit，ou的关系表数据")
    @GetMapping("statisticsUserUnitOu")
    public Response getUrl() {
        Map<String, Object> param = new HashMap<>();
        final String url = buildGetUrl(ModelsEnum.MDW.getBaseUrl(), "userUnitOu/statisticsUserUnitOu", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<String>>() {
        });
    }
}
