package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.entity.DocumentLibraryNew;
import com.midea.pam.common.ctc.entity.FileListInfo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/8 上午 10:12
 */
@Api("文档库")
@RestController
@RequestMapping("documentLibrary")
public class DocumentLibraryController extends ControllerHelper {
    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "新建文件夹")
    @PostMapping("newFolder")
    public Response newFolder(@RequestBody DocumentLibraryNew dto) {
        String url = String.format("%sdocumentLibrary/newFolder", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<DocumentLibraryNew> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<DocumentLibraryNew>>() {
        });
        return response;
    }

    @ApiOperation(value = "编辑文件夹")
    @PostMapping("editFolder")
    public Response editFolder(@RequestBody DocumentLibraryNew dto) {
        String url = String.format("%sdocumentLibrary/editFolder", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<DocumentLibraryNew> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<DocumentLibraryNew>>() {
        });
        return response;
    }

    @ApiOperation(value = "假删除文件夹")
    @GetMapping("deleteFolder")
    public Response deleteFolder(@RequestParam Long folderId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("folderId", folderId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/documentLibrary/deleteFolder", param);
        DataResponse<String> response = Response.dataResponse();
        response.setData(restTemplate.getForObject(url, String.class));
        return response;
    }

    @ApiOperation(value = "上传文件")
    @PostMapping("uploadFile")
    public Response uploadFile(@RequestBody FileListInfo dto) {
        String url = String.format("%sdocumentLibrary/uploadFile", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<FileListInfo> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<FileListInfo>>() {
        });
        return response;
    }

    @ApiOperation(value = "返回左侧文件夹列表")
    @GetMapping("leftFolderList")
    public Response leftFolderList(@RequestParam Long projectId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/documentLibrary/leftFolderList", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<DocumentLibraryNew>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<DocumentLibraryNew>>>() {
        });
        return response;
    }

    @ApiOperation(value = "返回右侧文件列表")
    @GetMapping("rightFileList")
    public Response rightFileList(@RequestParam Long folderId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("folderId", folderId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/documentLibrary/rightFileList", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<FileListInfo>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<FileListInfo>>>() {
        });
        return response;
    }

    @ApiOperation(value = "假删除文件")
    @GetMapping("deleteFile")
    public Response deleteFile(@RequestParam Long fileId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("fileId", fileId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/documentLibrary/deleteFile", param);
        DataResponse<String> response = Response.dataResponse();
        response.setData(restTemplate.getForObject(url, String.class));
        return response;
    }

    @ApiOperation(value = "编辑文件描述")
    @PostMapping("editFileDes")
    public Response editFileDes(@RequestBody FileListInfo dto) {
        String url = String.format("%sdocumentLibrary/editFileDes", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<FileListInfo> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<FileListInfo>>() {
        });
        return response;
    }
}
