package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailChangeRecord;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

@Api("详细设计变更记录")
@RestController
@RequestMapping({"milepostDesignPlanDetailChangeRecord"})
public class MilepostDesignPlanDetailChangeRecordController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    /**
     * 添加
     */
    @ApiOperation(value = "添加")
    @PostMapping("insert")
    public Response insert(@RequestBody MilepostDesignPlanDetailChangeRecord milepostDesignPlanDetailChangeRecord) {
        final String url = String.format("%smilepostDesignPlanDetailChangeRecord/insert", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, milepostDesignPlanDetailChangeRecord, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    /**
     * 修改
     */
    @ApiOperation(value = "修改")
    @PostMapping("update")
    public Response update(@RequestBody MilepostDesignPlanDetailChangeRecord milepostDesignPlanDetailChangeRecord) {
        final String url = String.format("%smilepostDesignPlanDetailChangeRecord/update", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, milepostDesignPlanDetailChangeRecord, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }
}
