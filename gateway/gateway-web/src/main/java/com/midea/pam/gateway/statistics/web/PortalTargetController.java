package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.statistics.entity.PortalTarget;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("门户指标")
@RestController
@RequestMapping("portalTarget")
public class PortalTargetController extends ControllerHelper {
    @Resource
    private RestTemplate restTemplate;



    @ApiOperation(value = "按门户指标ID删除")
    @GetMapping("deleteById")
    public Response deleteById(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/portalTarget/deleteById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
    }


    @ApiOperation(value = "新增/修改门户指标")
    @PostMapping("save")
    public Response save(@RequestBody PortalTarget portalTarget) {
        final String url = String.format("%sportalTarget/save", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, portalTarget, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Long>>() {
        });
    }


    @ApiOperation(value = "按id查询门户指标")
    @GetMapping("findById/{id}")
    public Response findById(@PathVariable Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/portalTarget/findById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PortalTarget>>() {
        });


    }

    @ApiOperation(value = " 分页查询门户指标")
    @GetMapping("page")
    public Response page(@RequestParam(required = false) final String moduleCodeStr,
                         @RequestParam(required = false) final String targetName,
                         @RequestParam(required = false) final Boolean enableFlag,
                         @RequestParam(required = false) final Boolean displayFlag,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "100") final Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put("moduleCodeStr", moduleCodeStr);
        param.put("targetName", targetName);
        param.put("enableFlag", enableFlag);
        param.put("displayFlag", displayFlag);
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/portalTarget/page", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<PortalTarget>>>() {
        });
    }

    @ApiOperation(value = " 查询所有门户指标")
    @GetMapping("findAll")
    public Response findAll() {
        final Map<String, Object> param = new HashMap<>();
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/portalTarget/findAll", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<PortalTarget>>>() {
        });
    }
}
