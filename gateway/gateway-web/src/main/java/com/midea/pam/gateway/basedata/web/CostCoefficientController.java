package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.CostCoefficientDto;
import com.midea.pam.common.basedata.entity.CostCoefficient;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping({"costCoefficient"})
@Api("成本系数")
public class CostCoefficientController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation("成本系数分页查询")
    @GetMapping("page")
    public Response page(@ApiParam("使用单位ID") @RequestParam Long unitId,
                         @ApiParam("产品意向（下拉多选）") @RequestParam(required = false) String productIntentionStr,
                         @ApiParam("成本系数类型，多个用,隔开") @RequestParam(required = false) String coefficientTypes,
                         @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") Integer pageSize) {

        final Map<String, Object> param = new HashMap<>();
        param.put("unitId", unitId);
        param.put("coefficientTypes", coefficientTypes);
        param.put("productIntentionStr", productIntentionStr);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);

        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/costCoefficient/page", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<CostCoefficientDto>>>() {
        });
    }

    @ApiOperation("成本系数查询")
    @GetMapping("list")
    public Response list(@ApiParam("使用单位ID") @RequestParam(required = false) Long unitId,
                         @ApiParam("产品意向") @RequestParam(required = false) String productIntention,
                         @ApiParam("有效期") @RequestParam(required = false) String currentDate,
                         @ApiParam("成本系数类型") @RequestParam(required = false) String coefficientType) {

        final Map<String, Object> param = new HashMap<>();
        param.put("unitId", unitId);
        param.put("productIntention", productIntention);
        param.put("coefficientType", coefficientType);
        param.put("currentDate", currentDate);

        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/costCoefficient/list", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();

        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<Map<String, Object>>>() {
        });
        return dataResponse;
    }

    @ApiOperation("新增成本系数")
    @PostMapping({"addCostCoefficient"})
    public com.midea.pam.common.base.Response addCostCoefficient(@RequestBody List<CostCoefficient> costCoefficientList) {
        final Map<String, Object> param = new HashMap<>();
        String url = String.format("%scostCoefficient/addCostCoefficient", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, costCoefficientList, String.class);
        com.midea.pam.common.base.DataResponse<Boolean> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<com.midea.pam.common.base.DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation("编辑成本系数")
    @PostMapping({"updateCostCoefficient"})
    public Response updateCostCoefficient(@RequestBody CostCoefficientDto dto) {
        String url = String.format("%scostCoefficient/updateCostCoefficient", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<CostCoefficientDto> response = JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<CostCoefficientDto>>() {
        });
        return response;
    }
}
