package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.MaterialPriceConfigDetailDto;
import com.midea.pam.common.basedata.dto.MaterialPriceConfigHeaderDto;
import com.midea.pam.common.basedata.entity.MaterialPriceConfigHeader;
import com.midea.pam.common.basedata.excelVo.MaterialPriceConfigExportExcelVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.BeanConverter;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Api("物料价格类型配置")
@RequestMapping({"materialPriceConfig"})
public class MaterialPriceConfigController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation("新增价格类型配置&重新编辑-暂存")
    @PostMapping({"draft"})
    public Response draft(@RequestBody MaterialPriceConfigHeaderDto dto) {
        String url = String.format("%smaterialPriceConfig/draft", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<MaterialPriceConfigHeaderDto>>() {
        });
    }

    @ApiOperation("新增价格类型配置&重新编辑-提交")
    @PostMapping({"submit"})
    public Response submit(@RequestBody MaterialPriceConfigHeaderDto dto) {
        String url = String.format("%smaterialPriceConfig/submit", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(cleanStr(responseEntity.getBody()), new TypeReference<DataResponse<Boolean>>() {
        });
    }


    @ApiOperation(value = "价格类型配置-列表")
    @GetMapping("page")
    public Response page(
            @RequestParam(required = false) @ApiParam("物料价格类型编码") String code,
            @RequestParam(required = false) @ApiParam("物料价格类型名称") String name,
            @RequestParam(required = false) @ApiParam("是否启用(0：启用、1：未启用)") Integer enable,
            @RequestParam(required = false) @ApiParam("状态ID（下拉多选）") String statusStr,
            @RequestParam(required = false) @ApiParam("运行状态ID（下拉多选）") String executionStatusStr,
            @RequestParam(required = false) @ApiParam("创建时间开始") String createAtStartTime,
            @RequestParam(required = false) @ApiParam("创建时间结束") String createAtEndTime,
            @RequestParam(required = false) @ApiParam("库存组织ID（下拉多选）") String organizationIdStr,
            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put("code", code);
        param.put("name", name);
        param.put("enable", enable);
        param.put("statusStr", statusStr);
        param.put("executionStatusStr", executionStatusStr);
        param.put("createAtStartTime", createAtStartTime);
        param.put("createAtEndTime", createAtEndTime);
        param.put("organizationIdStr", organizationIdStr);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "materialPriceConfig/page", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<MaterialPriceConfigHeaderDto>>>() {
        });
    }

    @ApiOperation(value = "价格类型配置-详情列表导出")
    @GetMapping("export/{id}")
    public void export(HttpServletResponse response, @PathVariable Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "materialPriceConfig/view", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<MaterialPriceConfigHeaderDto> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<MaterialPriceConfigHeaderDto>>() {
        });
        MaterialPriceConfigHeaderDto dto = dataResponse.getData();
        List<MaterialPriceConfigDetailDto> detailDtos = dto.getDetails();
        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("物料价格类型配置_" + dto.getName() + "_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");

        List<MaterialPriceConfigExportExcelVo> exportExcelVos = BeanConverter.copy(detailDtos, MaterialPriceConfigExportExcelVo.class);

        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(exportExcelVos, MaterialPriceConfigExportExcelVo.class, null,
                "物料价格类型配置", true);
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    @ApiOperation(value = "价格类型配置-详情")
    @GetMapping("view/{id}")
    public Response view(@PathVariable Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "materialPriceConfig/view", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<MaterialPriceConfigHeaderDto>>() {
        });
    }

    @ApiOperation(value = "草稿作废")
    @GetMapping("cancelDraft/{id}")
    public Response cancelDraft(@PathVariable Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "materialPriceConfig/cancelDraft", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "查询价格类型编码")
    @GetMapping("getMaterialPriceCode")
    public Response getMaterialPriceCode(
            @RequestParam() @ApiParam("库存组织id") Long organizationId,
            @RequestParam(required = false) @ApiParam("物料id") Long materialId,
            @RequestParam(required = false) @ApiParam("物料价格类型名称") String name,
            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put("organizationId", organizationId);
        param.put("materialId", materialId);
        param.put("name", name);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "materialPriceConfig/getMaterialPriceCode", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<MaterialPriceConfigHeader>>>() {
        });
    }

}
