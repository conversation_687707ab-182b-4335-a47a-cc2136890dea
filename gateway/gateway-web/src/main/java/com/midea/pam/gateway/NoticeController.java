package com.midea.pam.gateway;

import com.midea.pam.annotation.CheckSubmit;
import com.midea.pam.common.enums.EmailStatus;
import com.midea.pam.common.gateway.entity.Backlog;
import com.midea.pam.common.gateway.entity.Email;
import com.midea.pam.common.gateway.entity.EmailExample;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.job.BacklogPullJob;
import com.midea.pam.gateway.job.EmailPullJob;
import com.midea.pam.gateway.service.BacklogService;
import com.midea.pam.gateway.service.EmailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/20
 * @description 邮件
 */
@Api("通知")
@RestController
@RequestMapping(value = {"notice"})
public class NoticeController {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private EmailPullJob emailPullJob;

    @Resource
    private EmailService emailService;

    @Resource
    private BacklogService backlogService;

    @Resource
    private BacklogPullJob backlogPullJob;

    @CheckSubmit(delaySeconds = 10)
    @ApiOperation(value = "拉取数据")
    @GetMapping("email/emailPullJob")
    public Response emailPullJob() {
        try {
            emailPullJob.execute(null);
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return Response.dataResponse();
    }

    @ApiOperation(value = "发送邮件")
    @GetMapping("email/testEmail")
    public Response testEmail(@RequestParam Long id) {
        try {
            Email email = emailService.selectByPrimaryKey(id);
            emailService.sendSingleLangMail(email);
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return Response.dataResponse();
    }

    @ApiOperation(value = "发送多语言邮件")
    @GetMapping("email/testMultiEmail")
    public Response testMultiEmail(@RequestParam Integer businessType, @RequestParam String receiver) {
        try {
            EmailExample emailExample = new EmailExample();
            EmailExample.Criteria criteria = emailExample.createCriteria();
            criteria.andDeletedFlagEqualTo(Boolean.FALSE);
            criteria.andStatusEqualTo(EmailStatus.TO_DO.getCode());
            criteria.andBusinessTypeEqualTo(businessType);
            criteria.andReceiverEqualTo(receiver);

            List<Email> emails = emailService.selectByExampleWithBLOBs(emailExample);
            emailService.sendMultiLangMail(emails);
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return Response.dataResponse();
    }


    @ApiOperation(value = "拉取数据")
    @GetMapping("backlog/backlogPullJob")
    public Response backlogPullJob() {
        try {
            backlogPullJob.execute(null);
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return Response.dataResponse();
    }

    @ApiOperation(value = "发送待办数据")
    @GetMapping("backlog/test")
    public Response sendBacklog(@RequestParam Long id) {
        try {
            Backlog backlog = backlogService.selectByPrimaryKey(id);
            backlogService.sendBacklog(backlog);
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return Response.dataResponse();
    }

}
