package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.CustomViewConfigDTO;
import com.midea.pam.common.basedata.dto.DictDto;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.GlegalContractChangeDetailDto;
import com.midea.pam.common.ctc.vo.ContractInventoryExcelVo;
import com.midea.pam.common.ctc.vo.ContractSaleExcelVO;
import com.midea.pam.common.ctc.vo.ContractVO;
import com.midea.pam.common.ctc.vo.InvoicePlanExcelVo;
import com.midea.pam.common.ctc.vo.ReceiptPlanExcelVo;
import com.midea.pam.common.ctc.vo.SubContractExcelVO;
import com.midea.pam.common.enums.DictType;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.service.MipWorkflowInnerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Api("合同")
@RestController
@RequestMapping("statistics/contract")
public class ContractStatisticsController extends ControllerHelper {


    @Resource
    private RestTemplate restTemplate;

    @Resource
    private MipWorkflowInnerService mipWorkflowInnerService;

    private final static String CONTRACT_TEMPLATE_ID = "contractApp";


    @ApiOperation(value = "主合同导出")
    @GetMapping("excel/export")
    public void listContracts(@RequestParam(required = false) @ApiParam("合同编号") final String code,
                              @RequestParam(required = false) @ApiParam("合同名称") final String name,
                              @RequestParam(required = false) @ApiParam("合同类型") final Boolean frameFlag,
                              @RequestParam(required = false) @ApiParam("客户属性") final Boolean customerType,
                              @RequestParam(required = false) @ApiParam("客户名称") final String customerName,
                              @RequestParam(required = false) @ApiParam("客户CRM编码") final String customerCode,
                              @RequestParam(required = false) @ApiParam("销售经理") final String salesManagerName,
                              @RequestParam(required = false) @ApiParam("项目经理") final String managerName,
                              @RequestParam(required = false) @ApiParam("销售部门") final String unitName,
                              @RequestParam(required = false) @ApiParam("归档开始时间") final String filingStartDate,
                              @RequestParam(required = false) @ApiParam("归档结束时间") final String filingEndDate,
                              @RequestParam(required = false) @ApiParam("合同状态(多选)") final String manyStatus,
                              @RequestParam(required = false) @ApiParam("业务实体(多选)") final String ouName,
                              @RequestParam(required = false) @ApiParam("是否已上传双章合同") final Boolean isDoubleChapterContract,
                              @RequestParam(required = false) @ApiParam("关联商机") final String businessCodeOrName,
                              @RequestParam(required = false) @ApiParam("商机编号") final String businessCode,
                              @RequestParam(required = false) @ApiParam("排序") final String orderParam,
                              @RequestParam(required = false) @ApiParam("排序类型(1:升序；2：降序)") final Integer orderType,
                              @RequestParam(required = false) @ApiParam("法务系统合同编号") final String legalContractNum,
                              HttpServletResponse response) {
        Map<String, Object> param = new HashMap<>();
        param.put("type", DictType.MEASUREMENT_UNIT.code());
        // 获取计量单位
        final String url1 = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/ltcDict/getLtcDict", param);
        final String res1 = restTemplate.getForEntity(url1, String.class).getBody();
        final DataResponse<List<DictDto>> response1 = JSON.parseObject(res1, new TypeReference<DataResponse<List<DictDto>>>() {
        });
        List<DictDto> dictList = response1.getData();
        Map<String, String> unitMap = dictList.stream().collect(Collectors.toMap(DictDto::getCode, DictDto::getName, (a, b) -> a));
        param.put("code", code);
        param.put("name", name);
        param.put("frameFlag", frameFlag);
        param.put("customerName", customerName);
        param.put("customerType", customerType);
        param.put("unitName", unitName);
        param.put("customerCode", customerCode);
        param.put("salesManagerName", salesManagerName);
        param.put("managerName", managerName);
        param.put("manyStatus", manyStatus);
        param.put("manyOuName", ouName);
        param.put("filingStartDate", filingStartDate);
        param.put("filingEndDate", filingEndDate);
        param.put("orderParam", orderParam);
        param.put("orderType", orderType);
        param.put("isDoubleChapterContract", isDoubleChapterContract);
        param.put("businessCodeOrName", businessCodeOrName);
        param.put("businessCode", businessCode);
        param.put("legalContractNum", legalContractNum);

        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "contract/list", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        final DataResponse<Map<String, Object>> dataResponse =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
                });
        Map<String, Object> resultMap = dataResponse.getData();
        JSONArray contractArr = (JSONArray) resultMap.get("contractList");
        JSONArray childrenArr = (JSONArray) resultMap.get("childrenContractList");
        JSONArray receiptPlanArr = (JSONArray) resultMap.get("receiptPlanDetailDTOList");
        JSONArray invoicePlanArr = (JSONArray) resultMap.get("invoicePlanVOList");
        JSONArray contractInventoryArr = (JSONArray) resultMap.get("contractInventoryList");
        //解决导出对象中商机编号为关联商机导致导出无商机编号列，并防止ContractExcelVO其他处有共用 20220713
        List<ContractSaleExcelVO> ContractExcelVOList = new ArrayList<>();
        List<SubContractExcelVO> subContractExcelVOList = new ArrayList<>();
        List<InvoicePlanExcelVo> invoicePlanExcelVoList = new ArrayList<>();
        List<ReceiptPlanExcelVo> receiptPlanExcelVoList = new ArrayList<>();
        List<ContractInventoryExcelVo> contractInventoryExcelVoList = new ArrayList<>();
        if (contractArr != null) {
            ContractExcelVOList = JSONObject.parseArray(contractArr.toJSONString(), ContractSaleExcelVO.class);
            for (int i = 0; i < ContractExcelVOList.size(); i++) {
                ContractSaleExcelVO contractExcelVO = ContractExcelVOList.get(i);
                contractExcelVO.setNumber(i + 1);
            }
        }
        if (childrenArr != null) {
            subContractExcelVOList = JSONObject.parseArray(childrenArr.toJSONString(), SubContractExcelVO.class);
            for (int i = 0; i < subContractExcelVOList.size(); i++) {
                SubContractExcelVO subContractExcelVO = subContractExcelVOList.get(i);
                subContractExcelVO.setNumber(i + 1);
            }
        }
        if (invoicePlanArr != null) {
            invoicePlanExcelVoList = JSONObject.parseArray(invoicePlanArr.toJSONString(), InvoicePlanExcelVo.class);
            for (int i = 0; i < invoicePlanExcelVoList.size(); i++) {
                InvoicePlanExcelVo invoicePlanExcelVo = invoicePlanExcelVoList.get(i);
                invoicePlanExcelVo.setNumber(i + 1);
            }
        }
        if (receiptPlanArr != null) {
            receiptPlanExcelVoList = JSONObject.parseArray(receiptPlanArr.toJSONString(), ReceiptPlanExcelVo.class);
            for (int i = 0; i < receiptPlanExcelVoList.size(); i++) {
                ReceiptPlanExcelVo receiptPlanExcelVo = receiptPlanExcelVoList.get(i);
                receiptPlanExcelVo.setNumber(i + 1);
            }
        }

        if (contractInventoryArr != null) {
            contractInventoryExcelVoList = JSONObject.parseArray(contractInventoryArr.toJSONString(), ContractInventoryExcelVo.class);
            for (int i = 0; i < contractInventoryExcelVoList.size(); i++) {
                ContractInventoryExcelVo contractInventoryExcelVo = contractInventoryExcelVoList.get(i);
                contractInventoryExcelVo.setNumber(i + 1);
                contractInventoryExcelVo.setUnitName(unitMap.get(contractInventoryExcelVo.getUnit()));
            }
        }

        Workbook workbook = ExportExcelUtil.buildDefaultSheet(ContractExcelVOList, ContractSaleExcelVO.class, null, "主合同信息", true);
        ExportExcelUtil.addSheet(workbook, subContractExcelVOList, SubContractExcelVO.class, null, "子合同信息", true);
        ExportExcelUtil.addSheet(workbook, invoicePlanExcelVoList, InvoicePlanExcelVo.class, null, "开票计划", true);
        ExportExcelUtil.addSheet(workbook, receiptPlanExcelVoList, ReceiptPlanExcelVo.class, null, "回款计划", true);
        if (ListUtils.isNotEmpty(contractInventoryExcelVoList)) {
            ExportExcelUtil.addSheet(workbook, contractInventoryExcelVoList, ContractInventoryExcelVo.class, null, "产品清单", true);
        }

        // 获取配置
        String res = restTemplate.getForEntity(ModelsEnum.BASEDATA.getBaseUrl() + "/resource/user/queryCustomView", String.class).getBody();
        com.midea.pam.gateway.common.base.DataResponse<CustomViewConfigDTO> customViewResponse = JSON.parseObject(res, new TypeReference<com.midea.pam.gateway.common.base.DataResponse<CustomViewConfigDTO>>() {
        });
        CustomViewConfigDTO customViewConfigDTO = customViewResponse.getData();
        List<String> fieldList = new ArrayList<>();
        if (customViewConfigDTO != null) {
            // 主合同列表自定义视图导出
            if (StringUtils.isNotEmpty(customViewConfigDTO.getMainContractTableTemplate())) {
                JSONArray jsonArray = JSON.parseArray(customViewConfigDTO.getMainContractTableTemplate());
                for (int i = 0; i < jsonArray.size(); i++) {
                    fieldList.add(jsonArray.getJSONObject(i).getString("label"));
                }
                com.midea.pam.gateway.common.utils.ExportExcelUtil.dynamicExportFirstSheet(workbook, fieldList);
            }
        }

        ExportExcelUtil.downLoadExcel("销售主合同_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls", response, workbook);
    }

    @ApiOperation(value = "我的主合同导出")
    @GetMapping("excel/myexport")
    public void mylistContracts(@RequestParam(required = false) @ApiParam("合同编号") final String code,
                                @RequestParam(required = false) @ApiParam("合同名称") final String name,
                                @RequestParam(required = false) @ApiParam("合同类型") final Boolean frameFlag,
                                @RequestParam(required = false) @ApiParam("客户属性") final Boolean customerType,
                                @RequestParam(required = false) @ApiParam("客户名称") final String customerName,
                                @RequestParam(required = false) @ApiParam("客户CRM编码") final String customerCode,
                                @RequestParam(required = false) @ApiParam("销售经理") final String salesManagerName,
                                @RequestParam(required = false) @ApiParam("项目经理") final String managerName,
                                @RequestParam(required = false) @ApiParam("销售部门") final String unitName,
                                @RequestParam(required = false) @ApiParam("归档开始时间") final String filingStartDate,
                                @RequestParam(required = false) @ApiParam("归档结束时间") final String filingEndDate,
                                @RequestParam(required = false) @ApiParam("合同状态(多选)") final String manyStatus,
                                @RequestParam(required = false) @ApiParam("业务实体(多选)") final String ouName,
                                @RequestParam(required = false) @ApiParam("排序") final String orderParam,
                                @RequestParam(required = false) @ApiParam("排序类型(1:升序；2：降序)") final Integer orderType,
                                @RequestParam(required = false) @ApiParam("法务系统合同编号") final String legalContractNum,
                                HttpServletResponse response) {
        Map<String, Object> param = new HashMap<>();
        param.put("type", DictType.MEASUREMENT_UNIT.code());
        // 获取计量单位
        final String url1 = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/ltcDict/getLtcDict", param);
        final String res1 = restTemplate.getForEntity(url1, String.class).getBody();
        final DataResponse<List<DictDto>> response1 = JSON.parseObject(res1, new TypeReference<DataResponse<List<DictDto>>>() {
        });
        List<DictDto> dictList = response1.getData();
        Map<String, String> unitMap = dictList.stream().collect(Collectors.toMap(DictDto::getCode, DictDto::getName, (a, b) -> a));
        param.put("code", code);
        param.put("name", name);
        param.put("frameFlag", frameFlag);
        param.put("customerName", customerName);
        param.put("customerType", customerType);
        param.put("unitName", unitName);
        param.put("customerCode", customerCode);
        param.put("salesManagerName", salesManagerName);
        param.put("managerName", managerName);
        param.put("manyStatus", manyStatus);
        param.put("manyOuName", ouName);
        param.put("filingStartDate", filingStartDate);
        param.put("filingEndDate", filingEndDate);
        param.put("orderParam", orderParam);
        param.put("orderType", orderType);
        param.put("legalContractNum", legalContractNum);

        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "contract/mylist", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        final DataResponse<Map<String, Object>> dataResponse =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
                });
        Map<String, Object> resultMap = dataResponse.getData();
        JSONArray contractArr = (JSONArray) resultMap.get("contractList");
        JSONArray childrenArr = (JSONArray) resultMap.get("childrenContractList");
        JSONArray receiptPlanArr = (JSONArray) resultMap.get("receiptPlanDetailDTOList");
        JSONArray invoicePlanArr = (JSONArray) resultMap.get("invoicePlanVOList");
        JSONArray contractInventoryArr = (JSONArray) resultMap.get("contractInventoryList");
        List<ContractSaleExcelVO> ContractExcelVOList = new ArrayList<>();
        List<SubContractExcelVO> subContractExcelVOList = new ArrayList<>();
        List<InvoicePlanExcelVo> invoicePlanExcelVoList = new ArrayList<>();
        List<ReceiptPlanExcelVo> receiptPlanExcelVoList = new ArrayList<>();
        List<ContractInventoryExcelVo> contractInventoryExcelVoList = new ArrayList<>();
        if (contractArr != null) {
            ContractExcelVOList = JSONObject.parseArray(contractArr.toJSONString(), ContractSaleExcelVO.class);
            for (int i = 0; i < ContractExcelVOList.size(); i++) {
                ContractSaleExcelVO contractExcelVO = ContractExcelVOList.get(i);
                contractExcelVO.setNumber(i + 1);
            }
        }
        if (childrenArr != null) {
            subContractExcelVOList = JSONObject.parseArray(childrenArr.toJSONString(), SubContractExcelVO.class);
            for (int i = 0; i < subContractExcelVOList.size(); i++) {
                SubContractExcelVO subContractExcelVO = subContractExcelVOList.get(i);
                subContractExcelVO.setNumber(i + 1);
            }
        }
        if (invoicePlanArr != null) {
            invoicePlanExcelVoList = JSONObject.parseArray(invoicePlanArr.toJSONString(), InvoicePlanExcelVo.class);
            for (int i = 0; i < invoicePlanExcelVoList.size(); i++) {
                InvoicePlanExcelVo invoicePlanExcelVo = invoicePlanExcelVoList.get(i);
                invoicePlanExcelVo.setNumber(i + 1);
            }
        }
        if (receiptPlanArr != null) {
            receiptPlanExcelVoList = JSONObject.parseArray(receiptPlanArr.toJSONString(), ReceiptPlanExcelVo.class);
            for (int i = 0; i < receiptPlanExcelVoList.size(); i++) {
                ReceiptPlanExcelVo receiptPlanExcelVo = receiptPlanExcelVoList.get(i);
                receiptPlanExcelVo.setNumber(i + 1);
            }
        }

        Workbook workbook = ExportExcelUtil.buildDefaultSheet(ContractExcelVOList, ContractSaleExcelVO.class, null, "主合同信息", true);


        // 获取配置
        String res = restTemplate.getForEntity(ModelsEnum.BASEDATA.getBaseUrl() + "/resource/user/queryCustomView", String.class).getBody();
        DataResponse<CustomViewConfigDTO> customViewResponse = JSON.parseObject(res, new TypeReference<DataResponse<CustomViewConfigDTO>>() {
        });
        CustomViewConfigDTO customViewConfigDTO = customViewResponse.getData();
        List<String> fieldList = new ArrayList<>();
        if (customViewConfigDTO != null) {
            // 我的主合同列表自定义视图导出
            if (StringUtils.isNotEmpty(customViewConfigDTO.getMyMainContractTableTemplate())) {
                JSONArray jsonArray = JSON.parseArray(customViewConfigDTO.getMyMainContractTableTemplate());
                for (int i = 0; i < jsonArray.size(); i++) {
                    fieldList.add(jsonArray.getJSONObject(i).getString("label"));
                }
                com.midea.pam.gateway.common.utils.ExportExcelUtil.dynamicExportFirstSheet(workbook, fieldList);
            }
        }

        if (contractInventoryArr != null) {
            contractInventoryExcelVoList = JSONObject.parseArray(contractInventoryArr.toJSONString(), ContractInventoryExcelVo.class);
            for (int i = 0; i < contractInventoryExcelVoList.size(); i++) {
                ContractInventoryExcelVo contractInventoryExcelVo = contractInventoryExcelVoList.get(i);
                contractInventoryExcelVo.setNumber(i + 1);
                contractInventoryExcelVo.setUnitName(unitMap.get(contractInventoryExcelVo.getUnit()));
            }
        }

        ExportExcelUtil.addSheet(workbook, subContractExcelVOList, SubContractExcelVO.class, null, "子合同信息", true);
        ExportExcelUtil.addSheet(workbook, invoicePlanExcelVoList, InvoicePlanExcelVo.class, null, "开票计划", true);
        ExportExcelUtil.addSheet(workbook, receiptPlanExcelVoList, ReceiptPlanExcelVo.class, null, "回款计划", true);
        if (ListUtils.isNotEmpty(contractInventoryExcelVoList)) {
            ExportExcelUtil.addSheet(workbook, contractInventoryExcelVoList, ContractInventoryExcelVo.class, null, "产品清单", true);
        }

        ExportExcelUtil.downLoadExcel("销售主合同_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls", response, workbook);
    }

    @ApiOperation(value = "子合同导出")
    @GetMapping("children/excel/export")
    public void listChildrenContracts(@RequestParam(required = false) @ApiParam("合同编号") final String code,
                                      @RequestParam(required = false) @ApiParam("合同名称") final String name,
                                      @RequestParam(required = false) @ApiParam("关联主合同编号") final String parentContractCode,
                                      @RequestParam(required = false) @ApiParam("关联主合同名称") final String parentContractName,
                                      @RequestParam(required = false) @ApiParam("客户CRM编码") final String customerCode,
                                      @RequestParam(required = false) @ApiParam("客户名称") final String customerName,
                                      @RequestParam(required = false) @ApiParam("客户属性") final Boolean customerType,
                                      @RequestParam(required = false) @ApiParam("销售部门") final String unitName,
                                      @RequestParam(required = false) @ApiParam("销售经理") final String salesManagerName,
                                      @RequestParam(required = false) @ApiParam("项目经理") final String managerName,
                                      @RequestParam(required = false) @ApiParam("归档开始时间") final String filingStartDate,
                                      @RequestParam(required = false) @ApiParam("归档结束时间") final String filingEndDate,
                                      @RequestParam(required = false) @ApiParam("合同状态(多选)") final String manyStatus,
                                      @RequestParam(required = false) @ApiParam("业务实体(多选)") final String ouName,

                                      @RequestParam(required = false) @ApiParam("关联采购合同") final String purchaseContractCode,
                                      @RequestParam(required = false) @ApiParam("关联RDM项目") final String rdmPurchaseProject,
                                      @RequestParam(required = false) @ApiParam("是否人力外包") final String orPerson,
                                      @RequestParam(required = false) @ApiParam("商机名称") final String businessName,
                                      @RequestParam(required = false) @ApiParam("关联商机") final String businessCodeOrName,
                                      @RequestParam(required = false) @ApiParam("关联的项目号（模糊查询）") final String projectCode,
                                      @RequestParam(required = false) @ApiParam("关联的项目名称（模糊查询）") final String projectName,
                                      @RequestParam(required = false) @ApiParam("业务分部") final String businessSegment,
                                      @RequestParam(required = false) @ApiParam("排序") final String orderParam,
                                      @RequestParam(required = false) @ApiParam("排序类型(1:升序；2：降序)") final Integer orderType,
                                      HttpServletResponse response) {
        Map<String, Object> param = new HashMap<>();
        param.put("type", DictType.MEASUREMENT_UNIT.code());
        // 获取计量单位
        final String url1 = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/ltcDict/getLtcDict", param);
        final String res1 = restTemplate.getForEntity(url1, String.class).getBody();
        final DataResponse<List<DictDto>> response1 = JSON.parseObject(res1, new TypeReference<DataResponse<List<DictDto>>>() {
        });
        List<DictDto> dictList = response1.getData();
        Map<String, String> unitMap = dictList.stream().collect(Collectors.toMap(DictDto::getCode, DictDto::getName, (a, b) -> a));

        param.put("code", code);
        param.put("name", name);
        param.put("parentContractCode", parentContractCode);
        param.put("parentContractName", parentContractName);
        param.put("customerCode", customerCode);
        param.put("customerName", customerName);
        param.put("customerType", customerType);
        param.put("unitName", unitName);
        param.put("salesManagerName", salesManagerName);
        param.put("businessName", businessName);
        param.put("businessCodeOrName", businessCodeOrName);
        param.put("managerName", managerName);
        param.put("manyStatus", manyStatus);
        param.put("manyOuName", ouName);

        param.put("projectCode", projectCode);
        param.put("projectName", projectName);

        param.put("purchaseContractCode", purchaseContractCode);
        param.put("rdmPurchaseProject", rdmPurchaseProject);
        param.put("orPerson", orPerson);

        param.put("filingStartDate", filingStartDate);
        param.put("filingEndDate", filingEndDate);
        param.put("orderParam", orderParam);
        param.put("orderType", orderType);
        param.put("businessSegment", businessSegment);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "contract/listChildrenContracts", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        final DataResponse<Map<String, Object>> dataResponse =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
                });
        Map<String, Object> resultMap = dataResponse.getData();

        JSONArray childrenArr = (JSONArray) resultMap.get("childrenContractList");
        JSONArray receiptPlanArr = (JSONArray) resultMap.get("receiptPlanDetailDTOList");
        JSONArray invoicePlanArr = (JSONArray) resultMap.get("invoicePlanVOList");
        JSONArray contractInventoryArr = (JSONArray) resultMap.get("contractInventoryList");

        List<SubContractExcelVO> subContractExcelVOList = new ArrayList<>();
        List<InvoicePlanExcelVo> invoicePlanExcelVoList = new ArrayList<>();
        List<ReceiptPlanExcelVo> receiptPlanExcelVoList = new ArrayList<>();
        List<ContractInventoryExcelVo> contractInventoryExcelVoList = new ArrayList<>();

        if (childrenArr != null) {
            subContractExcelVOList = JSONObject.parseArray(childrenArr.toJSONString(), SubContractExcelVO.class);
            for (int i = 0; i < subContractExcelVOList.size(); i++) {
                SubContractExcelVO subContractExcelVO = subContractExcelVOList.get(i);
                subContractExcelVO.setNumber(i + 1);
            }
        }
        if (invoicePlanArr != null) {
            invoicePlanExcelVoList = JSONObject.parseArray(invoicePlanArr.toJSONString(), InvoicePlanExcelVo.class);
            for (int i = 0; i < invoicePlanExcelVoList.size(); i++) {
                InvoicePlanExcelVo invoicePlanExcelVo = invoicePlanExcelVoList.get(i);
                invoicePlanExcelVo.setNumber(i + 1);
            }
        }
        if (receiptPlanArr != null) {
            receiptPlanExcelVoList = JSONObject.parseArray(receiptPlanArr.toJSONString(), ReceiptPlanExcelVo.class);
            for (int i = 0; i < receiptPlanExcelVoList.size(); i++) {
                ReceiptPlanExcelVo receiptPlanExcelVo = receiptPlanExcelVoList.get(i);
                receiptPlanExcelVo.setNumber(i + 1);
            }
        }
        if (contractInventoryArr != null) {
            contractInventoryExcelVoList = JSONObject.parseArray(contractInventoryArr.toJSONString(), ContractInventoryExcelVo.class);
            for (int i = 0; i < contractInventoryExcelVoList.size(); i++) {
                ContractInventoryExcelVo contractInventoryExcelVo = contractInventoryExcelVoList.get(i);
                contractInventoryExcelVo.setNumber(i + 1);
                contractInventoryExcelVo.setUnitName(unitMap.get(contractInventoryExcelVo.getUnit()));
            }
        }

        Workbook workbook = ExportExcelUtil.buildDefaultSheet(subContractExcelVOList, SubContractExcelVO.class, null, "子合同信息", true);
        ExportExcelUtil.addSheet(workbook, invoicePlanExcelVoList, InvoicePlanExcelVo.class, null, "开票计划", true);
        ExportExcelUtil.addSheet(workbook, receiptPlanExcelVoList, ReceiptPlanExcelVo.class, null, "回款计划", true);
        if (ListUtils.isNotEmpty(contractInventoryExcelVoList)) {
            ExportExcelUtil.addSheet(workbook, contractInventoryExcelVoList, ContractInventoryExcelVo.class, null, "产品清单", true);
        }

        // 获取配置
        String res = restTemplate.getForEntity(ModelsEnum.BASEDATA.getBaseUrl() + "/resource/user/queryCustomView", String.class).getBody();
        DataResponse<CustomViewConfigDTO> customViewResponse = JSON.parseObject(res, new TypeReference<DataResponse<CustomViewConfigDTO>>() {
        });
        CustomViewConfigDTO customViewConfigDTO = customViewResponse.getData();
        List<String> fieldList = new ArrayList<>();
        if (customViewConfigDTO != null) {
            // 子合同列表自定义视图导出
            if (StringUtils.isNotEmpty(customViewConfigDTO.getSubContractTableTemplate())) {
                JSONArray jsonArray = JSON.parseArray(customViewConfigDTO.getSubContractTableTemplate());
                for (int i = 0; i < jsonArray.size(); i++) {
                    fieldList.add(jsonArray.getJSONObject(i).getString("label"));
                }
                com.midea.pam.gateway.common.utils.ExportExcelUtil.dynamicExportFirstSheet(workbook, fieldList);
            }
        }

        ExportExcelUtil.downLoadExcel("销售子合同_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls", response, workbook);
    }

    @ApiOperation(value = "我的子合同导出")
    @GetMapping("children/excel/myexport")
    public void mylistChildrenContracts(@RequestParam(required = false) @ApiParam("合同编号") final String code,
                                        @RequestParam(required = false) @ApiParam("合同名称") final String name,
                                        @RequestParam(required = false) @ApiParam("开票未回款金额是否大于0") final Boolean invoiceReceiptFlag,
                                        @RequestParam(required = false) @ApiParam("关联主合同编号") final String parentContractCode,
                                        @RequestParam(required = false) @ApiParam("关联主合同名称") final String parentContractName,
                                        @RequestParam(required = false) @ApiParam("客户CRM编码") final String customerCode,
                                        @RequestParam(required = false) @ApiParam("客户名称") final String customerName,
                                        @RequestParam(required = false) @ApiParam("客户属性") final Boolean customerType,
                                        @RequestParam(required = false) @ApiParam("销售部门") final String unitName,
                                        @RequestParam(required = false) @ApiParam("销售经理") final String salesManagerName,
                                        @RequestParam(required = false) @ApiParam("项目经理") final String managerName,
                                        @RequestParam(required = false) @ApiParam("归档开始时间") final String filingStartDate,
                                        @RequestParam(required = false) @ApiParam("归档结束时间") final String filingEndDate,
                                        @RequestParam(required = false) @ApiParam("合同状态(多选)") final String manyStatus,
                                        @RequestParam(required = false) @ApiParam("业务实体(多选)") final String ouName,

                                        @RequestParam(required = false) @ApiParam("关联采购合同") final String purchaseContractCode,
                                        @RequestParam(required = false) @ApiParam("关联RDM项目") final String rdmPurchaseProject,
                                        @RequestParam(required = false) @ApiParam("是否人力外包") final String orPerson,
                                        @RequestParam(required = false) @ApiParam("商机名称") final String businessName,
                                        @RequestParam(required = false) @ApiParam("关联商机") final String businessCodeOrName,

                                        @RequestParam(required = false) @ApiParam("排序") final String orderParam,
                                        @RequestParam(required = false) @ApiParam("排序类型(1:升序；2：降序)") final Integer orderType,
                                        HttpServletResponse response) {
        Map<String, Object> param = new HashMap<>();
        param.put("code", code);
        param.put("name", name);
        param.put("invoiceReceiptFlag", invoiceReceiptFlag);
        param.put("parentContractCode", parentContractCode);
        param.put("parentContractName", parentContractName);
        param.put("customerCode", customerCode);
        param.put("customerName", customerName);
        param.put("customerType", customerType);
        param.put("unitName", unitName);
        param.put("salesManagerName", salesManagerName);
        param.put("businessName", businessName);
        param.put("businessCodeOrName", businessCodeOrName);
        param.put("managerName", managerName);
        param.put("manyStatus", manyStatus);
        param.put("manyOuName", ouName);

        param.put("purchaseContractCode", purchaseContractCode);
        param.put("rdmPurchaseProject", rdmPurchaseProject);
        param.put("orPerson", orPerson);

        param.put("filingStartDate", filingStartDate);
        param.put("filingEndDate", filingEndDate);
        param.put("orderParam", orderParam);
        param.put("orderType", orderType);

        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "contract/mylistChildrenContracts", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        final DataResponse<Map<String, Object>> dataResponse =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
                });
        Map<String, Object> resultMap = dataResponse.getData();

        JSONArray childrenArr = (JSONArray) resultMap.get("childrenContractList");
        JSONArray receiptPlanArr = (JSONArray) resultMap.get("receiptPlanDetailDTOList");
        JSONArray invoicePlanArr = (JSONArray) resultMap.get("invoicePlanVOList");
        JSONArray contractInventoryArr = (JSONArray) resultMap.get("contractInventoryList");

        List<SubContractExcelVO> subContractExcelVOList = new ArrayList<>();
        List<InvoicePlanExcelVo> invoicePlanExcelVoList = new ArrayList<>();
        List<ReceiptPlanExcelVo> receiptPlanExcelVoList = new ArrayList<>();
        List<ContractInventoryExcelVo> contractInventoryExcelVoList = new ArrayList<>();

        if (childrenArr != null) {
            subContractExcelVOList = JSONObject.parseArray(childrenArr.toJSONString(), SubContractExcelVO.class);
            for (int i = 0; i < subContractExcelVOList.size(); i++) {
                SubContractExcelVO subContractExcelVO = subContractExcelVOList.get(i);
                subContractExcelVO.setNumber(i + 1);
            }
        }
        if (invoicePlanArr != null) {
            invoicePlanExcelVoList = JSONObject.parseArray(invoicePlanArr.toJSONString(), InvoicePlanExcelVo.class);
            for (int i = 0; i < invoicePlanExcelVoList.size(); i++) {
                InvoicePlanExcelVo invoicePlanExcelVo = invoicePlanExcelVoList.get(i);
                invoicePlanExcelVo.setNumber(i + 1);
            }
        }
        if (receiptPlanArr != null) {
            receiptPlanExcelVoList = JSONObject.parseArray(receiptPlanArr.toJSONString(), ReceiptPlanExcelVo.class);
            for (int i = 0; i < receiptPlanExcelVoList.size(); i++) {
                ReceiptPlanExcelVo receiptPlanExcelVo = receiptPlanExcelVoList.get(i);
                receiptPlanExcelVo.setNumber(i + 1);
            }
        }
        if (contractInventoryArr != null) {
            contractInventoryExcelVoList = JSONObject.parseArray(contractInventoryArr.toJSONString(), ContractInventoryExcelVo.class);
            for (int i = 0; i < contractInventoryExcelVoList.size(); i++) {
                ContractInventoryExcelVo contractInventoryExcelVo = contractInventoryExcelVoList.get(i);
                contractInventoryExcelVo.setNumber(i + 1);
            }
        }

        Workbook workbook = ExportExcelUtil.buildDefaultSheet(subContractExcelVOList, SubContractExcelVO.class, null, "子合同信息", true);
        ExportExcelUtil.addSheet(workbook, invoicePlanExcelVoList, InvoicePlanExcelVo.class, null, "开票计划", true);
        ExportExcelUtil.addSheet(workbook, receiptPlanExcelVoList, ReceiptPlanExcelVo.class, null, "回款计划", true);
        if (ListUtils.isNotEmpty(contractInventoryExcelVoList)) {
            ExportExcelUtil.addSheet(workbook, contractInventoryExcelVoList, ContractInventoryExcelVo.class, null, "产品清单", true);
        }

        // 获取配置
        String res = restTemplate.getForEntity(ModelsEnum.BASEDATA.getBaseUrl() + "/resource/user/queryCustomView", String.class).getBody();
        DataResponse<CustomViewConfigDTO> customViewResponse = JSON.parseObject(res, new TypeReference<DataResponse<CustomViewConfigDTO>>() {
        });
        CustomViewConfigDTO customViewConfigDTO = customViewResponse.getData();
        List<String> fieldList = new ArrayList<>();
        if (customViewConfigDTO != null) {
            // 子合同列表自定义视图导出
            if (StringUtils.isNotEmpty(customViewConfigDTO.getMySubContractTableTemplate())) {
                JSONArray jsonArray = JSON.parseArray(customViewConfigDTO.getMySubContractTableTemplate());
                for (int i = 0; i < jsonArray.size(); i++) {
                    fieldList.add(jsonArray.getJSONObject(i).getString("label"));
                }
                com.midea.pam.gateway.common.utils.ExportExcelUtil.dynamicExportFirstSheet(workbook, fieldList);
            }
        }

        ExportExcelUtil.downLoadExcel("销售子合同_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls", response, workbook);
    }


    @ApiOperation(value = "主合同分页查询")
    @GetMapping("page")
    public Response page(@RequestParam(required = false) @ApiParam("合同编号") final String code,
                         @RequestParam(required = false) @ApiParam("合同名称") final String name,
                         @RequestParam(required = false) @ApiParam("合同类型") final Boolean frameFlag,
                         @RequestParam(required = false) @ApiParam("客户属性") final Boolean customerType,
                         @RequestParam(required = false) @ApiParam("客户名称") final String customerName,
                         @RequestParam(required = false) @ApiParam("客户CRM编码") final String customerCode,
                         @RequestParam(required = false) @ApiParam("销售经理") final String salesManagerName,
                         @RequestParam(required = false) @ApiParam("项目经理") final String managerName,
                         @RequestParam(required = false) @ApiParam("销售部门") final String unitName,
                         @RequestParam(required = false) @ApiParam("归档开始时间") final String filingStartDate,
                         @RequestParam(required = false) @ApiParam("归档结束时间") final String filingEndDate,
                         @RequestParam(required = false) @ApiParam("合同状态(多选)") final String manyStatus,
                         @RequestParam(required = false) @ApiParam("业务实体(多选)") final String ouName,
                         @RequestParam(required = false) @ApiParam("是否已上传双章合同") final Boolean isDoubleChapterContract,
                         @RequestParam(required = false) @ApiParam("关联商机") final String businessCodeOrName,
                         @RequestParam(required = false) @ApiParam("商机编号") final String businessCode,
                         @RequestParam(required = false) @ApiParam("排序") final String orderParam,
                         @RequestParam(required = false) @ApiParam("排序类型(1:升序；2：降序)") final Integer orderType,
                         @RequestParam(required = false) @ApiParam("法务系统合同编号") final String legalContractNum,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        Map<String, Object> param = new HashMap<>();
        param.put("code", code);
        param.put("name", name);
        param.put("frameFlag", frameFlag);
        param.put("customerName", customerName);
        param.put("customerType", customerType);
        param.put("unitName", unitName);
        param.put("customerCode", customerCode);
        param.put("salesManagerName", salesManagerName);
        param.put("managerName", managerName);
        param.put("manyStatus", manyStatus);
        param.put("manyOuName", ouName);
        param.put("filingStartDate", filingStartDate);
        param.put("filingEndDate", filingEndDate);
        param.put("orderParam", orderParam);
        param.put("orderType", orderType);
        param.put("isDoubleChapterContract", isDoubleChapterContract);
        param.put("businessCodeOrName", businessCodeOrName);
        param.put("businessCode", businessCode);
        param.put("legalContractNum", legalContractNum);
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "contract/page", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ContractVO>>>() {
        });
    }


    @ApiOperation(value = "我的主合同分页查询")
    @GetMapping("mypage")
    public Response mypage(@RequestParam(required = false) @ApiParam("合同编号") final String code,
                           @RequestParam(required = false) @ApiParam("合同名称") final String name,
                           @RequestParam(required = false) @ApiParam("合同类型") final Boolean frameFlag,
                           @RequestParam(required = false) @ApiParam("客户属性") final Boolean customerType,
                           @RequestParam(required = false) @ApiParam("客户名称") final String customerName,
                           @RequestParam(required = false) @ApiParam("客户CRM编码") final String customerCode,
                           @RequestParam(required = false) @ApiParam("销售经理") final String salesManagerName,
                           @RequestParam(required = false) @ApiParam("项目经理") final String managerName,
                           @RequestParam(required = false) @ApiParam("销售部门") final String unitName,
                           @RequestParam(required = false) @ApiParam("归档开始时间") final String filingStartDate,
                           @RequestParam(required = false) @ApiParam("归档结束时间") final String filingEndDate,
                           @RequestParam(required = false) @ApiParam("合同状态(多选)") final String manyStatus,
                           @RequestParam(required = false) @ApiParam("业务实体(多选)") final String ouName,
                           @RequestParam(required = false) @ApiParam("排序") final String orderParam,
                           @RequestParam(required = false) @ApiParam("排序类型(1:升序；2：降序)") final Integer orderType,
                           @RequestParam(required = false) @ApiParam("法务系统合同编号") final String legalContractNum,
                           @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                           @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        Map<String, Object> param = new HashMap<>();
        param.put("code", code);
        param.put("name", name);
        param.put("frameFlag", frameFlag);
        param.put("customerName", customerName);
        param.put("customerType", customerType);
        param.put("unitName", unitName);
        param.put("customerCode", customerCode);
        param.put("salesManagerName", salesManagerName);
        param.put("managerName", managerName);
        param.put("manyStatus", manyStatus);
        param.put("manyOuName", ouName);
        param.put("filingStartDate", filingStartDate);
        param.put("filingEndDate", filingEndDate);
        param.put("orderParam", orderParam);
        param.put("orderType", orderType);
        param.put("legalContractNum", legalContractNum);
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "contract/mypage", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ContractVO>>>() {
        });
    }

    @ApiOperation(value = "子合同分页查询")
    @GetMapping("children/page")
    public Response pageChildrenContracts(@RequestParam(required = false) @ApiParam("合同编号") final String code,
                                          @RequestParam(required = false) @ApiParam("合同名称") final String name,
                                          @RequestParam(required = false) @ApiParam("开票未回款金额是否大于0") final Boolean invoiceReceiptFlag,
                                          @RequestParam(required = false) @ApiParam("关联主合同编号") final String parentContractCode,
                                          @RequestParam(required = false) @ApiParam("关联主合同名称") final String parentContractName,
                                          @RequestParam(required = false) @ApiParam("客户CRM编码") final String customerCode,
                                          @RequestParam(required = false) @ApiParam("客户名称") final String customerName,
                                          @RequestParam(required = false) @ApiParam("客户属性") final Boolean customerType,
                                          @RequestParam(required = false) @ApiParam("销售部门") final String unitName,
                                          @RequestParam(required = false) @ApiParam("销售经理") final String salesManagerName,
                                          @RequestParam(required = false) @ApiParam("项目经理") final String managerName,
                                          @RequestParam(required = false) @ApiParam("归档开始时间") final String filingStartDate,
                                          @RequestParam(required = false) @ApiParam("归档结束时间") final String filingEndDate,
                                          @RequestParam(required = false) @ApiParam("合同状态(多选)") final String manyStatus,
                                          @RequestParam(required = false) @ApiParam("业务实体(多选)") final String ouName,

                                          @RequestParam(required = false) @ApiParam("关联采购合同") final String purchaseContractCode,
                                          @RequestParam(required = false) @ApiParam("关联RDM项目") final String rdmPurchaseProject,
                                          @RequestParam(required = false) @ApiParam("是否人力外包") final String orPerson,

                                          @RequestParam(required = false) @ApiParam("客户合同编号") final String clientContractNumber,

                                          @RequestParam(required = false) @ApiParam("开票计划Id") final List<Long> invoiceDetailIds,
                                          @RequestParam(required = false) @ApiParam("商机名称") final String businessName,
                                          @RequestParam(required = false) @ApiParam("关联商机") final String businessCodeOrName,
                                          @RequestParam(required = false) @ApiParam("关联的项目号（模糊查询）") final String projectCode,
                                          @RequestParam(required = false) @ApiParam("关联的项目名称（模糊查询）") final String projectName,
                                          @RequestParam(required = false) @ApiParam("业务分部") final String businessSegment,
                                          @RequestParam(required = false) @ApiParam("排序") final String orderParam,
                                          @RequestParam(required = false) @ApiParam("排序类型(1:升序；2：降序)") final Integer orderType,
                                          @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                          @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        Map<String, Object> param = new HashMap<>();
        param.put("code", code);
        param.put("name", name);
        param.put("invoiceReceiptFlag", invoiceReceiptFlag);
        param.put("parentContractCode", parentContractCode);
        param.put("parentContractName", parentContractName);
        param.put("customerCode", customerCode);
        param.put("customerName", customerName);
        param.put("customerType", customerType);
        param.put("unitName", unitName);
        param.put("salesManagerName", salesManagerName);
        param.put("managerName", managerName);
        param.put("manyStatus", manyStatus);
        param.put("manyOuName", ouName);

        param.put("purchaseContractCode", purchaseContractCode);
        param.put("rdmPurchaseProject", rdmPurchaseProject);
        param.put("orPerson", orPerson);
        param.put("businessName", businessName);
        param.put("businessCodeOrName", businessCodeOrName);
        param.put("projectCode", projectCode);
        param.put("projectName", projectName);

        param.put("clientContractNumber", clientContractNumber);

        param.put("filingStartDate", filingStartDate);
        param.put("filingEndDate", filingEndDate);
        param.put("orderParam", orderParam);
        param.put("orderType", orderType);
        param.put("invoiceDetailIds", invoiceDetailIds);
        param.put("businessSegment", businessSegment);

        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "contract/children/page", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ContractVO>>>() {
        });
    }


    @ApiOperation(value = "我的子合同分页查询")
    @GetMapping("children/mypage")
    public Response myPageChildrenContracts(@RequestParam(required = false) @ApiParam("合同编号") final String code,
                                            @RequestParam(required = false) @ApiParam("合同名称") final String name,
                                            @RequestParam(required = false) @ApiParam("开票未回款金额是否大于0") final Boolean invoiceReceiptFlag,
                                            @RequestParam(required = false) @ApiParam("关联主合同编号") final String parentContractCode,
                                            @RequestParam(required = false) @ApiParam("关联主合同名称") final String parentContractName,
                                            @RequestParam(required = false) @ApiParam("客户CRM编码") final String customerCode,
                                            @RequestParam(required = false) @ApiParam("客户名称") final String customerName,
                                            @RequestParam(required = false) @ApiParam("客户属性") final Boolean customerType,
                                            @RequestParam(required = false) @ApiParam("销售部门") final String unitName,
                                            @RequestParam(required = false) @ApiParam("销售经理") final String salesManagerName,
                                            @RequestParam(required = false) @ApiParam("项目经理") final String managerName,
                                            @RequestParam(required = false) @ApiParam("归档开始时间") final String filingStartDate,
                                            @RequestParam(required = false) @ApiParam("归档结束时间") final String filingEndDate,
                                            @RequestParam(required = false) @ApiParam("合同状态(多选)") final String manyStatus,
                                            @RequestParam(required = false) @ApiParam("业务实体(多选)") final String ouName,

                                            @RequestParam(required = false) @ApiParam("关联采购合同") final String purchaseContractCode,
                                            @RequestParam(required = false) @ApiParam("关联RDM项目") final String rdmPurchaseProject,
                                            @RequestParam(required = false) @ApiParam("是否人力外包") final String orPerson,
                                            @RequestParam(required = false) @ApiParam("商机名称") final String businessName,
                                            @RequestParam(required = false) @ApiParam("关联商机") final String businessCodeOrName,

                                            @RequestParam(required = false) @ApiParam("开票计划Id") final String invoiceDetailIds,
                                            @RequestParam(required = false) @ApiParam("资源") final String resource,
                                            @RequestParam(required = false) @ApiParam("排序") final String orderParam,
                                            @RequestParam(required = false) @ApiParam("排序类型(1:升序；2：降序)") final Integer orderType,

                                            @RequestParam(required = false) @ApiParam("客户合同编号") final String clientContractNumber,
                                            @RequestParam(required = false) @ApiParam("业务分部") final String businessSegment,
                                            @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                            @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        Map<String, Object> param = new HashMap<>();
        param.put("code", code);
        param.put("name", name);
        param.put("invoiceReceiptFlag", invoiceReceiptFlag);
        param.put("parentContractCode", parentContractCode);
        param.put("parentContractName", parentContractName);
        param.put("customerCode", customerCode);
        param.put("customerName", customerName);
        param.put("customerType", customerType);
        param.put("unitName", unitName);
        param.put("salesManagerName", salesManagerName);
        param.put("businessName", businessName);
        param.put("businessCodeOrName", businessCodeOrName);
        param.put("managerName", managerName);
        param.put("manyStatus", manyStatus);
        param.put("manyOuName", ouName);

        param.put("purchaseContractCode", purchaseContractCode);
        param.put("rdmPurchaseProject", rdmPurchaseProject);
        param.put("orPerson", orPerson);

        param.put("filingStartDate", filingStartDate);
        param.put("filingEndDate", filingEndDate);
        param.put("resource", resource);
        if (StringUtils.isNotEmpty(invoiceDetailIds)) {
//            final String invoiceDetailIdStr = String.join(",", invoiceDetailIds.stream().map(String::valueOf).collect(Collectors.toList()));
            param.put("invoiceDetailIdStr", invoiceDetailIds);
        }
        param.put("orderParam", orderParam);
        param.put("orderType", orderType);
        param.put("clientContractNumber", clientContractNumber);
        param.put("businessSegment", businessSegment);
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "contract/children/mypage", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ContractVO>>>() {
        });
    }

    @ApiOperation(value = "根据起草人或合同编号查询合同")
    @GetMapping("selectContractBatch")
    public Response selectContractBatch(@RequestParam(required = false) @ApiParam("合同起草人") String drafterMiP,
                                        @RequestParam(required = false) @ApiParam("合同编号") String code,
                                        @RequestParam(required = false) @ApiParam("法务合同编号") String legalContractNum,
                                        @RequestParam(required = false) @ApiParam("合同名称") String name,
                                        @RequestParam(required = false) @ApiParam(value = "创建日期开始") String createAtStart,
                                        @RequestParam(required = false) @ApiParam(value = "创建日期结束") String createAtEnd) {
        Map<String, Object> param = new HashMap<>();
        param.put("drafterMiP", drafterMiP);
        param.put("code", code);
        param.put("legalContractNum", legalContractNum);
        param.put("name", name);
        param.put("createAtStart", createAtStart);
        param.put("createAtEnd", createAtEnd);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "contract/selectContractBatch", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<GlegalContractChangeDetailDto>>>() {
        });
    }
}
