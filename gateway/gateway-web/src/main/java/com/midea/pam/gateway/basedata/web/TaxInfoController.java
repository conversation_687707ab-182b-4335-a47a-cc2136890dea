package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.entity.TaxInfo;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.PageResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.common.utils.PamCurrentUserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;


@RestController
@Api("财务数据")
@RequestMapping("finance/taxInfo")
public class TaxInfoController extends ControllerHelper {
    @Autowired
    private RestTemplate restTemplate;

    @ApiOperation("(带参数)查询税码列表")
    @GetMapping("getTaxList")
    public Response getTaxList(@RequestParam(required = false) @ApiParam("税码") String taxCode,
                               @RequestParam(required = false) @ApiParam("税率")String taxRate,
                               @RequestParam(required = false) @ApiParam("类别") String taxType,
                               @RequestParam(required = false) @ApiParam("税值") String taxValue,
                               @RequestParam(required = false) @ApiParam("类型（合同下拉框 1，其他不传）") Integer type,
                               @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                               @RequestParam(required = false, defaultValue = "10") Integer pageSize) throws Exception {
        final Map<String, Object> param = new HashMap<>(5);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("taxCode", taxCode);
        param.put("taxRate", taxRate);
        param.put("taxType", taxType);
        param.put("taxValue", taxValue);
        param.put("type", type);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/finance/taxInfo/getTaxList", param);
        String res = restTemplate.getForObject(url, String.class);
        res = cleanStr(res);
        PageInfo<Map<String, Object>> data = JSON.parseObject(res, new TypeReference<PageInfo<Map<String, Object>>>() {
        });
        PageResponse<Map<String, Object>> response = Response.pageResponse();
        return response.convert(data);
    }


    @ApiOperation("查询税码列表")
    @GetMapping("getTaxInfoList")
    public Response listInfo(@RequestParam(required = false) String taxType,
                             @RequestParam(required = false) String taxValue,
                             @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                             @RequestParam(required = false, defaultValue = "10") Integer pageSize) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("taxType", taxType);
        param.put("taxValue", taxValue);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/finance/taxInfo/getTaxInfoList", param);
        String res = restTemplate.getForObject(url, String.class);
        res = cleanStr(res);
        PageInfo<Map<String, Object>> data = JSON.parseObject(res, new TypeReference<PageInfo<Map<String, Object>>>() {
        });
        PageResponse<Map<String, Object>> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation(value = "新增税码")
    @PostMapping("add")
    public Response add(@RequestBody TaxInfo taxInfo) {
        String url = String.format("%sfinance/taxInfo/add", ModelsEnum.BASEDATA.getBaseUrl());

        UserInfo user = CacheDataUtils.findUserByMip(PamCurrentUserUtil.getCurrentUserName());
        taxInfo.setCreateBy(user.getId());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, taxInfo, String.class);
        String res = cleanStr(responseEntity.getBody());
        DataResponse<String> response = Response.dataResponse();
        return response.setData(res);
    }

    @ApiOperation(value = "修改税码")
    @PutMapping("update")
    public Response update(@RequestBody TaxInfo taxInfo) {
        String url = String.format("%sfinance/taxInfo/update", ModelsEnum.BASEDATA.getBaseUrl());

        UserInfo user = CacheDataUtils.findUserByMip(PamCurrentUserUtil.getCurrentUserName());
        taxInfo.setUpdateBy(user.getId());
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.PUT, new HttpEntity<TaxInfo>(taxInfo), String.class);
        String res = cleanStr(responseEntity.getBody());
        DataResponse<String> response = Response.dataResponse();
        return response.setData(res);
    }

    @ApiOperation(value = "删除税码")
    @DeleteMapping("{id}")
    public Response delete(@PathVariable Long id) {
        String url = String.format("%sfinance/taxInfo/" + id, ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.DELETE, null, String.class);
        String res = cleanStr(responseEntity.getBody());
        DataResponse<String> response = Response.dataResponse();
        return response.setData(res);
    }
}
