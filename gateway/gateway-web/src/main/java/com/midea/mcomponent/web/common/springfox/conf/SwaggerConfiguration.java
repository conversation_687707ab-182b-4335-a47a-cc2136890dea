package com.midea.mcomponent.web.common.springfox.conf;

import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@Configuration
@EnableSwagger2
@EnableConfigurationProperties({ApiInfoProperties.class})
public class SwaggerConfiguration {

    @Value("${swagger.enable}")
    private boolean enable;

    public SwaggerConfiguration() {
    }

    @Bean
    public Docket myDocket() {
        Docket docket = new Docket(DocumentationType.SWAGGER_2);
        ApiInfo apiInfo = new ApiInfoBuilder()
                .title("API接口文档")
                .description("")
                .contact(new Contact("", "", ""))
                .version("2.0.0")
                .build();
        docket.apiInfo(apiInfo);
        docket.pathMapping("/").select().paths(PathSelectors.regex("^.*(?<!error)$")).build();
        if (enable) {
            docket.select().apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class)).build();
        } else {
            docket.select().apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
                    .paths(PathSelectors.none()).build();
        }
        return docket;
    }
}
