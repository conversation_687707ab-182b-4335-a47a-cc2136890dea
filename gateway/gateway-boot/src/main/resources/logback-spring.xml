<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true">
	<!--定义日志文件的存储地址 勿在 LogBack 的配置中使用相对路径 -->
	<include resource="org/springframework/boot/logging/logback/defaults.xml"/>
	<include resource="org/springframework/boot/logging/logback/console-appender.xml"/>
	<property name="log.path" value="./logs"/>
	<!-- 编码格式设置 -->
	<property name="ENCODING" value="UTF-8" />
	<!-- 文件输出格式 for apm -->
	<property name="APM_LOG_PATTERN" value="[%d{yyyy-MM-dd HH:mm:ss.SSS}][%p][{%thread}][GTID:%X{midea-apm-gtraceid}][TID:%X{midea-apm-traceid}][%logger{39}#%M %L]%m%n"/>
	<!-- 安全日志输出格式 for apm -->
	<property name="SECURITY_LOG_PATTERN" value="%m%n"/>
	<!-- 把日志pattern里的%tid占位符解析为链路ID -->
<!--	<conversionRule conversionWord="tid" converterClass="org.apache.skywalking.apm.toolkit.log.logback.v1.x.LogbackPatternConverter" />-->
	<property name="FILE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50}.%method@%line - %msg%n"/>

	<property name="securityMaxHistory" value="180"/>
	<property name="securityMaxFileSize" value="200MB"/>
	<property name="securityTotalSizeCap" value="10GB"/>

	<property name="infoMaxHistory" value="60"/>
	<property name="infoMaxFileSize" value="200MB"/>
	<property name="infoTotalSizeCap" value="90GB"/>

	<property name="errorMaxHistory" value="180"/>
	<property name="errorMaxFileSize" value="50MB"/>
	<property name="errorTotalSizeCap" value="5GB"/>

	<springProfile name ="dev,sit,ver,uat">
		<property name="securityMaxHistory" value="180"/>
		<property name="securityMaxFileSize" value="200MB"/>
		<property name="securityTotalSizeCap" value="10GB"/>

		<property name="infoMaxHistory" value="7"/>
		<property name="infoMaxFileSize" value="200MB"/>
		<property name="infoTotalSizeCap" value="20GB"/>

		<property name="errorMaxHistory" value="180"/>
		<property name="errorMaxFileSize" value="50MB"/>
		<property name="errorTotalSizeCap" value="5GB"/>
	</springProfile>

	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>${FILE_LOG_PATTERN}</pattern>
			<charset>${ENCODING}</charset>
		</encoder>
		<!--此日志appender是为开发使用，只配置最底级别，控制台输出的日志级别是大于或等于此级别的日志信息-->
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>INFO</level>
		</filter>
	</appender>

	<!-- 按照每天生成日志文件 -->
	<appender name="INFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<!-- 正在记录的日志文档的路径及文档名 -->
		<file>./logs/gateway.log</file>
		<append>true</append>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<!--日志文件输出的文件名 -->
			<FileNamePattern>./logs/gateway-%d{yyyy-MM-dd}.%i.log</FileNamePattern>
			<!--日志文件保留天数 -->
			<maxHistory>${infoMaxHistory}</maxHistory>
            <maxFileSize>${infoMaxFileSize}</maxFileSize>
            <totalSizeCap>${infoTotalSizeCap}</totalSizeCap>
			<!--启动时执行清除过期日志-->
			<cleanHistoryOnStart>true</cleanHistoryOnStart>
		</rollingPolicy>
		<!-- 此日志文件只记录info级别的 -->
		<filter class="ch.qos.logback.classic.filter.LevelFilter">   
			<level>info</level>
		</filter>
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>${APM_LOG_PATTERN}</pattern>
			<charset>${ENCODING}</charset>
		</encoder>

	</appender>

	<!-- 按照每天生成安全日志文件 -->
	<appender name="security_info" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<!-- 正在记录的日志文档的路径及文档名 -->
		<file>./logs/security.log</file>
		<append>true</append>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<!--日志文件输出的文件名 -->
			<FileNamePattern>./logs/security-%d{yyyy-MM-dd}.%i.log</FileNamePattern>
			<!--日志文件保留天数 -->
			<maxHistory>${securityMaxHistory}</maxHistory>
            <maxFileSize>${securityMaxFileSize}</maxFileSize>
            <totalSizeCap>${securityTotalSizeCap}</totalSizeCap>
			<!--启动时执行清除过期日志-->
			<cleanHistoryOnStart>true</cleanHistoryOnStart>
		</rollingPolicy>
		<!-- 此日志文件只记录info级别的 -->
		<filter class="ch.qos.logback.classic.filter.LevelFilter">   
			<level>info</level> 
		</filter>
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>${SECURITY_LOG_PATTERN}</pattern>
			<charset>${ENCODING}</charset>
		</encoder>

	</appender>

	<!-- 日志记录器，日期滚动记录 -->
	<appender name="ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<!-- 正在记录的日志文件的路径及文件名 --> 
		<file>./logs/gateway_error.log</file>
		<append>true</append>
		<!-- 日志记录器的滚动策略，按日期，按大小记录 -->
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<!--日志文件输出的文件名 -->
			<FileNamePattern>./logs/gateway-error-%d{yyyy-MM-dd}.%i.log</FileNamePattern>
			<!--日志文件保留天数 -->
			<maxHistory>${errorMaxHistory}</maxHistory>
			<maxFileSize>${errorMaxFileSize}</maxFileSize>
            <totalSizeCap>${errorTotalSizeCap}</totalSizeCap>
			<!--启动时执行清除过期日志-->
			<cleanHistoryOnStart>true</cleanHistoryOnStart>
		</rollingPolicy>
		<!-- 日志文件的格式 -->
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">       
			<pattern>${APM_LOG_PATTERN}</pattern>         
			<charset>${ENCODING}</charset>       
		</encoder>
		<!-- 此日志文件只记录error级别的 -->
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>error</level>
		</filter>
	</appender>

	<logger name="java.sql.ResultSet" level="DEBUG"/>
	<logger name="java.sql.PreparedStatement" level="DEBUG"/>
	<logger name="java.sql.Statement" level="DEBUG"/>
	<logger name="org.quartz" level="ERROR"/>
	<logger name="org.activiti" level="ERROR"/>
	<logger name="org.apache" level="ERROR"/>
	<logger name="org.springframework" level="ERROR"/>
	<logger name="org.springframework.session" level="ERROR"/>
	<logger name="org.springframework.jdbc" level="ERROR"/>
	<logger name="org.springframework.scheduling" level="ERROR"/>
	<logger name="java.sql.Connection" level="ERROR"/>
	<logger name="com.alibaba.dubbo" level="ERROR"/>
	<logger name="com.alibaba.druid" level="ERROR"/>
	<logger name="sun.net.www" level="ERROR"/>
	<logger name="sun.rim" level="ERROR"/>
	<logger name="org.apache.zookeeper" level="ERROR"/>
	<logger name="org.springframework.beans.factory.support" level="ERROR"/>
	<logger name="org.springframework.data.redis" level="ERROR"/>
	<logger name="org.springframework.session.data.redis" level="ERROR"/>
	<logger name="com.meicloud.uc" level="ERROR"/>
	<logger name="org.I0Itec" level="ERROR"/>
	<logger name="com.mchange.v2" level="ERROR"/>
	<logger name="com.netflix.discovery.shared" level="ERROR"/>
	<logger name="org.apache.cxf" level="INFO"/>
	<logger name="com.netflix" level="ERROR"/>
	<logger name="com.midea.pam" level="INFO"/>
	<!--安全日志-->
	<logger name="security_info" level="INFO">
		<appender-ref ref="security_info"/>
	</logger>

	<!-- 日志输出级别 -->
	<root level="INFO">
		<appender-ref ref="STDOUT"/>
		<appender-ref ref="INFO"/>
		<appender-ref ref="ERROR"/>
	</root>

<!--	<root level="DEBUG">-->
<!--		<appender-ref ref="CONSOLE"/>-->
<!--	</root>-->

</configuration>
