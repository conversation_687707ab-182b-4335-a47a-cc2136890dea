#DEV/SIT
document.transmission.credential.upload=https://imipsit.midea.com/mip-document/document/docTranmission3/credential/upload?systemId=%s&moduleName=%s&moduleId=%s&sectionId=%s
document.transmission.public.upload=https://imipsit.midea.com/mip-document/document/docTranmission3/public/upload?systemId=%s&moduleName=%s&moduleId=%s&sectionId=%s
document.info.query.getById=https://imipsit.midea.com/mip-document/document/sys/docMain2/getById
document.info.query.getByIds=https://imipsit.midea.com/mip-document/document/sys/docMain2/getByIds
document.info.query.searchDocs=https://imipsit.midea.com/mip-document/document/sys/docMain2/searchDocs
document.info.query.getLinks=https://imipsit.midea.com/mip-document/document/sys/docMain3/getLinks
document.info.query.getConvertStatusForDocs=https://imipsit.midea.com/mip-document/document/sys/docMain2/getConvertStatusForDocs
document.info.query.getConvertStatusForDoc=https://imipsit.midea.com/mip-document/document/sys/docMain2/getConvertStatusForDoc
document.transmission.callback=https://imipsit.midea.com/mip-document/document/docTranmission2/upload/callback

document.pdfConvert.getPdf.url= https://imipsit.midea.com/mip-doc-convert/document/conversion/convertDocToPdf?fd=%s&ext=%s&sys=mip-doc&src=%s
document.pdfConvert.getPageCount.url= https://imipsit.midea.com/mip-doc-convert/document/conversion/getPageCountForPdf?fd=%s&ext=%s&sys=mip-doc&src=%s
document.info.query.canConvertByExt= https://imipsit.midea.com/mip-document/document/sys/docMain3/canConvertByExt
document.info.query.canConvertById= https://imipsit.midea.com/mip-document/document/sys/docMain3/canConvertById
