spring:
  datasource:
    platform: mysql
    #    VER配置
    #    url: *********************************************************************************************************************************************
    #    username: pam_ver
    #    password: tqVhr1zM5

    #    DEV配置
    #    url: ********************************************************************************************************************************************
    #    username: pam_dev
    #    password: EmgPn2fo9

    #    SIT配置
    url: *********************************************************************************************************************************************
    username: pam_sit
    password: tqVhr1zM5

    #    UAT配置
    #    url: ********************************************************************************************************************************************
    #    username: pam_uat
    #    password: buSe77kIp

    driverClassName: com.mysql.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    initialSize: 5
    minIdle: 5
    maxActive: 50
    # 配置获取连接等待超时的时间
    maxWait: 60000
    validationQuery: SELECT 1
    testOnBorrow: false
    testOnReturn: false
    testWhileIdle: true
    minEvictableIdleTimeMillis: 25200000
    # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
    timeBetweenEvictionRunsMillis: 60000
    removeAbandoned: true
    removeAbandonedTimeout: 1800
    # 打开PSCache，并且指定每个连接上PSCache的大小
    poolPreparedStatements: true
    maxPoolPreparedStatementPerConnectionSize: 20
    logAbandoned: true
    # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
    filters: stat,wall,log4j
    useGlobalDataSourceStat: true
  cluster:
    redis:
      #      VER配置
      #      host: 6379-W-C-PAM-VER-01-REC4.service.dc_sd.consul
      #      port: 6379
      #      password: cpwD9ic3R
      #      database: 0

      #      DEV配置
      #      host: ************
      #      port: 6379
      #      password: T1qgbo4Gh
      #      database: 0

      #      SIT配置
      host: *************
      port: 6379
      password: ygwHPik57
      database: 0

      #      UAT配置
      #      host: ************
      #      port: 6379
      #      password: m7zJ9pLog
      #      database: 0

      masterName: cluster6379
      maxTotal: 200
      maxIdle: 50
      numTestsPerEvictionRun: 1024
      maxWaitMillis: 1500
      blockWhenExhausted: false
      type: singleton
      expire: 1800
  servlet:
    multipart:
      max-file-size: 501MB
      max-request-size: 501MB
mip:
  conf:
    defaultLocale: zh_CN
    debugPageResult: false
    recordOperateLog: true
    supportLocales:
      - zh_CN
      - en_US
      - ja_JP
    unwarpUrls:
      - /**
  api-info:
    title: pam
    description:
    version: 1.0.0
    termsOfServiceUrl:
    contact:
      name: oujh5
      url:
      email:
    license:
    licenseUrl:
    pathsRegex: ^.*(?<!error)$
  workflow:
    interfaceUrl: https://iflowsit.midea.com/mbpmEngineECO/data/
    appId: pam-dev
    secretKey: 95b9b1a3-7328-4c0f-a362-b131db25b4ff
eureka:
  instance:
    prefer-ip-address: true
  client:
    serviceUrl:
      defaultZone: http://localhost:8761/eureka
mobile:
  app:
    security:
      serviceUrl: https://mapnew5sit.midea.com/muc/v5/app/emp/get #移动端获取用户信息地址,测试环境地址http://************/muc/v5/app/emp/get,生产环境用https://mapnew5.midea.com/muc/v5/app/emp/get
      accessTokenKey: accessToken #移动端accessTokenKey,值由调用的APP传入
      interceptionPath: /mobile/app/ #移动端请求接口拦截路径
glegal:
  pushinfo:
    interfaceUrl:  http://glegalsit.midea.com/contract-application/api/
    interfaceUrlSec:  http://glegalsit.midea.com/property-application/api/
    appId: C-PAM
    secretKey: 763d0e71-6656-4c36-bf6c-31959f53d5b4

esb_client:
  JMSConfiguration:
    channel: PAM.DEF.SVRCONN
    connectionNameList: ************(11001),************(12001)
    CCSID: 1208
    transportType: 1
  targetDestination: LOCALQ.C.PAM.REQ
  receiveTimeout: 120000
  replyDestination: LOCALQ.PAM.RSP
  longReceiveTimeout: 600000
esb_server:
  JMSConfiguration:
    channel: PAM.DEF.SVRCONN
    connectionNameList1: ************(111001)
    connectionNameList2: ************(121001)
    CCSID: 1208
    transportType: 1
  sessionCacheSize: 100
  targetDestination: LOCALQ.P.PAM.REQ
  concurrentConsumers: 5
esb:
  gfp:
    securitycode: 123456
sdk:
  mform:
    bgHost:   http://iflowsit.midea.com/formEngine
    uploadServiceName: /mip-document/document/sys/docTranmission/upload
    downLoadServiceName: /mip-document/document/docTranmission2/public/download
    getInfoServiceName: /mip-document/document/sys/docMain/getById
    uploadRestUrlHeader:   http://imipsit.midea.com # sit
    #uploadRestUrlHeader:   http://mipuat.midea.com # uat
    #uploadRestUrlHeader:   http://imipdev.midea.com # dev
    #     messageRestUrl:   http://devcloudsit.midea.com:61000/mframework/excel/mframeworkExcelResult
    #    appKey: f1d82e0b-26ba-40c3-8be8-14ded9fff336 # sit appkey
    #appKey: adcaca23-e280-4947-8ac4-a680b55c8e68 # dev appkey
    appKey: bbdc9d4e-60f5-44dd-b095-ef01a1b8fcec # uat appkey
    moduleName: SRMGSC
    appID: SRMFILE
    forwardTo: mip4cdev5.midea.com:60050
    serviceName: /document/sys/docTransmission/download
xxl:
  job:
    enabled: false
    admin:
      addresses: https://apisit.midea.com/djs
    executor:
      version: 2.0
      port: 9990
      appName: PAMJOB-DEV-GATEWAY
      logPath: /apps/pam/logs/xxl-job/jobhandler
      logretentiondays: 7
      accessKeyId: vtYA2DSLDLpJnunayh3SZivf
      accessKeySecret: 18kM1A09xXX9Hei06ThZhc3KgDCTDca2
      idmTokenUrl: https://apisit.midea.com/iam/v1/security/getAccessToken
    #    ip: ************
    accessToken: 9b7171d0d7fa4f60b0a053e0f8f4b09d

meixing:
  tempMsgUrl: http://mapnew5sit.midea.com/mpm-api/api/tempmsg/create_temp_msg_and_push
  appKey: e21c13e0-b57a-49f1-985b-01af30232e1a
  accessToken: 2f5b38ff-f412-40aa-8a7d-0e004660eed9
  mcWidgetIdentifier: sit

rdm:
  url: http://itrdmsit.midea.com:8888

geam:
  url: http://eamtest.midea.com:9080/sit

# 前端路由
route:
  milepostUrl: https://pamsit.midea.com:8080/#/project/detail/%?active=milestone
  milepostNoticeUrl: https://pamsit.midea.com:8080/#/project/milestone-warn-list?batchId=
  workingHourUrl: https://pamsit.midea.com:8080/#/workhour/my?active=audit&userId=
  contractUrl: https://pamsit.midea.com:8080/#/contract/detail/
  projectUrl: https://pamsit.midea.com:8080/#/project/detail/
  subContractUrl: https://pamsit.midea.com:8080/#/contract/subcontract-detail/
  projectAwardUrl: https://pamsit.midea.com:8080/#/projectAward/project-award?formUrl=projectAwardApp

business:
  carryoverBillCheckFilePath: /apps/pam/gateway/file/
  meiCloudUnitId: 616744224850706432 # 美云-使用单位ID
  yueyunUnitId: 666278517574467584 # 粤云-使用单位ID
  robotUnitId: 581144287652085760 # 机器人-使用单位ID
  kukaRouxingUnitId: 1008730763140530176
imip:
  message:
    host: http://imipdev.midea.com
    appid: PAM_SYS
    fdKey: PAM_SYS
    fdModelName: PAM_SYS
    key: ac0e38eb-5f1a-4ec2-9588-f5a29f128584
mgp:
  carrier:
    discovery:
      # 服务治理组件简称（跟组件英文简称大小写保持一致）
      system: pam
      # 服务版本（默认值版本1.0）
      version: 1.0
      # 服务生命态：（默认取spring.profiles.active，只支持dev,sit,uat,ver,prd五种常量配置）
      env: sit
swagger:
  enable: false