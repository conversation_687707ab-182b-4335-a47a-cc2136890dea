package com.midea.mcomponent.sso.sys.authentication.security.config;

import com.midea.idm.mas.client.MasApiClient;
import com.midea.mcomponent.sso.sys.authentication.security.MframeworkAuthenticationEntryPoint;
import com.midea.mcomponent.sso.sys.authentication.security.filter.MframeworkAuthPermissionFilter;
import com.midea.mcomponent.sso.sys.authentication.security.filter.MframeworkLoginFilter;
import com.midea.mcomponent.sso.sys.authentication.security.filter.MideaSSOAuthenticationFilter;
import com.midea.mcomponent.sso.sys.authentication.security.login.intf.ICustomLoginHandler;
import com.midea.mcomponent.sso.sys.authentication.security.logout.MframeworkLogoutHandler;
import com.midea.mcomponent.sso.sys.authentication.security.provider.MframeworkAuthenticationProvider;
import com.midea.mcomponent.sso.sys.authentication.security.request.MframeworkSecurityContextRepository;
import com.midea.mcomponent.sso.sys.util.SSOConstant;
import com.midea.mcomponent.sso.sys.util.URLValidator;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.*;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;
import org.springframework.stereotype.Service;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 */
@Configuration
@Service
@EnableWebSecurity
public class WebSecurityConfig extends WebSecurityConfigurerAdapter{


    private final  Logger logger = LoggerFactory.getLogger(WebSecurityConfig.class);

    @Autowired
    private SecuritySetting securitySetting;
    @Autowired
    private MframeworkAuthenticationProvider authenticationProvider;
    @Autowired(required = false)
    private ICustomLoginHandler customLoginHandler;
    @Autowired
    private MframeworkAuthPermissionFilter authPermissionFilter;
    @Autowired
    private MframeworkSecurityContextRepository contextRepository;
    @Autowired
    private MframeworkLogoutHandler mframeworkLogoutHandler;

    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean(){
        AuthenticationManager authenticationManager = null;
        try {
            authenticationManager = super.authenticationManagerBean();
        } catch (Exception e) {
            logger.error("com.midea.mcomponent.sso.sys.authentication.security.config.WebSecurityConfig.authenticationManagerBean,异常",e);
        }
        return authenticationManager;
    }

    MideaSSOAuthenticationFilter ssoAuthFilter(){
        MideaSSOAuthenticationFilter ssoAuthFilter = new MideaSSOAuthenticationFilter("/**");
        ssoAuthFilter.setAuthenticationManager(authenticationManagerBean());
        ssoAuthFilter.setAuthenticationSuccessHandler(new AuthenticationSuccessHandler(){

            //token授权成功后继续访问当前连接
            @Override
            public void onAuthenticationSuccess(HttpServletRequest request,
                                                HttpServletResponse response, Authentication authentication)
                    throws IOException, ServletException {
                String targetUrl = URLValidator.getValidatorURI(request);
                request.getRequestDispatcher(StringUtils.isEmpty(targetUrl) ? "/" : targetUrl).forward(request, response);
            }

        });
        SimpleUrlAuthenticationFailureHandler failHandler = new SimpleUrlAuthenticationFailureHandler(SSOConstant.UnLogin);
        failHandler.setUseForward(true);
        ssoAuthFilter.setAuthenticationFailureHandler(failHandler);
        ssoAuthFilter.setSecuritySetting(securitySetting);
        ssoAuthFilter.setMasApiClient(masApiClient());
        return ssoAuthFilter;
    }

    MframeworkLoginFilter loginFilter(){
        MframeworkLoginFilter loginFilter = new MframeworkLoginFilter("/**");
        loginFilter.setAuthenticationManager(authenticationManagerBean());
        loginFilter.setAuthenticationSuccessHandler(authenticationSuccessHandler());
        loginFilter.setAuthenticationFailureHandler(authenticationFailureHandler());
        loginFilter.setMasApiClient(masApiClient());
        loginFilter.setSecuritySetting(securitySetting);
        loginFilter.setCustomLoginHandler(customLoginHandler);
        return loginFilter;
    }

    @Bean
    AuthenticationSuccessHandler authenticationSuccessHandler(){
        return new ForwardAuthenticationSuccessHandler(SSOConstant.LoginSuccess);
    }

    @Bean
    AuthenticationFailureHandler authenticationFailureHandler(){
        return new ForwardAuthenticationFailureHandler(SSOConstant.LoginFail);
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {

        http
                .securityContext().securityContextRepository(contextRepository).and()
                .csrf().disable()
                .exceptionHandling()
                .authenticationEntryPoint(new MframeworkAuthenticationEntryPoint())
                .and()
                .authenticationProvider(authenticationProvider)
                .addFilterBefore(ssoAuthFilter(), UsernamePasswordAuthenticationFilter.class)
                .addFilterAfter(loginFilter(), MideaSSOAuthenticationFilter.class)
                .addFilterAfter(authPermissionFilter, MframeworkLoginFilter.class)
                .authorizeRequests()
                .antMatchers(securitySetting.getAnonymousUrls()).permitAll()
                .anyRequest().authenticated()
                .and()
                .formLogin().loginPage(SSOConstant.Login).failureUrl(SSOConstant.LoginFail).permitAll()
                .and()
                .logout().addLogoutHandler(mframeworkLogoutHandler).logoutUrl(SSOConstant.Logout).logoutSuccessHandler(
                        new LogoutSuccessHandler() {

                            @Override
                            public void onLogoutSuccess(HttpServletRequest request,
                                                        HttpServletResponse response, Authentication authentication)
                                    throws IOException, ServletException {
                                request.getRequestDispatcher(SSOConstant.LoginSuccess).forward(request, response);
                            }
                        }).permitAll().and();
    }

    /**
     *  mas client api 超时时间，默认10s
     */
    @Value("${mas.api.connectTimeout:10000}")
    private int connectTimeout;
    @Value("${mas.api.readTimeout:10000}")
    private int readTimeout;
    @Value("${mas.api.requestTimeout:10000}")
    private int requestTimeout;

    @Bean
    public MasApiClient masApiClient() {
        MasApiClient  masApiClient = new MasApiClient(securitySetting.getMasAppId(),
                securitySetting.getMasAppKey(), securitySetting.getMasLoginUrl());
        //可选,默认1000ms
        masApiClient.setConnectTimeout(connectTimeout);
        //可选,默认1000ms
        masApiClient.setReadTimeout(readTimeout);
        //可选,默认1000ms
        masApiClient.setRequestTimeout(requestTimeout);

        return masApiClient;
    }
}
