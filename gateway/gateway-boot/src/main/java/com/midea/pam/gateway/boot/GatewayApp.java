package com.midea.pam.gateway.boot;

import org.apache.commons.lang.StringUtils;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.netflix.zuul.EnableZuulProxy;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.FilterType;
import org.springframework.scheduling.annotation.EnableAsync;

@ComponentScan(basePackages = {"com.midea.devcloud.sdk", "com.midea.mframework", "com.midea.mcomponent","com.midea.pam.gateway.service","com.midea.pam.gateway.job", "com.midea.mbf"}, excludeFilters = @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = {com.midea.mcomponent.sso.sys.authentication.security.filter.MframeworkRequestTimeComsumingFilter.class}))
@SpringBootApplication(scanBasePackages = {"com.midea.devcloud.sdk", "com.midea.mframework", "com.midea.mcomponent","com.midea.pam.gateway.service","com.midea.pam.gateway.job"})
@EnableAspectJAutoProxy(proxyTargetClass = true)
@EnableAsync
@MapperScan({"com.midea.**.mapper*"})
@EnableFeignClients(basePackages = {"com.midea.mframework", "com.midea.mcomponent","com.midea.pam.gateway.remote","com.midea.pam.gateway.feign"})
@EnableDiscoveryClient
@EnableZuulProxy
public class GatewayApp {
    protected  static final Logger logger = LoggerFactory.getLogger(GatewayApp.class);
    public static void main(String[] args) {

        SpringApplication.run(GatewayApp.class, args);
        logger.info(StringUtils.rightPad("*", 80, "*"));
        logger.info(StringUtils.center(" GatewayApp start success! ", 80, "*"));
        logger.info(StringUtils.rightPad("*", 80, "*"));
    }

}
