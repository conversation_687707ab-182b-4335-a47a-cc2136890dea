package com.midea.pam.gateway;

import org.aspectj.lang.annotation.Aspect;

@Aspect
public class AccessLogAspect {

//	private static final String LOGGER_NAME = "OTC_ACCESS";
//
//	private boolean logResponse;
//
//	public AccessLogAspect setLogResponse(boolean logResponse) {
//		this.logResponse = logResponse;
//		return this;
//	}
//
//	@Pointcut(value = "execution(* com.meicloud.otc..web.*Controller.*(..))")
//	public void controllerMethodPointcut() {
//	}
//
//	@Around(value = "controllerMethodPointcut()")
//	public Object handleAccessLogTrail(final ProceedingJoinPoint joinPoint) throws Throwable {
//
//		final StringBuilder log = new StringBuilder();
//		try {
//
//			User user = AuthenticationUserLocalBinder.getCurrentUser();
//			if (user != null) {
//				log.append("user[{'id: ").append(user.getId())
//						.append(", username: ").append(user.getUsername())
//						.append(", name: ").append(user.getName()).append("}]");
//			}
//			appendMDC(log, "RequestId");
//			appendMDC(log, "protocol");
//			appendMDC(log, "contextPath");
//			appendMDC(log, "requestUri");
//			appendMDC(log, "method", "httpMethod");
//			appendMDC(log, "queryString");
//
//			Instant start = Instant.now();
//
//			Object retVal = joinPoint.proceed();
//
//			long cost = Duration.between(start, Instant.now()).toMillis();
//			log.append("\nRequest Success! cost ").append(cost).append(" milliseconds");
//
//			if (logResponse) {
//				log.append("\nResponse:\n").append(JSON.toJSONString(retVal));
//			}
//			return retVal;
//		} catch (Throwable e) {
//			StringBufferWriter writer = new StringBufferWriter(new StringBuffer());
//			PrintWriter printWriter = new PrintWriter(writer);
//			e.printStackTrace(printWriter);
//			log.append("\nRequest Failed!").append("\nReason: ").append(e.getMessage()).append("\n").append(writer.toString());
//			throw e;
//		} finally {
//			Logger logger = LoggerFactory.getLogger(LOGGER_NAME);
//			logger.info("==>\n" + log.toString() + "\n<==");
//		}
//	}
//
//	private void appendMDC(final StringBuilder log, String key) {
//		appendMDC(log, key, null);
//	}
//
//	private void appendMDC(final StringBuilder log, String key, String name) {
//		if (log.length() > 0) {
//			log.append(", ");
//		}
//		if (name != null) {
//			log.append(name);
//		} else {
//			log.append(key);
//		}
//		log.append(": [").append(MDC.get(key)).append("]");
//	}
}
