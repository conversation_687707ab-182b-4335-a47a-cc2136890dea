package com.midea.pam.gateway.config;

import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

import javax.annotation.Resource;

@Configuration
@EnableConfigurationProperties(AccessLogProperties.class)
@EnableAspectJAutoProxy
public class AutoConfiguration {

    @Resource
    private AccessLogProperties accessLogProperties;

    @Bean
    public CommonExceptionHandler ltcCommonExceptionHandler() {
        return new CommonExceptionHandler();
    }

//	@Bean
//	public AccessLogAspect accessLogAspect() {
//		return new AccessLogAspect().setLogResponse(accessLogProperties.isLogResponse());
//	}
}
