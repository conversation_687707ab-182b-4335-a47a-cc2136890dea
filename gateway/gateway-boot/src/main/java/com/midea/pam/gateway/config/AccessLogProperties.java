package com.midea.pam.gateway.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "accesslog")
public class AccessLogProperties {
	private boolean logResponse = true;

	public boolean isLogResponse() {
		return logResponse;
	}

	public AccessLogProperties setLogResponse(boolean logResponse) {
		this.logResponse = logResponse;
		return this;
	}
}
